package excel

import "github.com/xuri/excelize/v2"

type StyleOptions struct {
	Font      *excelize.Font
	Fill      excelize.Fill
	Alignment *excelize.Alignment
	Border    []excelize.Border
}

type ExcelStyle struct {
	HeaderStyle  *StyleOptions
	TickStyle    *StyleOptions
	CrossStyle   *StyleOptions
	ContentStyle *StyleOptions
}

func DefaultExcelStyle() *ExcelStyle {
	return &ExcelStyle{
		HeaderStyle: &StyleOptions{
			Font: &excelize.Font{
				Bold:   true,
				Color:  "FFFFFF",
				Family: "Arial",
				Size:   11,
			},
			Fill: excelize.Fill{
				Type:    "pattern",
				Color:   []string{"4472C4"},
				Pattern: 1,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
			},
			Border: []excelize.Border{
				{Type: "left", Color: "FFFFFF", Style: 1},
				{Type: "top", Color: "FFFFFF", Style: 1},
				{Type: "bottom", Color: "FFFFFF", Style: 1},
				{Type: "right", Color: "FFFFFF", Style: 1},
			},
		},
		TickStyle: &StyleOptions{
			Font: &excelize.Font{
				Bold:   true,
				Color:  "008000",
				Family: "Arial",
				Size:   11,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
			},
		},
		CrossStyle: &StyleOptions{
			Font: &excelize.Font{
				Bold:   true,
				Color:  "FF0000",
				Family: "Arial",
				Size:   11,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
			},
		},
		ContentStyle: &StyleOptions{
			Font: &excelize.Font{
				Family: "Arial",
				Size:   11,
			},
			Alignment: &excelize.Alignment{
				Vertical: "center",
			},
		},
	}
}

func (c *Excel) createStyles(configs map[string]*StyleOptions) (map[string]int, error) {
	styles := make(map[string]int)
	for name, config := range configs {
		if config != nil {
			styleID, err := c.CreateStyle(config)
			if err != nil {
				return nil, err
			}
			styles[name] = styleID
		}
	}
	return styles, nil
}

func (c *Excel) CreateStyle(opts *StyleOptions) (int, error) {
	style := &excelize.Style{
		Font:      opts.Font,
		Fill:      opts.Fill,
		Alignment: opts.Alignment,
		Border:    opts.Border,
	}

	return c.client.NewStyle(style)
}

func (c *Excel) ApplyStyle(sheetName, cellRange string, styleID int) error {
	return c.client.SetCellStyle(sheetName, cellRange, cellRange, styleID)
}
