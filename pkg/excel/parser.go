package excel

import (
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"openedu-core/pkg/util"
	"reflect"
	"strings"
)

// getExcelTag extracts the excel tag from a struct field
func getExcelTag(field reflect.StructField) string {
	tag := field.Tag.Get("excel")
	if tag == "-" {
		return ""
	}
	return tag
}

// findColumnIndex finds the index of a column by its header name
func findColumnIndex(headers []string, columnName string) int {
	for i, header := range headers {
		if strings.TrimSpace(header) == columnName {
			return i
		}
	}
	return -1
}

// ParseValue parses a string value into the appropriate type for a struct field
func ParseValue(fieldType reflect.Type, value string) (reflect.Value, error) {
	value = strings.TrimSpace(value)
	if value == "" {
		return reflect.Zero(fieldType), nil
	}

	switch fieldType.String() {
	case "string":
		return reflect.ValueOf(value), nil
	case "int", "int64":
		var intVal int64
		_, err := fmt.Sscanf(value, "%d", &intVal)
		if err != nil {
			return reflect.Value{}, fmt.Errorf("error parsing int: %w", err)
		}
		return reflect.ValueOf(intVal).Convert(fieldType), nil
	case "float64":
		var floatVal float64
		_, err := fmt.Sscanf(value, "%f", &floatVal)
		if err != nil {
			return reflect.Value{}, fmt.Errorf("error parsing float: %w", err)
		}
		return reflect.ValueOf(floatVal), nil
	case "decimal.Decimal":
		dec, err := decimal.NewFromString(value)
		if err != nil {
			return reflect.Value{}, fmt.Errorf("error parsing decimal: %w", err)
		}
		return reflect.ValueOf(dec), nil
	case "bool":
		boolVal := strings.ToLower(value) == "true" || value == "1"
		return reflect.ValueOf(boolVal), nil
	default:
		return reflect.Value{}, fmt.Errorf("unsupported type: %s", fieldType.String())
	}
}

// ParseExcel parses an Excel file into a slice of structs of type T
func ParseExcel[T any](file *excelize.File, sheetName string) ([]*T, error) {
	rows, err := file.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("error getting rows: %w", err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("file has insufficient rows")
	}

	headers := rows[0]

	var zero T
	t := reflect.TypeOf(zero)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	columnMap := make(map[string]int)
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		if excelTag := getExcelTag(field); excelTag != "" {
			if colIndex := findColumnIndex(headers, excelTag); colIndex != -1 {
				columnMap[field.Name] = colIndex
			}
		}
	}

	var result []*T
	for rowIndex, row := range rows[1:] {
		item := reflect.New(t).Elem()

		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			if colIndex, ok := columnMap[field.Name]; ok && colIndex < len(row) {
				cellValue := row[colIndex]

				if parsedValue, err := ParseValue(field.Type, cellValue); err == nil {
					item.Field(i).Set(parsedValue)
				} else {
					return nil, fmt.Errorf("error parsing value at row %d, column %s: %w", rowIndex+2, field.Name, err)
				}
			}
		}

		result = append(result, util.NewT(item.Interface().(T)))
	}

	return result, nil
}
