package openedu_scheduler

import (
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/setting"
)

const (
	scheduledPath = "/scheduled-jobs"
)

type ScheduledService struct {
	httpClient *httpclient.Client
}

type ScheduledServiceIface interface {
	CreateScheduledJob(job *ScheduledJobRequest) error
	UpdateScheduledJob(jobID string, job *ScheduledJobRequest) error
	DeleteScheduledJob(jobID string) error
}

func NewScheduledService(httpClient *httpclient.Client) ScheduledServiceIface {
	return &ScheduledService{
		httpClient: httpClient,
	}
}

var Scheduled ScheduledServiceIface

func Setup() {
	httpClient := httpclient.NewClient(setting.OpeneduSchedulerSetting.Domain, nil)

	Scheduled = NewScheduledService(httpClient)
}
