package openedu_scheduler

import (
	"encoding/json"
	"openedu-core/models"
)

type ScheduledJobType string

type ScheduledJob<PERSON>ey string

type JobExecutionStatus string

const (
	Success JobExecutionStatus = "success"
	Failed  JobExecutionStatus = "failed"

	OnceJobType ScheduledJobType = "once"
	CronJobType ScheduledJobType = "cron"

	StartFundingLaunchpadJobKey      ScheduledJobKey = "start_funding"
	EndFundingLaunchpadJobKey        ScheduledJobKey = "end_funding"
	EndVotingMilestoneJobKey         ScheduledJobKey = "end_voting"
	CheckSettingFundingTimeJobKey    ScheduledJobKey = "check_setting_funding_time"
	CheckCreatorContinueVotingJobKey ScheduledJobKey = "check_creator_continue_voting"
)

type ScheduledJobRequest struct {
	Name           string           `json:"name" gorm:"varchar(255)" binding:"required"`
	Key            ScheduledJobKey  `json:"key" gorm:"varchar(255)" binding:"required,scheduled_job_key"`
	Type           ScheduledJobType `json:"type" gorm:"varchar(128)" binding:"required,scheduled_job_type"`
	Args           models.JSONB     `json:"args" gorm:"type:jsonb"`
	ScheduleAt     int64            `json:"schedule_at" gorm:"type:int8;not null;default:0"`
	CronExpression string           `json:"cron_expression" gorm:"varchar(128)"`
	Active         bool             `json:"active"`
}

type CurlArgs struct {
	URL     string                 `json:"url"`
	Method  string                 `json:"method"`
	Headers map[string]string      `json:"headers"`
	Body    map[string]interface{} `json:"body"`
}

func NewCurlArgsFromJSONB(j models.JSONB) (*CurlArgs, error) {
	var args CurlArgs
	b, err := json.Marshal(&j)
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal(b, &args); err != nil {
		return nil, err
	}
	return &args, nil
}

func (args *CurlArgs) ToJSONB() models.JSONB {
	return models.JSONB{
		"url":     args.URL,
		"method":  args.Method,
		"headers": args.Headers,
		"body":    args.Body,
	}
}
