package producer

import (
	"errors"
	"fmt"
	amqp "github.com/rabbitmq/amqp091-go"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"sync"
	"time"
)

const (
	defaultQueueDurable    = true
	defaultQueueAutoDelete = false
	defaultQueueExclusive  = false
	defaultQueueNoWait     = false

	defaultConsumeAutoAck   = false
	defaultConsumeExclusive = false
	defaultConsumeNoLocal   = false
	defaultConsumeNoWait    = false

	defaultMandatory = false
	defaultImmediate = false
)

type RabbitMQProducer struct {
	mu    sync.Mutex
	alive bool

	amqpConn *amqp.Connection
	amqpChan *amqp.Channel
}

func newRabbitMQProducer() (*RabbitMQProducer, error) {
	p := &RabbitMQProducer{}
	if err := p.connect(); err != nil {
		return nil, err
	}
	return p, nil
}

func (p *RabbitMQProducer) connect() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.alive {
		return nil
	}

	conn, err := amqp.Dial(setting.RabbitMQSetting.URL)
	if err != nil {
		return fmt.Errorf("connect rabbitmq error: %w", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		if err = conn.Close(); err != nil {
			log.Errorf("Failed to close RabbitMQ connection after declare channel failed: %v", err)
		}
		return fmt.Errorf("create rabbitmq channel error: %w", err)
	}

	p.amqpConn = conn
	p.amqpChan = ch
	p.alive = true
	return nil
}

func (p *RabbitMQProducer) Close() error {
	if err := p.amqpChan.Close(); err != nil {
		return err
	}
	return p.amqpConn.Close()
}

func (p *RabbitMQProducer) Publish(queueName string, msg *Message) error {
	var queue amqp.Queue
	var err error

	err = p.withReconnect(func() error {
		var declareErr error
		queue, declareErr = p.declareQueue(queueName)
		return declareErr
	})
	if err != nil {
		return fmt.Errorf("declare rabbitmq queue %s error: %w", queueName, err)
	}

	publishing := amqp.Publishing{
		ContentType:  msg.ContentType,
		DeliveryMode: amqp.Persistent,
		MessageId:    msg.ID,
		Timestamp:    time.Now(),
		Body:         msg.Body,
		ReplyTo:      msg.ReplyTo,
	}
	if msg.CorrelationID != nil {
		publishing.CorrelationId = *msg.CorrelationID
	}

	return p.amqpChan.Publish(
		"",
		queue.Name,
		defaultMandatory,
		defaultImmediate,
		publishing,
	)
}

func (p *RabbitMQProducer) PublishRPC(queueName string, msg *Message) (*Message, error) {
	// Declare temporary queue with reconnect logic
	var q amqp.Queue
	err := p.withReconnect(func() error {
		var dErr error
		q, dErr = p.amqpChan.QueueDeclare(
			"",    // name
			false, // durable
			true,  // delete when unused
			true,  // exclusive
			false, // no wait
			nil,   // arguments
		)
		return dErr
	})
	if err != nil {
		return nil, fmt.Errorf("declare reply queue error: %w", err)
	}

	defer func() {
		if _, dErr := p.amqpChan.QueueDelete(q.Name, false, false, false); dErr != nil {
			log.Errorf("Failed to delete reply queue error: %v", dErr)
		}
	}()

	var msgs <-chan amqp.Delivery
	err = p.withReconnect(func() error {
		var cErr error
		msgs, cErr = p.amqpChan.Consume(
			q.Name, // queue
			"",     // consumer
			true,   // auto-ack
			false,  // exclusive
			false,  // no-local
			false,  // no-wait
			nil,    // args
		)
		return cErr
	})
	if err != nil {
		return nil, fmt.Errorf("create consumer error: %w", err)
	}

	corrId := fmt.Sprintf("%d", time.Now().UnixNano())
	err = p.withReconnect(func() error {
		return p.amqpChan.Publish(
			"",        // exchange
			queueName, // routing key
			false,     // mandatory
			false,     // immediate
			amqp.Publishing{
				ContentType:   msg.ContentType,
				CorrelationId: corrId,
				ReplyTo:       q.Name,
				Body:          msg.Body,
			})
	})
	if err != nil {
		return nil, fmt.Errorf("publish message error: %w", err)
	}

	timeout := time.After(PublishRPCTimeout)
	for {
		select {
		case d := <-msgs:
			if corrId == d.CorrelationId {
				return &Message{
					ID:            d.MessageId,
					Body:          d.Body,
					ContentType:   d.ContentType,
					CorrelationID: &d.CorrelationId,
					ReplyTo:       d.ReplyTo,
				}, nil
			}
		case <-timeout:
			return nil, fmt.Errorf("timeout waiting for replied message")
		}
	}
}

func (p *RabbitMQProducer) withReconnect(operation func() error) error {
	err := operation()
	if errors.Is(err, amqp.ErrClosed) || errors.Is(err, amqp.ErrCommandInvalid) {
		log.Errorf("RabbitMQ connection/channel closed. Try to reconnect...")
		p.alive = false
		if reconnectErr := p.connect(); reconnectErr != nil {
			return fmt.Errorf("reconnect to RabbitMQ failed: %w", reconnectErr)
		}
		return operation()
	}
	return err
}

func (p *RabbitMQProducer) declareQueue(queueName string) (amqp.Queue, error) {
	return p.amqpChan.QueueDeclare(
		queueName,
		defaultQueueDurable,
		defaultQueueAutoDelete,
		defaultQueueExclusive,
		defaultQueueNoWait,
		nil,
	)
}
