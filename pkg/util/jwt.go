package util

import (
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

type Claims struct {
	UserID      string `json:"sub"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	OTPVerified bool   `json:"otp_verified"`
	jwt.RegisteredClaims
}

func AccessToken(id string, username, email string, otpVerified bool) (string, error) {
	duration := time.Hour * JwtAccessExpireIn

	return generateToken(JwtAccessSecret, id, username, email, otpVerified, duration)
}

func RefreshToken(id string, username, email string, otpVerified bool) (string, error) {
	duration := time.Hour * JwtRefreshExpireIn
	return generateToken(JwtRefreshSecret, id, username, email, otpVerified, duration)
}

// generateToken generate tokens used for auth
func generateToken(secretKey []byte, id string, username, email string, otpVerified bool, expDuration time.Duration) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(expDuration)

	claims := Claims{
		id,
		username,
		email,
		otpVerified,
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			IssuedAt:  jwt.NewNumericDate(nowTime),
			Issuer:    "openedu101",
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(secretKey)

	return token, err
}

// ParseAccessToken parsing token
func ParseAccessToken(token string) (*Claims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return JwtAccessSecret, nil
	})

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}

// ParseRefreshToken parsing token
func ParseRefreshToken(token string) (*Claims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return JwtRefreshSecret, nil
	})

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}

// ExtractTokenFromRequest extract JWT token from a HTTP request
func ExtractTokenFromRequestHeader(c *gin.Context) string {
	var token string
	authHeader := c.Request.Header.Get("Authorization")
	if authHeader == "" {
		return ""
	}

	authHeaderParts := strings.Split(authHeader, " ")
	if len(authHeaderParts) != 2 || authHeaderParts[0] != "Bearer" {
		return ""
	}

	token = authHeaderParts[1]
	return token
}
