package util

import (
	"time"
)

const (
	RunModeRelease = "release"
	RunModeDebug   = "debug"

	LogLevelInfo  = "info"
	LogLevelDebug = "debug"
)

type SNSProvider string

const (
	<PERSON><PERSON><PERSON>     SNSProvider = "kakao"
	Naver     SNSProvider = "naver"
	Twitter   SNSProvider = "twitter"
	Google    SNSProvider = "google"
	Microsoft SNSProvider = "microsoft"
	Facebook  SNSProvider = "facebook"
)

var SupportedSNSProvider = [...]SNSProvider{Kakao, Naver, Twitter, Google, Microsoft, Facebook}

// Header & Context keys
const (
	HeaderAPIKey    = "X-api-key"
	HeaderOriginKey = "Origin"
	HeaderXReferrer = "X-referrer"

	ContextUserKey    = "User"
	ContextOrgKey     = "Organization"
	ContextDomainInfo = "Domain-info"

	ContextBodyDataCopy = "cached-body"

	ContextAllowFields = "allow_fields"
)

// Query & URL Params
const (
	MeParamKey     = "me"
	IDParamKey     = "id"
	UserIDParamKey = "user_id"

	TypeQueryKey    = "type"
	SourceQueryKey  = "source"
	RefCodeQueryKey = "ref_code"
	RefUserQueryKey = "ref_by"
)

// FindPage
const (
	PageMin    = 1
	PerPageMax = 9999
)

// Validator tags
const (
	RequiredTag = "required"
	EmailTag    = "email"
	URLTag      = "url"
	MinTag      = "min"
	MaxTag      = "max"
)

// Status Send Email
const (
	SUCCESS = "Success"
	FAIL    = "Fail"
	PENDING = "Pending"
)

const (
	ONE_MINUTE   = 60
	MILLISECOND  = 1000
	EXPIREIN1DAY = 1
	EXPIREIN3DAY = 3
	EXPIREIN7DAY = 7
	EXPIRED      = 0
)

// Field name in string
const (
	FileField                  = "File"
	FilesField                 = "Files"
	QuestionsField             = "Questions"
	QuestionField              = "Question"
	AnswersField               = "Answers"
	AnswersQuestionField       = "Answers.Question"
	SubQuestionsField          = "SubQuestions"
	OptionsField               = "Options"
	QuestionsSubQuestionsField = "Questions.SubQuestions"
	QuestionsOptionsField      = "Questions.Options"
	AnswersFilesField          = "Answers.Files"
	ItemsFilesField            = "Items.Files"
)

const (
	OrderUnit    = 1
	FollowUnit   = 1
	HashtagUnit  = 1
	CategoryUnit = 1
	BlogUnit     = 1
	CourseUnit   = 1
)

const (
	SuccessStatusCode = 200
)

type BunnyVideoStatus int

const (
	Created      BunnyVideoStatus = 0
	Uploaded     BunnyVideoStatus = 1
	Processing   BunnyVideoStatus = 2
	Transcoding  BunnyVideoStatus = 3
	Finished     BunnyVideoStatus = 4
	Error        BunnyVideoStatus = 5
	UploadFailed BunnyVideoStatus = 6
	Transcribing BunnyVideoStatus = 7
)

const (
	BunnyVideoCreated       = "created"
	BunnyVideoUploaded      = "uploaded"
	BunnyVideoProcessing    = "processing"
	BunnyVideoTranscoding   = "transcoding"
	BunnyVideoFinished      = "finished"
	BunnyVideoError         = "error"
	BunnyVideoUploadFailed  = "upload_failed"
	BunnyVideoTranscribing  = "transcribing"
	BunnyVideoUnknownStatus = "unknown"
)

type WebhookBunnyStatus int

const (
	QueuedWebhookBunnyStatus                      WebhookBunnyStatus = 0
	ProcessingWebhookBunnyStatus                  WebhookBunnyStatus = 1
	EncodingWebhookBunnyStatus                    WebhookBunnyStatus = 2
	FinishedWebhookBunnyStatus                    WebhookBunnyStatus = 3
	ResolutionFinishedWebhookBunnyStatus          WebhookBunnyStatus = 4
	FailedWebhookBunnyStatus                      WebhookBunnyStatus = 5
	PresignedUploadStartedWebhookBunnyStatus      WebhookBunnyStatus = 6
	PresignedUploadFinishedWebhookBunnyStatus     WebhookBunnyStatus = 7
	PresignedUploadFailedWebhookBunnyStatus       WebhookBunnyStatus = 8
	CaptionsGeneratedWebhookBunnyStatus           WebhookBunnyStatus = 9
	TitleOrDescriptionGeneratedWebhookBunnyStatus WebhookBunnyStatus = 10
)

const (
	HashtagCount  = "use_count"
	CategoryCount = "use_count"
)

const (
	CompleteAtDefault    = 0
	DefaultLessonPercent = 0
)

const (
	MaxPercent = 100
)

const (
	MaxTryTimes               = 3
	RetryAfterInterval        = 100 * time.Millisecond
	ThumbnailMaxGenerateTimes = 2
)
const IsHasCertificate = true

const (
	DefaultFollowing    = 0
	DefaultFollowers    = 0
	DefaultTotalBlogs   = 0
	DefaultTotalCourses = 0
)

const (
	IsQuizIncluded = true
)

const (
	TimeOutGeneratingDefault     = 10
	DefaultWhitelistBalanceLimit = 5
	DefaultBonus                 = 0
	DefaultRequestLimit          = 0
	DefaultBalanceLimit          = 0
)

const NumberOfSectionRequiredToLaunchpad = 4

var Languages = map[string]string{
	"af":         "Afrikaans",
	"af-NA":      "Afrikaans (Namibia)",
	"af-ZA":      "Afrikaans (South Africa)",
	"ak":         "Akan",
	"ak-GH":      "Akan (Ghana)",
	"am":         "Amharic",
	"am-ET":      "Amharic (Ethiopia)",
	"an-ES":      "Aragonese (Spain)",
	"ar":         "Arabic",
	"ar-AE":      "Arabic (United Arab Emirates)",
	"ar-BH":      "Arabic (Bahrain)",
	"ar-DZ":      "Arabic (Algeria)",
	"ar-EG":      "Arabic (Egypt)",
	"ar-IN":      "Arabic (India)",
	"ar-IQ":      "Arabic (Iraq)",
	"ar-JO":      "Arabic (Jordan)",
	"ar-KW":      "Arabic (Kuwait)",
	"ar-LB":      "Arabic (Lebanon)",
	"ar-LY":      "Arabic (Libya)",
	"ar-MA":      "Arabic (Morocco)",
	"ar-OM":      "Arabic (Oman)",
	"ar-QA":      "Arabic (Qatar)",
	"ar-SA":      "Arabic (Saudi Arabia)",
	"ar-SD":      "Arabic (Sudan)",
	"ar-SY":      "Arabic (Syria)",
	"ar-TN":      "Arabic (Tunisia)",
	"ar-YE":      "Arabic (Yemen)",
	"ast-ES":     "Asturian (Spain)",
	"az":         "Azerbaijani",
	"az-AZ":      "Azerbaijani (Azerbaijan)",
	"az-Cyrl":    "Azerbaijani (Cyrillic)",
	"az-Cyrl-AZ": "Azerbaijani (Cyrillic, Azerbaijan)",
	"az-Latn":    "Azerbaijani (Latin)",
	"az-Latn-AZ": "Azerbaijani (Latin, Azerbaijan)",
	"be":         "Belarusian",
	"be-BY":      "Belarusian (Belarus)",
	"bem":        "Bemba",
	"bem-ZM":     "Bemba (Zambia)",
	"bg":         "Bulgarian",
	"bg-BG":      "Bulgarian (Bulgaria)",
	"bn":         "Bangla",
	"bn-BD":      "Bangla (Bangladesh)",
	"bn-IN":      "Bangla (India)",
	"br-FR":      "Breton (France)",
	"bs":         "Bosnian",
	"bs-BA":      "Bosnian (Bosnia & Herzegovina)",
	"ca":         "Catalan",
	"ca-AD":      "Catalan (Andorra)",
	"ca-ES":      "Catalan (Spain)",
	"ca-FR":      "Catalan (France)",
	"ca-IT":      "Catalan (Italy)",
	"chr":        "Cherokee",
	"chr-US":     "Cherokee (United States)",
	"cs":         "Czech",
	"cs-CZ":      "Czech (Czechia)",
	"cy":         "Welsh",
	"cy-GB":      "Welsh (United Kingdom)",
	"da":         "Danish",
	"da-DK":      "Danish (Denmark)",
	"de":         "German",
	"de-AT":      "Austrian German",
	"de-BE":      "German (Belgium)",
	"de-CH":      "Swiss High German",
	"de-DE":      "German (Germany)",
	"de-LI":      "German (Liechtenstein)",
	"de-LU":      "German (Luxembourg)",
	"ee":         "Ewe",
	"ee-GH":      "Ewe (Ghana)",
	"ee-TG":      "Ewe (Togo)",
	"el":         "Greek",
	"el-CY":      "Greek (Cyprus)",
	"el-GR":      "Greek (Greece)",
	"en":         "English",
	"en-AG":      "English (Antigua & Barbuda)",
	"en-AS":      "English (American Samoa)",
	"en-AU":      "Australian English",
	"en-BE":      "English (Belgium)",
	"en-BW":      "English (Botswana)",
	"en-BZ":      "English (Belize)",
	"en-CA":      "Canadian English",
	"en-DK":      "English (Denmark)",
	"en-GB":      "British English",
	"en-GU":      "English (Guam)",
	"en-HK":      "English (Hong Kong)",
	"en-IE":      "English (Ireland)",
	"en-IN":      "English (India)",
	"en-JM":      "English (Jamaica)",
	"en-MH":      "English (Marshall Islands)",
	"en-MP":      "English (Northern Mariana Islands)",
	"en-MT":      "English (Malta)",
	"en-MU":      "English (Mauritius)",
	"en-NA":      "English (Namibia)",
	"en-NG":      "English (Nigeria)",
	"en-NZ":      "English (New Zealand)",
	"en-PH":      "English (Philippines)",
	"en-PK":      "English (Pakistan)",
	"en-SG":      "English (Singapore)",
	"en-TT":      "English (Trinidad & Tobago)",
	"en-UM":      "English (U.S. Outlying Islands)",
	"en-US":      "American English",
	"en-VI":      "English (U.S. Virgin Islands)",
	"en-ZA":      "English (South Africa)",
	"en-ZM":      "English (Zambia)",
	"en-ZW":      "English (Zimbabwe)",
	"eo":         "Esperanto",
	"es":         "Spanish",
	"es-419":     "Latin American Spanish",
	"es-AR":      "Spanish (Argentina)",
	"es-BO":      "Spanish (Bolivia)",
	"es-CL":      "Spanish (Chile)",
	"es-CO":      "Spanish (Colombia)",
	"es-CR":      "Spanish (Costa Rica)",
	"es-CU":      "Spanish (Cuba)",
	"es-DO":      "Spanish (Dominican Republic)",
	"es-EC":      "Spanish (Ecuador)",
	"es-ES":      "European Spanish",
	"es-GQ":      "Spanish (Equatorial Guinea)",
	"es-GT":      "Spanish (Guatemala)",
	"es-HN":      "Spanish (Honduras)",
	"es-MX":      "Mexican Spanish",
	"es-NI":      "Spanish (Nicaragua)",
	"es-PA":      "Spanish (Panama)",
	"es-PE":      "Spanish (Peru)",
	"es-PR":      "Spanish (Puerto Rico)",
	"es-PY":      "Spanish (Paraguay)",
	"es-SV":      "Spanish (El Salvador)",
	"es-US":      "Spanish (United States)",
	"es-UY":      "Spanish (Uruguay)",
	"es-VE":      "Spanish (Venezuela)",
	"et":         "Estonian",
	"et-EE":      "Estonian (Estonia)",
	"eu":         "Basque",
	"eu-ES":      "Basque (Spain)",
	"fa":         "Persian",
	"fa-AF":      "Dari",
	"fa-IR":      "Persian (Iran)",
	"fi":         "Finnish",
	"fi-FI":      "Finnish (Finland)",
	"fil":        "Filipino",
	"fil-PH":     "Filipino (Philippines)",
	"fo":         "Faroese",
	"fo-FO":      "Faroese (Faroe Islands)",
	"fr":         "French",
	"fr-BE":      "French (Belgium)",
	"fr-BF":      "French (Burkina Faso)",
	"fr-BI":      "French (Burundi)",
	"fr-BJ":      "French (Benin)",
	"fr-BL":      "French (St. Barthélemy)",
	"fr-CA":      "Canadian French",
	"fr-CD":      "French (Congo - Kinshasa)",
	"fr-CF":      "French (Central African Republic)",
	"fr-CG":      "French (Congo - Brazzaville)",
	"fr-CH":      "Swiss French",
	"fr-CI":      "French (Côte d’Ivoire)",
	"fr-CM":      "French (Cameroon)",
	"fr-DJ":      "French (Djibouti)",
	"fr-FR":      "French (France)",
	"fr-GA":      "French (Gabon)",
	"fr-GN":      "French (Guinea)",
	"fr-GP":      "French (Guadeloupe)",
	"fr-GQ":      "French (Equatorial Guinea)",
	"fr-KM":      "French (Comoros)",
	"fr-LU":      "French (Luxembourg)",
	"fr-MC":      "French (Monaco)",
	"fr-MF":      "French (St. Martin)",
	"fr-MG":      "French (Madagascar)",
	"fr-ML":      "French (Mali)",
	"fr-MQ":      "French (Martinique)",
	"fr-NE":      "French (Niger)",
	"fr-RE":      "French (Réunion)",
	"fr-RW":      "French (Rwanda)",
	"fr-SN":      "French (Senegal)",
	"fr-TD":      "French (Chad)",
	"fr-TG":      "French (Togo)",
	"fy-DE":      "Western Frisian (Germany)",
	"fy-NL":      "Western Frisian (Netherlands)",
	"ga":         "Irish",
	"ga-IE":      "Irish (Ireland)",
	"gd-GB":      "Scottish Gaelic (United Kingdom)",
	"gl":         "Galician",
	"gl-ES":      "Galician (Spain)",
	"gu":         "Gujarati",
	"gu-IN":      "Gujarati (India)",
	"ha":         "Hausa",
	"ha-Latn":    "Hausa (Latin)",
	"ha-Latn-GH": "Hausa (Latin, Ghana)",
	"ha-Latn-NE": "Hausa (Latin, Niger)",
	"ha-Latn-NG": "Hausa (Latin, Nigeria)",
	"ha-NG":      "Hausa (Nigeria)",
	"haw":        "Hawaiian",
	"haw-US":     "Hawaiian (United States)",
	"he":         "Hebrew",
	"he-IL":      "Hebrew (Israel)",
	"hi":         "Hindi",
	"hi-IN":      "Hindi (India)",
	"hr":         "Croatian",
	"hr-HR":      "Croatian (Croatia)",
	"ht-HT":      "Haitian Creole (Haiti)",
	"hu":         "Hungarian",
	"hu-HU":      "Hungarian (Hungary)",
	"hy":         "Armenian",
	"hy-AM":      "Armenian (Armenia)",
	"id":         "Indonesian",
	"id-ID":      "Indonesian (Indonesia)",
	"ig":         "Igbo",
	"ig-NG":      "Igbo (Nigeria)",
	"is":         "Icelandic",
	"is-IS":      "Icelandic (Iceland)",
	"it":         "Italian",
	"it-CH":      "Italian (Switzerland)",
	"it-IT":      "Italian (Italy)",
	"iw-IL":      "Hebrew (Israel)",
	"ja":         "Japanese",
	"ja-JP":      "Japanese (Japan)",
	"ka":         "Georgian",
	"ka-GE":      "Georgian (Georgia)",
	"kk":         "Kazakh",
	"kk-Cyrl":    "Kazakh (Cyrillic)",
	"kk-Cyrl-KZ": "Kazakh (Cyrillic, Kazakhstan)",
	"kk-KZ":      "Kazakh (Kazakhstan)",
	"km":         "Khmer",
	"km-KH":      "Khmer (Cambodia)",
	"kn":         "Kannada",
	"kn-IN":      "Kannada (India)",
	"ko":         "Korean",
	"ko-KR":      "Korean (South Korea)",
	"ku-TR":      "Kurdish (Turkey)",
	"ky-KG":      "Kyrgyz (Kyrgyzstan)",
	"lb-LU":      "Luxembourgish (Luxembourg)",
	"lg":         "Ganda",
	"lg-UG":      "Ganda (Uganda)",
	"lo-LA":      "Lao (Laos)",
	"lt":         "Lithuanian",
	"lt-LT":      "Lithuanian (Lithuania)",
	"lv":         "Latvian",
	"lv-LV":      "Latvian (Latvia)",
	"mfe":        "Morisyen",
	"mfe-MU":     "Morisyen (Mauritius)",
	"mg":         "Malagasy",
	"mg-MG":      "Malagasy (Madagascar)",
	"mi-NZ":      "Māori (New Zealand)",
	"mk":         "Macedonian",
	"mk-MK":      "Macedonian (North Macedonia)",
	"ml":         "Malayalam",
	"ml-IN":      "Malayalam (India)",
	"mn-MN":      "Mongolian (Mongolia)",
	"mr":         "Marathi",
	"mr-IN":      "Marathi (India)",
	"ms":         "Malay",
	"ms-BN":      "Malay (Brunei)",
	"ms-MY":      "Malay (Malaysia)",
	"mt":         "Maltese",
	"mt-MT":      "Maltese (Malta)",
	"my":         "Burmese",
	"my-MM":      "Burmese (Myanmar [Burma])",
	"nb":         "Norwegian Bokmål",
	"nb-NO":      "Norwegian Bokmål (Norway)",
	"ne":         "Nepali",
	"ne-IN":      "Nepali (India)",
	"ne-NP":      "Nepali (Nepal)",
	"nl":         "Dutch",
	"nl-AW":      "Dutch (Aruba)",
	"nl-BE":      "Flemish",
	"nl-NL":      "Dutch (Netherlands)",
	"nn":         "Norwegian Nynorsk",
	"nn-NO":      "Norwegian Nynorsk (Norway)",
	"nso-ZA":     "Northern Sotho (South Africa)",
	"nyn":        "Nyankole",
	"nyn-UG":     "Nyankole (Uganda)",
	"oc-FR":      "Occitan (France)",
	"om":         "Oromo",
	"om-ET":      "Oromo (Ethiopia)",
	"om-KE":      "Oromo (Kenya)",
	"or":         "Odia",
	"or-IN":      "Odia (India)",
	"pa":         "Punjabi",
	"pa-Arab":    "Punjabi (Arabic)",
	"pa-Arab-PK": "Punjabi (Arabic, Pakistan)",
	"pa-Guru":    "Punjabi (Gurmukhi)",
	"pa-Guru-IN": "Punjabi (Gurmukhi, India)",
	"pa-IN":      "Punjabi (India)",
	"pa-PK":      "Punjabi (Pakistan)",
	"pl":         "Polish",
	"pl-PL":      "Polish (Poland)",
	"ps":         "Pashto",
	"ps-AF":      "Pashto (Afghanistan)",
	"pt":         "Portuguese",
	"pt-BR":      "Brazilian Portuguese",
	"pt-GW":      "Portuguese (Guinea-Bissau)",
	"pt-MZ":      "Portuguese (Mozambique)",
	"pt-PT":      "European Portuguese",
	"rm":         "Romansh",
	"rm-CH":      "Romansh (Switzerland)",
	"ro":         "Romanian",
	"ro-MD":      "Moldavian",
	"ro-RO":      "Romanian (Romania)",
	"ru":         "Russian",
	"ru-MD":      "Russian (Moldova)",
	"ru-RU":      "Russian (Russia)",
	"ru-UA":      "Russian (Ukraine)",
	"rw":         "Kinyarwanda",
	"rw-RW":      "Kinyarwanda (Rwanda)",
	"sd-IN":      "Sindhi (India)",
	"si":         "Sinhala",
	"si-LK":      "Sinhala (Sri Lanka)",
	"sk":         "Slovak",
	"sk-SK":      "Slovak (Slovakia)",
	"sl":         "Slovenian",
	"sl-SI":      "Slovenian (Slovenia)",
	"sn":         "Shona",
	"sn-ZW":      "Shona (Zimbabwe)",
	"so":         "Somali",
	"so-DJ":      "Somali (Djibouti)",
	"so-ET":      "Somali (Ethiopia)",
	"so-KE":      "Somali (Kenya)",
	"so-SO":      "Somali (Somalia)",
	"sq":         "Albanian",
	"sq-AL":      "Albanian (Albania)",
	"sq-MK":      "Albanian (North Macedonia)",
	"sr":         "Serbian",
	"sr-Cyrl":    "Serbian (Cyrillic)",
	"sr-Cyrl-BA": "Serbian (Cyrillic, Bosnia & Herzegovina)",
	"sr-Cyrl-ME": "Montenegrin (Cyrillic)",
	"sr-Cyrl-RS": "Serbian (Cyrillic, Serbia)",
	"sr-Latn":    "Serbian (Latin)",
	"sr-Latn-BA": "Serbian (Latin, Bosnia & Herzegovina)",
	"sr-Latn-ME": "Montenegrin (Latin)",
	"sr-Latn-RS": "Serbian (Latin, Serbia)",
	"sr-ME":      "Montenegrin",
	"sr-RS":      "Serbian (Serbia)",
	"st-ZA":      "Southern Sotho (South Africa)",
	"sv":         "Swedish",
	"sv-FI":      "Swedish (Finland)",
	"sv-SE":      "Swedish (Sweden)",
	"sw":         "Swahili",
	"sw-KE":      "Swahili (Kenya)",
	"sw-TZ":      "Swahili (Tanzania)",
	"ta":         "Tamil",
	"ta-IN":      "Tamil (India)",
	"ta-LK":      "Tamil (Sri Lanka)",
	"te":         "Telugu",
	"te-IN":      "Telugu (India)",
	"tg-TJ":      "Tajik (Tajikistan)",
	"th":         "Thai",
	"th-TH":      "Thai (Thailand)",
	"ti":         "Tigrinya",
	"ti-ER":      "Tigrinya (Eritrea)",
	"ti-ET":      "Tigrinya (Ethiopia)",
	"tk-TM":      "Turkmen (Turkmenistan)",
	"tl-PH":      "Filipino (Philippines)",
	"tn-ZA":      "Tswana (South Africa)",
	"to":         "Tongan",
	"to-TO":      "Tongan (Tonga)",
	"tr":         "Turkish",
	"tr-CY":      "Turkish (Cyprus)",
	"tr-TR":      "Turkish (Turkey)",
	"tt-RU":      "Tatar (Russia)",
	"ug-CN":      "Uyghur (China)",
	"uk":         "Ukrainian",
	"uk-UA":      "Ukrainian (Ukraine)",
	"ur":         "Urdu",
	"ur-IN":      "Urdu (India)",
	"ur-PK":      "Urdu (Pakistan)",
	"uz":         "Uzbek",
	"uz-Arab":    "Uzbek (Arabic)",
	"uz-Arab-AF": "Uzbek (Arabic, Afghanistan)",
	"uz-Cyrl":    "Uzbek (Cyrillic)",
	"uz-Cyrl-UZ": "Uzbek (Cyrillic, Uzbekistan)",
	"uz-Latn":    "Uzbek (Latin)",
	"uz-Latn-UZ": "Uzbek (Latin, Uzbekistan)",
	"uz-UZ":      "Uzbek (Uzbekistan)",
	"vi":         "Vietnamese",
	"vi-VN":      "Vietnamese (Vietnam)",
	"wa-BE":      "Walloon (Belgium)",
	"wo-SN":      "Wolof (Senegal)",
	"xh-ZA":      "Xhosa (South Africa)",
	"yi-US":      "Yiddish (United States)",
	"yo":         "Yoruba",
	"yo-NG":      "Yoruba (Nigeria)",
	"yue-HK":     "Cantonese (Hong Kong)",
	"zh":         "Chinese",
	"zh-CN":      "Chinese (China)",
	"zh-HK":      "Chinese (Hong Kong)",
	"zh-Hans":    "Simplified Chinese",
	"zh-Hans-CN": "Simplified Chinese (China)",
	"zh-Hans-HK": "Simplified Chinese (Hong Kong)",
	"zh-Hans-MO": "Simplified Chinese (Macao)",
	"zh-Hans-SG": "Simplified Chinese (Singapore)",
	"zh-Hant":    "Traditional Chinese",
	"zh-Hant-HK": "Traditional Chinese (Hong Kong)",
	"zh-Hant-MO": "Traditional Chinese (Macao)",
	"zh-Hant-TW": "Traditional Chinese (Taiwan)",
	"zh-SG":      "Chinese (Singapore)",
	"zh-TW":      "Chinese (Taiwan)",
	"zu":         "Zulu",
	"zu-ZA":      "Zulu (South Africa)",
}

var WhitelistEmailAIResourceUsage = []string{
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
}

var WhitelistEmailPattern = ".*@gfigroup\\.io$"

var BlacklistEmailPattern = ""

const (
	SubscriptionEntity = "subscription"
)

const (
	NextPathField = "next_path"
)

const (
	AIApiKey = "X-ai-api-key"
)
