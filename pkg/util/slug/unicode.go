package slug

import (
	"compress/zlib"
	"io"
	"strings"
	"sync"
	"unicode"
)

const pooledCapacity = 64

var (
	slicePool    sync.Pool
	decodingOnce sync.Once
)

const (
	dummyLenght = byte(0xff)
)

var (
	transliterations [65536][]rune
	transCount       = rune(len(transliterations))
)

func decodeTransliterations() {
	r, err := zlib.NewReader(strings.NewReader(tableData))
	if err != nil {
		panic(err)
	}
	defer r.Close()
	b := make([]byte, 0, 13) // 13 = longest transliteration, adjust if needed
	lenB := b[:1]
	chr := uint16(0xffff) // char counter, rely on overflow on first pass
	for {
		chr++
		if _, err := io.ReadFull(r, lenB); err != nil {
			if err == io.EOF {
				break
			}
			panic(err)
		}
		if lenB[0] == dummyLenght {
			continue
		}
		b = b[:lenB[0]] // resize, preserving allocation
		if _, err := io.ReadFull(r, b); err != nil {
			panic(err)
		}
		transliterations[int(chr)] = []rune(string(b))
	}
}

// Unidecode implements a unicode transliterator, which
// replaces non-ASCII characters with their ASCII
// counterparts.
// Given an unicode encoded string, returns
// another string with non-ASCII characters replaced
// with their closest ASCII counterparts.
// e.g. Unicode("áéíóú") => "aeiou"
func Unidecode(s string) string {
	decodingOnce.Do(decodeTransliterations)
	l := len(s)
	var r []rune
	if l > pooledCapacity {
		r = make([]rune, 0, len(s))
	} else {
		if x := slicePool.Get(); x != nil {
			r = x.([]rune)[:0]
		} else {
			r = make([]rune, 0, pooledCapacity)
		}
	}
	for _, c := range s {
		if c <= unicode.MaxASCII {
			r = append(r, c)
			continue
		}
		if c > unicode.MaxRune || c >= transCount {
			/* Ignore reserved chars */
			continue
		}
		if d := transliterations[c]; d != nil {
			r = append(r, d...)
		}
	}
	res := string(r)
	if l <= pooledCapacity {
		slicePool.Put(r)
	}
	return res
}
