package util

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"net/url"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util/slug"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/shopspring/decimal"

	"github.com/aws/aws-sdk-go-v2/aws"
	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/go-playground/validator/v10"

	gonanoid "github.com/matoous/go-nanoid/v2"
)

const (
	phoneRegex           = `^\+?[\d-]+\d$`
	SignBunnyIFrameRegex = `([?&])token=[^&]*&expires=[^&]*`
	PlaylistIDRegex      = `list=([a-zA-Z0-9_-]+)`

	MaxPerPage = 99999
)

var Validator *validator.Validate
var JwtAccessExpireIn time.Duration
var JwtAccessSecret []byte
var JwtRefreshSecret []byte
var JwtRefreshExpireIn time.Duration

// Setup Initialize the util
func Setup() {
	JwtAccessSecret = []byte(setting.AppSetting.JwtAccessSecret)
	JwtAccessExpireIn = time.Duration(setting.AppSetting.JwtAccessExpireIn)
	JwtRefreshSecret = []byte(setting.AppSetting.JwtRefreshSecret)
	JwtRefreshExpireIn = time.Duration(setting.AppSetting.JwtRefreshExpireIn)
}

func GenerateId() string {
	id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", setting.DatabaseSetting.IdSize)
	if err != nil {
		return GenerateId()
	}
	return id
}

func SubsetArrayString(A, B []string) []string {
	subset := []string{}
	set := make(map[string]bool)

	for _, value := range A {
		set[value] = true
	}

	for _, value := range B {
		if set[value] {
			subset = append(subset, value)
		}
	}

	return subset
}
func IsSubsetArrayString(A, B []string) bool {
	set := make(map[string]bool)

	for _, value := range A {
		set[value] = true
	}

	for _, value := range B {
		if !set[value] {
			return false
		}
	}

	return true
}

func GenerateOrderCode(orderNumber int64) (*string, error) {
	// prefix _ 0000001 _ subfix = code
	// orderNumber = 1
	// OE_000000001_AAAAA
	// OE000000001AAAAA

	suffix, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)
	if err != nil {
		return nil, err
	}

	//WARNING CHANGE FROM EDU -> OE
	widthNumber := max(len(fmt.Sprintf("%d", orderNumber)), 9)
	code := fmt.Sprintf("EDU%0*d%s", widthNumber, orderNumber, suffix)
	if !setting.IsProduction() {
		stage := os.Getenv("STAGE")
		if stage == "" {
			stage = "local"
		}
		code = fmt.Sprintf("EDU%0*d%s%s", widthNumber, orderNumber, suffix, stage)
	}

	return &code, nil
}

func RemoveElemFromSlice(slice interface{}, index int) interface{} {
	v := reflect.ValueOf(slice)
	if v.Kind() != reflect.Slice {
		panic("InterfaceSlice() given a non-slice type")
	}

	if index < 0 || index >= v.Len() {
		panic("Index out of range")
	}

	result := reflect.MakeSlice(v.Type(), 0, v.Len()-1)
	reflect.Copy(result, v.Slice(0, index))
	reflect.Copy(result.Slice(index, result.Len()), v.Slice(index+1, v.Len()))
	return result.Interface()
}

func GenerateCode(size int) string {
	id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", size)
	if err != nil {
		return GenerateId()
	}
	return id
}

func GenerateToken() string {
	id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-", 40)
	if err != nil {
		return GenerateToken()
	}
	return id
}

func GenerateCoupon(attempts ...int) string {
	coupon, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-", 8)
	if err != nil {
		return GenerateCoupon()
	}
	return coupon

}

func NameOfFunction(f any) string {
	return runtime.FuncForPC(reflect.ValueOf(f).Pointer()).Name()
}

func DifferenceBy[T comparable](list1 []T, list2 []T, getKey func(elm T) string) ([]T, []T) {
	left := []T{}
	right := []T{}

	seenLeft := map[string]struct{}{}
	seenRight := map[string]struct{}{}

	for _, elem := range list1 {
		seenLeft[getKey(elem)] = struct{}{}
	}

	for _, elem := range list2 {
		seenRight[getKey(elem)] = struct{}{}
	}

	for _, elem := range list1 {
		if _, ok := seenRight[getKey(elem)]; !ok {
			left = append(left, elem)
		}
	}

	for _, elem := range list2 {
		if _, ok := seenLeft[getKey(elem)]; !ok {
			right = append(right, elem)
		}
	}

	return left, right
}

func NewBool(b bool) *bool {
	return &b
}

func NewString(s string) *string {
	return &s
}

func NewInt(i int) *int {
	return &i
}

func NewInt64(i int64) *int64 {
	return &i
}

func NewT[T any](t T) *T {
	return &t
}

func IsSameDomain(url1 string, url2 string) bool {
	re, _ := regexp.Compile("^(?:https?:\\/\\/)?(?:[^@\\/\\n]+@)?(?:www\\.)?([^:\\/?\\n]+)")
	domain1 := re.Find([]byte(url1))
	domain2 := re.Find([]byte(url2))
	if string(domain1) == string(domain2) {
		return true
	}
	return false
}

func StructToMap(obj any) (map[string]interface{}, error) {
	// Marshal the struct to JSON
	b, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}

	// Unmarshal the JSON into a map
	var result map[string]interface{}
	err = json.Unmarshal(b, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func IsEmail(email string) bool {
	// Regular expression for checking email format
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`

	match, _ := regexp.MatchString(emailRegex, email)
	return match
}

func Struct2Json(obj any) string {
	jsonData, _ := json.Marshal(obj)
	return string(jsonData)
}

func GetCurrentTime() int {
	return int(time.Now().UnixMilli())
}

func AddCurrentTimeWithDay(timeToAdd int) int {
	return int(time.Now().Add(time.Duration(timeToAdd) * time.Hour * 24).UnixMilli())
}

func AddCurrentTimeWithHour(timeToAdd int) int {
	return int(time.Now().Add(time.Duration(timeToAdd) * time.Hour).UnixMilli())
}

func AddCurrentTimeWithMinute(timeToAdd int) int {
	return int(time.Now().Add(time.Duration(timeToAdd) * time.Minute).UnixMilli())
}

func CheckTimeIsOver(targetTime int, referenceTime int) bool {
	now := GetCurrentTime()

	return now-targetTime > referenceTime*MILLISECOND
}

// RemoveElement removes a element from slice
func RemoveElement(slice []string, value string) []string {
	for i, v := range slice {
		if v == value {
			return append(slice[:i], slice[i+1:]...)
		}
	}
	return slice
}

func Remove[T comparable](slice []T, value T) []T {
	var result []T
	for _, v := range slice {
		if v != value {
			result = append(result, v)
		}
	}
	return result
}

func Base64Encode(content string) string {
	return base64.StdEncoding.EncodeToString([]byte(content))
}

func Base64Decode(content string) string {
	decodedString, _ := base64.StdEncoding.DecodeString(content)

	return string(decodedString)
}

func ConvertToArray[T any](v T) ([]T, error) {
	// Sử dụng reflect để lấy giá trị của v
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Array || val.Kind() == reflect.Slice {
		// Tạo một slice mới để chứa các phần tử của array/slice
		result := make([]T, val.Len())
		for i := 0; i < val.Len(); i++ {
			result[i] = val.Index(i).Interface().(T)
		}
		return result, nil
	}
	return nil, fmt.Errorf("input is not an array or slice")
}

func Domain2Link2(domain string) []string {
	return []string{"https://" + domain, "http://" + domain}
}

func HashSHA256(content string) string {
	hash := sha256.New()
	hash.Write([]byte(content))
	hashedBytes := hash.Sum(nil)
	return hex.EncodeToString(hashedBytes)
}

func IsValidPhoneNumber(phoneNumber string) bool {
	re := regexp.MustCompile(phoneRegex)
	return re.MatchString(phoneNumber)
}

func Slugify(s string, postfixLen int) (string, error) {
	reg, err := regexp.Compile("[\"!@#$%^&*(),.:;~'+-=<>?/]+")
	if err != nil {
		return "", err
	}

	removedSpecialChar := reg.ReplaceAllString(s, "")

	res := slug.Make(removedSpecialChar)

	postfix, err := gonanoid.Generate("0123456789", postfixLen)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-%s", res, postfix), nil
}

func RemoveSpecialCharacter(s string) (string, error) {
	reg, err := regexp.Compile("[\"!@#$%^&*(),.:;~'+-=<>?/]+")
	if err != nil {
		return "", err
	}

	return reg.ReplaceAllString(s, ""), nil
}

func CheckS3ObjectExists(ctx context.Context, s3Client *s3.Client, bucketName, key string) (bool, error) {
	_, err := s3Client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})

	if err != nil {
		var nfe *types.NotFound
		if errors.As(err, &nfe) {
			return false, nil // Object does not exist
		}
		return false, fmt.Errorf("unable to check if object exists: %w", err)
	}

	return true, nil // Object exists
}

func GetS3PresignedURL(ctx context.Context, s3Client *s3.Client, bucketName, key string, expireTime time.Duration) (*v4.PresignedHTTPRequest, error) {
	// Create a presigner for S3 service
	presigner := s3.NewPresignClient(s3Client)

	// Build the request
	req, err := presigner.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	}, s3.WithPresignExpires(expireTime))
	if err != nil {
		return nil, fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	return req, nil
}

func GetS3ObjectKeyFromURL(s3URL string) (string, error) {
	parsedURL, err := url.Parse(s3URL)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	pathSegments := strings.Split(parsedURL.Path, "/")
	if len(pathSegments) < 2 {
		return "", fmt.Errorf("invalid S3 URL format")
	}

	objectKey := strings.Join(pathSegments[2:], "/")
	return objectKey, nil
}

func GetS3BucketFromURL(s3URL string) (string, error) {
	parsedURL, err := url.Parse(s3URL)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	host := parsedURL.Host

	// Check for virtual-hosted–style URL
	if strings.Contains(host, ".s3.") {
		// Extract the bucket name from the host
		bucketName := strings.Split(host, ".s3.")[0]
		return bucketName, nil
	}

	// Check for path-style URL
	if strings.HasPrefix(parsedURL.Path, "/") {
		// Extract the bucket name from the path
		parts := strings.Split(parsedURL.Path, "/")
		if len(parts) > 1 {
			return parts[1], nil
		}
	}

	return "", fmt.Errorf("unable to extract bucket name from URL: %s", s3URL)
}

func RemoveDuplicateValues[T comparable](slice []T) []T {
	keys := make(map[T]bool)
	list := []T{}

	for _, entry := range slice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}

	return list
}

func FindIntersectionValues[T comparable](sliceA, slideB []T) []T {
	lookup := make(map[T]bool)
	for _, item := range sliceA {
		lookup[item] = true
	}

	var intersections []T
	for _, item := range slideB {
		if lookup[item] {
			intersections = append(intersections, item)
		}
	}
	return intersections
}

func GenerateOTP() string {
	// Generate a random number between 100000 and 999999 (inclusive)
	num := rand.Intn(900000) + 100000
	return strconv.Itoa(num)
}

func MergeMaps[K comparable, V any](map1, map2 map[K][]V) map[K][]V {
	mergedMap := make(map[K][]V)

	for key, value := range map1 {
		mergedMap[key] = value
	}

	for key, value := range map2 {
		if existing, ok := mergedMap[key]; ok {
			mergedMap[key] = append(existing, value...)
		} else {
			mergedMap[key] = value
		}
	}

	return mergedMap
}

func RemoveElementFromAnotherSlice[T comparable](originalSlice, removeSlice []T) []T {
	removeMap := make(map[T]bool)
	for _, elem := range removeSlice {
		removeMap[elem] = true
	}

	var result []T
	for _, elem := range originalSlice {
		if !removeMap[elem] {
			result = append(result, elem)
		}
	}

	return result
}

func UsernameWithSuffix(username string, sizeSuffix int) string {
	return username + GenerateCode(sizeSuffix)
}

func MergeMap[K comparable, V any](map1, map2 map[K]V) map[K]V {
	mergedMap := make(map[K]V)

	if map1 != nil {
		for key, value := range map1 {
			mergedMap[key] = value
		}
	}

	if map2 != nil {
		for key, value := range map2 {
			mergedMap[key] = value
		}

	}

	return mergedMap
}

func (s BunnyVideoStatus) StatusString() string {
	switch s {
	case Created:
		return BunnyVideoCreated
	case Uploaded:
		return BunnyVideoUploaded
	case Processing:
		return BunnyVideoProcessing
	case Transcoding:
		return BunnyVideoTranscoding
	case Finished:
		return BunnyVideoFinished
	case Error:
		return BunnyVideoError
	case UploadFailed:
		return BunnyVideoUploadFailed
	case Transcribing:
		return BunnyVideoTranscribing
	default:
		return BunnyVideoUnknownStatus
	}
}

func GetPlaylistIDFromYoutubeLink(url string) string {
	re := regexp.MustCompile(PlaylistIDRegex)

	matches := re.FindStringSubmatch(url)
	if len(matches) > 1 {
		playlistID := matches[1]
		return playlistID
	}
	return ""
}

func GetFileNameFromURL(urlStr string) string {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return ""
	}

	path := parsedURL.Path

	fileName := filepath.Base(path)

	return fileName
}
func GetFirst[T any](slice []T) (T, bool) {
	var zero T
	if len(slice) == 0 {
		return zero, false
	}
	return slice[0], true
}

func IsMultipleOfFive(number decimal.Decimal) bool {
	if number.Equal(decimal.Zero) {
		return false
	}

	five := decimal.NewFromInt(5)
	remainder := number.Mod(five)

	return remainder.Equal(decimal.Zero)
}

func NewDefaultExpBackoff() *backoff.ExponentialBackOff {
	expBackoff := backoff.NewExponentialBackOff()
	expBackoff.InitialInterval = 200 * time.Millisecond
	expBackoff.MaxInterval = 2 * time.Second
	expBackoff.MaxElapsedTime = 10 * time.Minute
	return expBackoff
}

func Json2Struct[T any](j interface{}, s *T) error {
	b, err := json.Marshal(&j)
	if err != nil {
		return err
	}
	return json.Unmarshal(b, s)
}

func IsDisposableEmail(email string, disposableDomainMap map[string]struct{}) bool {
	at := strings.LastIndex(email, "@")
	if at == -1 {
		return true
	}
	domain := email[at+1:]
	_, ok := disposableDomainMap[domain]
	return ok
}

func MapToStruct(data map[string]interface{}, target interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(dataBytes, target); err != nil {
		return err
	}

	return nil
}

func GetDisposableEmailsAsMap() map[string]struct{} {
	m := make(map[string]struct{})
	for _, domain := range GetDisposableEmails() {
		m[domain] = struct{}{}
	}
	return m
}

func CopyStruct(source, destination interface{}) error {
	bytes, err := json.Marshal(source)
	if err != nil {
		return fmt.Errorf("error marshaling source: %v", err)
	}

	return json.Unmarshal(bytes, destination)
}

func GetStartOfToday() time.Time {
	now := time.Now()
	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
}

func GetStartOfMonth() time.Time {
	now := time.Now()
	return time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
}

// GetCurrentWeekRange trả về ngày bắt đầu (thứ Hai) và ngày kết thúc (Chủ nhật) của tuần hiện tại
func GetCurrentWeekRange() (time.Time, time.Time) {
	now := time.Now()
	weekday := now.Weekday()

	var daysToMonday int
	if weekday == 0 {
		daysToMonday = 6
	} else {
		daysToMonday = int(weekday) - 1
	}

	// Tính ngày thứ Hai (ngày bắt đầu tuần)
	startOfWeek := now.AddDate(0, 0, -daysToMonday)
	// Đặt giờ về 00:00:00
	startOfWeek = time.Date(startOfWeek.Year(), startOfWeek.Month(), startOfWeek.Day(),
		0, 0, 0, 0, startOfWeek.Location())

	// Tính ngày Chủ nhật (ngày kết thúc tuần)
	// Ngày Chủ nhật = ngày thứ Hai + 6 ngày
	endOfWeek := startOfWeek.AddDate(0, 0, 6)
	// Đặt giờ về 23:59:59.999999999
	endOfWeek = time.Date(endOfWeek.Year(), endOfWeek.Month(), endOfWeek.Day(),
		23, 59, 59, 999999999, endOfWeek.Location())

	return startOfWeek, endOfWeek
}

// GetCurrentMonthRange trả về ngày bắt đầu và ngày kết thúc của tháng hiện tại
func GetCurrentMonthRange() (time.Time, time.Time) {
	now := time.Now()

	// Lấy năm và tháng hiện tại
	currentYear, currentMonth, _ := now.Date()

	// Ngày đầu tiên của tháng hiện tại (ngày 1)
	startOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, now.Location())

	// Ngày đầu tiên của tháng tiếp theo
	firstOfNextMonth := startOfMonth.AddDate(0, 1, 0)

	// Ngày cuối cùng của tháng hiện tại (ngày cuối cùng trước ngày 1 của tháng tiếp theo)
	endOfMonth := firstOfNextMonth.Add(-time.Nanosecond)

	return startOfMonth, endOfMonth
}

func GenerateUsername(fullName string) string {
	fullName = strings.ToLower(fullName)

	fullName = removeVietnameseAccents(fullName)

	reg := regexp.MustCompile("[^a-z0-9]+")
	fullName = reg.ReplaceAllString(fullName, " ")

	fullName = strings.TrimSpace(fullName)
	parts := strings.Fields(fullName)

	username := strings.Join(parts, "")
	return username
}

func GenerateEmail(fullname, domain string) string {
	fullname = strings.ToLower(fullname)

	fullname = removeVietnameseAccents(fullname)

	reg := regexp.MustCompile("[^a-z0-9]+")
	fullname = reg.ReplaceAllString(fullname, " ")

	fullname = strings.TrimSpace(fullname)
	parts := strings.Fields(fullname)

	username := strings.Join(parts, "")

	email := username + domain

	return email
}

func removeVietnameseAccents(s string) string {
	mappings := map[rune]rune{
		'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
		'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
		'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
		'đ': 'd',
		'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
		'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
		'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
		'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
		'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
		'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
		'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
		'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
		'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
	}

	var result strings.Builder
	for _, ch := range s {
		if newChar, ok := mappings[ch]; ok {
			result.WriteRune(newChar)
		} else {
			result.WriteRune(ch)
		}
	}

	return result.String()
}

// Hàm tạo số ngẫu nhiên trong khoảng [min, max]
func GetRandomNumber(min, max int) int {
	return rand.Intn(max-min+1) + min
}

// RoundDivision chia 2 số và làm tròn đến số chữ số thập phân chỉ định
func RoundDivision(a, b float64, decimals int) (float64, error) {
	// Kiểm tra mẫu số có bằng 0 không
	if b == 0 {
		return 0, errors.New("không thể chia cho số 0")
	}

	result := a / b
	// Làm tròn đến số chữ số thập phân được chỉ định
	factor := math.Pow10(decimals)
	return math.Round(result*factor) / factor, nil
}

// Hàm lấy phần tử ngẫu nhiên từ slice của struct
func GetRandomElement[T any](slice []T) (T, error) {
	// Kiểm tra slice rỗng
	if len(slice) == 0 {
		var zero T
		return zero, fmt.Errorf("empty slice")
	}

	// Tạo chỉ số ngẫu nhiên
	randomIndex := rand.Intn(len(slice))

	// Trả về phần tử ở chỉ số ngẫu nhiên
	return slice[randomIndex], nil
}

func ConvertToReadableTime(epochMilis int64) string {
	location := time.FixedZone("ICT", 7*60*60)
	return time.UnixMilli(int64(epochMilis)).In(location).Format("02-01-2006 15:04:05")
}

func ConvertToReadableTimeDate(epochMilis int64) string {
	location := time.FixedZone("ICT", 7*60*60)
	return time.UnixMilli(int64(epochMilis)).In(location).Format("02-01-2006")
}

func ConvertToReadableTimeHour(epochMilis int64) string {
	location := time.FixedZone("ICT", 7*60*60)
	return time.UnixMilli(int64(epochMilis)).In(location).Format("15:04:05")
}

func GetEndOfDay(unixTime int64) int64 {
	return unixTime + 86399999
}

func IsTimestampOlderThanNDays(epochMillis int64, days int) bool {
	timestampTime := time.UnixMilli(epochMillis)

	now := time.Now()

	nDaysAgo := now.AddDate(0, 0, -days)

	return timestampTime.Before(nDaysAgo) || timestampTime.Equal(nDaysAgo)
}

func GetUnixMillisNDaysAgo(days int) int64 {
	now := time.Now()

	nDaysAgo := now.AddDate(0, 0, -days)

	return nDaysAgo.UnixMilli()
}
