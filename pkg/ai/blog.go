package ai

import (
	"encoding/json"
	"fmt"
	"net/http"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
)

func (s *BlogAIService) OfferGenerateBlogFromLink(params *OfferBlogFromLinkRequest) (*OfferBlogFromLinkResponse, error) {
	reqBody := map[string]any{
		"link":     params.Link,
		"language": params.Language,
		"tone":     params.Tone,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    params.XAPIKey,
	}

	resp, body, err := s.httpClient.Post(rewriteFromLink, reqBody, headers)
	if err != nil {
		log.Debugf("Call Generate Blog From Link Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp OfferBlogFromLinkResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *BlogAIService) GetBlogGenerateFromLink(offerID string, xAPIKey string) (*GenerateBlogFromLinkResponse, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    xAPIKey,
	}

	url := generateStatusTask
	if offerID != "" {
		url = fmt.Sprintf("%s/%s", url, offerID)
	}

	resp, body, err := s.httpClient.Get(url, headers, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Generate Blog From Link Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp GenerateBlogFromLinkResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *BlogAIService) OfferRewriteParagraph(params *OfferBlogFromTextRequest) (*OfferBlogFromTextResponse, error) {
	reqBody := map[string]any{
		"text": params.Text,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    params.XAPIKey,
	}

	resp, body, err := s.httpClient.Post(rewriteFromText, reqBody, headers)
	if err != nil {
		log.Debugf("Call Rewrite Paragraph Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp OfferBlogFromTextResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *BlogAIService) GetRewriteParagraphGenerate(offerID string, xAPIKey string) (*RewriteParagraphResponse, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    xAPIKey,
	}

	url := generateStatusTask
	if offerID != "" {
		url = fmt.Sprintf("%s/%s", url, offerID)
	}

	resp, body, err := s.httpClient.Get(url, headers, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Rewrite Paragraph Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp RewriteParagraphResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}
