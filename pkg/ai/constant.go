package ai

type GenerateStatus string

const (
	StatusManual     GenerateStatus = "MANUAL"
	StatusPending    GenerateStatus = "PENDING"
	StatusGenerating GenerateStatus = "GENERATING"
	StatusWaiting    GenerateStatus = "WAITING"
	StatusCompleted  GenerateStatus = "COMPLETED"
	StatusFailed     GenerateStatus = "FAILED"
	StatusStarted    GenerateStatus = "STARTED"
	StatusGenerated  GenerateStatus = "GENERATED"
	StatusInProgress GenerateStatus = "INPROGRESS"
)

type QuizType string

const (
	QuizTypeSingleChoice   QuizType = "SINGLE_CHOICE"
	QuizTypeMultipleChoice QuizType = "MULTIPLE_CHOICE"
	QuizTypeText           QuizType = "TEXT"
	QuizTypeMatching       QuizType = "MATCHING"
	QuizTypeOrdering       QuizType = "ORDERING"
	QuizTypeFillInBlanks   QuizType = "FILL_IN_BLANKS"
)

type Tone string

const (
	ToneProfessional Tone = "PROFESSIONAL"
	ToneHumorous     Tone = "HUMOROUS"
	ToneNormal       Tone = "NORMAL"
)

type ThumbnailStyle string

const (
	GeneralThumbnailStyle      ThumbnailStyle = "GENERAL"
	AnimeThumbnailStyle        ThumbnailStyle = "ANIME"
	CreativeThumbnailStyle     ThumbnailStyle = "CREATIVE"
	DynamicThumbnailStyle      ThumbnailStyle = "DYNAMIC"
	EnvironmentThumbnailStyle  ThumbnailStyle = "ENVIRONMENT"
	IllustrationThumbnailStyle ThumbnailStyle = "ILLUSTRATION"
	PhotographyThumbnailStyle  ThumbnailStyle = "PHOTOGRAPHY"
	RayTrace3DThumbnailStyle   ThumbnailStyle = "RAY_TRACE_3D"
	Render3DThumbnailStyle     ThumbnailStyle = "RENDER_3D"
	SketchBWThumbnailStyle     ThumbnailStyle = "SKETCH_BW"
	SketchColorThumbnailStyle  ThumbnailStyle = "SKETCH_COLOR"
)

type DurationType string

const (
	WeekTypeDuration DurationType = "WEEK"
	DayTypeDuration  DurationType = "DAY"
)
