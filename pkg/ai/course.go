package ai

import (
	"encoding/json"
	"fmt"
	"net/http"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
)

func (s *CourseAIService) OfferGenerateCourseFromYoutube(params *OfferCourseFromYoutubeRequest) (*OfferCourseFromYoutubeResponse, error) {
	reqBody := map[string]any{
		"tone":        string(params.Tone),
		"playlist_id": params.PlaylistID,
		"language":    params.Language,
		"is_summary":  params.SummaryIncluded,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    params.XAPIKey,
	}

	resp, body, err := s.httpClient.Post(courseGenerateFromYoutube, reqBody, headers)
	if err != nil {
		log.Debugf("Call Generate Course From Youtube Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp OfferCourseFromYoutubeResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) GetCourseGenerateFromYoutube(offerID string, xAPIKey string) (*GenerateCourseFromYoutubeResponse, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    xAPIKey,
	}

	url := generateStatusTask
	if offerID != "" {
		url = fmt.Sprintf("%s/%s", url, offerID)
	}

	resp, body, err := s.httpClient.Get(url, headers, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Generate Course From Youtube Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp GenerateCourseFromYoutubeResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) OfferGenerateQuiz(params *OfferGenerateQuizRequest) (*OfferQuizResponse, error) {
	reqBody := map[string]any{
		"lesson_name":    params.LessonName,
		"lesson_embeded": params.LessonEmbedded,
		"question_type":  string(params.QuestionType),
		"language":       params.Language,
		"total_question": params.TotalQuestion,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    params.XAPIKey,
	}

	resp, body, err := s.httpClient.Post(quizGenerate, reqBody, headers)
	if err != nil {
		log.Debugf("Call Generate Course From Youtube Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp OfferQuizResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) GetQuizGenerate(offerID string, xAPIKey string) (*GenerateQuizResponse, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    xAPIKey,
	}

	url := generateStatusTask
	if offerID != "" {
		url = fmt.Sprintf("%s/%s", url, offerID)
	}

	resp, body, err := s.httpClient.Get(url, headers, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Generate Course From Youtube Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp GenerateQuizResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) OfferGenerateCourseFromLearnerDescription(params *OfferCourseFromLearnerDescriptionRequest) (*OfferCourseFromLearnerDescriptionResponse, error) {
	reqBody := map[string]any{
		"learner_information": params.LearnerInformation,
		"course_content":      params.CourseContent,
		"language":            params.Language,
		"material_urls":       params.MaterialURLs,
		"course_level":        params.CourseLevel,
		"course_duration":     params.CourseDuration,
		"course_duration_in":  params.CourseDurationIn,
		"study_load":          params.StudyLoad,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    params.XAPIKey,
	}

	resp, body, err := s.httpClient.Post(courseGenerateFromLearnerDescription, reqBody, headers)
	if err != nil {
		log.Debugf("Call Generate Course From Learner Description Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp OfferCourseFromLearnerDescriptionResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) GetCourseGenerateFromLearnerDescription(offerID string, xAPIKey string) (*GenerateCourseFromLearnerDescriptionResponse, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    xAPIKey,
	}

	url := generateStatusTask
	if offerID != "" {
		url = fmt.Sprintf("%s/%s", url, offerID)
	}

	resp, body, err := s.httpClient.Get(url, headers, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Generate Course From Learner Description Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp GenerateCourseFromLearnerDescriptionResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) OfferGenerateThumbnail(params *OfferGenerateThumbnailRequest) (*OfferGenerateThumbnailResponse, error) {
	reqBody := map[string]any{
		"course_thumbnail_prompt": params.ThumbnailPrompt,
		"number_of_thumbnails":    params.NumberOfThumbnails,
		"style":                   params.ThumbnailStyle,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    params.XAPIKey,
	}

	url := courseGenerateFromLearnerDescription
	if params.OfferID != "" {
		url = fmt.Sprintf("%s/%s/thumbnails", url, params.OfferID)
	}

	resp, body, err := s.httpClient.Post(url, reqBody, headers)
	if err != nil {
		log.Debugf("Call Generate Course From Generate Thumbnail Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp OfferGenerateThumbnailResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) GetCourseThumbnail(offerID string, xAPIKey string) (*GenerateThumbnailResponse, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    xAPIKey,
	}

	url := generateStatusTask
	if offerID != "" {
		url = fmt.Sprintf("%s/%s", url, offerID)
	}

	resp, body, err := s.httpClient.Get(url, headers, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Generate Course Thumbnail Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp GenerateThumbnailResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) OfferGenerateOutline(params *OfferGenerateOutlineRequest) (*OfferGenerateOutlineResponse, error) {
	reqBody := map[string]any{
		"course_title":       params.CourseTitle,
		"course_description": params.CourseDescription,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    params.XAPIKey,
	}

	url := courseGenerateFromLearnerDescription
	if params.OfferID != "" {
		url = fmt.Sprintf("%s/%s/outline", url, params.OfferID)
	}

	resp, body, err := s.httpClient.Post(url, reqBody, headers)
	if err != nil {
		log.Debugf("Call Generate Course Outline Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp OfferGenerateOutlineResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *CourseAIService) GetCourseOutline(offerID string, xAPIKey string) (*GenerateOutlineResponse, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"x-api-key":    xAPIKey,
	}

	url := generateStatusTask
	if offerID != "" {
		url = fmt.Sprintf("%s/%s", url, offerID)
	}

	resp, body, err := s.httpClient.Get(url, headers, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Generate Course Outline Failed:  %v", err.Error())
		return nil, err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		var dataResp GenerateOutlineResponse
		if err := json.Unmarshal(body, &dataResp); err != nil {
			return nil, fmt.Errorf("%w: failed to unmarshal response body %s from ai with error: %s", httpclient.ErrDecodeResponse, string(body), err)
		}

		return &dataResp, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return nil, fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}
