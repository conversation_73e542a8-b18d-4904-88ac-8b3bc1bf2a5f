package upload

import (
	// ... (other import statements)
	"bytes"
	"context"
	"fmt"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"openedu-core/pkg/util"
	"path"
	"sync"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/disintegration/imaging"
)

type s3UploadProviderOptions struct {
	AwsConfig   aws.Config
	BucketName  string
	PathPrefix  string
	Concurrency int
}

type s3UploadProvider struct {
	s3Client    *s3.Client
	uploader    *manager.Uploader
	bucketName  string
	pathPrefix  string
	concurrency int
	region      string
}

func newS3Provider(opts *s3UploadProviderOptions) *s3UploadProvider {
	// Create an S3 uploader
	s3Client := s3.NewFromConfig(opts.AwsConfig)
	uploader := manager.NewUploader(s3Client)

	p := s3UploadProvider{
		uploader:    uploader,
		s3Client:    s3Client,
		bucketName:  opts.BucketName,
		pathPrefix:  opts.PathPrefix,
		concurrency: opts.Concurrency,
		region:      opts.AwsConfig.Region,
	}
	return &p
}

func (p *s3UploadProvider) uploadFileToS3(r io.Reader, s3Key, contentType string, size *int64) (any, error) {
	result, err := p.uploader.Upload(context.Background(), &s3.PutObjectInput{
		Bucket:      aws.String(p.bucketName),
		Key:         aws.String(s3Key),
		ContentType: aws.String(contentType),
		Body:        r,
		ACL:         types.ObjectCannedACLPublicRead,
	})
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (p *s3UploadProvider) UploadFiles(files []*File, pathPrefix string) ([]*UploadedFileInfo, error) {
	var fileInfos []*UploadedFileInfo
	var wg sync.WaitGroup
	var mu sync.Mutex
	if pathPrefix == "" {
		pathPrefix = p.pathPrefix
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	errCh := make(chan error, len(files))
	concurrencyLimit := p.concurrency
	semaphore := make(chan struct{}, concurrencyLimit)
	for _, file := range files {
		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore
		go func(file *File) {
			defer func() {
				<-semaphore // Release semaphore
				wg.Done()
			}()
			select {
			case <-ctx.Done():
				// Context cancelled, do not proceed
				return
			default:
				var fileInfo UploadedFileInfo
				fileInfo.Name = generateFileName(file.Name, file.Hash)
				fileInfo.Hash = file.Hash
				fileInfo.Mime = file.Mime
				fileInfo.Ext = getExt(file.Name)
				fileInfo.Size = int64(len(file.Content))

				// Upload file to S3
				objectKey := path.Join(pathPrefix, fileInfo.Name)
				if _, err := p.uploadFileToS3(bytes.NewReader(file.Content), objectKey, fileInfo.Mime, &fileInfo.Size); err != nil {
					errCh <- err
					cancel()
					return
				}

				fileInfo.URL = fmt.Sprintf("https://s3.%s.amazonaws.com/%s/%s", p.region, p.bucketName, objectKey)

				// If file is not an image, skip to extract its thumbnail, width, and height
				if !file.IsImage() {
					mu.Lock()
					fileInfos = append(fileInfos, &fileInfo)
					mu.Unlock()
					return
				}

				// Get image dimension and thumbnail
				width, height, thumbnail, err := GetImageDimensionAndThumbnail(bytes.NewReader(file.Content), DefaultWidthThumbnail, DefaultHeightThumbnail)
				if err != nil {
					// Go cannot process animated WebP and AVIF images yet. Skipping related errors
					// until support is added in future releases.
					// Ref: https://github.com/golang/go/issues/53364
					if file.Mime == GIFMimeType || file.Mime == AVIFMimeType {
						mu.Lock()
						fileInfos = append(fileInfos, &fileInfo)
						mu.Unlock()
						return
					}
					errCh <- err
					cancel()
					return
				}
				fileInfo.Width = int64(width)
				fileInfo.Height = int64(height)

				// Upload thumbnail to S3
				thumbnailName := generateThumbnailName(file.Name, fileInfo.Hash)
				objectKey = path.Join(pathPrefix, thumbnailName)
				thumbnailBuffer := new(bytes.Buffer)
				if err := imaging.Encode(thumbnailBuffer, thumbnail, imaging.PNG); err != nil {
					errCh <- err
					cancel()
					return
				}

				if _, err := p.uploadFileToS3(bytes.NewReader(thumbnailBuffer.Bytes()), objectKey, "image/png", nil); err != nil {
					cancel()
					return
				}

				// https://s3.ap-southeast-1.amazonaws.com/openedu.net-dev/images/G7S6Hvc4PcUVDySh_19db31732931019b73bedcf17924f814.jpg
				fileInfo.ThumbnailURL = fmt.Sprintf("https://s3.%s.amazonaws.com/%s/%s", p.region, p.bucketName, objectKey)
				mu.Lock()
				fileInfos = append(fileInfos, &fileInfo)
				mu.Unlock()
			}
		}(file)
	}

	wg.Wait()
	select {
	case err := <-errCh:
		return nil, err
	default:
		return fileInfos, nil
	}
}

func (p *s3UploadProvider) RemoveFiles(fileInfos []*UploadedFileInfo, pathPrefix string) error {
	if len(fileInfos) == 0 {
		return nil
	}

	if pathPrefix == "" {
		pathPrefix = p.pathPrefix
	}

	objectKeys := make([]string, len(fileInfos))
	for idx, fileInfo := range fileInfos {
		key := path.Join(pathPrefix, fileInfo.Name)
		objectKeys[idx] = key
	}

	var objectIds []types.ObjectIdentifier
	for _, key := range objectKeys {
		objectIds = append(objectIds, types.ObjectIdentifier{Key: aws.String(key)})
	}

	_, err := p.s3Client.DeleteObjects(context.TODO(), &s3.DeleteObjectsInput{
		Bucket: aws.String(p.bucketName),
		Delete: &types.Delete{Objects: objectIds},
	})
	return err
}

func (p *s3UploadProvider) GetFileData(fileInfo *UploadedFileInfo) ([]byte, error) {
	objectKey, err := util.GetS3ObjectKeyFromURL(fileInfo.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to get object key from S3 URL: %w", err)
	}

	input := &s3.GetObjectInput{
		Bucket: aws.String(p.bucketName),
		Key:    aws.String(objectKey),
	}

	result, err := p.s3Client.GetObject(context.TODO(), input)
	if err != nil {
		return nil, fmt.Errorf("failed to get object from S3: %w", err)
	}
	defer result.Body.Close()

	data, err := io.ReadAll(result.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read object data: %w", err)
	}

	return data, nil
}
