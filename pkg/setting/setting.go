package setting

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/samber/lo"

	"github.com/go-ini/ini"
)

type App struct {
	Name               string
	JwtAccessSecret    string
	JwtAccessExpireIn  int
	JwtRefreshSecret   string
	JwtRefreshExpireIn int
	ApiKey             string
	AIApiKey           string
	AllowOrigin        []string
	AdminHost          []string
	BaseDomain         string
	OrgAdminPath       string
	PartnerPath        string
	LearnerPath        string
	BlogPath           string
	AffiliatePath      string

	DefaultEmailDomain string

	RuntimeRootPath string

	ImageSavePath string

	LogSavePath string
	LogSaveName string
	LogFileExt  string
	TimeFormat  string

	DefaultPerPage int

	ImageMaxSize    int
	ImageAllowExts  []string
	InitDefaultRole bool

	SystemAdminAccount  string
	SystemAdminPassword string

	CacheService string

	EnableAPIFailureAlerts bool
	MsTeamWebHookURL       string
	MsTeamMentionedEmails  []string

	CouponReserveDuration time.Duration

	EnableTracing bool
}

type Server struct {
	RunMode      string
	Host         string
	HttpPort     int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	HostDomain   string
	BuildName    string
}

type Database struct {
	Type        string
	User        string
	Password    string
	Host        string
	Name        string
	Port        string
	TablePrefix string
	IdSize      int
	SSLMode     string
}

type Redis struct {
	Host          string
	Password      string
	MaxIdle       int
	MaxActive     int
	IdleTimeout   time.Duration
	PrefixChannel string
}

type Aws struct {
	AccessKey string
	SecretKey string
	Region    string
}

type Email struct {
	Provider          string
	From              string
	FromName          string
	ReplyTo           string
	RedirectClientURI string
	TokenExpireIn     int
	ResendAfter       int
	ApiKey            string
	AIGovEmail        string
}

type Upload struct {
	UploadProvider     string
	UploadLocalDir     string
	UploadS3BucketName string
	UploadS3PathPrefix string

	UploadBunnyApiURI    string
	UploadBunnyIFrameURL string

	UploadPrivateBunnyAPIKey        string
	UploadPrivateBunnyLibraryID     string
	UploadPrivateBunnyIFrameAuthKey string
	UploadBunnyIFrameTokenExpireIn  int

	UploadPublicBunnyAPIKey    string
	UploadPublicBunnyLibraryID string
}

type OauthSetting struct {
	ClientID     string
	ClientSecret string
	RedirectURI  string
	TokenURL     string
	UserDataURL  string
}

type PaymentService struct {
	SepayURI string
}

type ExternalService struct {
	AIDomain                string
	XAPIKey                 string
	AvailGameLeaderboardURL string
}

type RabbitMQ struct {
	URL    string
	Prefix string
}

type OpenEduChain struct {
	BaseURL   string
	ApiKey    string
	IsMainnet bool
}

type OpenExchangeRates struct {
	AppID   string
	BaseURL string
}

type CoinGecko struct {
	BaseURL string
	ApiKey  string
}

type CoinMarketCap struct {
	BaseURL string
	ApiKey  string
}

type OpeneduCommunication struct {
	CommunicationDomain string
	CommunicationAPIKey string
}

type OpeneduScheduler struct {
	Domain string
	ApiKey string
}

type Jaeger struct {
	ServiceName       string
	AgentHostPort     string
	LogSpans          bool
	SamplingType      string
	SamplingParam     float64
	CollectorEndpoint string
}

var AppSetting = &App{}
var ServerSetting = &Server{}
var DatabaseSetting = &Database{}
var UploadSetting = &Upload{}
var GoogleSetting = &OauthSetting{}
var FacebookSetting = &OauthSetting{}
var RedisSetting = &Redis{}
var AwsSetting = &Aws{}
var EmailSetting = &Email{}
var PaymentSetting = &PaymentService{}
var ExternalServiceSetting = &ExternalService{}
var RabbitMQSetting = &RabbitMQ{}
var OpenEduChainSetting = &OpenEduChain{}
var OpenExchangeRatesSetting = &OpenExchangeRates{}
var CoinGeckoSetting = &CoinGecko{}
var CoinMarketCapSetting = &CoinMarketCap{}
var OpeneduCommunicationSetting = &OpeneduCommunication{}
var OpeneduSchedulerSetting = &OpeneduScheduler{}
var JaegerSetting = &Jaeger{}

var cfg *ini.File

func getInitFilename() string {
	appEnvFile := os.Getenv("APP_ENV_FILE")
	if appEnvFile != "" {
		return appEnvFile
	}
	stage := os.Getenv("STAGE")
	test := os.Getenv("TEST")
	if test == "true" {
		return "../config/app.ini"
	}
	if stage == "" {
		return "config/app.ini"
	}

	return "config/app-" + stage + ".ini"
}

func IsTraceEnabled() bool {
	return AppSetting.EnableTracing
}

func IsProduction() bool {
	stage := os.Getenv("STAGE")
	return stage == "prod"
}

func IsStaging() bool {
	stage := os.Getenv("STAGE")
	return stage == "staging"
}

func IsDev() bool {
	stage := os.Getenv("STAGE")
	return stage == "dev"
}

// Setup initialize the configuration instance
func Setup() {
	var err error
	fmt.Println("getInitFilename()", getInitFilename())
	cfg, err = ini.Load(getInitFilename())
	if err != nil {
		log.Fatalf("setting.Setup, fail to parse 'conf/app.ini': %v", err)
	}

	mapTo("app", AppSetting)
	mapTo("server", ServerSetting)
	mapTo("database", DatabaseSetting)
	mapTo("redis", RedisSetting)
	mapTo("upload", UploadSetting)
	mapTo("google", GoogleSetting)
	mapTo("facebook", FacebookSetting)
	mapTo("aws", AwsSetting)
	mapTo("email", EmailSetting)
	mapTo("payment", PaymentSetting)
	mapTo("external-service", ExternalServiceSetting)
	mapTo("rabbitmq", RabbitMQSetting)
	mapTo("openedu-chain", OpenEduChainSetting)
	mapTo("coin-gecko", CoinGeckoSetting)
	mapTo("coinmarketcap", CoinMarketCapSetting)
	mapTo("openexchangerates", OpenExchangeRatesSetting)
	mapTo("openedu-communication", OpeneduCommunicationSetting)
	mapTo("openedu-scheduler", OpeneduSchedulerSetting)
	mapTo("jaeger", JaegerSetting)

	AppSetting.ImageMaxSize = AppSetting.ImageMaxSize * 1024 * 1024
	ServerSetting.ReadTimeout = ServerSetting.ReadTimeout * time.Second
	ServerSetting.WriteTimeout = ServerSetting.WriteTimeout * time.Second
	RedisSetting.IdleTimeout = RedisSetting.IdleTimeout * time.Second
}

// mapTo map section
func mapTo(section string, v interface{}) {
	err := cfg.Section(section).MapTo(v)
	if err != nil {
		log.Fatalf("Cfg.MapTo %s err: %v", section, err)
	}
}

func IsSysAdminSite(domain string) bool {
	return lo.Contains(AppSetting.AdminHost, domain)
}

func IsAdminSite(uri string) bool {
	return strings.HasPrefix(uri, AppSetting.OrgAdminPath)
}

func IsCreatorSite(uri string) bool {
	return strings.HasPrefix(uri, AppSetting.PartnerPath)
}

func IsLearnerSite(uri string) bool {
	return strings.HasPrefix(uri, AppSetting.LearnerPath)
}

func IsBaseDomain(url string) bool {
	return url == AppSetting.BaseDomain
}

func DefaultEmailDomain() string {
	if AppSetting.DefaultEmailDomain == "" {
		return "@openedu.net"
	}
	return AppSetting.DefaultEmailDomain
}

//func AllowOrigin(url string) bool {
//	re, _ := regexp.Compile("^(?:https?:\\/\\/)?(?:[^@\\/\\n]+@)?(?:www\\.)?([^:\\/?\\n]+)")
//	domain := re.Find([]byte(url))
//
//	allowDomains := AppSetting.AllowOrigin
//	allowDomains = append(allowDomains, AppSetting.ClientHost...)
//	allowDomains = append(allowDomains, AppSetting.AdminHost...)
//	allowDomains = lo.Uniq(allowDomains)
//	allowed := false
//	for _, v := range allowDomains {
//		if strings.Contains(v, string(domain)) {
//			allowed = true
//		}
//	}
//	return allowed
//}
