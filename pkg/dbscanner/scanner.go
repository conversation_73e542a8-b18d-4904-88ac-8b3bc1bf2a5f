package dbscanner

import (
	"database/sql"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"reflect"
	"strings"
)

const (
	DbScanTag    = "dbscan"
	TagSeparator = "__"
)

type NullableField struct {
	Value interface{}
	Valid bool
}

func ScanRows(rows *sql.Rows, dest interface{}) error {
	destVal := reflect.ValueOf(dest)
	if destVal.Kind() != reflect.Ptr || destVal.Elem().Kind() != reflect.Slice {
		return errors.New("destination must be a pointer to a slice")
	}

	sliceVal := destVal.Elem()
	elementType := sliceVal.Type().Elem()
	isPtr := elementType.Kind() == reflect.Ptr
	if isPtr {
		elementType = elementType.Elem()
	}

	columns, err := rows.Columns()
	if err != nil {
		return err
	}

	// Map of prefix -> column names
	nestedColumns := make(map[string][]string)
	for _, col := range columns {
		parts := strings.Split(col, TagSeparator)
		if len(parts) > 1 {
			prefix := parts[0]
			nestedColumns[prefix] = append(nestedColumns[prefix], col)
		}
	}

	for rows.Next() {
		newElem := reflect.New(elementType).Elem()

		// First pass: collect all fields without initializing nested structs
		fieldMap := make(map[string]reflect.Value)
		buildFieldMap(newElem, "", fieldMap, false)

		// Prepare scan destinations
		scanDest := make([]interface{}, len(columns))
		nullableFields := make(map[int]*NullableField)

		for i, _ := range columns {
			nullableFields[i] = &NullableField{}
			scanDest[i] = nullableFields[i]
		}

		// Perform the scan
		if err := rows.Scan(scanDest...); err != nil {
			return err
		}

		// Check which nested structs have non-null values
		nestedStructsWithValues := make(map[string]bool)
		for prefix, cols := range nestedColumns {
			for _, col := range cols {
				for i, curCol := range columns {
					if curCol == col {
						if nullableFields[i].Valid && nullableFields[i].Value != nil {
							nestedStructsWithValues[prefix] = true
							break
						}
					}
				}
			}
		}

		// Initialize nested structs that have values
		for prefix := range nestedStructsWithValues {
			field := newElem.FieldByName(prefix)
			if field.IsValid() && field.CanSet() {
				if field.Kind() == reflect.Ptr {
					if field.IsNil() {
						field.Set(reflect.New(field.Type().Elem()))
					}
				}
			}
		}

		// Second pass: rebuild field map with initialized nested structs
		fieldMap = make(map[string]reflect.Value)
		buildFieldMap(newElem, "", fieldMap, true)

		// Set values
		for i, col := range columns {
			if field, ok := fieldMap[col]; ok {
				nullField := nullableFields[i]
				if nullField.Valid && nullField.Value != nil {
					if err = setFieldValue(field, nullField.Value); err != nil {
						return fmt.Errorf("error setting field %s: %v", col, err)
					}
				}
			}
		}

		if isPtr {
			sliceVal.Set(reflect.Append(sliceVal, newElem.Addr()))
		} else {
			sliceVal.Set(reflect.Append(sliceVal, newElem))
		}
	}

	return rows.Err()
}

func buildFieldMap(val reflect.Value, prefix string, fieldMap map[string]reflect.Value, initNested bool) {
	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)
		fieldVal := val.Field(i)

		if !fieldVal.CanSet() {
			continue
		}

		tag := field.Tag.Get(DbScanTag)
		if tag == "" || tag == "-" {
			continue
		}

		fullName := prefix + tag

		switch {
		case fieldVal.Kind() == reflect.Ptr && fieldVal.Type().Elem().Kind() == reflect.Struct:
			if !fieldVal.IsNil() || initNested {
				if initNested && fieldVal.IsNil() {
					fieldVal.Set(reflect.New(fieldVal.Type().Elem()))
				}
				buildFieldMap(fieldVal.Elem(), fullName+TagSeparator, fieldMap, initNested)
			}

		case fieldVal.Kind() == reflect.Struct:
			if fieldVal.Type() == reflect.TypeOf(decimal.Decimal{}) {
				fieldMap[fullName] = fieldVal
			} else {
				buildFieldMap(fieldVal, fullName+TagSeparator, fieldMap, initNested)
			}

		default:
			fieldMap[fullName] = fieldVal
		}
	}
}

func setFieldValue(field reflect.Value, value interface{}) error {
	if value == nil {
		return nil
	}

	fieldType := field.Type()

	// Special handling for decimal.Decimal
	if fieldType == reflect.TypeOf(decimal.Decimal{}) {
		switch v := value.(type) {
		case []uint8:
			d, err := decimal.NewFromString(string(v))
			if err != nil {
				return fmt.Errorf("error converting []uint8 to decimal: %v", err)
			}
			field.Set(reflect.ValueOf(d))
			return nil
		case string:
			d, err := decimal.NewFromString(v)
			if err != nil {
				return fmt.Errorf("error converting string to decimal: %v", err)
			}
			field.Set(reflect.ValueOf(d))
			return nil
		case float64:
			d := decimal.NewFromFloat(v)
			field.Set(reflect.ValueOf(d))
			return nil
		case int64:
			d := decimal.NewFromInt(v)
			field.Set(reflect.ValueOf(d))
			return nil
		}
	}

	// Handle string enums
	if fieldType.Kind() == reflect.String {
		switch v := value.(type) {
		case []uint8:
			field.SetString(string(v))
			return nil
		case string:
			field.SetString(v)
			return nil
		}
	}

	// Handle boolean
	if fieldType.Kind() == reflect.Bool {
		switch v := value.(type) {
		case []uint8:
			field.SetBool(string(v) == "true" || string(v) == "1")
			return nil
		case int64:
			field.SetBool(v != 0)
			return nil
		case bool:
			field.SetBool(v)
			return nil
		}
	}

	// Handle numeric types
	if fieldType.Kind() == reflect.Int || fieldType.Kind() == reflect.Int64 {
		switch v := value.(type) {
		case []uint8:
			var i int64
			_, err := fmt.Sscanf(string(v), "%d", &i)
			if err == nil {
				field.SetInt(i)
				return nil
			}
		case int64:
			field.SetInt(v)
			return nil
		}
	}

	val := reflect.ValueOf(value)
	if val.Type().ConvertibleTo(fieldType) {
		field.Set(val.Convert(fieldType))
		return nil
	}

	return fmt.Errorf("incompatible types: cannot convert %T to %v", value, fieldType)
}

func (n *NullableField) Scan(value interface{}) error {
	if value == nil {
		n.Value = nil
		n.Valid = false
		return nil
	}
	n.Value = value
	n.Valid = true
	return nil
}

func ScanRow(rows *sql.Rows, dest interface{}) error {
	destVal := reflect.ValueOf(dest)
	if destVal.Kind() != reflect.Ptr {
		return errors.New("destination must be a pointer")
	}

	slicePtr := reflect.New(reflect.SliceOf(destVal.Type()))
	if err := ScanRows(rows, slicePtr.Interface()); err != nil {
		return err
	}

	slice := slicePtr.Elem()
	if slice.Len() == 0 {
		return sql.ErrNoRows
	}

	destVal.Elem().Set(slice.Index(0).Elem())
	return nil
}
