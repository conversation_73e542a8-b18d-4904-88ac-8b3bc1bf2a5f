package payment

import (
	"fmt"
	"github.com/shopspring/decimal"
	"openedu-core/pkg/setting"
)

type sepayProvider struct {
	url string
}

func (p *sepayProvider) GetUrl(account string, service string, amount decimal.Decimal, des string) *string {
	url := fmt.Sprintf(p.url, account, service, amount.InexactFloat64(), des)
	return &url
}

func newSepayProvider() *sepayProvider {
	return &sepayProvider{url: setting.PaymentSetting.SepayURI}
}
