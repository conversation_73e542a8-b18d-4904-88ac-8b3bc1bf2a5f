package dto

import "github.com/shopspring/decimal"

type WalletQueryRequest struct {
	Type QueryType   `json:"type"`
	Data interface{} `json:"data"`
}

type CreateWalletRequest struct {
	UserID       string            `json:"user_id"`
	Network      BlockchainNetwork `json:"network"`
	CoreWalletID string            `json:"core_wallet_id"`
}

type WalletResponse struct {
	Model
	UserID       string `json:"user_id"`
	Address      string `json:"address"`
	PublicKey    string `json:"public_key"`
	Type         string `json:"type"`
	Status       string `json:"status"`
	CoreWalletID string `json:"core_wallet_id"`
}

type GetAccountInfoRequest struct {
	Network   BlockchainNetwork `json:"network"`
	Address   string            `json:"address"`
	IsMainnet bool              `json:"is_mainnet"`
}

type GetAccountInfoResponse struct {
	Network     BlockchainNetwork `json:"network"`
	Address     string            `json:"address"`
	AccountInfo interface{}       `json:"account_info"`
}

type WalletEarningResponse struct {
	BlockchainWalletID string            `json:"blockchain_wallet_id"`
	CoreWalletID       string            `json:"core_wallet_id"`
	Address            string            `json:"address"`
	Network            BlockchainNetwork `json:"network"`
	Token              BlockchainToken   `json:"token"`
	TokenID            string            `json:"token_id"`
	Amount             decimal.Decimal   `json:"amount"`
}

type GetWalletGasSponsorBalanceRequest struct {
	WalletID   string `json:"wallet_id"`
	CourseCuid string `json:"course_cuid"`
	IsMainnet  bool   `json:"is_mainnet"`
}
