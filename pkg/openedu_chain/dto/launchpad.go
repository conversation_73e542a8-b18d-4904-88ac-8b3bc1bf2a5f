package dto

import "github.com/shopspring/decimal"

// Launchpad Pool Statuses
const (
	PoolStatusInit     = "INIT"
	PoolStatusFunding  = "FUNDING"
	PoolStatusVoting   = "VOTING"
	PoolStatusSuccess  = "SUCCESSFUL"
	PoolStatusRefunded = "REFUNDED"
	PoolStatusRejected = "REJECTED"
	PoolStatusApproved = "APPROVED"
	PoolStatusFailed   = "FAILED"
	PoolStatusWaiting  = "WAITING"
)

type GetVotingPowersRequest struct {
	PoolID    string            `json:"pool_id"`
	Network   BlockchainNetwork `json:"network"`
	IsMainnet bool              `json:"is_mainnet"`
}

type GetVotingPowersResponse struct {
	VotingPowers []*VotingPowerEntry `json:"voting_powers"`
}

type VotingPowerEntry struct {
	UserID      string          `json:"user_id"`
	Address     string          `json:"address"`
	Amount      decimal.Decimal `json:"amount"`
	TotalAmount decimal.Decimal `json:"total_amount"`
	VotingPower float64         `json:"voting_power"`
}

type InitLaunchpadPoolRequest struct {
	WalletID         string          `json:"wallet_id"`
	CoreTxID         string          `json:"core_tx_id"`
	LaunchpadID      string          `json:"launchpad_id"`
	Token            BlockchainToken `json:"token"`
	MinPledge        decimal.Decimal `json:"min_pledge"`
	FundingStartDate int64           `json:"funding_start_date"`
	FundingEndDate   int64           `json:"funding_end_date"`
	TargetFunding    decimal.Decimal `json:"target_funding"`
	IsMainnet        bool            `json:"is_mainnet"`
}

type ApproveLaunchpadPoolRequest struct {
	PoolID     string            `json:"pool_id"`
	IsApproved bool              `json:"is_approved"`
	Network    BlockchainNetwork `json:"network"`
	IsMainnet  bool              `json:"is_mainnet"`
}

type UpdateLpPoolFundingTimeRequest struct {
	PoolID           string            `json:"pool_id"`
	FundingStartDate int64             `json:"funding_start_date"`
	FundingEndDate   int64             `json:"funding_end_date"`
	Network          BlockchainNetwork `json:"network"`
	IsMainnet        bool              `json:"is_mainnet"`
}

type CancelLpPoolRequest struct {
	WalletID  string `json:"wallet_id"`
	PoolID    string `json:"pool_id"`
	IsMainnet bool   `json:"is_mainnet"`
}

type ContinueLpPartialFundRequest struct {
	PoolID     string            `json:"pool_id"`
	IsApproved bool              `json:"is_approved"`
	Network    BlockchainNetwork `json:"network"`
	IsMainnet  bool              `json:"is_mainnet"`
}

type WithdrawLaunchpadFundToCreatorRequest struct {
	PoolID    string            `json:"pool_id"`
	Token     BlockchainToken   `json:"token"`
	Amount    decimal.Decimal   `json:"amount"`
	Network   BlockchainNetwork `json:"network"`
	IsMainnet bool              `json:"is_mainnet"`
}

type CheckLpFundingResultRequest struct {
	PoolID           string            `json:"pool_id"`
	IsWaitingFunding bool              `json:"is_waiting_funding"`
	Network          BlockchainNetwork `json:"network"`
	IsMainnet        bool              `json:"is_mainnet"`
}

type CheckLpFundingResultResponse struct {
	PoolID string `json:"pool_id"`
	Status string `json:"status"`
}

type SetLpFundingTimeRequest struct {
	WalletID            string `json:"wallet_id"`
	PoolID              string `json:"pool_id"`
	FundingStartDate    int64  `json:"funding_start_date"`
	FundingDurationDays int    `json:"funding_duration_days"`
	IsMainnet           bool   `json:"is_mainnet"`
}

type ClaimLaunchpadRefundRequest struct {
	WalletID  string          `json:"wallet_id"`
	CoreTxID  string          `json:"core_tx_id"`
	Token     BlockchainToken `json:"token"`
	PoolID    string          `json:"pool_id"`
	IsMainnet bool            `json:"is_mainnet"`
}

type PledgeLaunchpadRequest struct {
	WalletID  string          `json:"wallet_id"`
	CoreTxID  string          `json:"core_tx_id"`
	PoolID    string          `json:"pool_id"`
	Amount    decimal.Decimal `json:"amount"`
	Token     BlockchainToken `json:"token"`
	IsMainnet bool            `json:"is_mainnet"`
}

type UpdateLaunchpadPoolStatusRequest struct {
	PoolID    string            `json:"pool_id"`
	Status    string            `json:"status"`
	Network   BlockchainNetwork `json:"network"`
	IsMainnet bool              `json:"is_mainnet"`
}
