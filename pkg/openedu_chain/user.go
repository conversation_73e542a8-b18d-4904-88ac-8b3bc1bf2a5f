package openedu_chain

import (
	"encoding/json"
	"fmt"
	"net/http"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/openedu_chain/dto"
	"openedu-core/pkg/setting"
)

const (
	UserPath     = "/api/chain-v1/users"
	HeaderAPIKey = "X-api-key"
)

type userService struct {
	httpClient *httpclient.Client
}

func newUserService() (*userService, error) {
	return &userService{
		httpClient: httpclient.NewClient(setting.OpenEduChainSetting.BaseURL, nil),
	}, nil
}

func (s *userService) GetEarnings(userID string) ([]*dto.WalletEarningResponse, error) {
	headers := map[string]string{
		HeaderAPIKey: setting.OpenEduChainSetting.ApiKey,
	}
	queryParams := map[string]interface{}{
		"is_mainnet": setting.OpenEduChainSetting.IsMainnet,
	}
	resp, data, err := s.httpClient.Get(UserPath+"/"+userID, headers, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to make request to get earnings by user: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		errResp := map[string]interface{}{}
		if err = json.Unmarshal(data, &errResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal latest crypto exchange rates error response with status %d (%s): %w", resp.StatusCode, string(data), err)
		}
		return nil, fmt.Errorf("failed to get earnings by user with status %d: %v", resp.StatusCode, errResp)
	}

	var respData dto.ResponseT[[]*dto.WalletEarningResponse]
	if err = json.Unmarshal(data, &respData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal get earnings by user response: %w", err)
	}
	return respData.Data, nil
}
