package openedu_chain

import "errors"

var (
	ErrMakeRequest                   = errors.New("making request failed")
	ErrDecodeResponse                = errors.New("decoding response failed")
	ErrInsufficientBalance           = errors.New("insufficient balance")
	ErrInsufficientGasFee            = errors.New("insufficient gas fee")
	ErrGetSponsorBalanceFailed       = errors.New("get sponsor balance failed")
	ErrDepositSponsorFailed          = errors.New("deposit sponsor gas failed")
	ErrWithdrawSponsorFailed         = errors.New("withdraw sponsor gas failed")
	ErrMintNFTFailed                 = errors.New("mint nft failed")
	ErrTransferFailed                = errors.New("transfer failed")
	ErrGetAccountInfoFailed          = errors.New("get account info failed")
	ErrInvalidAddress                = errors.New("invalid address")
	ErrBatchTransferFailed           = errors.New("batch transfer failed")
	ErrInitPoolFailed                = errors.New("init pool failed")
	ErrApprovePoolFailed             = errors.New("approve pool failed")
	ErrPledgeLaunchpadFailed         = errors.New("pledge launchpad failed")
	ErrUpdateLpPoolFundingTimeFailed = errors.New("update launchpad pool funding time failed")
	ErrGetVotingPowersFailed         = errors.New("get voting powers failed")
	ErrCancelLaunchpadFailed         = errors.New("cancel launchpad failed")
	ErrCheckFundingResultFailed      = errors.New("check launchpad funding result failed")
)
