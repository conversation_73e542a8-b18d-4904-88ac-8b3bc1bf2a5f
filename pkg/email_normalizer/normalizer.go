package email_normalizer

import (
	"openedu-core/pkg/email_normalizer/rules"
	"strings"
)

// Normalizer defines the interface for email normalization
type Normalizer interface {
	Normalize(email string, rule *rules.NormalizeEmailRule) string
}

// RuleBasedNormalizer implements email normalization based on rules
type RuleBasedNormalizer struct{}

// Normalize applies normalization rules to an email address
func (n *RuleBasedNormalizer) Normalize(email string, rule *rules.NormalizeEmailRule) string {
	parts := ParseEmail(email)
	if parts == nil {
		return email
	}

	local := parts.Local
	domain := parts.Domain
	if domain != rule.PrimaryDomain {
		found := false
		for _, aliasDomain := range rule.AliasDomains {
			if domain == aliasDomain {
				found = true
				if rule.ConvertToPrimaryDomain {
					domain = rule.PrimaryDomain
				}
			}
		}

		if !found {
			return email
		}
	}

	if rule.RemoveDots {
		local = strings.ReplaceAll(local, ".", "")
	}

	if rule.RemovePlusSuffix {
		if plusIdx := strings.Index(local, "+"); plusIdx != -1 {
			local = local[:plusIdx]
		}
	}

	if rule.ConvertToLowercase {
		local = strings.ToLower(local)
		domain = strings.ToLower(domain)
	}

	return local + "@" + domain
}
