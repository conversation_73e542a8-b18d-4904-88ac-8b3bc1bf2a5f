package rules

// NormalizeEmailRule defines the normalization rules for an email domain
type NormalizeEmailRule struct {
	PrimaryDomain          string   `json:"primary_domain"`            // Primary domain for the rule
	AliasDomains           []string `json:"alias_domains"`             // Alias domains mapped to the same rule
	RemoveDots             bool     `json:"remove_dots"`               // Whether to remove dots from local part
	RemovePlusSuffix       bool     `json:"remove_plus_suffix"`        // Whether to remove plus suffixes
	ConvertToLowercase     bool     `json:"convert_to_lowercase"`      // Whether to convert email to lowercase
	ConvertToPrimaryDomain bool     `json:"convert_to_primary_domain"` // Whether to convert to primary case
}
