package email_normalizer

import (
	"openedu-core/pkg/email_normalizer/rules"
	"strings"
	"sync"
)

// Registry manages normalization rules and the normalizer instance
type Registry struct {
	rules      map[string]*rules.NormalizeEmailRule // Maps domains to their rules
	normalizer Normalizer                           // Shared normalizer instance
	mu         sync.RWMutex                         // Ensures thread-safe access
}

var (
	registry *Registry
	once     sync.Once
)

// GetRegistry returns the singleton instance of Registry
func GetRegistry() *Registry {
	once.Do(func() {
		registry = &Registry{
			rules:      make(map[string]*rules.NormalizeEmailRule),
			normalizer: &RuleBasedNormalizer{},
		}
		registry.Register(&rules.DefaultRule)
	})
	return registry
}

// Register adds a new normalization rule to the registry
func (r *Registry) Register(rule *rules.NormalizeEmailRule) {
	r.mu.Lock()
	defer r.mu.Unlock()

	primary := strings.ToLower(rule.PrimaryDomain)
	r.rules[primary] = rule
	for _, alias := range rule.AliasDomains {
		r.rules[strings.ToLower(alias)] = rule
	}
}

// GetRule retrieves the normalization rule for a given domain
func (r *Registry) GetRule(domain string) *rules.NormalizeEmailRule {
	r.mu.RLock()
	defer r.mu.RUnlock()

	domain = strings.ToLower(domain)
	if rule, ok := r.rules[domain]; ok {
		return rule
	}
	return r.rules[""] // Fallback to default rule
}
