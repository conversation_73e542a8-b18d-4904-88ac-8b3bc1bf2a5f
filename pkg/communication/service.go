package communication

import (
	dto "openedu-core/pkg/communication/dto"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
	"openedu-core/pkg/queue/producer"
	"openedu-core/pkg/setting"
)

const apikeyHeaderName = "X-api-key"

const (
	NotificationQueueName = "notification_queue"
	// total notification for every message
	BatchNotificationSize = 10
)

type NotificationServiceIface interface {
	PushNotification(req *dto.PushNotificationRequest) error
	PushMultipleNotification(req []*dto.PushNotificationRequest) error
}

type WebsocketServiceIface interface {
	SendMsgToUserWebSocket(req *dto.WebsocketMessageRequest) error
}

type TrackingServiceIface interface {
	CreateTracking(req *dto.TrackingRequest) error
	FindTopUserBlogView(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.UserResp, *dto.Pagination, error)
	FindTopViewedBlog(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.BlogResp, *dto.Pagination, error)
	FindPaginationTracking(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.TrackingResponse, *dto.Pagination, error)
	UpdateRefTracking(req *dto.UpdateRefTrackingRequest) error
	CountTrackingByUserAndEvent(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.CountRefUserResp, *dto.Pagination, error)
	FindPaginationAIMessage(query *dto.MessageQuery, options *dto.FindPageOptions) ([]*dto.Message, *dto.Pagination, error)
}

type EmailServiceIface interface {
	SendEmail(req *dto.SendEmailRequest) ([]byte, error)
	CreateEmailTemplate(tpml *dto.CreateEmailTemplateRequest) ([]byte, error)
	UpdateEmailTemplate(req *dto.UpdateEmailTemplateRequest, templateID string) ([]byte, error)
	InitDefaultEmailTemplateForOrg(org *dto.Organization) error
	FindOneEmailTemplate(templateID string) (*dto.EmailTemplate, error)
	FindPageEmailTemplate(query *dto.EmailTemplateQuery, options *dto.FindPageOptions) ([]*dto.EmailTemplate, *dto.Pagination, error)
	DeleteEmailTemplate(req *dto.DeleteEmailTemplateRequest) ([]byte, error)
	PreviewEmailTemplate(req *dto.PreviewEmailTemplateRequest) ([]byte, error)
	FindEmailTemplateVariables() (map[dto.EmailCodeType][]dto.EmailTemplateVarName, error)
}

type ReportServiceIface interface {
	ReportCourseEnrollments(courseCuid string) ([]byte, error)
}

type notificationService struct {
	httpClient *httpclient.Client
	producer   producer.Producer
}

type emailService struct {
	httpClient *httpclient.Client
}

type reportService struct {
	httpClient *httpclient.Client
}

type websocketService struct {
	httpClient *httpclient.Client
}

type trackingService struct {
	httpClient *httpclient.Client
}

var Websocket WebsocketServiceIface
var Tracking TrackingServiceIface
var Notification NotificationServiceIface
var Email EmailServiceIface
var Report ReportServiceIface

func Setup() {
	client := httpclient.NewClient(setting.OpeneduCommunicationSetting.CommunicationDomain, map[string]string{apikeyHeaderName: setting.OpeneduCommunicationSetting.CommunicationAPIKey})
	p, err := producer.NewProducer(producer.RabbitMQ)
	if err != nil {
		log.Error("failed to create producer: %v", err)
	}

	Websocket = &websocketService{httpClient: client}
	Tracking = &trackingService{httpClient: client}
	Notification = &notificationService{httpClient: client, producer: p}
	Email = &emailService{httpClient: client}
	Report = &reportService{httpClient: client}
}
