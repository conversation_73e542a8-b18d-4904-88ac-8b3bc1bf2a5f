package communicationdto

type EntityType string

const (
	CourseEntity         EntityType = "course"
	CourseApprovalEntity EntityType = "course_approval"
	OrganizationEntity   EntityType = "organization"
	SectionEntity        EntityType = "section"
	LessonEntity         EntityType = "lesson"
	FormAnswerEntity     EntityType = "form_answer"
	VideoEntity          EntityType = "video"
	PaymentEntity        EntityType = "payment"
	BlogEntity           EntityType = "blog"
	UserEntity           EntityType = "user"
	BadgeEntity          EntityType = "badge"
	CertificateEntity    EntityType = "certificate"
	AIBlogEntity         EntityType = "ai_blog"
	ApprovalEntity       EntityType = "approval"
	WalletEntity         EntityType = "wallet"
	PointHistory         EntityType = "point_history"
	SubscriptionEntity   EntityType = "subscription"
	ClpLaunchpadEntity   EntityType = "clp_launchpad"

	// Course (0-99)
	CodeRequestPublishCourseApproved  = 1
	CodeRequestPublishCourseRejected  = 3
	CodeRequestPublishCourseCancelled = 4
	CodeNewCoursePartnerAdded         = 5
	CodeNewCoursePartnerRemoved       = 6
	CodeNewPublishCourseRequest       = 7
	CodeAdminFeedback                 = 8
	CodeCreatorReplyFeedback          = 9
	CodeOrgAdminEnableCourse          = 10
	CodeOrgAdminDisableCourse         = 11
	CodeOEAdminEnableCourse           = 12
	CodeOEAdminDisableCourse          = 13
	CodeCourseAIGenerateSuccess       = 14
	CodeCourseAIGenerateFail          = 15
	CodeCourseEnrollmentSuccess       = 16
	CodeRemindShortBreak              = 17
	CodeRemindLongBreak               = 18
	CodeNewContentAlert               = 19
	CodeLearnedHalfCourse             = 20

	// Blog (100-199)
	CodeRequestPublishBlogApproved  = 100
	CodeRequestPublishBlogRejected  = 101
	CodeRequestPublishBlogCancelled = 102
	CodeNewOrgWriterAdded           = 103
	CodeNewOrgWriterRemoved         = 104
	CodeNewOrgEditorAdded           = 105
	CodeNewOrgEditorRemoved         = 106
	CodeBlogUnpublishedByAdmin      = 107
	CodeBlogAIGenerateSuccess       = 108
	CodeBlogAIGenerateFailed        = 109

	// Certificate (200-299)
	CodeNewCertificateReceived  = 200
	CodeNewNFTCertificateMinted = 201

	// Wallet (300-399)
	CodeNewWithdrawalRequest      = 300
	CodeWithdrawalRequestApproved = 301
	CodeWithdrawalRequestRejected = 302

	// Avail Retroactive (400-499)
	CodeNewRetroactiveReceived = 400

	// OE EarnedPoint System (500-599)
	CodeExpiredOEPointHistory        = 500
	CodeReminderExpiryOEPointHistory = 501

	// Subscription (600-699)
	CodeSubscriptionExpiration      = 600
	CodePhoCapAIRegistrationSuccess = 650
	// Launchpad (700-799)
	CodeNewPublishLaunchpadRequestForAdmin        = 700
	CodeRequestPublishLaunchpadApprovedForCreator = 701
	CodeRequestPublishLaunchpadRejectedForCreator = 702
	CodeLaunchpadFundingFailedForCreator          = 703
	CodeLaunchpadFundingWaitingForCreator         = 704
	CodeLaunchpadFundingSuccessForCreator         = 705
	CodeLaunchpadVotingStartedForBacker           = 706
	CodeLaunchpadVoteAutoApprovedForBacker        = 707
	CodeLaunchpadVotingPassedForCreator           = 708
	CodeLaunchpadVotingFailedForCreator           = 709
	CodeRequestPublishCourseLaunchpadForAdmin     = 710
	CodeLaunchpadVotingFailedForBacker            = 711
	CodeLaunchpadEndByCreatorForBacker            = 712

	//User Login/Logout
	CodeUserRegister = 800
)

type NotificationBroadcastRequest struct {
	UserIDs          []string `json:"user_ids"`
	OmitUserIDs      []string `json:"omit_user_ids"`
	UserID           string   `json:"user_id"`
	CourseID         string   `json:"course_id"`
	OrgID            string   `json:"org_id"`
	ChannelID        string   `json:"channel_id"`
	OmitConnectionId string   `json:"omit_connection_id"`
	All              bool     `json:"all"`
}

type NotificationCode int

type PushNotificationRequest struct {
	Code              NotificationCode `json:"code,omitempty"`
	Content           string           `json:"content,omitempty"`
	EntityID          string           `json:"entity_id,omitempty"`
	EntityType        EntityType       `json:"entity_type,omitempty"`
	Props             JSONB            `json:"props,omitempty"`
	RuleTree          *TreeNodeRequest `json:"rule_tree,omitempty"`
	OmitUserIDs       []string         `json:"omit_user_ids,omitempty"`
	CampaignID        string           `json:"campaign_id,omitempty"`
	CampaignSegmentID string           `json:"campaign_segment_id,omitempty"`
	Org               *Organization
	// Scheduled
	ScheduleType   ScheduledJobType `json:"schedule_type" gorm:"varchar(128)"`
	ScheduleAt     int64            `json:"schedule_at" gorm:"type:int8;not null;default:0"`
	CronExpression string           `json:"cron_expression" gorm:"varchar(128)"`
}

// INFO: rule tree
type RuleOperator string

const (
	OperatorAND RuleOperator = "AND"
	OperatorOR  RuleOperator = "OR"
)

type NotificationVerb string

const (
	Joined    NotificationVerb = "joined"
	Completed NotificationVerb = "completed"
	Inactive  NotificationVerb = "inactive"
	IsIn      NotificationVerb = "is_in"
	Is        NotificationVerb = "is"
)

type NotificationSubject string

const (
	Learner    NotificationSubject = "learner"
	Instructor NotificationSubject = "instructor"
	NotiUser   NotificationSubject = "user"
)

type NotificationObject string

const (
	CourseNotificationObject NotificationObject = "course"
	OrgNotificationObject    NotificationObject = "org"
)

type Rule struct {
	Subject   NotificationSubject `json:"subject,omitempty"`
	Verb      NotificationVerb    `json:"verb,omitempty"`
	Object    NotificationObject  `json:"object,omitempty"`
	ObjectIDs []string            `json:"object_ids,omitempty"`
	OrgID     string              `json:"org_id,omitempty"`
}
type TreeNodeRequest struct {
	Rule     *Rule            `json:"rule,omitempty"`
	Operator RuleOperator     `json:"operator,omitempty"`
	Left     *TreeNodeRequest `json:"left,omitempty"`
	Right    *TreeNodeRequest `json:"right,omitempty"`
}
