package communicationdto

type ActionType string

const (
	Followed ActionType = "followed"
	Blocked  ActionType = "blocked"
	Reported ActionType = "reported"
)

type UserResp struct {
	ID            string     `json:"id"`
	Username      string     `json:"username"`
	Email         string     `json:"email"`
	Active        bool       `json:"active"`
	Blocked       bool       `json:"blocked"`
	Avatar        string     `json:"avatar"`
	DisplayName   string     `json:"display_name"`
	Phone         string     `json:"phone"`
	TotalBlogView int        `json:"total_blog_view"`
	FollowStatus  ActionType `json:"follow_status" `
	Followers     int64      `json:"followers" `
}

type ListUsersResponse struct {
	Results    []*UserResp `json:"results"`
	Pagination *Pagination `json:"pagination"`
}

type User struct {
	Model
	Username           string `json:"username" validate:"required,omitempty" gorm:"unique"`
	Email              string `json:"email" validate:"required,omitempty" gorm:"unique"`
	Phone              string `json:"phone"`
	Password           string `json:"password" gorm:"omitempty"`
	Active             bool   `json:"active" gorm:"default:0"`
	Blocked            bool   `json:"blocked" gorm:"default:0"`
	CoverPhoto         string `json:"cover_photo"`
	Avatar             string `json:"avatar"`
	DisplayName        string `json:"display_name"`
	Headline           string `json:"headline"`
	About              string `json:"about"`
	Position           string `json:"position"`
	RequireSetPassword bool   `json:"require_set_password" gorm:"default:false"`
}
