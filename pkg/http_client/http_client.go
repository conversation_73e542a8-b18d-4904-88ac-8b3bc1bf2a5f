package httpclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"openedu-core/pkg/log"
	"strings"
)

type Client struct {
	baseURL        string
	client         *http.Client
	defaultHeaders map[string]string
}

func NewClient(baseURL string, defaultHeaders map[string]string) *Client {
	return &Client{
		baseURL:        baseURL,
		client:         &http.Client{},
		defaultHeaders: defaultHeaders,
	}
}

func (c *Client) Get(path string, headers map[string]string, queryParams map[string]interface{}) (*http.Response, []byte, error) {
	return c.handleResponse(c.doRequest(http.MethodGet, path, nil, headers, queryParams))
}

func (c *Client) Post(path string, body interface{}, headers map[string]string) (*http.Response, []byte, error) {
	return c.handleResponse(c.doRequest(http.MethodPost, path, body, headers, nil))
}

func (c *Client) Put(path string, body interface{}, headers map[string]string) (*http.Response, []byte, error) {
	return c.handleResponse(c.doRequest(http.MethodPut, path, body, headers, nil))
}

func (c *Client) Delete(path string, body interface{}, headers map[string]string) (*http.Response, []byte, error) {
	return c.handleResponse(c.doRequest(http.MethodDelete, path, body, headers, nil))
}

// doRequest, helper function to call request
func (c *Client) doRequest(method string, path string, body interface{}, headers map[string]string, queryParams map[string]interface{}) (*http.Response, error) {
	urlStr := c.baseURL
	if path != "" {
		if strings.HasPrefix(path, "/") {
			urlStr = c.baseURL + path
		} else {
			urlStr = c.baseURL + "/" + path
		}
	}

	if len(queryParams) > 0 {
		queryString := "?"
		for key, value := range queryParams {
			if value != nil {
				switch v := value.(type) {
				case []interface{}:
					for _, item := range v {
						if str, ok := item.(string); ok && str != "" {
							queryString += fmt.Sprintf("%s=%s&", key, url.QueryEscape(str))
						} else {
							queryString += fmt.Sprintf("%s=%s&", key, url.QueryEscape(fmt.Sprintf("%v", item)))
						}
					}
				case []int:
					for _, item := range v {
						queryString += fmt.Sprintf("%s=%d&", key, item)
					}
				case []int64:
					for _, item := range v {
						queryString += fmt.Sprintf("%s=%d&", key, item)
					}
				case []float64:
					for _, item := range v {
						if item == float64(int(item)) {
							queryString += fmt.Sprintf("%s=%d&", key, int(item))
						} else {
							queryString += fmt.Sprintf("%s=%f&", key, item)
						}
					}
				case string:
					queryString += fmt.Sprintf("%s=%s&", key, url.QueryEscape(v))
				case int, int64:
					queryString += fmt.Sprintf("%s=%d&", key, v)
				case float64:
					if v == float64(int(v)) {
						queryString += fmt.Sprintf("%s=%d&", key, int(v))
					} else {
						queryString += fmt.Sprintf("%s=%f&", key, v)
					}
				default:
					queryString += fmt.Sprintf("%s=%s&", key, url.QueryEscape(fmt.Sprintf("%v", v)))
				}
			}
		}

		if len(queryString) > 1 && queryString[len(queryString)-1] == '&' {
			queryString = queryString[:len(queryString)-1]
		}
		urlStr += queryString
	}

	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		bodyReader = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, urlStr, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("%w: new request failed: %w", ErrMakeRequest, err)
	}

	for key, value := range c.defaultHeaders {
		req.Header.Set(key, value)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	log.Debugf("Make request method %s to %s", method, urlStr)

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("%w: do request failed: %w", ErrMakeRequest, err)
	}

	return resp, nil
}

// Helper function to handle response and close body

func (c *Client) handleResponse(resp *http.Response, err error) (*http.Response, []byte, error) {
	if err != nil {
		return nil, nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if IsConnectionError(err) {
			return nil, nil, fmt.Errorf("%w: failed to connect to server: %s", ErrConnectionFailed, err.Error())
		}
		return nil, nil, err
	}
	if resp.StatusCode == http.StatusNotFound {
		return nil, nil, fmt.Errorf("%w: endpoint not found: %s", ErrNotFoundEndpoint, string(body))
	}

	if err = handleErrorResp(resp, body); err != nil {
		return nil, nil, err
	}

	return resp, body, nil
}
