package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-core/dto"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

func CreateFormRelation(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	var req dto.FormRelationRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	req.OrgID = org.ID
	req.OrgSchema = org.Schema
	formRelation, cErr := services.FormRelation.Create(&req)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}
	appG.Response201(formRelation)
}

func UpdateFormRelation(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	var req dto.FormRelationRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	relationID := c.Param(util.IDParamKey)
	formRelation, appErr := services.FormRelation.FindById(relationID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	req.OrgID = org.ID
	req.OrgSchema = org.Schema
	formRelation, appErr = services.FormRelation.Update(formRelation, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(formRelation)
}

func DeleteFormRelation(c *gin.Context) {
	appG := app.Gin{C: c}
	relationID := c.Param(util.IDParamKey)
	formRelation, appErr := services.FormRelation.FindById(relationID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if appErr = services.FormRelation.Delete(formRelation); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}
