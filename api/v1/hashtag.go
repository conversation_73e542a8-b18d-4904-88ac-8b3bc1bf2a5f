package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Create Hashtag
// @Description	Create Hashtag
//
// @Tags			hashtag
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string						true	"Origin"
// @Param			X-referer	header		string						true	"X-referer"
// @Param			body		body		dto.CreateHashtagRequest	true	"Create Hashtag request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/hashtags [POST]
func CreateHashtag(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.CreateHashtagRequest

	if err := c.Should<PERSON>ind<PERSON>(&reqBody); err != nil {
		log.Error("Api::Hashtag.Create Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		log.Error("Api::Hashtag.Create validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var hashtags []*models.Hashtag

	for _, hashtagName := range reqBody.Names {
		hashtags = append(hashtags, &models.Hashtag{Name: *hashtagName})
	}

	// Call hashtag create service
	if err := services.Hashtag.CreateMany(hashtags, appG.GetOrg().ID); err != nil {
		log.Error("[services.Hashtag.CreateMany]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response201("Success")
}

// @Summary		Delete Hashtag
// @Description	Delete Hashtag
//
// @Tags			hashtag
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string						true	"Origin"
// @Param			X-referer	header		string						true	"X-referer"
// @Param			body		body		dto.DeleteHashtagRequest	true	"Delete Hashtag request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/hashtags [DELETE]
func DeleteHashtag(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()

	var reqBody dto.DeleteHashtagRequest

	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Hashtag.Delete Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		log.Error("Api::Hashtag.Delete validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	orgID := appG.GetOrg().ID
	query := models.HashtagQuery{
		HashIn:         reqBody.IDs,
		IncludeDeleted: util.NewBool(false),
	}

	if currentUser.IsOrgAdmin(orgID) {
		query.OrgID = &orgID
	}

	// Call hashtag delete service
	if err := services.Hashtag.DeleteMany(&query); err != nil {
		log.Error("[services.Hashtag.Delete]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("Success")

}

// @Summary		Find Page Hashtags
// @Description	Find Page Hashtags
//
// @Tags			hashtag
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string					true	"Origin"
// @Param			X-referer	header		string					true	"X-referer"
// @Param			option		query		models.FindPageOptions	false	"Find Page Hashtags request options"
// @Param			query		query		models.HashtagQuery		false	"Find Page Hashtags request options"
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/hashtags [GET]
func FindPageHashtag(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		log.Error("Api::Hashtag.FindPage Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query models.HashtagQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if currentUser := appG.GetLoggedUser(); currentUser != nil && !currentUser.IsSysAdmin() {
		query.OrgID = &org.ID
	}

	query.IncludeDeleted = util.NewBool(false)

	resp := dto.ListHashtagResponse{
		Results:    []*models.SimpleHashtag{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	// Call hashtag find page service
	hashtags, pagination, err := services.Hashtag.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = lo.Map(hashtags, func(h *models.Hashtag, _ int) *models.SimpleHashtag {
		return h.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find Page Hashtag Relations
// @Description	Find Page Hashtag Relations
//
// @Tags			hashtag
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string						true	"Origin"
// @Param			X-referer	header		string						true	"X-referer"
// @Param			option		query		models.FindPageOptions		false	"Find Page Hashtag relations request options"
// @Param			query		query		models.HashtagRelationQuery	false	"Find Page Hashtag relations request options"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/hashtags/entities [GET]
func FindPageHashtagRelation(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		log.Error("Api::Hashtag.FindPage Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query models.HashtagRelationQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	resp := dto.ListHashtagRelationResponse{
		Results:    []*models.SimpleHashtagRelation{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	// Call hashtag find page service
	hashtagRelations, pagination, err := services.HashtagRelation.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = lo.Map(hashtagRelations, func(h *models.HashtagRelation, _ int) *models.SimpleHashtagRelation {
		return h.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}
