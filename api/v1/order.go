package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/payment"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CreateOrder
// @Summary		Create a new order or return the existing order
// @Description	Create a new order or return the existing order
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.CreateOrderRequest		true	"Create order input"
//
// @Success		201			{object}	app.ResponseT[dto.OrderPaymentResponse]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/orders [POST]
func CreateOrder(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CreateOrderRequest{}
	currentUser := appG.GetLoggedUser()

	if err := appG.BindAndValidateJSON(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	// Check course is already purchased
	order, oErr := services.Order.IsPurchased(currentUser.ID, &data)
	// already purchased
	if oErr != nil {
		appG.Response400(oErr.ErrCode, oErr.Msg)
		return
	}

	var refErr *e.AppError
	if data.ReferralCode != nil {
		if _, refCodeErr := services.ReferralLink.ValidateReferralCode(&dto.ValidateReferralCodeRequest{
			CourseCuid:   data.CourseCuid,
			ReferralCode: *data.ReferralCode,
		}); refCodeErr != nil {
			refErr = refCodeErr
			data.ReferralCode = util.NewString("")
		}
	}

	// if status insufficient then return latest order
	// if status success return 400 error
	if order == nil {
		if data.PaymentMethodID != "" {
			// get payment service url
			paymentMethod, pmErr := services.PaymentMethod.FindByID(data.PaymentMethodID, false, &models.FindOneOptions{})
			if pmErr != nil {
				appG.ResponseAppError(pmErr)
				return
			}
			data.PaymentMethodID = paymentMethod.ID
			data.Currency = paymentMethod.PaymentType
		}

		//Else return new order
		data.Org = appG.GetOrg()
		o, aErr := services.Order.Create(currentUser, &data)
		if aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}
		order = o
	} else {
		// Update ref code
		refCode := ""
		if data.ReferralCode != nil {
			refCode = *data.ReferralCode
		}

		aErr := services.Order.ChangeReferralCode(order, refCode)
		if aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}
	}

	// Get payment service url
	if order.PaymentMethodID != "" {
		paymentMethod, pmErr := services.PaymentMethod.FindByID(order.PaymentMethodID, false, &models.FindOneOptions{})
		if pmErr != nil {
			appG.ResponseAppError(pmErr)
			return
		}
		paymentProvider, err := payment.New(payment.PaymentServiceSepay)
		if err != nil {
			appG.Response500(e.Payment_service_not_handle, err.Error())
			return
		}
		paymentServiceUrl := paymentProvider.GetUrl(paymentMethod.AccountNumber, paymentMethod.Account, order.MissingAmount, order.Code)
		appG.Response200(dto.OrderPaymentResponse{Order: *order, PaymentUrl: paymentServiceUrl, PaymentMethod: paymentMethod, ReferralError: refErr})
		return
	}

	appG.Response201(dto.OrderPaymentResponse{Order: *order, ReferralError: refErr})
}

// ChangePaymentMethodForOrder
// @Summary		Add a payment method to the order
// @Description	Add a payment method to the order
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.VerifyPayment	true	"Add payment method to order input"
//
// @Success		201				{object}		app.ResponseT[dto.OrderPaymentResponse]
// @Failure		400				{object}		app.ResponseT[app.ErrorData]
// @Failure		500				{object}		app.ResponseT[app.ErrorData]
// @Router		/api/v1/orders/:id/payment [POST]
func ChangePaymentMethodForOrder(c *gin.Context) {
	appG := app.Gin{C: c}
	reqBody := dto.VerifyPayment{}
	orderID := c.Param(models.PathParamKeyID)
	currentUser := appG.GetLoggedUser()
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	order, oErr := services.Order.FindByID(orderID, false, nil)
	if oErr != nil {
		log.Error("find order by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if isAllow := services.Order.CanUpdateOrder(order, currentUser); !isAllow {
		appG.Response400(e.OrderOwnerRequired, "Require order owner")
		return
	}

	paymentMethodId := reqBody.PaymentMethodID
	if paymentMethodId == "" {
		appG.Response400(e.OrderNeedPayment, "Require payment method")
		return
	}

	// get payment service url
	paymentMethod, pmErr := services.PaymentMethod.FindByID(paymentMethodId, false, &models.FindOneOptions{})
	if pmErr != nil {
		appG.ResponseAppError(pmErr)
		return
	}

	if aErr := services.Order.ChangePaymentMethod(order, paymentMethod); aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	paymentProvider, err := payment.New(payment.PaymentServiceSepay)
	if err != nil {
		appG.Response500(e.Payment_service_not_handle, err.Error())
		return
	}
	paymentServiceUrl := paymentProvider.GetUrl(paymentMethod.AccountNumber, paymentMethod.Account, order.MissingAmount, order.Code)
	appG.Response201(dto.OrderPaymentResponse{Order: *order, PaymentUrl: paymentServiceUrl, PaymentMethod: paymentMethod})
}

// UpdateOrder
// @Summary		Update the order by ID
// @Description	Update the order by ID
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id				path		string					true	"Order ID"
// @Param		Input			body		dto.UpdateOrderRequest	true	"Update order input"
//
// @Success		200			{object}	app.ResponseT[models.SimpleOrder]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/orders/{id} [PUT]
func UpdateOrder(c *gin.Context) {
	appG := app.Gin{C: c}
	reqBody := dto.UpdateOrderRequest{}
	orderID := c.Param(models.PathParamKeyID)
	currentUser := appG.GetLoggedUser()
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	order, oErr := services.Order.FindByID(orderID, false, nil)
	if oErr != nil {
		log.Error("find order by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if isAllow := services.Order.CanUpdateOrder(order, currentUser); !isAllow {
		appG.Response400(e.OrderOwnerRequired, "Require order owner")
		return
	}
	// set new value

	if reqBody.Status != nil {
		order.Status = *reqBody.Status
	}

	if reqBody.PaymentMethodID != nil {
		paymentMethod, pmErr := services.PaymentMethod.FindByID(order.PaymentMethodID, false, &models.FindOneOptions{})
		if pmErr != nil {
			appG.ResponseAppError(pmErr)
			return
		}

		order.PaymentMethodID = paymentMethod.ID
		order.Currency = paymentMethod.PaymentType
	}

	aErr := services.Order.Update(order)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(order.Sanitize())
}

// FindOrderByID
// @Summary		Find the order by ID
// @Description	Find the order by ID
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string		true	"Order ID"
//
// @Success		200			{object}	app.ResponseT[models.SimpleOrder]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/orders/{id} [GET]
func FindOrderByID(c *gin.Context) {
	appG := app.Gin{C: c}
	orderID := c.Param("id")

	order, oErr := services.Order.FindByID(orderID, true, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find order by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200(order.Sanitize())
}

// FindMyOrders
// @Summary		Find the list my orders with pagination
// @Description	Find the list my orders with pagination
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		filters		query		models.OrderQuery		true	"Filter query"
// @Param		pagination	query		models.FindPageOptions	true	"Pagination query"
//
// @Success		200			{object}	app.ResponseT[dto.ListOrdersResponse]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/orders [GET]
func FindMyOrders(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	query := models.OrderQuery{
		IncludeDeleted: util.NewBool(false),
		StatusIn:       []models.OrderStatus{models.OrderStatusInsufficient, models.OrderStatusSuccess, models.OrderStatusFailed},
		UserId:         &currentUser.ID,
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListOrdersResponse{
		Results:    []*models.SimpleOrder{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	orders, pagination, err := services.Order.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(orders, func(o *models.Order, _ int) *models.SimpleOrder {
		return o.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// FindPageOrdersByAdmin
// @Summary		Find the list orders by pagination
// @Description	Find the list orders by pagination
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		filters		query		models.OrderQuery		true	"Filter query"
// @Param		pagination	query		models.FindPageOptions	true	"Pagination query"
//
// @Success		200			{object}	app.ResponseT[dto.ListOrdersResponse]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/admin/orders [GET]
func FindPageOrdersByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	query := models.OrderQuery{
		IncludeDeleted: util.NewBool(false),
		NotStatus:      util.NewT(models.OrderStatusNew),
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListOrdersResponse{
		Results:    []*models.SimpleOrder{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	orders, pagination, err := services.Order.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(orders, func(o *models.Order, _ int) *models.SimpleOrder {
		return o.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)

}

// UseCoupon
// @Summary		Use coupon
// @Description	Use coupon
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		code		path		string	true	"Coupon code"
// @Param		id			path		string	true	"Order ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/orders/{id}/coupons/{code} [POST]
func UseCoupon(c *gin.Context) {
	appG := app.Gin{C: c}
	orderID := c.Param(models.PathParamKeyID)
	couponCode := c.Param(models.PathParamKeyCode)

	order, oErr := services.Order.FindByID(orderID, false, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find order by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	coupon, cErr := services.Coupon.FindOne(&models.CouponQuery{CouponCode: &couponCode}, &models.FindOneOptions{})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if order.Status != models.OrderStatusNew {
		appG.Response400(e.OrderStatusNotAllow, "Order status not allow to apply coupon")
		return
	}

	if cErr = services.Order.ApplyCoupon(appG.GetOrg(), appG.GetLoggedUser(), coupon, order); cErr != nil {
		log.Error("Use coupon errors", cErr)
		appG.ResponseAppError(cErr)
		return
	}

	// get payment service url
	paymentMethod, pmErr := services.PaymentMethod.FindByID(order.PaymentMethodID, false, &models.FindOneOptions{})
	if pmErr != nil {
		appG.ResponseAppError(pmErr)
		return
	}
	paymentProvider, err := payment.New(payment.PaymentServiceSepay)
	if err != nil {
		appG.Response500(e.Payment_service_not_handle, err.Error())
		return
	}
	paymentServiceUrl := paymentProvider.GetUrl(paymentMethod.AccountNumber, paymentMethod.Account, order.MissingAmount, order.Code)
	appG.Response200(dto.OrderPaymentResponse{Order: *order, PaymentUrl: paymentServiceUrl, PaymentMethod: paymentMethod})
}

// RemoveCoupon
// @Summary		Use coupon
// @Description	Use coupon
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string	true	"Order ID"
//
// @Success		200				{object}	app.ResponseT[dto.OrderPaymentResponse]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/orders/{id}/coupons [DELETE]
func RemoveCoupon(c *gin.Context) {
	appG := app.Gin{C: c}
	orderID := c.Param(models.PathParamKeyID)

	order, appErr := services.Order.FindByID(orderID, false, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	order, appErr = services.Order.RemoveCoupon(order, true)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	// get payment service url
	paymentMethod, pmErr := services.PaymentMethod.FindByID(order.PaymentMethodID, false, &models.FindOneOptions{})
	if pmErr != nil {
		appG.ResponseAppError(pmErr)
		return
	}
	paymentProvider, err := payment.New(payment.PaymentServiceSepay)
	if err != nil {
		appG.Response500(e.Payment_service_not_handle, err.Error())
		return
	}
	paymentServiceUrl := paymentProvider.GetUrl(paymentMethod.AccountNumber, paymentMethod.Account, order.MissingAmount, order.Code)
	appG.Response200(dto.OrderPaymentResponse{Order: *order, PaymentUrl: paymentServiceUrl, PaymentMethod: paymentMethod})
}

// MakeOrderSuccessByCoupon
// @Summary		Make the order success by coupon
// @Description	Make the order success by coupon
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string		true	"Order ID"
//
// @Success		200				{object}	app.ResponseT[string]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/orders/:id/success [POST]
func MakeOrderSuccessByCoupon(c *gin.Context) {
	appG := app.Gin{C: c}
	orderID := c.Param(models.PathParamKeyID)
	reqBody := dto.VerifyPayment{}
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	order, oErr := services.Order.FindByID(orderID, false, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("Find order by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if appErr := services.Order.MarkOrderSuccessByCoupon(order, &reqBody); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200("success")
}

// PayOrderWithWallet
// @Summary		Make the order success by coupon
// @Description	Make the order success by coupon
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string		true	"Order ID"
//
// @Success		200				{object}	app.ResponseT[string]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/orders/:id/payment [POST]
func PayOrderWithWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	orderID := c.Param(models.PathParamKeyID)
	user := appG.GetLoggedUser()

	req := dto.PayOrderRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	order, appErr := services.Order.FindByID(orderID, false, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{ID: util.NewString(req.WalletID)}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appErr = services.Order.ValidateBeforeProcessPayment(user, order, wallet)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appErr = services.Order.PayThroughWallet(order, wallet)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// CheckOrderStatus
// @Summary		Check order status by ID
// @Description	Check order status by ID
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string		true	"Order ID"
//
// @Success		200			{object}	app.ResponseT[models.OrderStatus]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/orders/{id}/status [GET]
func CheckOrderStatus(c *gin.Context) {
	appG := app.Gin{C: c}
	orderID := c.Param(models.PathParamKeyID)
	order, oErr := services.Order.FindByID(orderID, true, &models.FindOneOptions{})
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}
	appG.Response200(dto.CheckOrderStatusResponse{
		Status: order.Status,
	})
}
