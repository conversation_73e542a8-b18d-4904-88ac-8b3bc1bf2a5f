package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

//	@Summary		Create payment method
//	@Description	Create payment method
//
//	@Tags			paymentMethod
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string						true	"Origin"
//	@Param			X-referer		header		string						true	"X-referer"
//	@Param			Authorization	header		string						true	"Bearer"
//
//	@Param			Input			body		dto.PaymentMethodRequest	true	"Webhook input"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/payment-methods [POST]
func CreatePaymentMethod(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	data := dto.PaymentMethodRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	paymentMethod, aErr := services.PaymentMethod.Create(currentUser, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(paymentMethod.Sanitize())
}

//	@Summary		Find Page
//	@Description	Find Page
//
//	@Tags			paymentMethod
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//	@Params			Input query 																																																																																																models.FindPageOptions 			 false  	"find page options"
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/payment-methods [GET]
func FindPagePaymentMethod(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	query := models.PaymentMethodQuery{
		IncludeDeleted: util.NewBool(false),
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return

	}

	resp := dto.ListPaymentMethodResponse{
		Results:    []*models.SimplePaymentMethod{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	paymentMethods, pagination, err := services.PaymentMethod.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map[*models.PaymentMethod, *models.SimplePaymentMethod](paymentMethods, func(p *models.PaymentMethod, _ int) *models.SimplePaymentMethod {
		return p.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

//	@Summary		Update payment method
//	@Description	Update payment method
//
//	@Tags			paymentMethod
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string						true	"Origin"
//	@Param			X-referer		header		string						true	"X-referer"
//	@Param			Authorization	header		string						true	"Bearer"
//
//	@Param			id				path		string						true	"payment id"
//
//	@Param			Input			body		dto.PaymentMethodRequest	true	"Update payment method input"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/payment-methods/{id} [PUT]
func UpdatePaymentMethod(c *gin.Context) {
	appG := app.Gin{C: c}
	reqBody := dto.PaymentMethodRequest{}
	paymentMethodId := c.Param("id")
	currentUser := appG.GetLoggedUser()
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	err := util.Validator.Struct(reqBody)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	paymentMethod, oErr := services.PaymentMethod.FindByID(paymentMethodId, false, nil)
	if oErr != nil {
		log.Error("find payment method by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if isAllow := services.PaymentMethod.CanUpdatePaymentMethod(paymentMethod, currentUser); !isAllow {
		appG.Response400(e.Payment_method_owner_required, "Require payment method owner")
		return
	}
	// set new value

	paymentMethod.OrganizeID = reqBody.OrganizeID
	paymentMethod.CourseID = reqBody.CourseID
	paymentMethod.Service = reqBody.Service
	paymentMethod.Account = reqBody.Account
	paymentMethod.AccountNumber = reqBody.AccountNumber
	paymentMethod.AccountName = reqBody.AccountName
	paymentMethod.Network = reqBody.Network
	paymentMethod.Enable = reqBody.Enable

	aErr := services.PaymentMethod.Update(paymentMethod)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(paymentMethod.Sanitize())
}

//	@Summary		Find By ID
//	@Description	Find By ID
//
//	@Tags			paymentMethod
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"payment method id"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/payment-methods/{id} [GET]
func FindPaymentMethodByID(c *gin.Context) {
	appG := app.Gin{C: c}
	paymentMethodId := c.Param("id")

	paymentMethod, oErr := services.PaymentMethod.FindByID(paymentMethodId, true, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find payment method by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200(paymentMethod.Sanitize())
}

//	@Summary		Delete By ID
//	@Description	Delete By ID
//
//	@Tags			paymentMethod
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"payment method id"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/payment-methods/{id} [DELETE]
func DeletePaymentMethodById(c *gin.Context) {
	appG := app.Gin{C: c}
	paymentMethodId := c.Param("id")

	var model models.PaymentMethod
	model.ID = paymentMethodId

	oErr := services.PaymentMethod.Delete(&model)
	if oErr != nil {
		log.Error("find payment method by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200("success")
}
