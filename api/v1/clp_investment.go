package v1

import (
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"time"

	"github.com/gin-gonic/gin"
)

// CreateInvestment
//
// @Summary		Create investment
// @Description	Create investment
//
// @Tags     	investment
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string						true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string						true	"X-referrer"	default(openedu101dev.com)
// @Param		Input		body		dto.CreateInvestmentRequest	true	"Create investment input"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/investments [POST]
func CreateInvestment(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	req := dto.CreateInvestmentRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	launchpad, aErr := services.ClpLaunchpad.FindByID(req.LaunchpadID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	timeNow := time.Now().UnixMilli()
	if launchpad.FundingStartDate > timeNow || launchpad.FundingEndDate < timeNow {
		errorMsg := fmt.Sprintf("Funding time not invalid. Start date: %d, End date: %d, Current time: %d",
			launchpad.FundingStartDate,
			launchpad.FundingEndDate,
			timeNow)
		appG.Response400(e.INVALID_PARAMS, errorMsg)
		return
	}

	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{ID: util.NewString(req.WalletID)}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if wallet.UserID != user.ID {
		appG.Response400(e.WalletOwnerRequired, "Wallet owner required")
		return
	}

	if wallet.Currency != launchpad.FundingGoal.Currency {
		appG.Response400(e.INVALID_PARAMS,
			fmt.Sprintf("Launchpad currency is %v but wallet currency is %v",
				launchpad.FundingGoal.Currency, wallet.Currency))
		return
	}

	req.Launchpad = launchpad
	req.User = appG.GetLoggedUser()
	req.Org = appG.GetOrg()
	req.Wallet = wallet
	investment, aErr := services.ClpInvestment.Create(&req)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	newInvestment, aErr := services.ClpInvestment.FindByID(investment.ID)
	if aErr != nil {
		log.Error("find investment failed", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(newInvestment)
}

// FindInvestmentsForLaunchpad
//
// @Summary		Find page investment
// @Description	Find page investment
//
// @Tags     	investment
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string						true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string						true	"X-referrer"	default(openedu101dev.com)
// @Param		Input		body		dto.CreateInvestmentRequest	true	"Create investment input"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/launchpads/{id}/investments [GET]
func FindInvestmentsForLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	listInvestments, appErr := services.ClpInvestment.FindInvestmentsLaunchpad(launchpad, &options)
	if appErr != nil {
		log.Error("find page investment for launchpad failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(listInvestments)
}

// InvestmentVotingForLaunchpad
//
// @Summary		Backer vote accept or approve for launchpad
// @Description	Backer vote accept or approve for launchpad
//
// @Tags     	investment
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string						true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string						true	"X-referrer"	default(openedu101dev.com)
// @Param		Input		body		dto.CreateInvestmentRequest	true	"Create investment input"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/milestones/{id}/voting [POST]
func InvestmentVotingForLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()
	req := dto.VotingLaunchpadRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	votingMilestone, aErr := services.ClpVotingMilestone.FindByID(id)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	launchpad, aErr := services.ClpLaunchpad.FindByID(votingMilestone.ClpLaunchpadID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	req.Launchpad = launchpad
	req.User = user
	req.Milestone = votingMilestone
	aErr = services.ClpVotingMilestone.VotingForLaunchpad(&req)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}

// GetMyLaunchpads
//
// @Summary		Find my launchpads list
// @Description	Find my launchpads list
//
// @Tags     	launchpad
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string						true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string						true	"X-referrer"	default(openedu101dev.com)
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/launchpads/investments/me [GET]
func GetMyLaunchpads(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.ClpInvestmentQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if query.Status == nil {
		appG.Response400(e.INVALID_PARAMS, "query status not null")
		return
	}

	resp, appErr := services.ClpInvestment.FindMyLaunchpads(user, &query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(resp)
}
