package v1

import (
	"fmt"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/upload"
	"openedu-core/services"
	"time"

	"github.com/gin-gonic/gin"
)

// @Summary		Report course enrollments
// @Description	Report course enrollments
//
// @Tags			Report
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string		true	"Origin"
// @Param			X-referer		header					string		true	"X-referer"
// @Param			Authorization	header					string		true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions 	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/reports/course-enrollments [POST]
func ReportCourseEnrollment(c *gin.Context) {
	appG := app.Gin{C: c}

	data := models.ReportCourseEnrollmentRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if validateErr := e.HandleValidationError(data); validateErr != nil {
		appG.Response400(e.INVALID_PARAMS, validateErr)
		return
	}

	var fileContent []byte
	var fileName string
	var fileMime string
	var filePublic bool
	var err *e.AppError

	switch data.ReportType {
	case models.ReportCourseEnrollment:
		fileContent, err = services.Report.ReportUserEnrollmentCourse(&data)
		if err != nil {
			appG.ResponseAppError(err)
			return
		}
		fileName = fmt.Sprintf("course_enrollment_report_%s_%s.xlsx", *data.CourseCuid, time.Now().Format("2006-01-02"))
		fileMime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		filePublic = true
	case models.ReportCourseOrderedEnrolled:
		fileContent, err = services.Report.ReportOrderedAndEnrolled(&data)
		if err != nil {
			appG.ResponseAppError(err)
			return
		}
		fileName = fmt.Sprintf("report/enrolled_ordered_report_%s_%s.xlsx", *data.CourseCuid, time.Now().Format("2006-01-02"))
		fileMime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		filePublic = true

	case models.ReportCourseLearningStatus:
		fileContent, err = services.Report.ReportCourseLearningStatus(&data)
		if err != nil {
			appG.ResponseAppError(err)
			return
		}
		fileName = fmt.Sprintf("report/course_learning_status_%s_%s.xlsx", *data.CourseCuid, time.Now().Format("2006-01-02"))
		fileMime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		filePublic = true
	}

	file := &upload.File{
		Name:    fileName,
		Mime:    fileMime,
		Content: fileContent,
		Public:  filePublic,
	}

	uploadedFiles, errU := upload.DefaultProvider.UploadFiles([]*upload.File{file}, "reports")
	if errU != nil {
		appG.ResponseAppError(e.NewError500(e.Error_file_upload_fail, errU.Error()))
		return
	}

	appG.Response200(uploadedFiles)
}

// @Summary		Report course referral
// @Description	Report course referral
//
// @Tags			Report
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string		true	"Origin"
// @Param			X-referer		header					string		true	"X-referer"
// @Param			Authorization	header					string		true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions 	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/reports/course-referrals [POST]
func ReportCourseReferral(c *gin.Context) {
	appG := app.Gin{C: c}

	data := models.ReportReferralLinkByUserRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if validateErr := e.HandleValidationError(data); validateErr != nil {
		appG.Response400(e.INVALID_PARAMS, validateErr)
		return
	}

	var fileContent []byte
	var fileName string
	var fileMime string
	var filePublic bool
	var err *e.AppError

	switch data.ReportType {
	case models.ReportCourseReferral:
		fileContent, err = services.Report.ReportReferralLinkByUser(&data)
		if err != nil {
			appG.ResponseAppError(err)
			return
		}
		fileName = fmt.Sprintf("course_referral_report_%s_%s.xlsx", data.CourseCuid, time.Now().Format("2006-01-02"))
		fileMime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		filePublic = true
	}

	file := &upload.File{
		Name:    fileName,
		Mime:    fileMime,
		Content: fileContent,
		Public:  filePublic,
	}

	uploadedFiles, errU := upload.DefaultProvider.UploadFiles([]*upload.File{file}, "reports")
	if errU != nil {
		appG.ResponseAppError(e.NewError500(e.Error_file_upload_fail, errU.Error()))
		return
	}

	appG.Response200(uploadedFiles)
}

func FindStructuresReport(c *gin.Context) {
	appG := app.Gin{C: c}

	reportOpts := services.Report.FindStructuresReportType()

	appG.Response200(reportOpts)
}

func ReportLearningProgress(c *gin.Context) {
	appG := app.Gin{C: c}

	var fileContent []byte
	var fileName string
	var fileMime string
	var filePublic bool
	var err *e.AppError

	fileContent, err = services.Report.ReportCourseEngagement()
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	fileName = "course_engagements.xlsx"
	fileMime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	filePublic = true

	file := &upload.File{
		Name:    fileName,
		Mime:    fileMime,
		Content: fileContent,
		Public:  filePublic,
	}

	uploadedFiles, errU := upload.DefaultProvider.UploadFiles([]*upload.File{file}, "reports")
	if errU != nil {
		appG.ResponseAppError(e.NewError500(e.Error_file_upload_fail, errU.Error()))
		return
	}

	appG.Response200(uploadedFiles)
}

func ReportQuizSubmission(c *gin.Context) {
	appG := app.Gin{C: c}

	var fileContent []byte
	var fileName string
	var fileMime string
	var filePublic bool
	var err *e.AppError

	fileContent, err = services.Report.ReportCourseQuiz()
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	fileName = "course_quizzes.xlsx"
	fileMime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	filePublic = true

	file := &upload.File{
		Name:    fileName,
		Mime:    fileMime,
		Content: fileContent,
		Public:  filePublic,
	}

	uploadedFiles, errU := upload.DefaultProvider.UploadFiles([]*upload.File{file}, "reports")
	if errU != nil {
		appG.ResponseAppError(e.NewError500(e.Error_file_upload_fail, errU.Error()))
		return
	}

	appG.Response200(uploadedFiles)
}

func ReportAIUsage(c *gin.Context) {
	appG := app.Gin{C: c}

	data := models.ReportAIUserUsageRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if validateErr := e.HandleValidationError(data); validateErr != nil {
		appG.Response400(e.INVALID_PARAMS, validateErr)
		return
	}

	var fileContent []byte
	var fileName string
	var fileMime string
	var filePublic bool
	var err *e.AppError

	fileContent, err = services.Report.ReportAIUserUsage(&data)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	fileName = fmt.Sprintf("ai_usage_%s.xlsx", time.Now().Format("2006-01-02"))
	fileMime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	filePublic = true

	file := &upload.File{
		Name:    fileName,
		Mime:    fileMime,
		Content: fileContent,
		Public:  filePublic,
	}

	uploadedFiles, errU := upload.DefaultProvider.UploadFiles([]*upload.File{file}, "reports")
	if errU != nil {
		appG.ResponseAppError(e.NewError500(e.Error_file_upload_fail, errU.Error()))
		return
	}

	appG.Response200(uploadedFiles)
}
