package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Create Lesson
// @Description	Create Lesson
//
// @Tags			lesson
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.LessonContentRequest	true	"Create lesson input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/lessons [POST]
func CreateLessonContent(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.LessonContentRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}
	data.OrgID = appG.GetOrg().ID
	section, cErr := services.LessonContent.Create(appG.GetLoggedUser(), &data)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(section)
}

// @Summary		Update lesson
// @Description	Update lesson
//
// @Tags			lesson
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"id"
//
// @Param			Input			body		dto.LessonContentRequest	true	"Update lesson request"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/lessons/{id} [PUT]
func UpdateLesson(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.LessonContentRequest{}
	id := c.Param("id")
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	lesson, fErr := services.LessonContent.FindOne(&models.LessonContentQuery{ID: util.NewString(id)}, nil)
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	saved, cErr := services.LessonContent.Update(lesson, &data)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(saved)
}

// @Summary		Find lesson by ID
// @Description	Find lesson by ID
//
// @Tags			lesson
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/lessons/{id} [GET]
func GetLesson(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	var user models.User
	if !appG.RequiredLogin(&user) {
		return
	}

	// Check if course has register course forms
	//if hErr := services.Form.HandleEventForCourse(&loggedUser, course, models.FormEventRegisterCourse); hErr != nil {
	//	appG.ResponseAppError(hErr)
	//	return
	//}

	query := models.SectionQuery{
		ID:             util.NewString(id),
		IncludeDeleted: util.NewBool(false),
	}
	section, cErr := services.Section.FindOne(&query, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(section)
}

// GetLessonForLearning
//
//	@Summary		Find lesson by ID
//	@Description	Find lesson by ID
//
//	@Tags			lesson
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"id"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/lessons/{id}/learn [GET]
func GetLessonForLearning(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	org := appG.GetOrg()
	var user models.User
	if !appG.RequiredLogin(&user) {
		return
	}

	query := models.SectionQuery{
		ID:             util.NewString(id),
		IncludeDeleted: util.NewBool(false),
	}
	section, cErr := services.Section.FindOne(&query, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	// handle check certificate condition for learner can receive certificate
	go func() {
		course, appErr := services.Course.FindById(section.CourseID, false, nil)
		if appErr != nil {
			log.Error("Find course by section id failed", appErr)
			return
		}

		services.Certificate.PushNotificationReceiveCertificate(course, &user, org)
	}()

	// ToSimple sensitive fields like quiz questions before returning section data to the clients
	appG.Response200(section.Sanitize())
}

// GetLessonForLearningByUID
//
//	@Summary		Find lesson by ID
//	@Description	Find lesson by ID
//
//	@Tags			lesson
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"id"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/lessons/{id}/learn [GET]
func GetLessonForLearningByUID(c *gin.Context) {
	appG := app.Gin{C: c}
	uid := c.Param("id")  // section uid
	cid := c.Param("cid") // course cuid
	org := appG.GetOrg()
	var user models.User
	if !appG.RequiredLogin(&user) {
		return
	}

	query := models.SectionQuery{
		UID:            util.NewString(uid),
		CourseID:       util.NewString(cid),
		IncludeDeleted: util.NewBool(false),
	}
	section, cErr := services.Section.FindOne(&query, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	var quizUIDs []string
	for _, content := range section.Contents {
		for _, quiz := range content.Quizzes {
			quizUIDs = append(quizUIDs, quiz.UID)
		}
	}

	if len(quizUIDs) > 0 {
		submissions, err := models.Repository.QuizSubmission.FindMany(&models.QuizSubmissionQuery{
			UserID:    &user.ID,
			QuizUIDIn: quizUIDs,
		}, nil)
		if err != nil {
			appG.ResponseAppError(e.NewError500(e.ERROR, err.Error()))
			return
		}

		submissionCountByQuizUID := make(map[string]int)
		for _, submission := range submissions {
			submissionCountByQuizUID[submission.QuizUID]++
		}

		for _, content := range section.Contents {
			for _, quiz := range content.Quizzes {
				quiz.SubmissionCount = submissionCountByQuizUID[quiz.UID]
			}
		}
	}

	// handle check certificate condition for learner can receive certificate
	go func() {
		course, appErr := services.Course.FindById(section.CourseID, false, nil)
		if appErr != nil {
			log.Error("Find course by section id failed", appErr)
			return
		}

		services.Certificate.PushNotificationReceiveCertificate(course, &user, org)
	}()

	// ToSimple sensitive fields like quiz questions before returning section data to the clients
	appG.Response200(section.Sanitize())
}

// @Summary		FindPage lesson
// @Description	FindPage lesson
//
// @Tags			lesson
// @Accept			json
// @Produce		json
//
// @Param			Origin			header										string	true	"Origin"
// @Param			X-referer		header										string	true	"X-referer"
// @Param			Authorization	header										string	true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions "find page options"																																																								query 		models.FindPageOptions 		 false  	"find page options"
//
// @Success		200				{object}									app.Response
// @Failure		400				{object}									app.Response
// @Failure		500				{object}									app.Response
// @Router			/api/v1/lessons [GET]
func FindLesson(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.LessonContentQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	query.IncludeDeleted = util.NewBool(false)

	resp := dto.ListLessonResponse{
		Results:    []*models.SimpleLessonContent{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = []string{models.FilesField}
	lessons, pagination, err := services.LessonContent.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = lo.Map(lessons, func(c *models.LessonContent, _ int) *models.SimpleLessonContent {
		cc := c.ToSimple()
		return cc
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Delete lesson
// @Description	Delete lesson
//
// @Tags			lesson
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"lesson id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/lessons/{id} [DELETE]
func DeleteLesson(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	lesson, fErr := services.LessonContent.FindOne(&models.LessonContentQuery{ID: util.NewString(id)}, nil)
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	if dErr := services.LessonContent.Delete(lesson); dErr != nil {
		appG.ResponseAppError(dErr)
		return
	}
	appG.Response200("success")
}
