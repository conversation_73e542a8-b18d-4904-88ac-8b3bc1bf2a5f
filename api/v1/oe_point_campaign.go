package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Create point campaign
// @Description	Create point campaign
//
// @Tags			PointCampaign
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			Input			body		dto.CreatePointCampaignRequest	true	"Create EarnedPoint Campaign Request"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/point-campaigns [POST]
func CreateRefUserCampaign(c *gin.Context) {
	appG := app.Gin{C: c}

	var reqBody dto.RefUserConfigRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::OEPointCampaign.CreateRefUserCampaign Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var config *models.OEPointCampaign
	if reqBody.ID != nil {
		foundConfig, foundErr := services.OEPointCampaign(c.Request.Context()).FindById(*reqBody.ID, nil)
		if foundErr != nil {
			appG.ResponseAppError(foundErr)
			return
		}
		config = foundConfig
	}

	campaign, err := services.OEPointCampaign(c.Request.Context()).ReferralUserCampaign(&reqBody, config)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(campaign)
}

// @Summary		Find Page EarnedPoint Campaign
// @Description	Find Page EarnedPoint Campaign
//
// @Tags			PointCampaign
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string					true	"Origin"
// @Param			X-referer	header		string					true	"X-referer"
// @Param			option		query		models.FindPageOptions	false	"Find Page query options"
// @Param			query		query		models.OEPointCampaignQuery		false	"Find Page query options"
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/point-campaigns [GET]
func FindPagePointCampaign(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		log.Error("Api::OEPointCampaign.FindPagePointCampaign Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query models.OEPointCampaignQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	query.Deleted = util.NewBool(false)
	campaigns, pagination, err := services.OEPointCampaign(c.Request.Context()).FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp := dto.ListPointCampaignResponse{
		Results:    []*models.OEPointCampaign{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	resp.Results = campaigns
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find EarnedPoint Campaign By ID
// @Description	Find EarnedPoint Campaign By ID
//
// @Tags			PointCampaign
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"Find point campaign id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/point-campaigns/{id} [GET]
func FindPointCampaignByID(c *gin.Context) {
	appG := app.Gin{C: c}
	// org := appG.GetOrg()
	id := c.Param(util.IDParamKey)

	campaign, err := services.OEPointCampaign(c.Request.Context()).FindById(id, &models.FindOneOptions{})
	if err != nil {
		log.Error("[services.OEPointCampaign.FindById failed]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(campaign)
}

// @Summary		Update EarnedPoint Campaign
// @Description	Update EarnedPoint Campaign
//
// @Tags			PointCampaign
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			id				path		string							true	"Update point campaign id"
//
// @Param			Input			body		dto.UpdatePointCampaignRequest	true	"Update EarnedPoint Campaign Request"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/point-campaigns/{id} [PUT]
func UpdatePointCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	// org := appG.GetOrg()
	id := c.Param(util.IDParamKey)
	var reqBody dto.UpdatePointCampaignRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::OEPointCampaign.UpdatePointCampaign Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	campaign, err := services.OEPointCampaign(c.Request.Context()).FindById(id, &models.FindOneOptions{})
	if err != nil {
		log.Error("[services.OEPointCampaign.FindById failed]", err)
		appG.ResponseAppError(err)
		return
	}

	campaign.Name = reqBody.Name
	campaign, aErr := services.OEPointCampaign(c.Request.Context()).Update(campaign)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(campaign)

}

// @Summary		Delete EarnedPoint Campaign By ID
// @Description	Delete EarnedPoint Campaign By ID
//
// @Tags			PointCampaign
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"Delete point campaign id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/point-campaigns/{id} [DELETE]
func DeletePointCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	// org := appG.GetOrg()
	id := c.Param(util.IDParamKey)

	oErr := services.OEPointCampaign(c.Request.Context()).Delete(id)
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response201("success")

}
