package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/services"
)

// Update Featured Content
//
//	@Summary		Featured Content
//	@Description	Featured Content
//
//	@Tags			Featured Content
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			input			body		dto.BulkUpdateFeaturedContent	true	"body"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/featured-contents [POST]
func UpdateFeaturedContent(c *gin.Context) {
	appG := app.Gin{c}
	var request dto.BulkUpdateFeaturedContent
	if err := appG.BindAndValidateJSON(&request); err != nil {
		log.Error("BulkUpdateFeaturedContent Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	resp, eErr := services.
		NewFeaturedContentService(c.Request.Context()).
		UpsertManyFeaturedContent(&request)
	if eErr != nil {
		appG.ResponseAppError(eErr)
		return
	}

	appG.Response200(resp)
}

// Update Featured Content
//
//	@Summary		Featured Content
//	@Description	Featured Content
//
//	@Tags			Featured Content
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			query			query		models.FeaturedContentQuery	true	"query"
//	@Param			query			query		models.FindPageOptions	true	"query"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/featured-contents [GET]
func FindPageFeaturedContent(c *gin.Context) {
	appG := app.Gin{c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.FeaturedContentQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	resp := &dto.ListFeaturedContentResponse{
		Results:    []*models.FeaturedContent{},
		Pagination: &models.Pagination{},
	}
	contents, pagination, pErr := services.NewFeaturedContentService(c.Request.Context()).FindPage(&query, &options)
	if pErr != nil {
		appG.ResponseAppError(pErr)
		return
	}

	resp.Results = contents
	resp.Pagination = pagination

	appG.Response200(resp)
}

// Update Featured Content
//
//	@Summary		Featured Content
//	@Description	Featured Content
//
//	@Tags			Featured Content
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			query			query		dto.FindByTypeAndEntityRequest	true	"query"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/featured-contents/by-types [GET]
func FindFeaturedContentByType(c *gin.Context) {
	appG := app.Gin{c}
	var query dto.FindByTypeAndEntityRequest
	if err := c.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	contents, pErr := services.
		NewFeaturedContentService(c.Request.Context()).
		FindByEntityTypeAndType(query.OrgID, query.EntityType, query.Type)
	if pErr != nil {
		appG.ResponseAppError(pErr)
		return
	}

	appG.Response200(contents)
}
