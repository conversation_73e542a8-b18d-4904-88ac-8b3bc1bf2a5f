package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// CreateLearningProgress
//
//	@Summary		Create Learning Progress
//	@Description	Create Learning Progress
//
//	@Tags			learning progress
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin							header		string								true	"Origin"
//	@Param			X-referrer						header		string								true	"X-referrer"
//
//	@Param			Authorization					header		string								true	"Bearer"
//
//	@Param			createLearningProgressRequest	body		dto.CreateLearningProgressRequest	true	"Create Learning Progress request body"
//
//	@Success		200								{object}	app.Response
//	@Failure		400								{object}	app.Response
//	@Failure		500								{object}	app.Response
//	@Router			/api/v1/learning-progresses [POST]
func CreateLearningProgress(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	ctx := appG.GetCtx()
	org := appG.GetOrg()

	var reqBody dto.CreateLearningProgressRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}
	if reqBody.Event == models.LatestLessonProgress {
		overview, _, appErr := services.NewLearningStatus(ctx).UpdateLatestLesson(&dto.UpdateCurrentLessonRequest{
			UserID:            currentUser.ID,
			CourseSlug:        reqBody.CourseSlug,
			CurrentSectionUID: reqBody.SectionUID,
			CurrentLessonUID:  reqBody.LessonUID,
			OrgID:             org.ID,
		})
		if appErr != nil {
			log.Error("[services.LearningProgress.Upsert]", appErr)
			appG.ResponseAppError(appErr)
			return
		}

		appG.Response200(overview)
		return
	}

	overview, course, appErr := services.NewLearningStatus(ctx).AddLearningStatus(&dto.CreateLearningProgressParams{
		UserID:           currentUser.ID,
		CourseSlug:       reqBody.CourseSlug,
		SectionUID:       reqBody.SectionUID,
		LessonUID:        reqBody.LessonUID,
		LessonContentUID: reqBody.LessonContentUID,
		CompleteAt:       reqBody.CompleteAt,
		StartAt:          reqBody.StartAt,
		PauseAt:          reqBody.PauseAt,
		OrgID:            org.ID,
		Org:              org,
		User:             currentUser,
	})
	if appErr != nil {
		log.Error("[services.LearningProgress.Upsert]", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	// Check certificate condition for learner can receive certificate
	go func() {
		services.Certificate.PushNotificationReceiveCertificate(course, currentUser, org)
	}()

	appG.Response200(overview)
	return
}

// GetUserLearningProgressByCourse
// @Summary		Get Learning Progress by Course
// @Description	Get Learning Progress by Course
//
// @Tags			learning progress
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referrer		header		string	true	"X-referrer"
//
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course slug"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/learning-progresses/courses/:id [GET]
func GetUserLearningProgressByCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	ctx := appG.GetCtx()
	slug := c.Param(util.IDParamKey)
	userLearningProgress, course, appErr := services.NewLearningStatus(ctx).
		GetUserLearningStatusByCourse(slug, currentUser.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	// Check certificate condition for learner can receive certificate
	go func() {
		services.Certificate.PushNotificationReceiveCertificate(course, currentUser, org)
	}()

	appG.Response200(userLearningProgress)
	return
}

// CheckCompleteCourse
// @Summary      Check course completion status for multiple users
// @Description  Check course completion status for multiple users
//
// @Tags         learning progress
// @Accept       json
// @Produce      json
//
// @Param        Origin        header      string  true    "Origin"
// @Param        X-referrer    header      string  true    "X-referrer"
//
// @Param        Authorization header      string  true    "Bearer"
//
// @Param        userIDs      query       []string  true    "List of user IDs"
// @Param        courseCuid   query       string    true    "Course CUID"
// @Param        status       query       string    false   "Filter by status (completed, in_progress, not_started)"
//
// @Success      200          {object}    app.Response
// @Failure      400          {object}    app.Response
// @Failure      500          {object}    app.Response
// @Router       /api/v1/learning-progresses/check-complete-course [GET]
func CheckCompleteCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	var reqBody dto.CheckUserCompleteCourse
	if err := c.ShouldBindQuery(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if lps, err := services.NewLearningStatus(ctx).CheckCompleteCourse(reqBody); err != nil {
		appG.ResponseAppError(err)
	} else {
		appG.Response200(lps)
	}
}
