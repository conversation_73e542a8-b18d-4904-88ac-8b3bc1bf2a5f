package v1

import (
	"openedu-core/dto"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Get Available AI Models
// @Description	Get Available AI Models
//
// @Tags			AI
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referrer		header		string	true	"X-referrer"
//
// @Param			Authorization	header		string	true	"Bearer"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/ai/models [GET]
func GetAvailableAIModel(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	models, aErr := services.AIModel.GetAvailableAIModel(currentUser, org)
	if aErr != nil {
		log.Error("[services.AIModel.GetAvailableAIModel failed]", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(models)
}

// @Summary		Get Available AI Models
// @Description	Get Available AI Models
//
// @Tags		AI
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referrer		header		string	true	"X-referrer"
//
// @Param		Authorization	header		string	true	"Bearer"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/internal-v1/ai/models [GET]
func FindOneAvailableAIModel(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.FindAIModelRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if req.OrgID == "" {
		appG.Response400(e.INVALID_PARAMS, "org_id required")
		return
	}

	if req.UserID == "" {
		appG.Response400(e.INVALID_PARAMS, "user_id required")
		return
	}

	user, uErr := services.User.FindByID(req.UserID, nil)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	model, aErr := services.AIModel.FindOneAvailableAIModel(user, req.OrgID, req.ExtendedThinking)
	if aErr != nil {
		log.Error("[services.AIModel.FindOneAvailableAIModel failed]", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(model)
}

// @Summary		Create ai model
// @Description	Create ai model
//
// @Tags		Blog
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referer		header		string					true	"X-referer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Param		Input			body		dto.AIModelRequest	true	"Create blog input"
//
// @Success		201				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/ai/models [POST]
func CreateAIModel(c *gin.Context) {
	appG := app.Gin{C: c}

	data := dto.AIModelRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("CreateAIModel::Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	aiModel, aErr := services.AIModel.Create(&data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response201(aiModel)
}
