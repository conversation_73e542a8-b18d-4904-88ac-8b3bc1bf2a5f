package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// @Summary		Invite referee by email
// @Description	Invite referee by email
//
// @Tags			OE Referral
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.OEReferralInviteRefereeRequest	true	"input body"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/oe-referrals/invites [GET]
func OEReferralInviteReferee(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.OEReferralInviteRefereeRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	inErr := services.OEReferral(c.Request.Context()).InviteReferee(data)

	if inErr != nil {
		appG.ResponseAppError(inErr)
		return
	}

	appG.Response200("success")
}

// GetMyOEReferral
// @Summary		My OE referrals
// @Description	My OE referrals
//
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
//
// @Param			Input			query		models.OEReferralQuery	true	"input body"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/oe-referrals [GET]
func GetMyOEReferral(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.OEReferralQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	ctx := c.Request.Context()
	query.UserID = util.NewString(app.GetLoggedUser(ctx).ID)

	resp := dto.ListOEReferralResponse{
		Results:    []*models.OEReferral{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	referrals, pagination, listErr := services.OEReferral(c.Request.Context()).FindPage(&query, &options)
	if listErr != nil {
		appG.ResponseAppError(listErr)
		return
	}

	resp.Results = referrals
	resp.Pagination = pagination
	appG.Response200(resp)
}

// GetOERefWidgetStatistic
// @Summary		Get OE Referral Widget Statistic
// @Description	Get OE Referral Widget Statistic
//
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
//
// @Param		Filter			query		dto.OERefWidgetStatisticRequest		true	"Filter query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/oe-referrals/:id/statistics/widget [GET]
func GetOERefWidgetStatistic(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	campaignKey := c.Param(models.PathParamKeyID)

	var req dto.OERefWidgetStatisticRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	// TODO check permission

	resp, appErr := services.OEReferralReport(ctx).FindWidgetStatistic(campaignKey, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(resp)
}

// GetOERefLearnerGrowthStatistic
// @Summary		Get OE Referral Learner Growth Statistic
// @Description	Get OE Referral Learner Growth Statistic
//
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
//
// @Param		Filter			query		dto.OERefLearnerGrowthStatisticRequest		true	"Filter query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/oe-referrals/:id/statistics/learner-growth [GET]
func GetOERefLearnerGrowthStatistic(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	campaignKey := c.Param(models.PathParamKeyID)

	var req dto.OERefLearnerGrowthStatisticRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	// TODO check permission

	resp, appErr := services.OEReferralReport(ctx).FindLearnerGrowthStatistic(campaignKey, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(resp)
}

// GetOERefSectionCompletionStatistic
// @Summary		Get OE Referral Section Completion Statistic
// @Description	Get OE Referral Section Completion Statistic
//
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
//
// @Param		Filter			query		dto.OERefSectionCompletionRequest		true	"Filter query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/oe-referrals/:id/statistics/section-completion [GET]
func GetOERefSectionCompletionStatistic(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	campaignKey := c.Param(models.PathParamKeyID)

	var req dto.OERefSectionCompletionRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	// TODO check permission

	resp, appErr := services.OEReferralReport(ctx).FindSectionCompletionStatistic(campaignKey, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(resp)
}

// GetListOERefProvinces
// @Summary		Get List OE Referral Provinces
// @Description	Get List OE Referral Provinces
// openedu_oe_referral_codes
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
//
// @Param		Filter			query		dto.OERefLearnerGrowthStatisticRequest		true	"Filter query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/oe-referrals/:id/provinces [GET]
func GetListOERefProvinces(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response200(util.DefaultProvinceVN())
}

// GetOERefLearnerCountByProvincesStatistic
// @Summary		Get OE Referral Learner Count By Province Statistic
// @Description	Get OE Referral Learner Count By Province Statistic
//
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
//
// @Param		Filter			query		dto.OERefLearnerCountByProvinceRequest		true	"Filter query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/oe-referrals/:id/statistics/provinces [GET]
func GetOERefLearnerCountByProvincesStatistic(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	campaignKey := c.Param(models.PathParamKeyID)

	var req dto.OERefLearnerCountByProvinceRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	// TODO check permission

	resp, appErr := services.OEReferralReport(ctx).FindLearnerCountByProvinces(campaignKey, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(resp)
}

// GetOERefDetailsByProvinceStatistic
// @Summary		Get OE Referral Province Detail Statistic
// @Description	Get OE Referral Province Detail Statistic
//
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
//
// @Param		Filter			query		dto.OERefLearnerCountByProvinceRequest		true	"Filter query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/oe-referrals/:id/statistics/provinces/details [GET]
func GetOERefDetailsByProvinceStatistic(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	campaignKey := c.Param(models.PathParamKeyID)

	var req dto.OERefDetailsByProvinceStatsRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	// TODO check permission

	resp, appErr := services.OEReferralReport(ctx).FindDetailStatsByProvinces(campaignKey, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(resp)
}

// GetOERefLearnersByCampaign
// @Summary		Get OE Referral Province Learners
// @Description	Get OE Referral Province Learners
//
// @Tags		OE Referral
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
//
// @Param		Filter			query		dto.OERefLearnersByCampaignRequest		true	"Filter query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/oe-referrals/:id/learners [GET]
func GetOERefLearnersByCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	campaignKey := c.Param(models.PathParamKeyID)

	var req dto.OERefLearnersByCampaignRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	// TODO check permission

	resp, appErr := services.OEReferralReport(ctx).FindPageLearners(campaignKey, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(resp)
}
