package v1

import (
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CreateForm
//
//	@Summary		Create form
//	@Description	Create form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer	header		string					true	"X-referrer"	default(openedu101.com)
//
//	@Param			Input		body		dto.CreateFormRequest	true	"Create form input"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms [POST]
func CreateForm(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CreateFormRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	form, cErr := services.Form.Create(appG.GetOrg(), appG.GetLoggedUser(), &data)
	if cErr != nil {
		log.Error("Create form errors", cErr)
		appG.ResponseAppError(cErr)
		return
	}

	form, fErr := services.Form.FindById(form.ID)
	if fErr != nil {
		log.Error("Find form errors", fErr)
		appG.ResponseAppError(fErr)
		return
	}
	appG.Response201(form.Sanitize())
}

// FindPageForms
//
//	@Summary		Get list forms
//	@Description	Get list forms
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//
//	@Param			pagination	query		models.FindPageOptions	true	"Pagination query"
//
//	@Success		200			{object}	app.ResponseT[dto.ListFormResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms [GET]
func FindPageForms(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	var user models.User
	if !appG.RequiredLogin(&user) {
		return
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.FormQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	// Sysadmins, admin and moderators can see all forms
	// Other users can only see forms of that org
	if !user.IsSysAdmin() {
		query.OrgID = &org.ID
	}

	forms, pagination, appErr := services.Form.FindPage(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	resp := dto.ListFormResponse{
		Results:    lo.If(len(forms) == 0, []*models.Form{}).Else(forms),
		Pagination: pagination,
	}
	appG.Response200(resp)
}

// FindFormByIdOrSlug
//
//	@Summary		Find form by ID or slug
//	@Description	Find form by ID or slug
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id} [GET]
func FindFormByIdOrSlug(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(util.IDParamKey)

	var form *models.Form
	var appErr *e.AppError
	switch {
	case models.IsFormSlug(id):
		form, appErr = services.Form.FindBySlug(id)

	case lo.Some([]models.FormEvent{
		models.FormEventRegisterOrg,
		models.FormEventRegisterCreator,
		models.FormEventRegisterWriter,
		models.FormEventContactOrg,
		models.FormEventNewUser,
	}, []models.FormEvent{models.FormEvent(id)}):
		form, appErr = services.Form.FindOne(&models.FormQuery{
			Event: util.NewT(models.FormEvent(id)),
		}, models.FindOneFormOptsFullPreloads)

	default:
		form, appErr = services.Form.FindById(id)
	}

	if appErr != nil {
		log.Error("Find form errors", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if allowed := form.CanViewForm(appG.GetLoggedUser(), appG.GetOrg()); !allowed {
		appG.Response400(e.Form_view_need_permission, "You do not have permission to view this form")
		return
	}
	appG.Response200(form.Sanitize())
}

// FindFormRegisterOrg
//
//	@Summary		Find register organization form
//	@Description	Find register organization form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/register-organization [GET]
func FindFormRegisterOrg(c *gin.Context) {
	appG := app.Gin{C: c}
	form, appErr := services.Form.FindOne(&models.FormQuery{
		Event: util.NewT(models.FormEventRegisterOrg),
	}, models.FindOneFormOptsFullPreloads)
	if appErr != nil {
		log.Error("Find form errors", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if allowed := form.CanViewForm(appG.GetLoggedUser(), appG.GetOrg()); !allowed {
		appG.Response400(e.Form_view_need_permission, "You do not have permission to view this form")
		return
	}
	appG.Response200(form.Sanitize())
}

// FindFormRegisterCreator
//
//	@Summary		Find register organization form
//	@Description	Find register organization form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/register-creator [GET]
func FindFormRegisterCreator(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	form, appErr := services.Form.FindOne(&models.FormQuery{
		OrgID: util.NewString(org.ID),
		Event: util.NewT(models.FormEventRegisterCreator),
	}, models.FindOneFormOptsFullPreloads)
	if appErr != nil {
		log.Error("Find form errors", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if allowed := form.CanViewForm(appG.GetLoggedUser(), appG.GetOrg()); !allowed {
		appG.Response400(e.Form_view_need_permission, "You do not have permission to view this form")
		return
	}
	appG.Response200(form.Sanitize())
}

// FindFormRegisterWriter
//
//	@Summary		Find register writer form
//	@Description	Find register writer form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/register-writer [GET]
func FindFormRegisterWriter(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	form, appErr := services.Form.FindOne(&models.FormQuery{
		OrgID: util.NewString(org.ID),
		Event: util.NewT(models.FormEventRegisterCreator),
	}, models.FindOneFormOptsFullPreloads)
	if appErr != nil {
		log.Error("Find form errors", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if allowed := form.CanViewForm(appG.GetLoggedUser(), appG.GetOrg()); !allowed {
		appG.Response400(e.Form_view_need_permission, "You do not have permission to view this form")
		return
	}
	appG.Response200(form.Sanitize())
}

// SubmitForm
//
//	@Summary		Submit response to form
//	@Description	Submit response to form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Param			Input		body		dto.SubmitFormRequest	true	"Submit form input"
//
//	@Param			id			path		string					true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleFormSession]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id}/submit [POST]
func SubmitForm(c *gin.Context) {
	appG := app.Gin{C: c}
	formID := c.Param(util.IDParamKey)
	org := appG.GetOrg()

	source := c.Query(util.SourceQueryKey)
	refCode := c.Query(util.RefCodeQueryKey)
	refUser := c.Query(util.RefUserQueryKey)

	req := dto.SubmitFormRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	form, appErr := services.Form.FindById(formID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	if form.Event == models.FormEventRegisterOrg || form.Event == models.FormEventRegisterCreator {
		if handleErr := services.Form.HandleDuplicatedFormAnswer(form, &req); handleErr != nil {
			appG.ResponseAppError(handleErr)
			return
		}
	}

	if appErr = services.Form.CheckFormCanBeSubmitted(appG.GetOrg(), appG.GetLoggedUser(), form); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	formSession, appErr := services.Form.Submit(appG.GetLoggedUser(), form, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	formSession, appErr = services.FormSession.FindByID(formSession.ID, &models.FindOneOptions{
		Preloads: []string{"Answers.Question", "Answers.SubQuestion", "Answers.Option", "Answers.Files"},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if appErr = services.Form.HandleEventsAfterSubmit(form, formSession, org, source, refCode, refUser); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(formSession.ToSimple())
}

// UpdateFormStatus
//
//	@Summary		Update form status
//	@Description	Update form status
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string						true	"Origin"
//	@Param			X-referrer	header		string						true	"X-referrer"
//
//	@Param			Input		body		dto.UpdateFormStatusRequest	true	"Update form status input"
//
//	@Param			id			path		string						true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id}/status [PUT]
func UpdateFormStatus(c *gin.Context) {
	appG := app.Gin{C: c}
	formID := c.Param(util.IDParamKey)
	data := dto.UpdateFormStatusRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	form, fErr := services.Form.FindById(formID)
	if fErr != nil {
		log.Error("Find form errors", fErr)
		appG.ResponseAppError(fErr)
		return
	}

	if !form.CanUpdateForm(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Form_update_need_permission, "Need admin for update permission")
		return
	}

	newStatus := models.FormStatus(data.Status)
	switch newStatus {
	case models.FormStatusDraft,
		models.FormStatusPublishedAll:
		if form.Status == models.FormStatus(data.Status) {
			appG.Response200(form.Sanitize())
			return
		}
	default:
		appG.Response400(e.Form_invalid_status, "Invalid form status, only accept: "+models.FormStatusDraft+", "+models.FormStatusPublishedAll)
		return
	}

	form.Status = models.FormStatus(data.Status)
	if uErr := models.Repository.Form.Update(form, nil); uErr != nil {
		log.Error("Update form errors", uErr)
		appG.Response500(e.Form_update_failed, fmt.Sprintf("Update form status failed: %v", uErr))
		return
	}

	appG.Response200(form.Sanitize())
}

// UpdateForm
//
//	@Summary		Update form
//	@Description	Update form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Param			Input		body		dto.UpdateFormRequest	true	"Update form input"
//
//	@Param			id			path		string					true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id} [PUT]
func UpdateForm(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.UpdateFormRequest{}
	formID := c.Param(util.IDParamKey)
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	form, cErr := services.Form.FindById(formID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if !form.CanUpdateForm(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Form_update_need_permission, "Need permission for update")
		return
	}

	if _, uErr := services.Form.Update(form, &data); uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	form, fErr := services.Form.FindById(formID)
	if fErr != nil {
		log.Error("Find form errors", fErr)
		appG.ResponseAppError(fErr)
		return
	}
	appG.Response200(form.Sanitize())
}

// GetFormSummary
//
//	@Summary		Get form summary
//	@Description	Get form summary
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//
//	@Param			id			path		string	true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.FormSummary]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id}/summary [GET]
func GetFormSummary(c *gin.Context) {
	appG := app.Gin{C: c}
	formUID := c.Param(util.IDParamKey)
	form, cErr := services.Form.FindOne(&models.FormQuery{
		UID: &formUID,
	}, &models.FindOneOptions{
		Sort: []string{models.CreateAtDESC},
	})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if !form.CanViewSummary(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Form_summary_need_permission, "Need permission for view summary")
		return
	}

	formSummary, sErr := services.Form.Summary(form)
	if sErr != nil {
		appG.ResponseAppError(sErr)
		return
	}

	appG.Response200(formSummary)
}

// GetFormSessions
//
//	@Summary		Get form responses
//	@Description	Get form responses
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//
//	@Param			pagination	query		models.FindPageOptions	true	"Pagination query"
//	@Param			id			path		string					true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[dto.ListFormSessionResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id}/sessions [GET]
func GetFormSessions(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.FormSessionQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	org := appG.GetOrg()
	formID := c.Param(util.IDParamKey)

	var form *models.Form
	var appErr *e.AppError
	switch {
	case models.IsFormSlug(formID):
		form, appErr = services.Form.FindBySlug(formID)

	case lo.Some([]models.FormEvent{
		models.FormEventRegisterOrg,
		models.FormEventRegisterCreator,
		models.FormEventRegisterWriter,
		models.FormEventContactOrg,
	}, []models.FormEvent{models.FormEvent(formID)}):
		form, appErr = services.Form.FindOne(&models.FormQuery{
			OrgID: util.NewString(org.ID),
			Event: util.NewT(models.FormEvent(formID)),
		}, models.FindOneFormOptsFullPreloads)

	default:
		form, appErr = services.Form.FindById(formID)
	}

	if appErr != nil {
		log.Error("Find form errors", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if !form.CanViewSessions(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Form_summary_need_permission, "Need permission for view sessions")
		return
	}

	if c.Query("all_versions") == "true" {
		query.FormUID = &form.UID
	} else {
		query.FormID = &form.ID
	}

	options.Preloads = []string{models.AnswersField, models.AnswersQuestionField, models.AnswersQuestionOptionsField, models.UserField}
	formSessions, pagination, fErr := services.FormSession.FindPage(&query, &options)
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	resp := dto.ListFormSessionResponse{
		Results:    []*models.SimpleFormSession{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}
	resp.Results = lo.Map(formSessions, func(s *models.FormSession, _ int) *models.SimpleFormSession {
		return s.ToSimple()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// FindFormSessionByID
//
//	@Summary		Find form session by ID
//	@Description	Find form session by ID
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Form Session ID"
//
//	@Success		200			{object}	app.ResponseT[models.FormSession]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms-sessions/{id} [GET]
func FindFormSessionByID(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	formSessionID := c.Param(util.IDParamKey)
	formSession, appErr := services.FormSession.FindByID(formSessionID, &models.FindOneOptions{
		Preloads: []string{models.AnswersField},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if formSession.UserID != nil {
		var user models.User
		if !appG.RequiredLogin(&user) {
			return
		}

		if user.ID != *formSession.UserID && !user.IsSysAdmin() && !user.IsOrgAdmin(org.ID) && !user.IsCreator(org.ID) {
			appG.Response403(e.FORBIDDEN, "Need permission to view form response")
			return
		}
	}
	appG.Response200(formSession)
}

// RejectFormSession reject form session
//
//	@Summary		Reject form session
//	@Description	Reject form session
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Param			input		body		dto.RejectFormSession	true	"Reject form session request"
//
//	@Param			id			path		string					true	"Form Session ID"
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id}/sessions [GET]
func RejectFormSession(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.RejectFormSession
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	id := c.Param("id")
	session, err := services.FormSession.FindOne(&models.FormSessionQuery{ID: util.NewString(id)}, &models.FindOneOptions{Preloads: []string{"Answers"}})
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if session.Status == models.FormSessionsStatusApproved {
		appG.Response400(e.Form_session_approved, "form session already approved")
		return
	}

	if session.Status == models.FormSessionsStatusRejected {
		appG.Response400(e.Form_session_rejected, "form session already rejected")
		return
	}

	uErr := services.FormSession.UpdateStatus(appG.GetLoggedUser(), nil, appG.GetOrg(), session, models.FormSessionsStatusRejected, req.Reason)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}
	appG.Response200("success")
}

// DuplicateForm
//
//	@Summary		Duplicate form
//	@Description	Duplicate form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Param			id			path		string					true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[models.SimpleForm]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id}/duplicate [POST]
func DuplicateForm(c *gin.Context) {
	appG := app.Gin{C: c}
	formID := c.Param(util.IDParamKey)
	form, appErr := services.Form.FindById(formID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if !form.CanDuplicateForm(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Form_duplicate_need_permission, "Need permission for duplicate")
		return
	}

	newForm, appErr := services.Form.Duplicate(form, false)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	newForm, appErr = services.Form.FindById(newForm.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(newForm.Sanitize())
}

// DeleteForm
//
//	@Summary		Delete form
//	@Description	Delete form
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Param			id			path		string					true	"Form ID"
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/forms/{id}/duplicate [POST]
func DeleteForm(c *gin.Context) {
	appG := app.Gin{C: c}
	formID := c.Param(util.IDParamKey)
	form, appErr := services.Form.FindById(formID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if !form.CanDeleteForm(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Form_delete_need_permission, "Need permission for delete")
		return
	}

	if appErr = services.Form.Delete(form); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}
