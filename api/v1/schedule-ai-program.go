package v1

import (
	"context"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// CreateSchedule
//
//	@Summary		Create Schedule
//	@Description	Create Schedule
//
//	@Tags			Schedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string			true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer	header		string			true	"X-referrer"	default(openedu101.com)
//
//	@Param			Input		body		dto.ScheduleRequest	true	"Create Schedule input"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/schedules [POST]
func CreateSchedule(c *gin.Context) {
	appG := app.Gin{C: c}
	data := models.Schedule{}
	org := appG.GetOrg()
	if err := appG.BindAndValidateJSON(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}
	if org != nil {
		data.OrgID = org.ID
	}
	schedule, appErr := services.NewSchedule(context.Background()).Create(&data)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response201(schedule)
}

// FindScheduleDetail
//
//	@Summary		Find Schedule detail
//	@Description	Find Schedule detail
//
//	@Tags			Schedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Schedule ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/schedules [GET]
func FindSchedules(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindManyOptions
	if err := appG.C.ShouldBindQuery(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.ScheduleQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	schedules, cErr := services.NewSchedule(context.Background()).FindMany(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(schedules)
}

// UpdateSchedule
//
//	@Summary		Update Schedule
//	@Description	Update Schedule
//
//	@Tags			Schedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string			true	"Origin"
//	@Param			X-referrer	header		string			true	"X-referrer"
//
//	@Param			Input		body		dto.ScheduleRequest	true	"Update Schedule input"
//
//	@Param			id			path		string			true	"Schedule ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/Schedulezes/{id} [PUT]
func UpdateSchedule(c *gin.Context) {
	appG := app.Gin{C: c}
	scheduleRequest := models.Schedule{}

	scheduleID := c.Param(util.IDParamKey)

	if err := appG.BindAndValidateJSON(&scheduleRequest); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	schedule, cErr := services.NewSchedule(context.Background()).FindById(scheduleID, &models.FindOneOptions{})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	schedule.Name = scheduleRequest.Name
	schedule.Description = scheduleRequest.Description
	schedule.StartAt = scheduleRequest.StartAt
	schedule.EndAt = scheduleRequest.EndAt

	updateSchedule, uErr := services.NewSchedule(context.Background()).Update(schedule)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200(updateSchedule)
}

// DeleteSchedule
//
//	@Summary		Delete Schedule
//	@Description	Delete Schedule
//
//	@Tags			Schedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Schedule ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/schedules/{id} [DELETE]
func DeleteSchedule(c *gin.Context) {
	appG := app.Gin{C: c}
	scheduleID := c.Param(util.IDParamKey)

	if appErr := services.NewSchedule(context.Background()).Delete(scheduleID); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// CreateEventSchedule
//
//	@Summary		Create EventSchedule
//	@Description	Create EventSchedule
//
//	@Tags			EventSchedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string			true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer	header		string			true	"X-referrer"	default(openedu101.com)
//
//	@Param			Input		body		dto.EventScheduleRequest	true	"Create EventSchedule input"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/event-schedules [POST]
func CreateEventSchedule(c *gin.Context) {
	appG := app.Gin{C: c}
	data := models.EventSchedule{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	EventSchedule, appErr := services.NewEventSchedule(context.Background()).Create(&data)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response201(EventSchedule)
}

// FindEventScheduleDetail
//
//	@Summary		Find EventSchedule detail
//	@Description	Find EventSchedule detail
//
//	@Tags			EventSchedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"EventSchedule ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/event-schedules [GET]
func FindEventSchedule(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.EventScheduleQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	eventSchedules, pagination, cErr := services.NewEventSchedule(context.Background()).FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	result := dto.ListEventSchedule{
		Results:    eventSchedules,
		Pagination: pagination,
	}

	appG.Response200(result)
}

// UpdateEventSchedule
//
//	@Summary		Update EventSchedule
//	@Description	Update EventSchedule
//
//	@Tags			EventSchedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string			true	"Origin"
//	@Param			X-referrer	header		string			true	"X-referrer"
//
//	@Param			Input		body		dto.EventScheduleRequest	true	"Update EventSchedule input"
//
//	@Param			id			path		string			true	"EventSchedule ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/EventSchedulezes/{id} [PUT]
func UpdateEventSchedule(c *gin.Context) {
	appG := app.Gin{C: c}
	eventScheduleRequest := models.EventSchedule{}

	eventScheduleID := c.Param(util.IDParamKey)

	if err := appG.BindAndValidateJSON(&eventScheduleRequest); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	EventSchedule, cErr := services.NewEventSchedule(context.Background()).FindById(eventScheduleID, &models.FindOneOptions{})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	EventSchedule.Name = eventScheduleRequest.Name
	EventSchedule.Description = eventScheduleRequest.Description
	EventSchedule.StartAt = eventScheduleRequest.StartAt
	EventSchedule.EndAt = eventScheduleRequest.EndAt
	EventSchedule.Location = eventScheduleRequest.Location
	EventSchedule.JoinLink = eventScheduleRequest.JoinLink
	EventSchedule.EventType = eventScheduleRequest.EventType

	updateEventSchedule, uErr := services.NewEventSchedule(context.Background()).Update(EventSchedule)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200(updateEventSchedule)
}

// DeleteEventSchedule
//
//	@Summary		Delete EventSchedule
//	@Description	Delete EventSchedule
//
//	@Tags			EventSchedule
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"EventSchedule ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/EventSchedules/{id} [DELETE]
func DeleteEventSchedule(c *gin.Context) {
	appG := app.Gin{C: c}
	EventScheduleID := c.Param(util.IDParamKey)

	if appErr := services.NewEventSchedule(context.Background()).Delete(EventScheduleID); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}
