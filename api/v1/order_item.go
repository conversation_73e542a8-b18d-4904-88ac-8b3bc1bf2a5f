package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

//	@Summary		Find Page
//	@Description	Find Page
//
//	@Tags			order
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"	@Params	Input	query	models.FindPageOptions	false	"find page options"
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/order-items [GET]
func FindPageOrderItem(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	query := models.OrderItemQuery{
		IncludeDeleted: util.NewBool(false),
		NotStatus:      util.NewT(models.OrderStatusNew),
		UserID:         &currentUser.ID,
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListOrderItemsResponse{
		Results:    []*models.SimpleOrderItem{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	orders, pagination, err := services.OrderItem.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map[*models.OrderItem, *models.SimpleOrderItem](orders, func(o *models.OrderItem, _ int) *models.SimpleOrderItem { return o.Sanitize() })
	resp.Pagination = pagination
	appG.Response200(resp)
}

//	@Summary		Find Page
//	@Description	Find Page
//
//	@Tags			orderItem
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//	@Params			Input query 																																																																																																models.FindPageOptions 			 false  	"find page options"
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/admin/order-items [GET]
func FindPageOrderItemByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	query := models.OrderItemQuery{
		IncludeDeleted: util.NewBool(false),
		NotStatus:      util.NewT(models.OrderStatusNew),
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListOrderItemsResponse{
		Results:    []*models.SimpleOrderItem{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	orderItems, pagination, err := services.OrderItem.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map[*models.OrderItem, *models.SimpleOrderItem](orderItems, func(o *models.OrderItem, _ int) *models.SimpleOrderItem { return o.Sanitize() })
	resp.Pagination = pagination
	appG.Response200(resp)

}
