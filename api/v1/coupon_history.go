package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		FindPage Coupon History
// @Description	FindPage CouponHistory
//
// @Tags			CouponHistory
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string	true	"Origin"
// @Param			X-referer		header					string	true	"X-referer"
// @Param			Authorization	header					string	true	"Bearer"
//
// @Params			Input query 																																																																																																																																																																											models.FindPageOptions 		 false  	"find page options"
// @Params			Input query 	models.FindPageOptions 																																	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
// @Router			/api/v1/admin/coupon-histories [GET]
func FindPageCouponHistoryByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	// Check role
	if !currentUser.CanActCoupon(org.ID) {
		appG.Response403(e.FORBIDDEN, "Need permission for find coupon")
	}

	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CouponHistoryQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	query.IncludeDeleted = util.NewBool(false)
	if currentUser.IsOrgAdmin(org.ID) {
		query.OrgIDIn = []string{org.ID, ""}
	}

	resp := dto.ListCouponHistoryResponse{
		Results:    []*models.SimpleCouponHistory{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	Coupons, pagination, err := services.CouponHistory.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(Coupons, func(c *models.CouponHistory, _ int) *models.SimpleCouponHistory {
		return c.ToSimple()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}
