package v1

import (
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"strings"
	"time"

	"github.com/samber/lo"

	"github.com/gin-gonic/gin"
)

// CreateLaunchpad
//
// @Summary		Create a launchpad
// @Description	Create a launchpad
//
// @Tags     	launchpad
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string							true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string							true	"X-referrer"	default(openedu101dev.com)
// @Param		Input		body		dto.CreateClpLaunchpadRequest	true	"Create launchpad input"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/launchpads [POST]
func CreateLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	data := dto.CreateClpLaunchpadRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("CreateLaunchpad::Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if validateErr := e.HandleValidationError(data); validateErr != nil {
		appG.Response400(e.INVALID_PARAMS, validateErr)
		return
	}

	launchpad, aErr := services.ClpLaunchpad.Create(org, user, &data)
	if aErr != nil {
		log.Error("create failed", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	newLaunchpad, aErr := services.ClpLaunchpad.FindByID(launchpad.ID)
	if aErr != nil {
		log.Error("find launchpad by id failed", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response201(newLaunchpad)
}

// FindPageLaunchpads
//
// @Summary		Find Page Launchpad
// @Description	Find Page Launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referrer		header		string					true	"X-referrer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Params		options 		query 		models.FindPageOptions 	false  	"Find page options"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/launchpads [GET]
func FindPageLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.LaunchpadQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	resp := dto.ListClpLaunchpadResponse{
		Results:    []*models.ClpLaunchpad{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	launchpads, pagination, err := services.ClpLaunchpad.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if user := appG.GetLoggedUser(); user != nil {
		if aErr := services.ClpLaunchpad.AssignUserStatusToLaunchpads(user, launchpads); aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}
	}

	resp.Results = launchpads
	resp.Pagination = pagination
	appG.Response200(resp)
}

// GetLaunchpadByID
//
// @Summary		Get launchpad By ID
// @Description	Get launchpad By ID
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101dev.com)
//
// @Param		id			path		string		true	"Launchpad ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/launchpads/{id} [GET]
func GetLaunchpadByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	var options models.FindOneOptions
	if bErr := appG.C.ShouldBindQuery(&options); bErr != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+bErr.Error())
		return
	}

	launchpad, aErr := services.ClpLaunchpad.GetLaunchpadDetail(id, &options)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	if user := appG.GetLoggedUser(); user != nil {
		if aErr = services.ClpLaunchpad.AssignUserStatusToLaunchpads(user, []*models.ClpLaunchpad{launchpad}); aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}

		if aErr = services.ClpLaunchpad.AssignPledgeStatusToLaunchpad(user, launchpad); aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}

		if launchpad.IsStatusVoting() {
			if aErr = services.ClpLaunchpad.PreloadVotingProcessLaunchpad(user, launchpad); aErr != nil {
				appG.ResponseAppError(aErr)
				return
			}
		}
	}

	appG.Response200(launchpad.Sanitize())
}

// UpdateLaunchpad
// @Summary		Update launchpad
// @Description	Update launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referer		header		string		true	"X-referer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"Launchpad ID"
//
// @Param		Input			body		dto.UpdateClpLaunchpadRequest	true	"Update launchpad input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/launchpads/{id} [PATCH]
func UpdateLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	data := dto.UpdateClpLaunchpadRequest{}
	id := c.Param("id")
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	cErr = services.ClpLaunchpad.CanUpdate(launchpad, user)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if launchpad.Status != models.LaunchpadSTTDraft {
		appG.Response400(e.Update_launchpad_failed, "Launchpad status is: "+launchpad.Status)
		return
	}

	if data.CourseCuid != nil {
		course, cErr := services.ClpLaunchpad.CourseCanMakeLaunchpad(user, *data.CourseCuid, &launchpad.ID)
		if cErr != nil {
			appG.ResponseAppError(cErr)
			return
		}

		// TODO move into services.ClpLaunchpad.Update
		_, cErr = services.ClpCourseLaunchpad.Create(course, launchpad)
		if cErr != nil {
			appG.ResponseAppError(cErr)
			return
		}
	}

	_, cErr = services.ClpLaunchpad.Update(launchpad, &data)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	launchpadUpdated, cErr := services.ClpLaunchpad.FindOne(&models.LaunchpadQuery{
		ID: &launchpad.ID,
	}, &models.FindOneOptions{
		Preloads: []string{
			models.CategoriesField,
			models.LevelsField,
			models.OwnerField,
			models.CoursesField,
			models.VotingMilestonesField,
		},
	})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(launchpadUpdated.Sanitize())
}

// @Summary		Delete launchpad
// @Description	Delete launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"launchpad id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/launchpads/{id} [DELETE]
func DeleteLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")

	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	cErr = services.ClpLaunchpad.CanUpdate(launchpad, user)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if launchpad.Status != models.LaunchpadSTTDraft {
		appG.Response400(e.Delete_launchpad_failed, "Delete launchpad failed")
		return
	}

	cErr = services.ClpLaunchpad.Delete(launchpad)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Request to publish launchpad
// @Description	Request to publish launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"launchpad id"
// @Param		Input			body		dto.PublishLaunchpadRequest	true	"Approve approval input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/launchpads/{id}/publish [POST]
func PublishLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")

	req := dto.PublishLaunchpadRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if req.Status != models.LaunchpadSTTPublish {
		appG.Response400(e.INVALID_PARAMS, "publish launchpad")
		return
	}

	launchpad, appErr := services.ClpLaunchpad.FindByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appErr = services.ClpLaunchpad.CanUpdate(launchpad, user)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if launchpad.Status != models.LaunchpadSTTDraft {
		appG.Response400(e.INVALID_PARAMS, "Launchpad can not publish")
		return
	}

	req.Launchpad = launchpad
	req.Org = appG.GetOrg()
	req.Requester = user
	approval, appErr := services.ClpLaunchpad.RequestPublishLaunchpad(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	go func() {
		services.ClpLaunchpad.PushNotificationsForReqPublish(launchpad, approval, user)
	}()

	appG.Response200("success")
}

// @Summary		Get launchpad partners
// @Description	Get launchpad partners
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referer		header		string	true	"X-referer"
// @Param		Authorization	header		string	true	"Bearer"
//
// @Param		id				path		string	true	"launchpad id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/launchpads/{id}/partners [GET]
func GetLaunchpadPartners(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	id := c.Param("id")
	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	launchpadPartner, cErr := services.ClpLaunchpad.FindLaunchpadPartner(launchpad, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(launchpadPartner)
}

// BookmarkLaunchpad
//
// @Summary		Bookmark a launchpad
// @Description	Bookmark a launchpad
//
// @Tags     	launchpad
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string						true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string						true	"X-referrer"	default(openedu101dev.com)
//
// @Success		200			{object}	app.ResponseT[string]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/launchpads/{id}/bookmark [POST]
func BookmarkLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	launchpadID := c.Param(models.PathParamKeyID)
	user := appG.GetLoggedUser()

	launchpad, aErr := services.ClpLaunchpad.FindByID(launchpadID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	existingBookmark, aErr := services.Bookmark.FindOne(&models.BookmarkQuery{
		UserID:     &user.ID,
		EntityID:   &launchpad.ID,
		EntityType: util.NewT(models.ClpLaunchpadModelName),
	}, &models.FindOneOptions{})
	if aErr != nil && aErr.ErrCode != e.Bookmark_not_found {
		appG.ResponseAppError(aErr)
		return
	}

	if existingBookmark != nil {
		appG.Response200("success")
		return
	}

	if _, aErr = services.Bookmark.Create(&dto.BookmarkRequest{
		UserID:     user.ID,
		EntityID:   launchpad.ID,
		EntityType: models.ClpLaunchpadModelName,
	}); aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}

// RemoveBookmarkLaunchpad
//
// @Summary		Remove bookmark a launchpad
// @Description	Remove bookmark a launchpad
//
// @Tags     	launchpad
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string						true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string						true	"X-referrer"	default(openedu101dev.com)
//
// @Success		200			{object}	app.ResponseT[string]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/launchpads/{id}/bookmark [DELETE]
func RemoveBookmarkLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	launchpadID := c.Param(models.PathParamKeyID)
	user := appG.GetLoggedUser()

	launchpad, aErr := services.ClpLaunchpad.FindByID(launchpadID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	existingBookmark, aErr := services.Bookmark.FindOne(&models.BookmarkQuery{
		UserID:     &user.ID,
		EntityID:   &launchpad.ID,
		EntityType: util.NewT(models.ClpLaunchpadModelName),
	}, &models.FindOneOptions{})
	if aErr != nil && aErr.ErrCode != e.Bookmark_not_found {
		appG.ResponseAppError(aErr)
		return
	}

	if existingBookmark == nil {
		appG.Response200("success")
		return
	}

	if aErr = services.Bookmark.Delete(existingBookmark); aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Cancel publish launchpad request
// @Description	Cancel publish launchpad request
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referrer		header		string	true	"X-referrer"
// @Param		Authorization	header		string	true	"Bearer"
//
// @Param		id				path		string	true	"launchpad id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/launchpads/{id}/publish [PUT]
func CancelPublishLaunchpadRequest(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")

	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	cErr = services.ClpLaunchpad.CanUpdate(launchpad, user)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if cErr := services.ClpLaunchpad.CancelRequest(launchpad); cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200("success")
}

// StartVotingMilestoneLaunchpad
// @Summary		Open the voting milestone launchpad
// @Description	Open the voting milestone launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referrer		header		string	true	"X-referrer"
// @Param		Authorization	header		string	true	"Bearer"
//
// @Param		id				path		string	true	"Launchpad ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/launchpads/{id}/milestones/{milestone_id}/start [POST]
func StartVotingMilestoneLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	launchpadID := c.Param(models.PathParamKeyID)
	milestoneID := c.Param(models.PathParamKeyMilestoneID)

	launchpad, aErr := services.ClpLaunchpad.FindByID(launchpadID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	milestone, found := lo.Find(launchpad.VotingMilestones, func(milestone *models.ClpVotingMilestone) bool {
		return milestone.ID == milestoneID
	})
	if !found {
		appG.Response400(e.Voting_milestone_not_found, "Voting milestone ID "+milestoneID+" not found")
	}

	req := &dto.OpenVotingMilestoneRequest{
		User:            user,
		Launchpad:       launchpad,
		VotingMilestone: milestone,
	}
	if aErr = services.ClpLaunchpad.StartVoting(req); aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(milestone)
}

// InitLaunchpadPool
// @Summary		Open the voting milestone launchpad
// @Description	Open the voting milestone launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referrer		header		string	true	"X-referrer"
// @Param		Authorization	header		string	true	"Bearer"
//
// @Param		id				path		string	true	"Launchpad ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/launchpads/{id}/pools [POST]
func InitLaunchpadPool(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	user := appG.GetLoggedUser()
	id := c.Param(models.PathParamKeyID)

	var req dto.InitLaunchpadPoolRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	launchpad, aErr := services.ClpLaunchpad.FindByID(id)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	wallet, aErr := services.Wallet.FindOne(&models.WalletQuery{
		ID: &req.WalletID,
	}, &models.FindOneOptions{})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	if !lo.Contains(models.ValidInitLaunchpadPoolCurrencies, wallet.Currency) {
		appG.Response400(e.INVALID_PARAMS, "Init launchpad pool only accepted: "+
			strings.Join(lo.Map(models.ValidInitLaunchpadPoolCurrencies, func(item models.Currency, _ int) string {
				return string(item)
			}), ","))
		return
	}

	req.Wallet = wallet
	req.User = user
	req.Org = org
	req.Launchpad = launchpad
	transaction, aErr := services.Transaction.InitLaunchpadPool(&req)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(transaction)
}

// SetLaunchFundingTime
// @Summary		Set funding time for the launchpad
// @Description	Set funding time for the launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"launchpad id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/launchpads/{id}/funding-time [POST]
func SetLaunchFundingTime(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param(models.PathParamKeyID)

	var req dto.SetFundingTimeLaunchpadRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	if req.FundingStartDate <= time.Now().UnixMilli() {
		appG.Response400(e.INVALID_PARAMS, "Start time must be in the future")
		return
	}

	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	cErr = services.ClpLaunchpad.CanUpdate(launchpad, user)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if launchpad.HasFundingTimePeriod() {
		appG.Response400(e.Launchpad_funding_time_already_configured,
			fmt.Sprintf("Launchpad funding time already configured: %v, %v", launchpad.FundingStartDate, launchpad.FundingEndDate))
		return
	}

	req.Launchpad = launchpad
	launchpad, cErr = services.ClpLaunchpad.SetFundingTime(&req)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(launchpad)
}

// ClaimLaunchpadRefund
// @Summary		Claim launchpad refund
// @Description	Claim launchpad refund
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"launchpad id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/launchpads/{id}/claim-refund [POST]
func ClaimLaunchpadRefund(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param(models.PathParamKeyID)

	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	req := dto.ClaimLaunchpadRefundRequest{
		User:      user,
		Launchpad: launchpad,
	}
	transaction, cErr := services.Transaction.ClaimLaunchpadRefund(&req)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(transaction)
}

// CancelLaunchpad
// @Summary		Cancel the launchpad
// @Description	Cancel the launchpad
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"launchpad id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/launchpads/{id}/cancel [POST]
func CancelLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param(models.PathParamKeyID)
	launchpad, appErr := services.ClpLaunchpad.FindByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	if appErr = services.ClpLaunchpad.CanUpdate(launchpad, user); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appErr = services.ClpLaunchpad.Cancel(&dto.CancelLaunchpadRequest{
		Launchpad: launchpad,
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200("success")
}

// CreatorDecideContinueVoting
// @Summary		Claim launchpad refund
// @Description	Claim launchpad refund
//
// @Tags		launchpad
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"launchpad id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/launchpads/{id}/decide-voting [POST]
func CreatorDecideContinueVoting(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param(models.PathParamKeyID)

	var req dto.DecideContinueVotingRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	launchpad, cErr := services.ClpLaunchpad.FindByID(id)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if launchpad.UserID != user.ID {
		appG.Response403(e.Not_launchpad_owner, "must owner can decide launchpad")
		return
	}

	cErr = services.ClpLaunchpad.DecideContinueVotingLaunchpad(launchpad, &req)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if req.IsContinued {
		milestone, found := lo.Find(launchpad.VotingMilestones, func(milestone *models.ClpVotingMilestone) bool {
			return milestone.Order == 1
		})
		if !found {
			appG.Response400(e.Voting_milestone_not_found, "Voting milestone not found")
			return
		}

		milestoneReq := &dto.OpenVotingMilestoneRequest{
			User:            user,
			Launchpad:       launchpad,
			VotingMilestone: milestone,
		}
		if aErr := services.ClpLaunchpad.StartVoting(milestoneReq); aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}
	} else {
		go func() {
			services.ClpVotingMilestone.PushNotifForBackerWhenCreatorEndLaunchpad(launchpad)
		}()
	}

	appG.Response200("success")
}
