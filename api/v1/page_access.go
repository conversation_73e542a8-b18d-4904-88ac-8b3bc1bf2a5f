package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

func CreateUpdatePageAccess(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	data := dto.BulkCreatePageAccessRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	validateError := e.HandleValidationError(data)
	if len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return
	}

	if !user.IsSysAdmin() && !user.IsOrgAdmin(appG.GetOrg().ID) {
		appG.Response400(e.Organization_owner_required, "sysadmin or org owner required")
		return
	}

	pageAccess, failed, aErr := services.PageAccess.CreateOrUpdate(&data, appG.GetOrg())
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(map[string]any{"success": pageAccess, "failed": failed})
}

func FindPagePageAccess(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	var query = models.PageAccessQuery{}

	if !user.IsSysAdmin() && user.IsOrgAdmin(appG.GetOrg().ID) {
		appG.Response400(e.Organization_owner_required, "sysadmin or org owner required")
		return
	}

	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	//if !user.IsOpeneduAdmin(appG.GetOrg().ID) {
	//	query.OrgID = util.NewString(appG.GetOrg().ID)
	//}

	resp := dto.ListPageAccessResponse{
		Results:    []*models.PageAccess{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	pageAccess, pagination, err := services.PageAccess.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = pageAccess
	resp.Pagination = pagination
	appG.Response200(resp)
}

func GetMyPermission(c *gin.Context) {
	appG := app.Gin{C: c}
	orgID := appG.GetOrg().ID
	if setting.IsAdminSite(appG.GetDomain().Domain) {
		orgID = ""
	}
	pageAccesses, aErr := services.PageAccess.GetUserPermission(appG.GetLoggedUser(), orgID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}
	result := lo.Map(pageAccesses, func(item *models.PageAccess, _ int) *models.SimplePageAccess {
		return item.Sanitize()
	})

	appG.Response200(result)
}
