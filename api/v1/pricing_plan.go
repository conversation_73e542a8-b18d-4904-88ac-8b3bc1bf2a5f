package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Create Pricing Plan
// @Description	Create Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.PricingPlanRequest	true	"create pricing plan"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans [POST]
func CreatePricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	data := dto.PricingPlanRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	data.User = currentUser
	plan, aErr := services.PricingPlan.Create(&data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(plan)
}

// @Summary		Update Pricing Plan
// @Description	Update Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"plan_id"
//
// @Param			Input			body		dto.PricingPlanRequest	true	"Update pricing plan input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans/{id} [PUT]
func UpdatePricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	data := dto.PricingPlanRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	plan, err := services.PricingPlan.FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	updated, aErr := services.PricingPlan.Update(plan, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(updated)
}

// @Summary		Get Pricing Plan
// @Description	Get Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"plan id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans/{id} [GET]
func GetPricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	var options models.FindOneOptions
	if err := appG.C.BindQuery(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind FindOneOptions failed: "+err.Error())
		return
	}
	plan, err := services.PricingPlan.FindById(id, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(plan)
}

// @Summary		Find Pricing Plan
// @Description	Find Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Query			query		models.PricingPlanQuery	true	"Find pricing plan"
//
//	@Param			pagination	query		models.FindPageOptions	true	"Pagination query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans [GET]
func FindPricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.PricingPlanQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListPricingPlanResponse{
		Results:    []*models.PricingPlan{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	pricingPlans, pagination, err := services.PricingPlan.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = pricingPlans
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Get All Pricing Plan
// @Description	Get all Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
//	@Param			pagination	query		models.FindPageOptions	true	"Pagination query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans/all [GET]
func FindAllPricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindManyOptions
	if err := appG.C.BindQuery(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind FindManyOptions failed: "+err.Error())
		return
	}

	var query models.PricingPlanQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	query.Enable = util.NewBool(true)
	pricingPlans, err := services.PricingPlan.FindMany(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(pricingPlans)
}

// @Summary		Delete Pricing Plan
// @Description	Delete Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"plan id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans/:id [DELETE]
func DeletePricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	plan, err := services.PricingPlan.FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	aErr := services.PricingPlan.Delete(plan.ID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Enable Pricing Plan
// @Description	Enable Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"plan id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans/:id/enable [POST]
func EnablePricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	plan, err := services.PricingPlan.FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	aErr := services.PricingPlan.EnablePlan(plan, true)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Disable Pricing Plan
// @Description	Disable Pricing Plan
//
// @Tags			pricingPlan
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"plan id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans/:id/disable [POST]
func DisablePricingPlan(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	plan, err := services.PricingPlan.FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	aErr := services.PricingPlan.EnablePlan(plan, false)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Subscribe Pricing Plan
// @Description	Subscribe Pricing Plan
//
// @Tags		pricingPlan
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"plan id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/plans/:id/subscribes [POST]
func SubscribePlan(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	org := appG.GetOrg()
	user := appG.GetLoggedUser()

	data := dto.UserSubscribePlanRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	plan, err := services.PricingPlan.FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	subscriptionExisted, sErr := services.Subscription.FindOneOptions(&models.SubscriptionQuery{
		UserID: util.NewString(user.ID),
		PlanID: util.NewString(plan.ID),
		Enable: util.NewBool(true),
		Status: util.NewT(models.SubscriptionStatusActive),
	}, nil)
	if sErr != nil {
		appG.ResponseAppError(err)
		return
	}

	if subscriptionExisted != nil {
		appG.Response200("success")
		return
	}

	_, aErr := services.Subscription.Subscribe(&dto.SubscribePlanRequest{
		User:        user,
		Plan:        plan,
		Event:       data.Event,
		EnableEmail: data.EnableEmail,
		Org:         org,
	})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}
