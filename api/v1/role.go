package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

func CreateRole(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	data := dto.RoleRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if !user.IsSysAdmin() && !user.IsOrgAdmin(appG.GetOrg().ID) {
		appG.Response400(e.Organization_owner_required, "sysadmin or org owner required")
		return
	}

	if !user.IsSysAdmin() {
		data.OrgId = appG.GetOrg().ID
	}

	role, aErr := services.Role.Create(&data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(role)
}

func UpdateRole(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	data := dto.RoleRequest{}
	id := c.Param("id")

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	if !user.IsSysAdmin() && !user.IsOrgAdmin(appG.GetOrg().ID) {
		appG.Response400(e.Organization_owner_required, "sysadmin or org owner required")
		return
	}

	role, oErr := services.Role.FindByID(id)
	if oErr != nil {
		log.Error("find role by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if user.IsOrgAdmin(appG.GetOrg().ID) && role.OrgId != appG.GetOrg().ID {
		appG.Response400(e.Organization_owner_required, "role owner required")
		return
	}

	role.Description = data.Description
	aErr := services.Role.Update(role)
	if aErr != nil {
		appG.Response500(e.Update_custom_role_failed, aErr)
		return
	}

	appG.Response200(role)
}

func DeleteRole(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	roleId := c.Param("id")

	if !user.IsSysAdmin() && !user.IsOrgAdmin(appG.GetOrg().ID) {
		appG.Response400(e.Organization_owner_required, "sysadmin or org owner required")
		return
	}

	role, oErr := services.Role.FindByID(roleId)
	if oErr != nil {
		log.Error("find role by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if user.IsOrgAdmin(appG.GetOrg().ID) && role.OrgId != appG.GetOrg().ID {
		appG.Response400(e.Organization_owner_required, "role owner required")
		return
	}

	aErr := services.Role.Delete(role, nil)
	if aErr != nil {
		appG.Response500(e.Delete_custom_role_failed, aErr)
		return
	}
	appG.Response200(nil)
}

func FindPageRole(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	query := models.RoleQuery{}
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}
	query.AllAndOrgID = util.NewString(appG.GetOrg().ID)

	resp := dto.ListRoleResponse{
		Results:    []*models.Role{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	roles, pagination, err := services.Role.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = roles
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		FindAllRoles
// @Description	FindAllRoles
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/internal-v1/roles [GET]
func FindAllRoles(c *gin.Context) {
	appG := app.Gin{C: c}

	roles, qErr := services.Role.FindAll()
	if qErr != nil {
		appG.ResponseAppError(qErr)
		return
	}

	appG.Response200(roles)
}
