package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		enrollment course
// @Description	enrollment course
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Success		201				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/:id/enroll [POST]
func EnrollCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	courseCuid := c.Param(util.IDParamKey)
	org := appG.GetOrg()
	source := c.Query(util.SourceQueryKey)
	refCode := c.Query(util.RefCodeQueryKey)
	refUser := c.Query(util.RefUserQueryKey)

	publishCourse, cErr := services.PublishCourse.FindOne(&models.PublishCourseQuery{CourseCuid: &courseCuid, OrgID: &org.ID}, &models.FindOneOptions{})
	if cErr != nil {
		appG.Response400(cErr.ErrCode, cErr.Msg)
		return
	}
	var course *models.Course
	course, cErr = services.Course.FindById(publishCourse.CourseID, false, nil)
	if cErr != nil {
		appG.Response400(cErr.ErrCode, cErr.Msg)
		return
	}

	courseEnrollment, cErr := services.CourseEnrollment.VerifyCreateCourseEnrollment(publishCourse.OrgID, publishCourse.OrgSchema, currentUser, course, source, refCode)

	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if refUser != "" && refUser != currentUser.ID {
		trackData := communicationdto.TrackingRequest{
			ActorID:      currentUser.ID,
			Verb:         communicationdto.Referred,
			Object:       communicationdto.UserModelName,
			ObjectID:     refUser,
			Context:      communicationdto.CourseContext,
			ContextValue: course.Cuid,
			OrgID:        org.ID,
			OrgSchema:    org.Schema,
			IsValid:      false,
		}

		if err := communication.Tracking.CreateTracking(&trackData); err != nil {
			appG.ResponseAppError(e.NewError500(e.External_call_error, "Failed to ref for user"))
			return
		}
	}

	log.Debugf("CourseInfo: %#v", course)
	log.Debugf("CourseProps: %#v", course.Props)

	//Send Email
	if course.Props.CourseEnrollmentEmailTemplateCode != "" {
		req := &communicationdto.SendEmailRequest{
			User: currentUser.IntoComm(),
			Org:  org.IntoComm(),
			Code: util.NewT(communicationdto.EmailCodeType((course.Props.CourseEnrollmentEmailTemplateCode))),
			ExtendDatas: communicationdto.MapEmailParams{
				"course_name": course.Name,
				"course_slug": course.Slug,
			},
			From:    org.Settings.SenderEmail,
			IsQueue: false,
		}
		if _, err := communication.Email.SendEmail(req); err != nil {
			log.ErrorWithAlertf("CourseEnrollment::Send email failed: %v", err)
		}
	}

	//TODO: push notification when enroll success
	req := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeCourseEnrollmentSuccess,
		EntityID:   courseCuid,
		EntityType: communicationdto.CourseEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{currentUser.ID},
			},
		},
		Props: communicationdto.JSONB(services.MakeNotificationPropsForCourse(course, org, currentUser)),
	}
	if err := communication.Notification.PushNotification(req); err != nil {

		log.Errorf("Push notification after course enrollments error: %v", err)
	}

	appG.Response201(courseEnrollment.Sanitize())
}

//	@Summary		Find course enrollment by user
//	@Description	Find course enrollment by user
//
//	@Tags			user
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header	string	true	"Origin"
//	@Param			X-referer		header	string	true	"X-referer"
//	@Param			Authorization	header	string	true	"Bearer"
//
//	@Params			Input							query 						models.FindPageOptions 		 false  	"find page options"

// @Success	200	{object}	app.Response
// @Failure	400	{object}	app.Response
// @Failure	500	{object}	app.Response
// @Router		/api/v1/users/:id/enrollments [GET]
func GetUserEnrollments(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	userID := c.Param("id")

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CourseEnrollmentQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if !currentUser.IsSysAdmin() && !currentUser.IsOrgAdmin(org.ID) {
		if userID != currentUser.ID {
			appG.Response403(e.FORBIDDEN, "You are not allowed to view this page")
			return
		}
	}

	query.UserID = &userID
	options.Preloads = append(options.Preloads, "Course")

	resp := dto.ListCourseEnrollmentResponse{
		Results:    []*models.SimpleCourseEnrollment{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	courseEnrollments, pagination, cErr := services.CourseEnrollment.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = lo.Map(courseEnrollments, func(c *models.CourseEnrollment, _ int) *models.SimpleCourseEnrollment { return c.Sanitize() })
	resp.Pagination = pagination

	appG.Response200(resp)
}

//	@Summary		Find course enrollment by course
//	@Description	Find course enrollment by course
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header	string	true	"Origin"
//	@Param			X-referer		header	string	true	"X-referer"
//	@Param			Authorization	header	string	true	"Bearer"
//
//	@Params			Input							query 						models.FindPageOptions 		 false  	"find page options"

// @Success	200	{object}	app.Response
// @Failure	400	{object}	app.Response
// @Failure	500	{object}	app.Response
// @Router		/api/v1/courses/:id/enrollments [GET]
func GetCourseEnrollments(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	courseCuid := c.Param("id")
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CourseEnrollmentQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	// Check and make sure user is allowed to view this page: sysadmin, orgadmin, creator
	if err := services.CourseEnrollment.CanActionCourseEnrollment(courseCuid, currentUser, org); err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp := dto.ListCourseEnrollmentResponse{
		Results:    []*models.SimpleCourseEnrollment{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	query.CourseCuid = &courseCuid
	options.Preloads = append(options.Preloads, models.UserField, models.CourseField, models.LearningProgressOverviewField)
	courseEnrollments, pagination, cErr := services.CourseEnrollment.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = lo.Map(courseEnrollments, func(c *models.CourseEnrollment, _ int) *models.SimpleCourseEnrollment { return c.Sanitize() })
	resp.Pagination = pagination

	appG.Response200(resp)
}

// @Summary		Update course enrollment
// @Description	Update course enrollment
//
// @Tags			CourseEnrollment
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string								true	"Origin"
// @Param			X-referer		header		string								true	"X-referer"
// @Param			Authorization	header		string								true	"Bearer"
//
// @Param			id				path		string								true	"course enrollment id"
//
// @Param			Input			body		dto.UpdateCourseEnrollmentRequest	true	"Update course enrollment input"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/enrollments/:id [PUT]
func UpdateCourseEnrollment(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	id := c.Param("id")
	data := dto.UpdateCourseEnrollmentRequest{}

	courseEnrollment, cErr := services.CourseEnrollment.FindByID(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if err := services.CourseEnrollment.CanActionCourseEnrollment(courseEnrollment.CourseCuid, currentUser, org); err != nil {
		appG.ResponseAppError(err)
		return
	}

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	valErr := e.HandleValidationError(data)
	if len(valErr) > 0 {
		appG.Response400(e.INVALID_PARAMS, valErr)
		return
	}

	if updateErr := services.CourseEnrollment.Update(courseEnrollment, &data); updateErr != nil {
		appG.ResponseAppError(updateErr)
		return
	}

	appG.Response200(courseEnrollment.Sanitize())
}
