package v1

import (
	"openedu-core/dto"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Bunny webhook
// @Description	Bunny webhook
// @Tags			webhook
// @Accept			json
// @Produce		json
//
// @Success		200				{object}	app.Response
// @Failure		404				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/webhook/bunny [POST]
func BunnyVideoWebhook(c *gin.Context) {
	appG := app.Gin{C: c}

	var reqBody dto.BunnyVideoWebhookRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Webhook.BunnyVideoWebhook Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		log.Error("Api::Webhook.BunnyVideoWebhook validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := services.Webhook.HandleBunnyVideoWebhook(&reqBody); err != nil {
		log.Error("[services.Webhook.HandleBunnyVideoWebhook]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(e.MsgFlags[e.SUCCESS])
}
