package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Get My Point History
// @Description	Get My Point History
//
// @Tags			Point History
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/points/:id [GET]
func GetMyPoint(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	campaign, cErr := services.OEPointCampaign(c.Request.Context()).FindById(id, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	response, err := services.OEPointHistory(c.Request.Context()).GetReferralProgramPoints(campaign)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200(response)
}

// @Summary		User claim new point
// @Description	User claim new point
//
// @Tags			Point History
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.UserClaimNewPointRequest	true	"input body"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/claim-points [POST]
func ClaimNewPoints(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.UserClaimNewPointRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	if len(data.Sources) < 0 {
		appG.Response400(e.INVALID_PARAMS, "sources required")
		return
	}

	err := services.OEPointHistory(c.Request.Context()).ConfirmClaimNewPointsBySources(data.Sources)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// @Summary		User point histories
// @Description	User point histories
//
// @Tags			Point History
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			query		models.OEPointHistoryQuery	true	"input body"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/point-histories [GET]
func GetMyPointHistories(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.OEPointHistoryQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	ctx := c.Request.Context()
	query.UserID = util.NewString(app.GetLoggedUser(ctx).ID)

	resp := dto.ListOEPointHistoryResponse{
		Results:    []*models.OEPointHistory{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	histories, pagination, listErr := services.OEPointHistory(c.Request.Context()).FindPage(&query, &options)
	if listErr != nil {
		appG.ResponseAppError(listErr)
		return
	}

	resp.Results = histories
	resp.Pagination = pagination
	appG.Response200(resp)
}
