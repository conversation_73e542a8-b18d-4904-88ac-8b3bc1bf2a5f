package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CreateCoupon
// @Summary		Create a new coupon
// @Description	Create a new coupon
//
// @Tags		Coupon
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true		"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true		"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.CreateCouponRequest		true	"Create coupon input"
//
// @Success		201				{object}	app.ResponseT[models.SimpleCoupon]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		403				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/coupons [POST]
func CreateCoupon(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	req := dto.CreateCouponRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	// Check role
	if !currentUser.CanActCoupon(org.ID) {
		appG.Response403(e.FORBIDDEN, "Need permission for create coupon")
		return
	}

	if currentUser.IsOrgAdmin(org.ID) {
		if (req.OrgID != "") && (req.OrgID != org.ID) {
			appG.Response400(e.FORBIDDEN, "Need permission for create coupon")
			return
		}
		req.OrgID = org.ID
	}

	coupon, cErr := services.Coupon.Create(currentUser, &req)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response201(coupon.ToSimple())
}

// UpdateCoupon
// @Summary		Update the coupon by ID
// @Description	Update the coupon by ID
//
// @Tags		Coupon
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true		"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true		"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string		true	"Coupon ID"
//
// @Param		Input		body		dto.UpdateCouponRequest		true	"Update Coupon input"
//
// @Success		200				{object}	app.ResponseT[models.SimpleCoupon]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		403				{object}	app.ResponseT[app.ErrorData]
// @Failure		404				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
//
// @Router			/api/v1/coupons/{id} [PUT]
func UpdateCoupon(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	couponID := c.Param(models.PathParamKeyID)

	req := dto.UpdateCouponRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	// Check role
	if !currentUser.CanActCoupon(org.ID) {
		appG.Response403(e.FORBIDDEN, "Need permission for update coupon")
		return
	}

	if currentUser.IsOrgAdmin(org.ID) {
		if (req.OrgID != "") && (req.OrgID != org.ID) {
			appG.Response400(e.FORBIDDEN, "Need permission for create coupon")
			return
		}
		req.OrgID = org.ID
	}

	coupon, cErr := services.Coupon.FindByID(couponID, false, &models.FindOneOptions{})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if !services.Coupon.CanUpdateCoupon(coupon, appG.GetLoggedUser(), org) {
		appG.Response400(e.Coupon_update_need_permission_or_owner, "Need admin or Coupon owner for update permission")
		return
	}
	// Check Coupon owner can edit Coupon
	cErr = services.Coupon.Update(coupon, &req)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(coupon.ToSimple())
}

// FindDetailCoupon
// @Summary		Find the coupon details by ID
// @Description	Find the coupon details by ID
//
// @Tags		Coupon
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true		"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true		"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string		true	"Coupon ID"
//
// @Success		200				{object}	app.ResponseT[models.SimpleCoupon]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		403				{object}	app.ResponseT[app.ErrorData]
// @Failure		404				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/coupons/{id} [GET]
func FindDetailCoupon(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	// Check role
	if !currentUser.CanActCoupon(org.ID) {
		appG.Response403(e.FORBIDDEN, "Need permission for find coupon")
		return
	}

	id := c.Param("id")
	query := &models.CouponQuery{
		ID:      util.NewString(id),
		OrgIDIn: []string{org.ID, ""},
	}
	coupon, cErr := services.Coupon.FindOne(query, &models.FindOneOptions{Preloads: []string{"Course"}})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(coupon.ToSimple())
}

// FindPageCouponsByAdmin
// @Summary		Find the list coupons with pagination
// @Description	Find the list coupons with pagination
//
// @Tags		Coupon
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true		"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true		"X-referrer"	default(openedu101.com)
//
//	@Param		pagination	query		models.FindPageOptions	true	"Pagination query"																																																																																													models.FindPageOptions 						 false  	"find page options"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/coupons [GET]
func FindPageCouponsByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	// Check role
	if !currentUser.CanActCoupon(org.ID) {
		appG.Response403(e.FORBIDDEN, "Need permission for find coupon")
		return
	}

	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CouponQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	query.IncludeDeleted = util.NewBool(false)
	//Check view for org admin
	if currentUser.IsOrgAdmin(org.ID) {
		query.OrgIDIn = []string{org.ID, ""}
	}

	resp := dto.ListCouponResponse{
		Results:    []*models.SimpleCoupon{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, "User")

	coupons, pagination, err := services.Coupon.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(coupons, func(c *models.Coupon, _ int) *models.SimpleCoupon {
		return c.ToSimple()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// DeleteCoupon
// @Summary		Delete the coupon by ID
// @Description	Delete the coupon by ID
//
// @Tags		Coupon
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true		"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true		"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string		true	"Coupon ID"
//
// @Success		200				{object}	app.ResponseT[string]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		403				{object}	app.ResponseT[app.ErrorData]
// @Failure		404				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/coupons/{id} [DELETE]
func DeleteCoupon(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	// Check role
	if !currentUser.CanActCoupon(org.ID) {
		appG.Response403(e.FORBIDDEN, "Need permission for delete coupon")
		return
	}

	id := c.Param("id")
	coupon, cErr := services.Coupon.FindByID(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if !services.Coupon.CanUpdateCoupon(coupon, appG.GetLoggedUser(), org) {
		appG.Response400(e.Coupon_delete_need_permission_or_owner, "Need admin for delete permission")
		return
	}

	if dErr := services.Coupon.Delete(coupon); dErr != nil {
		appG.ResponseAppError(dErr)
		return
	}
	appG.Response200("success")
}

// FindPageCouponsByCretor
// @Summary		Find the list coupons with pagination
// @Description	Find the list coupons with pagination
//
// @Tags		Coupon
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true		"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true		"X-referrer"	default(openedu101.com)
//
//	@Param		pagination	query		models.FindPageOptions	true	"Pagination query"																																																																																													models.FindPageOptions 						 false  	"find page options"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/coupons/partners [GET]
func FindPageCouponsByPartner(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	// Check role
	if !currentUser.CanActCoupon(org.ID) {
		appG.Response403(e.FORBIDDEN, "Need permission for find coupon")
		return
	}

	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CouponQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	query.IncludeDeleted = util.NewBool(false)
	//Check view for org admin
	resp := dto.ListCouponResponse{
		Results:    []*models.SimpleCoupon{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, "User")

	coupons, pagination, err := services.Coupon.ListCouponsByCreator(currentUser, &query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(coupons, func(c *models.Coupon, _ int) *models.SimpleCoupon {
		return c.ToSimple()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}
