package v1

import (
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Create blog
// @Description	Create blog
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			Input			body		dto.CreateBlogRequest	true	"Create blog input"
//
// @Success		201				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs [POST]
func CreateBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	data := dto.CreateBlogRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := e.HandleValidationError(data); len(err) > 0 {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err)
		return
	}

	if data.BlogType == models.BlogTypeOrg {
		if !services.Blog.CanPublishOrgBlog(currentUser, org.ID) {
			appG.Response400(e.Blog_publish_org_permission_required, "You don't have permission to publish blog in this organization")
			return
		}
	}

	blog, aErr := services.Blog.Create(org, currentUser, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response201(blog)
}

// @Summary		Update blog
// @Description	Update blog
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			id				path		string					true	"blog id"
//
// @Param			Input			body		dto.UpdateBlogRequest	true	"Update blog input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id} [PUT]
func UpdateUserBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.UpdateBlogRequest{}
	blogId := c.Param("id")
	org := appG.GetOrg()
	currentUser := appG.GetLoggedUser()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	blog, oErr := services.Blog.FindByID(blogId, false, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find blog by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if blog.Status != models.BlogStatusDraft {
		appG.Response400(e.INVALID_PARAMS, "You don't have permission to update not draft blog")
		return

	}

	if blog.BlogType == models.BlogTypeOrg {
		appG.Response400(e.Blog_update_permission_required, "You don't have permission to update blog org on this page")
		return
	}

	if blog.AuthorID != currentUser.ID {
		appG.Response400(e.Blog_update_permission_required, "You don't have permission")
		return
	}

	// set new value
	aErr := services.Blog.Update(blog, &data, org)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(blog.Sanitize())
}

// @Summary		Update blog by admin
// @Description	Update blog by admin
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			id				path		string					true	"blog id"
//
// @Param			Input			body		dto.UpdateBlogRequest	true	"Update blog input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/blogs/{id} [PUT]
func UpdateBlogByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.UpdateBlogRequest{}
	blogId := c.Param("id")
	org := appG.GetOrg()
	currentUser := appG.GetLoggedUser()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	blog, oErr := services.Blog.FindByID(blogId, false, &models.FindOneOptions{})
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	if blog.Status != models.BlogStatusDraft {
		appG.Response400(e.INVALID_PARAMS, "You don't have permission to update not draft blog")
		return

	}

	// if !services.Blog.CanUpdateBlog(blog, currentUser, org) || !setting.IsAdminSite(appG.GetDomain().Uri) {
	// 	appG.Response400(e.Blog_update_permission_required, "You must have permission and is on admin site")
	// 	return
	// }

	if !services.Blog.CanUpdateBlog(blog, currentUser, org) {
		appG.Response400(e.Blog_update_permission_required, "You must have permission and is on admin site")
		return
	}

	// set new value
	aErr := services.Blog.Update(blog, &data, org)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(blog.Sanitize())
}

// @Summary		Publish blog
// @Description	Publish blog
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			id				path		string					true	"blog id"
//
// @Param			Input			body		dto.UpdateBlogRequest	true	"Update blog input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id}/publish [PUT]
func PublishBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.PublishBlogRequest{}
	blogId := c.Param("id")
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	blog, oErr := services.Blog.FindByID(blogId, false, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find blog by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if blog.BlogType == models.BlogTypePersonal {
		if blog, err := services.Blog.HandlePublishBlogTypePersonal(currentUser, org, blog); err != nil {
			appG.ResponseAppError(err)
			return
		} else {
			appG.Response200(blog.Sanitize())
			return
		}
	}

	isPassApprovals := services.Blog.CanPassApprovals(currentUser, org)

	if blog.BlogType == models.BlogTypeOrg {
		if !services.Blog.CanPublishOrgBlog(currentUser, org.ID) {
			appG.Response400(e.Blog_publish_org_permission_required, "You don't have permission to publish blog in this organization")
			return
		}
	}

	if blog.Status == models.BlogStatusPublish {
		appG.Response400(e.Blog_status_already_existed, fmt.Sprintf("blog status %s already updated", blog.Status))
		return
	}

	if blog.Status == models.BlogStatusReviewing {
		appG.Response400(e.Blog_status_reviewing, "blog reviewing")
		return
	}

	// set new value
	data.Blog = blog
	data.Org = org
	data.Requester = currentUser

	if newBlog, reqErr := services.Blog.RequestPublishBlog(org, &data, isPassApprovals); reqErr != nil {
		appG.ResponseAppError(reqErr)
		return
	} else {
		appG.Response200(newBlog.Sanitize())
		return
	}
}

// @Summary		Delete By ID
// @Description	Delete By ID
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"blog id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id} [DELETE]
func DeleteBlogById(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	blogId := c.Param("id")

	blogQuery := models.BlogQuery{
		Cuid: &blogId,
	}

	_, findErr := services.Blog.FindOne(&blogQuery, &models.FindOneOptions{})
	if findErr != nil {
		appG.ResponseAppError(findErr)
		return
	}

	blogQuery.NotStatus = util.NewT(models.BlogStatusDraft)

	blogOptions := models.FindOneOptions{
		Sort: []string{"version DESC"},
	}

	nonDraftLastestVersionblog, cErr := services.Blog.FindOne(&blogQuery, &blogOptions)
	if cErr != nil && cErr.ErrCode != e.Blog_not_found {
		appG.ResponseAppError(cErr)
		return
	}

	if nonDraftLastestVersionblog != nil {
		if nonDraftLastestVersionblog.AuthorID != currentUser.ID {
			appG.Response400(e.Blog_delete_permission_required, "You don't have permission to delete this blog")
			return
		}

		if nonDraftLastestVersionblog.Status == models.BlogStatusPublish {
			appG.Response400(e.Blog_delete_permission_required, "You don't have permission to delete published blog")
			return
		}
	}

	oErr := services.Blog.DeleteMany(&models.BlogQuery{Cuid: &blogId})
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Find By ID
// @Description	Find By ID
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"blog id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id}/personal [GET]
func FindBlogPersonByID(c *gin.Context) {
	appG := app.Gin{C: c}
	blogId := c.Param("id")
	query := &models.BlogQuery{
		IDOrSlug:        &blogId,
		NotStatus:       util.NewT(models.BlogStatusDraft),
		BlogType:        util.NewT(models.BlogTypePersonal),
		ScheduleAtLower: util.NewT(time.Now().Unix()),
		IncludeDeleted:  util.NewBool(true),
	}

	options := &models.FindOneOptions{}
	options.Sort = []string{"cuid, version DESC"}
	options.Preloads = append(options.Preloads, models.BlogPreloadsHashTag)
	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories)
	options.Preloads = append(options.Preloads, models.BlogPreloadsUser)

	blog, oErr := services.Blog.FindOne(query, options)
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	if blog.Status != models.BlogStatusPublish {
		appG.Response400(e.Blog_not_found, "Blog not found")
		return
	}
	blog.ProcessBlogContentBunnyUrl()
	// check if author, pass permission
	appG.Response200(blog)
}

// @Summary		Find By ID
// @Description	Find By ID
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"blog id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id}/org [GET]
func FindBlogOrgByID(c *gin.Context) {
	appG := app.Gin{C: c}
	blogId := c.Param("id")
	query := &models.PublishBlogQuery{
		IDOrSlug:           &blogId,
		IncludeUnPublished: util.NewBool(false),
		IncludeDeleted:     util.NewBool(false),
	}

	pubBlog, oErr := services.PublishBlog.FindOne(query, &models.FindOneOptions{})
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	if pubBlog != nil {
		org, fOrgErr := services.Organization.FindOne(&models.OrganizationQuery{ID: &pubBlog.OrgID}, &models.FindOneOptions{})
		if fOrgErr != nil {
			appG.ResponseAppError(fOrgErr)
			return
		}

		findBlogOptions := &models.FindOneOptions{}
		findBlogOptions.Preloads = append(findBlogOptions.Preloads, models.BlogPreloadsHashTag,
			models.BlogPreloadsCategories, models.BlogPreloadsUser)

		findBlogQuery := &models.BlogQuery{ID: &pubBlog.BlogID}
		blog, bErr := services.Blog.FindOne(findBlogQuery, findBlogOptions)
		if bErr != nil {
			appG.ResponseAppError(bErr)
			return
		}
		blog.Org = org
		blog.ProcessBlogContentBunnyUrl()
		appG.Response200(blog)
		return
	}
	appG.Response400(e.Blog_not_found, "Blog not found")
}

// @Summary		Find Page User Blog Personal
// @Description	Find Page User Blog Personal
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string		true	"Origin"
// @Param			X-referer		header					string		true	"X-referer"
// @Param			Authorization	header					string		true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions 	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/users/:id/person-blogs [GET]
func FindPageUserPersonBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	userID := c.Param("id")
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.BlogQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	options.Preloads = append(options.Preloads, models.BlogPreloadsHashTag, models.BlogPreloadsCategories, models.BlogPreloadsUser)

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	query.AuthorID = &userID
	query.OrgID = util.NewString(org.ID)
	query.NotStatus = util.NewT(models.BlogStatusDraft)

	blogs, pagination, err := services.Blog.GetPublishedPersonalBlogsWithHighestVersion(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	blogs = lo.Filter(blogs, func(item *models.Blog, _ int) bool {
		return item.Status == models.BlogStatusPublish
	})

	resp.Results = lo.Map[*models.Blog, *models.SimpleBlog](blogs, func(p *models.Blog, _ int) *models.SimpleBlog {
		return p.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(dto.UserPersonBlogResponse{ListBlogsResponse: resp, Organize: org})
}

// @Summary		Find Page User Blog
// @Description	Find Page User Blog
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string		true	"Origin"
// @Param			X-referer		header					string		true	"X-referer"
// @Param			Authorization	header					string		true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions 	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/users/:id/org-blogs [GET]
func FindPageUserOrgBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	userID := c.Param("id")
	org := appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.PublishBlogQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	query.AuthorID = &userID
	query.OrgID = &org.ID
	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories, models.BlogPreloadsHashTag, models.BlogPreloadsUser)

	blogs, pagination, err := services.Blog.GetPublishBlog(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(blogs, func(p *models.Blog, _ int) *models.SimpleBlog {
		return p.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(dto.UserOrgBlogResponse{ListBlogsResponse: resp, Organize: org})
}

// @Summary		Find Page
// @Description	Find Page
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string	true	"Origin"
// @Param			X-referer		header					string	true	"X-referer"
// @Param			Authorization	header					string	true	"Bearer"
// @Params			Input query 	models.FindPageOptions 					 false  	"find page options"
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/blogs [GET]
func GetPublishBlogs(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	//bind filter
	var query models.PublishBlogQuery

	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	query.IncludeDeleted = util.NewBool(false)
	query.OrgID = &org.ID

	if query.NotOrgID != nil {
		query.OrgID = nil
	}

	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories, models.BlogPreloadsHashTag, models.BlogPreloadsUser)

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	blogs, pagination, err := services.Blog.GetPublishBlog(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(blogs, func(p *models.Blog, _ int) *models.SimpleBlog {
		return p.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(dto.HomePageBlogResponse{ListBlogsResponse: resp, Organize: org})
}

// @Summary		Find Page admin
// @Description	Find Page admin
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string	true	"Origin"
// @Param			X-referer		header					string	true	"X-referer"
// @Param			Authorization	header					string	true	"Bearer"
// @Params			Input query 	models.FindPageOptions 					 false  	"find page options"
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/admin/blogs [GET]
func FindPageBlogByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	if !user.CanManagementBlog(org.ID) {
		appG.Response400(e.INVALID_PARAMS, "You don't have permission")
		return

	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	//bind filter
	var query models.BlogQuery

	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if user.IsOrgWriter(org.ID) {
		query.AuthorID = &user.ID
	}

	query.Latest = util.NewBool(true)
	query.IsOriginDraft = util.NewBool(false)
	query.OrgID = util.NewString(org.ID)

	options.Preloads = append(options.Preloads, models.BlogPreloadPublished, models.BlogPreloadReviewing, models.BlogPreloadsCategories, models.BlogPreloadsHashTag, models.BlogPreloadsUser, models.BlogPreloadUnPublished)

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	blogs, pagination, err := services.Blog.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if query.BlogType != nil && *query.BlogType == models.BlogTypePersonal {
		blogs = lo.Filter(blogs, func(item *models.Blog, _ int) bool {
			for _, p := range item.UnPublished {
				if p.Version == item.Version {
					return false
				}
			}
			return true
		})
		pagination.TotalItems = len(blogs)
	}

	resp.Results = lo.Map[*models.Blog, *models.SimpleBlog](blogs, func(p *models.Blog, _ int) *models.SimpleBlog {
		return p.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Request to un-publish blog org
// @Description	Request to un-publish blog org
//
// @Tags			blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"blog id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id}/publish-org [DELETE]
func UnPublishBlogOrg(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()
	id := c.Param("id") // blog cuid

	if !services.Blog.CanPassApprovals(user, org) {
		appG.Response400(e.Blog_update_permission_required, "You don't have permission to unpublish this blog")
		return
	}

	if uErr := services.Blog.UnpublishBlogOrg(user, org, id); uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Request to un-publish blog person
// @Description	Request to un-publish blog person
//
// @Tags			blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"blog id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id}/publish-person [DELETE]
func UnPublishBlogPerson(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()
	id := c.Param("id") // blog cuid

	blog, err := services.Blog.FindOne(&models.BlogQuery{Cuid: &id}, &models.FindOneOptions{})
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	// Just need to check blog type and author
	// So we don't need to care many record with find by Cuid
	if !services.Blog.CanPassApprovals(user, org) && user.ID != blog.AuthorID {
		appG.Response400(e.Blog_update_permission_required, "You don't have permission to unpublish this blog")
		return
	}

	if uErr := services.Blog.UnpublishBlogPerson(appG.GetLoggedUser(), appG.GetOrg(), id); uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200("ok")
}

// @Summary		Find Page blog by categories
// @Description	Find Page blog by categories
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string	true	"Origin"
// @Param			X-referer		header					string	true	"X-referer"
// @Param			Authorization	header					string	true	"Bearer"
// @Params			Input query 	models.FindPageOptions 					 false  	"find page options"
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/blogs/categories [GET]
func GetPublishBlogByCategories(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CategoryRelationQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories, models.BlogPreloadsHashTag)

	query.RelatedType = util.NewT(models.BlogModelName)

	blogs, pagination, findPublishErr := services.Blog.GetPublishBlogByCategory(org, &query, &options)
	if findPublishErr != nil {
		appG.ResponseAppError(findPublishErr)
		return
	}

	resp.Results = lo.Map(blogs, func(item *models.Blog, _ int) *models.SimpleBlog {
		return item.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)

}

// @Summary		Find Page blog by categories
// @Description	Find Page blog by categories
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string	true	"Origin"
// @Param			X-referer		header					string	true	"X-referer"
// @Param			Authorization	header					string	true	"Bearer"
// @Params			Input query 	models.FindPageOptions 					 false  	"find page options"
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/blogs/categories/:id [GET]
func GetPublishBlogByCategoriesParent(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	cateID := c.Param("id")

	var options models.FindManyOptions
	if err := c.ShouldBindQuery(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CategoryRelationQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if options.Limit == nil || *options.Limit > 15 {
		options.Limit = util.NewInt(15)
	}
	options.Preloads = append(options.Preloads, models.BlogPreloadsUser)

	query.RelatedType = util.NewT(models.BlogModelName)

	blogs, findPublishErr := services.Blog.GetPublishBlogByCategoryParent(org, cateID, &options)
	if findPublishErr != nil {
		appG.ResponseAppError(findPublishErr)
		return
	}

	appG.Response200(dto.BlogTreeByCateOrgResponse{Result: blogs, Organize: org})

}

//	@Summary		Find Page recommend blog by categories
//	@Description	Find Page recommend blog by categories
//
//	@Tags			Blog
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header					string	true	"Origin"
//	@Param			X-referer		header					string	true	"X-referer"
//	@Param			Authorization	header					string	true	"Bearer"
//	@Params			Input query 	models.FindPageOptions 					 false  	"find page options"
//	@Success		200				{object}				app.Response
//	@Failure		400				{object}				app.Response
//	@Failure		500				{object}				app.Response
//
//	@Router			/api/v1/blogs/categories/recommend [GET]

func GetRecommendPublishBlogByCategories(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CategoryRelationQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories, models.BlogPreloadsHashTag)
	query.RelatedType = util.NewT(models.BlogModelName)

	blogs, pagination, findPublishErr := services.Blog.GetRecommendPublishBlogByCategory(&query, &options)
	if findPublishErr != nil {
		appG.ResponseAppError(findPublishErr)
		return
	}

	resp.Results = lo.Map(blogs, func(item *models.Blog, _ int) *models.SimpleBlog {
		return item.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)

}

//	@Summary		Find Page blog by hashtag
//	@Description	Find Page blog by hashtag
//
//	@Tags			Blog
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header					string	true	"Origin"
//	@Param			X-referer		header					string	true	"X-referer"
//	@Param			Authorization	header					string	true	"Bearer"
//	@Params			Input query 	models.FindPageOptions 					 false  	"find page options"
//	@Success		200				{object}				app.Response
//	@Failure		400				{object}				app.Response
//	@Failure		500				{object}				app.Response
//
//	@Router			/api/v1/blogs/hashtags/[GET]

func GetPublishBlogByHashTags(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.HashtagRelationQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories, models.BlogPreloadsHashTag)
	query.RelatedType = util.NewT(models.BlogModelName)

	blogs, pagination, findPublishErr := services.Blog.GetPublishBlogByHashtag(org, &query, &options)
	if findPublishErr != nil {
		appG.ResponseAppError(findPublishErr)
		return
	}

	resp.Results = lo.Map(blogs, func(item *models.Blog, _ int) *models.SimpleBlog {
		return item.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)

}

//	@Summary		Find Page recommend blog by hashtag
//	@Description	Find Page recommend blog by hashtag
//
//	@Tags			Blog
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header					string	true	"Origin"
//	@Param			X-referer		header					string	true	"X-referer"
//	@Param			Authorization	header					string	true	"Bearer"
//	@Params			Input query 	models.FindPageOptions 					 false  	"find page options"
//	@Success		200				{object}				app.Response
//	@Failure		400				{object}				app.Response
//	@Failure		500				{object}				app.Response
//
//	@Router			/api/v1/blogs/hashtags/recommend [GET]

func GetRecommendPublishBlogByHashTags(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.HashtagRelationQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories, models.BlogPreloadsHashTag)
	query.RelatedType = util.NewT(models.BlogModelName)

	blogs, pagination, findPublishErr := services.Blog.GetRecommendPublishBlogByHashtag(&query, &options)
	if findPublishErr != nil {
		appG.ResponseAppError(findPublishErr)
		return
	}

	resp.Results = lo.Map(blogs, func(item *models.Blog, _ int) *models.SimpleBlog {
		return item.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)

}

//	@Summary		Toggle pin blog
//	@Description	Toggle pin blog
//
//	@Tags			Blog
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"blog id"
//
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/admin/blogs/{id}/toggle-pin [PUT]

func TogglePinBlogByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	blogId := c.Param("id")
	org := appG.GetOrg()
	data := dto.PinBlogRequest{}
	currentUser := appG.GetLoggedUser()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	if err := e.HandleValidationError(data); len(err) > 0 {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err)
		return
	}

	blog, oErr := services.Blog.FindByID(blogId, false, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find blog by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if blog.Status != models.BlogStatusPublish {
		appG.Response400(e.INVALID_PARAMS, "You don't have permission to pin not publish blog")
		return

	}

	if !services.Blog.CanUpdateBlog(blog, currentUser, org) || !setting.IsAdminSite(appG.GetDomain().Uri) {
		appG.Response400(e.Blog_update_permission_required, "You must have permission and is on admin site")
		return
	}

	// set new value
	aErr := services.Blog.TogglePin(blog, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(blog.Sanitize())

}

// @Summary		Toggle pin blog
// @Description	Toggle pin blog
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"blog id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id}/toggle-pin [PUT]
func TogglePinUserBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	blogId := c.Param("id")
	data := dto.PinBlogRequest{}
	currentUser := appG.GetLoggedUser()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	blog, oErr := services.Blog.FindByID(blogId, false, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find blog by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	if blog.Status != models.BlogStatusPublish {
		appG.Response400(e.INVALID_PARAMS, "You don't have permission to pin not publish blog")
		return
	}

	if blog.BlogType == models.BlogTypeOrg {
		appG.Response400(e.Blog_update_permission_required, "You don't have permission to update blog org on this page")
		return
	}

	if blog.AuthorID != currentUser.ID {
		appG.Response400(e.Blog_update_permission_required, "You don't have permission")
		return
	}

	// set new value
	aErr := services.Blog.TogglePin(blog, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(blog.Sanitize())

}

// @Summary		Find top viewed blog
// @Description	Find top viewed blog
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string		true	"Origin"
// @Param			X-referer		header					string		true	"X-referer"
// @Param			Authorization	header					string		true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions 	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/blogs/top-viewed [GET]
func FindTopViewedBlog(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.TrackingQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.TopViewBlogResponse{
		Results:    []*communicationdto.BlogResp{},
		Pagination: communicationdto.NewPagination(options.Page, options.PerPage, 0),
	}

	blogs, pagination, err := communication.Tracking.FindTopViewedBlog(query.IntoComm(), options.IntoComm())
	if err != nil {
		appG.ResponseAppError(e.NewError500(e.External_call_error, err.Error()))
		return
	}

	resp.Results = blogs
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find By ID
// @Description	Find By ID
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"blog id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/{id}/edit [GET]
func FindBlogByIDToEdit(c *gin.Context) {
	appG := app.Gin{C: c}
	blogId := c.Param("id")
	user := appG.GetLoggedUser()
	org := appG.GetOrg()
	query := &models.BlogQuery{
		ID:     &blogId,
		Status: util.NewT(models.BlogStatusDraft),
	}

	options := &models.FindOneOptions{}
	options.Sort = []string{"cuid, version DESC"}
	options.Preloads = append(options.Preloads, models.BlogPreloadsHashTag)
	options.Preloads = append(options.Preloads, models.BlogPreloadsCategories)
	options.Preloads = append(options.Preloads, models.BlogPreloadsUser)

	blog, oErr := services.Blog.FindOne(query, options)
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}
	blog.ProcessBlogContentBunnyUrl()

	switch blog.BlogType {
	case models.BlogTypeOrg:
		if blog.ID != user.ID && !user.IsOrgEditor(org.ID) && !user.IsOrgAdmin(org.ID) {
			appG.Response400(e.FORBIDDEN, "You don't have permission to find this blog")
			return
		}
		appG.Response200(blog)
		return
	case models.BlogTypePersonal:
		if blog.AuthorID != user.ID {
			appG.Response400(e.FORBIDDEN, "You don't have permission to find this blog")
			return
		}
		appG.Response200(blog)
		return
	}

	appG.Response400(e.Blog_not_found, "Blog not found")
}
