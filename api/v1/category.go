package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// UpsertCategories
//
//	@Summary		Create Categories
//	@Description	Create Categories
//
//	@Tags			category
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin		header		string						true	"Origin"
//	@Param			X-referer	header		string						true	"X-referer"
//	@Param			Input		body		dto.UpsertCategoriesRequest	true	"Create Categories request body"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/categories/bulk-upsert [POST]
func UpsertCategories(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.UpsertCategoriesRequest
	var user models.User
	if !appG.RequiredLogin(&user) {
		return
	}

	if err := c.ShouldBindJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	org := appG.GetOrg()
	appErr := services.Category.CheckPermissionUpsertCategories(&user, org, &reqBody)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if appErr = services.Category.UpsertMany(&dto.CreateCategoriesParams{
		Categories: reqBody.Categories,
		OrgID:      org.ID,
	}); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// FindPageCategory
//
//	@Summary		Find Page Category
//	@Description	Find Page Category
//
//	@Tags			category
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referer	header		string					true	"X-referer"
//	@Param			Input		query		models.FindPageOptions	true	"Find Page Category request options"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/categories [GET]
func FindPageCategory(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()

	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		log.Error("Api::Category.Find Page Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query models.CategoryQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	query.IncludeDeleted = util.NewBool(false)
	if query.Active == nil {
		query.Active = util.NewBool(true)
	}

	// Sysadmin see all course levels, org admin only see course levels of that org
	if query.Type != nil && *query.Type == models.TypeLevel {
		currentUser := appG.GetLoggedUser()
		if currentUser != nil && !currentUser.IsSysAdmin() {
			query.OrgID = &org.ID
		}
	}

	// Sysadmin see all blog categories, org admin only see blog categories of that org
	if query.Type != nil && *query.Type == models.TypeCategoryBlog {
		currentUser := appG.GetLoggedUser()
		if currentUser != nil && !currentUser.IsSysAdmin() {
			query.OrgID = &org.ID
		}
	}

	// sort by order asc for promt
	if query.Type != nil && *query.Type == models.TypeCategoryPrompt {
		options.Sort = append(options.Sort, models.OrderASC, models.IdDESC)
	}

	resp := dto.ListCategoryResponse{
		Results:    []*models.SimpleCategory{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	categories, pagination, err := services.Category.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = lo.Map(categories, func(c *models.Category, _ int) *models.SimpleCategory {
		return c.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// DeleteCategories
//
//	@Summary		Delete Category
//	@Description	Delete Category
//
//	@Tags			category
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referer	header		string	true	"X-referer"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/categories/bulk-delete [POST]
func DeleteCategories(c *gin.Context) {
	appG := app.Gin{C: c}
	// currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	var req dto.DeleteCategoryRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Errorf("Api::Category.DeleteCategories Bind JSON error: %v", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := util.Validator.Struct(&req); err != nil {
		log.Error("Api::Category.DeleteCategories validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	// var orgID string
	// if currentUser.IsOrgAdmin(org.ID) {
	// 	orgID = org.ID
	// }

	if appErr := services.Category.DeleteMany(req.IDs, org.ID); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200("success")
}

// GetCategoryTree
//
//	@Summary		Get Tree Category
//	@Description	Get Tree Category
//
//	@Tags			category
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referer	header		string	true	"X-referer"
//	@Param			active		query		string	true	"Get Tree Category query options"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/categories/tree [GET]
func GetCategoryTree(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	var query models.CategoryQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	// Ensure the Active flag is set to true if not already specified.
	if query.Active == nil {
		query.Active = util.NewBool(true)
	}

	// Sysadmins can view all blog categories, while other users
	// can only view the list within their current organization.
	if query.Type != nil {
		switch *query.Type {
		case models.TypeCategoryBlog:
			user := appG.GetLoggedUser()
			if user != nil && !user.IsSysAdmin() {
				query.OrgID = &org.ID
			}

		case models.TypeLevel:
			query.OrgID = nil
		}
	}

	categoryTree, err := services.Category.GetTree(&query)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200(categoryTree)
}
