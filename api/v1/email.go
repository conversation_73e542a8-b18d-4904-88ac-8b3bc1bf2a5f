package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// CreateEmailTemplate
//
//	@Summary		Create email template
//	@Description	Create email template
//
//	@Tags			Email
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string						true	"Origin"
//	@Param			X-referer	header		string						true	"X-referer"
//
//	@Param			Input		body		dto.CreateEmailTemplateRequest	true	"Create email template input"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/emails/templates [POST]
func CreateEmailTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.CreateEmailTemplateRequest{}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	if req.Code == "" {
		nameSlugify, _ := util.Slugify(req.Name, 3)
		req.Code = models.EmailCodeType(nameSlugify)
	}

	template, err := services.Email.CreateEmailTemplate(&req, user, org)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200(template)
}

// UpdateEmailTemplate
//
//	@Summary		Update the email template by ID
//	@Description	Update the email template by ID
//
//	@Tags			Email
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string		true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string		true	"X-referrer"	default(openedu101.com)
//
//	@Param			id				path		string		true	"Email template ID"
//
//	@Param			Input			body		dto.UpdateEmailTemplateRequest	true	"Update email template request"
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/emails/templates/{id} [PUT]
func UpdateEmailTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	user := appG.GetLoggedUser()
	templateID := c.Param(models.PathParamKeyID)

	var req dto.UpdateEmailTemplateRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	if uErr := services.Email.UpdateEmailTemplate(&req, templateID, user, org); uErr != nil {
		appG.ResponseAppError(e.NewError500(e.Email_template_update_failed, uErr.Error()))
		return
	}

	appG.Response200("success")
}

// GetEmailTemplate
//
//	@Summary		Get email template
//	@Description	Get email template
//
//	@Tags			Email
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referer	header		string	true	"X-referer"
//
//	@Param			id			path		string	true	"id"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/emails/templates/{id} [GET]
func GetEmailTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	template, err := services.Email.FindOneEmailTemplate(id)
	if err != nil {
		appG.ResponseAppError(e.NewError500(e.Email_template_find_one_failed, err.Error()))
		return
	}

	appG.Response200(template)

}

// FindEmailTemplates
//
//	@Summary		Find the list email templates with pagination
//	@Description	Find the list email templates with pagination
//
//	@Tags			Email
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string		true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string		true	"X-referrer"	default(openedu101.com)
//
//	@Params			filter			query 		models.EmailTemplateQuery  	true  	"Filter query params"
//	@Param			pagination		query		models.FindPageOptions		true	"Pagination query params"																																																																																																																																															query 		models.FindPageOptions "find page options"																																															query 		models.FindPageOptions 		 false  	"find page options"
//
//	@Success		200				{object}	app.ResponseT[communicationdto.ListEmailTemplateResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/emails/templates [GET]
func FindEmailTemplates(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	var query models.EmailTemplateQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	query.OrgID = &org.ID
	query.IncludeDeleted = util.NewBool(false)

	resp := communicationdto.ListEmailTemplateResponse{
		Results:    []*communicationdto.EmailTemplate{},
		Pagination: communicationdto.NewPagination(options.Page, options.PerPage, 0),
	}

	templates, pagination, err := services.Email.FindPageEmailTemplate(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = templates
	resp.Pagination = pagination
	appG.Response200(resp)
}

// DeleteEmailTemplate
//
//	@Summary		Delete email template
//	@Description	Delete email template
//
//	@Tags			Email
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referer	header		string	true	"X-referer"
//
//	@Param			id			path		string	true	"email template id"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/emails/templates/{id} [DELETE]
func DeleteEmailTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(models.PathParamKeyID)
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	if dErr := services.Email.DeleteEmailTemplate(id, user, org); dErr != nil {
		appG.ResponseAppError(dErr)
		return
	}
	appG.Response200("success")
}

// GetEmailTemplateVariables
//
//	@Summary		Get email template variables
//	@Description	Get email template variables
//
//	@Tags			Email
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referer	header		string	true	"X-referer"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/emails/templates/variables [GET]
func GetEmailTemplateVariables(c *gin.Context) {
	appG := app.Gin{C: c}
	tmplVarsByEmailCode, err := services.Email.FindEmailTemplateVariables()
	if err != nil {
		appG.ResponseAppError(e.NewError500(e.Email_template_get_variables_failed, err.Error()))
		return
	}
	appG.Response200(tmplVarsByEmailCode)

}

// SendPreviewEmailTemplate
//
//	@Summary		Send the preview email with the template
//	@Description	Send the preview email with the template
//
//	@Tags			Email
//	@Accept			json
//	@Produce		json
//
//	@Security		BearerAuth
//
//	@Param			Origin		header		string						true	"Origin"
//	@Param			X-referer	header		string						true	"X-referer"
//
//	@Param			id			path		string						true	"id"
//
//	@Param			Input		body		dto.PreviewEmailTemplateRequest		true	"Update email template request"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/emails/templates/{id}/preview [POST]
func SendPreviewEmailTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(models.PathParamKeyID)
	var req dto.PreviewEmailTemplateRequest

	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	if _, uErr := services.Email.PreviewEmailTemplate(appG.GetOrg(), id, &req); uErr != nil {
		appG.ResponseAppError(e.NewError500(e.Email_template_preview_failed, uErr.Error()))
		return
	}

	appG.Response200("success")
}
