package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// Lam sao de biet user co the tao duoc extend link
// summary referral

// Create Affiliate campaign
//
//	@Summary		Create affiliate campaign
//	@Description	Create affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string							true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string							true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string							true	"Bearer"
//
//	@Param			Input			body		dto.AffiliateCampaignRequest	true	"Create form input"
//
//	@Success		200				{object}	app.ResponseT[models.AffiliateCampaign]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns [POST]
func CreateAffiliateCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody *dto.AffiliateCampaignRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	reqBody.User = appG.GetLoggedUser()
	reqBody.Org = appG.GetOrg()
	campaign, err := services.AffiliateCampaign.Create(reqBody)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(campaign)
	return
}

// Update Affiliate campaign
//
//	@Summary		Update affiliate campaign
//	@Description	Update affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string							true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string							true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string							true	"Bearer"
//
//	@Param			Input			body		dto.AffiliateCampaignRequest	true	"Update form input"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns/:id [PUT]
func UpdateAffiliateCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var id = c.Param("id")
	var reqBody *dto.AffiliateCampaignRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if checkErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(appG.GetLoggedUser(), campaign); checkErr != nil {
		appG.ResponseAppError(checkErr)
		return
	}

	reqBody.User = appG.GetLoggedUser()
	reqBody.Org = appG.GetOrg()
	updated, updateErr := services.AffiliateCampaign.Update(campaign, reqBody)
	if updateErr != nil {
		appG.ResponseAppError(updateErr)
		return
	}

	appG.Response200(updated)
	return
}

// Find one Affiliate campaign
//
//	@Summary		Find One affiliate campaign
//	@Description	Find One affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string	true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string	true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"affiliate campaign id"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns/:id [GET]
func FindOneAffiliateCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var id = c.Param("id")

	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	var options models.FindOneOptions
	if bErr := appG.C.ShouldBindQuery(&options); bErr != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+bErr.Error())
		return
	}

	if len(options.Preloads) > 0 {
		load := lo.Contains(options.Preloads, models.UserCommissionField)
		if load {
			if ucErr := services.Commission.FindUserCommissionByCampaign(appG.GetLoggedUser(), campaign); ucErr != nil {
				appG.ResponseAppError(ucErr)
				return
			}
		}

		commissions, comErr := services.Commission.GetCommissionByCampaign(campaign)
		if comErr != nil {
			appG.ResponseAppError(comErr)
			return
		}
		campaign.Commissions = commissions
	}

	appG.Response200(campaign)
	return
}

// Delete Affiliate campaign
//
//	@Summary		Delete affiliate campaign
//	@Description	Delete affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string	true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string	true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"affiliate campaign id"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns/:id [DELETE]
func DeleteAffiliateCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var id = c.Param("id")
	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	delErr := services.AffiliateCampaign.Delete(campaign)
	if delErr != nil {
		appG.ResponseAppError(delErr)
		return
	}

	appG.Response200("success")
	return
}

// Find Affiliate campaign
//
//	@Summary		Find affiliate campaign
//	@Description	Find affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string							true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string							true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string							true	"Bearer"
//
//	@Param			Input			query		models.AffiliateCampaignQuery	true	"affiliate campaign find"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns [GET]
func FindAffiliateCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.AffiliateCampaignQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if query.OrgID == nil {
		query.OrgID = util.NewString(appG.GetOrg().ID)
	}
	if query.UserID == nil {
		query.UserID = util.NewString(appG.GetLoggedUser().ID)
	}

	resp := dto.ListAffiliateCampaignResponse{
		Results:    []*models.AffiliateCampaign{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	campaigns, pagination, cErr := services.AffiliateCampaign.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = campaigns
	resp.Pagination = pagination
	appG.Response200(resp)
}

// Add courses to Affiliate campaign
//
//	@Summary		Add course to affiliate campaign
//	@Description	Add course to affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string						true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string						true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string						true	"Bearer"
//
//	@Param			Input			body		dto.AffiliateCampaignCourse	true	"add courses body"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns/:id/courses [POST]
func AddCourses2AffiliateCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()
	var reqBody dto.AffiliateCampaignCourse
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if perErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(appG.GetLoggedUser(), campaign); perErr != nil {
		appG.Response400(e.Affiliate_campaign_remove_courses_failed, "campaign owner required")
		return
	}

	reqBody.Org = appG.GetOrg()
	addErr := services.AffiliateCampaign.AddCourses(user, campaign, &reqBody)
	if addErr != nil {
		appG.ResponseAppError(addErr)
		return
	}

	appG.Response200("success")
	return
}

// Delete course from Affiliate campaign
//
//	@Summary		Remove to affiliate campaign
//	@Description	Remove to affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string							true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string							true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string							true	"Bearer"
//
//	@Param			id				path		string							true	"campaign id"
//	@Param			Input			query		dto.DefaultDeleteManyRequest	true	"delete many"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns/:id/courses [DELETE]
func DeleteCoursesFromAffiliateCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var id = c.Param("id")
	var req dto.DefaultDeleteManyRequest
	if err := appG.C.ShouldBindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	if perErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(appG.GetLoggedUser(), campaign); perErr != nil {
		appG.Response400(e.Affiliate_campaign_remove_courses_failed, "campaign owner required")
		return
	}
	delErr := services.AffiliateCampaign.RemoveCourses(&req)
	if delErr != nil {
		appG.ResponseAppError(delErr)
		return
	}

	appG.Response200("success")
	return
}

func AffiliateCampaignByUser(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	var query models.UserCamapaignQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	resp := dto.ListUserCampaignResponse{
		Results:    []*models.UserCampaign{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	campaigns, pagination, cErr := services.AffiliateCampaign.FindCampaignByUser(user.ID, &query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = campaigns
	resp.Pagination = pagination
	appG.Response200(resp)
}

// Find course by Affiliate campaign
//
//	@Summary		Find courses by affiliate campaign
//	@Description	Find courses by affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string						true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string						true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string						true	"Bearer"
//
//	@Param			id				path		string						true	"campaign id"
//	@Param			Input			query		models.CourseCampaignQuery	true	"query"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns/:id/courses [GET]
func FindCourseByCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var id = c.Param("id")
	var query models.CourseCampaignQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp := dto.ListCourseByCampaignRequest{
		Results:    []*models.CourseCampaign{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	request := dto.FindCourseCampaignRequest{
		Org:      appG.GetOrg(),
		Campaign: campaign,
	}
	courseCampaigns, pagination, cErr := services.AffiliateCampaign.FindCourseByCampaign(&request, &query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = courseCampaigns
	resp.Pagination = pagination
	appG.Response200(resp)
}

// Find publish course by Affiliate campaign
//
//	@Summary		Find publish courses by affiliate campaign
//	@Description	Find publish courses by affiliate campaign
//
//	@Tags			Affiliate Campaign
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string						true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string						true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string						true	"Bearer"
//
//	@Param			id				path		string						true	"campaign id"
//	@Param			Input			query		models.CourseCampaignQuery	true	"query"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaigns/:id/publish-courses [GET]
func FindPublishCourseByCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	var id = c.Param("id")
	var query models.CourseCampaignQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp := dto.ListCourseByCampaignRequest{
		Results:    []*models.CourseCampaign{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	//todo: cho nay sai roi, de sua lai sau
	courseCampaigns, pagination, cErr := services.AffiliateCampaign.FindPublishCourseByCampaign(campaign, nil, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = courseCampaigns
	resp.Pagination = pagination
	appG.Response200(resp)
}
