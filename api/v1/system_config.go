package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// GetConfig
// @Summary		Get the public configurations: locale, currency, learn method
// @Description	Get the public configurations: locale, currency, learn method
//
// @Tags		System Config
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Success		200				{object}	app.ResponseT[dto.PublicConfig]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/system-configs/{id} [GET]
func GetConfig(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response200(&dto.PublicConfig{
		Locales:     models.GetConfig[models.StringArray](models.LocaleConfig),
		Currency:    models.GetConfig[models.StringArray](models.CurrencyConfig),
		LearnMethod: models.GetConfig[models.StringArray](models.LearnMethodConfig),
	})
}

// CreateSystemConfig
// @Summary		Create a new system config
// @Description	Create a new system config
//
// @Tags		System Config
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.SystemConfigRequest	true	"Create system config input"
//
// @Success		200				{object}	app.ResponseT[models.SystemConfig]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/system-configs [POST]
func CreateSystemConfig(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	user := appG.GetLoggedUser()

	if !currentUser.IsSysAdmin() && !currentUser.IsOrgAdmin(org.ID) {
		appG.Response400(e.FORBIDDEN, "Only sysadmin or org admin can create system config")
		return
	}

	req := dto.SystemConfigRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	if !setting.IsSysAdminSite(appG.GetDomain().Domain) {
		req.OrgID = org.ID
	}

	systemConfig, sErr := services.SystemConfig.Create(user, &req)
	if sErr != nil {
		appG.ResponseAppError(sErr)
		return
	}

	appG.Response200(systemConfig)
}

// GetSystemConfigs
// @Summary		Find the list system configs with pagination
// @Description	Find the list system configs with pagination
//
// @Tags		System Config
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer		header		string		true	"X-referrer"	default(openedu101.com)
//
// @Params		Input query     dto.QuerySystemConfig 	false  "Filter query"
//
// @Success		200				{object}	app.ResponseT[models.SystemConfig]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/system-configs [GET]
func GetSystemConfigs(c *gin.Context) {
	appG := app.Gin{C: c}
	//bind filter
	query := dto.QuerySystemConfig{}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	systemConfigs, err := services.SystemConfig.FindAll(query.Keys, query.OrgIds, query.Domains, query.Locales)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	if systemConfigs == nil {
		appG.Response200([]*models.SimpleConfig{})
		return
	}
	appG.Response200(systemConfigs)
}

// UpdateSystemConfig
// @Summary		Update the system config by ID
// @Description	Update the system config by ID
//
// @Tags		System Config
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer		header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id				path		string						true	"System config ID"
// @Param		Input			body		dto.SystemConfigRequest		true	"Update system config input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/system-configs/{id} [PUT]
func UpdateSystemConfig(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()

	req := dto.SystemConfigRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	sysConfig, appErr := services.SystemConfig.FindByID(id, false, nil)
	if appErr != nil {
		log.Error("find system config by ID failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if isAllow := services.SystemConfig.CanUpdateSystemConfig(sysConfig, user); !isAllow {
		appG.Response403(e.FORBIDDEN, "You don't have permission")
		return
	}

	sysConfig, appErr = services.SystemConfig.Update(user, sysConfig, &req, appG.GetOrg().ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(sysConfig)
}

// FindSystemConfigByID
// @Summary		Find the system config by ID
// @Description	Find the system config by ID
//
// @Tags		System Config
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer		header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id				path		string		true	"System config ID"
//
// @Success		200				{object}	app.ResponseT[models.SystemConfig]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/system-configs/{id} [GET]
func FindSystemConfigByID(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	id := c.Param("id")

	query := &models.SystemConfigQuery{
		ID:             util.NewString(id),
		IncludeDeleted: util.NewBool(true),
		OrgID:          util.NewString(org.ID),
	}

	systemConfig, oErr := services.SystemConfig.FindOne(query, &models.FindOneOptions{})
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200(systemConfig)
}

// DeleteSystemConfig
// @Summary		Delete the system config ID
// @Description	Delete the system config ID
//
// @Tags		System Config
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin			header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer		header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id				path		string		true	"System config ID"
//
// @Success		200				{object}	app.ResponseT[string]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/admin/system-configs/{id} [DELETE]
func DeleteSystemConfig(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()

	id := c.Param("id")

	sysConfig, sErr := services.SystemConfig.FindByID(id, false, nil)
	if sErr != nil {
		log.Error("find system config by ID failed", sErr)
		appG.ResponseAppError(sErr)
		return
	}

	if isAllow := services.SystemConfig.CanUpdateSystemConfig(sysConfig, currentUser); !isAllow {
		appG.Response403(e.FORBIDDEN, "You don't have permission")
		return
	}

	oErr := services.SystemConfig.Delete(sysConfig)
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response201("success")
}
