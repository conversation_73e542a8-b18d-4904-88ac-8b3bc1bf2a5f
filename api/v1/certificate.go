package v1

import (
	"context"
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CreateCertificate
//
//	@Summary		Create a certificate
//	@Description	Create a certificate
//
//	@Tags			certificate
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
//	@Param			Input		body		dto.CertificateRequest	true	"Create certificate input"
//
//	@Success		200			{object}	app.ResponseT[models.Certificate]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/certificates/issue [POST]
func CreateCertificate(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CertificateRequest{}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	if err := appG.BindAndValidateJSON(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	cert, appErr := services.Certificate.FindOneOptions(&models.CertificateQuery{
		CourseCuid: &data.CourseCuid,
		UserID:     &user.ID,
		OrgID:      &org.ID}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if cert != nil {
		appG.Response400(e.CertificateAlreadyExist, "Certificate already exist: "+cert.ID)
		return
	}

	publishCourse, appErr := services.PublishCourse.FindOne(&models.PublishCourseQuery{
		CourseCuid: &data.CourseCuid,
	}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	course, appErr := services.Course.FindOne(&models.CourseQuery{
		ID: &publishCourse.CourseID}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	// init certificate
	certificate := &models.Certificate{
		UserID:      user.ID,
		CourseCuid:  course.Cuid,
		CourseName:  course.Name,
		OrgID:       org.ID,
		OrgSchema:   org.Schema,
		CompletedAt: data.CompletedAt,
		Image:       data.Image,
		Files:       []*models.File{data.File},
	}

	certificateRequest, aErr := services.Certificate.Create(&dto.ClaimCertificateRequest{
		Org:                org,
		User:               user,
		Cert:               certificate,
		Course:             course,
		CheckCertCondition: true,
	})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(certificateRequest)
}

// FindPageCertificate
//
// @Summary		Find list certificates with pagination
// @Description	Find list certificates with pagination
//
// @Tags		certificate
// @Accept		json
// @Produce		json
// @Security		BearerAuth
//
// @Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param			X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
// @Params		options 		query 		models.FindPageOptions 	false  	"Find page options"
// @Param		filter			query		models.CertificateQuery	false	"Find Page Certificates"
//
// @Success		200				{object}	app.ResponseT[dto.ListCertificateResponse]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/certificates [GET]
func FindPageCertificate(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{models.FilesField}
	var query models.CertificateQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	resp := dto.ListCertificateResponse{
		Results:    []*dto.CertificateResponse{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	certificates, pagination, err := services.Certificate.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	publishCourses, pErr := models.Repository.PublishCourse(context.TODO()).FindMany(&models.PublishCourseQuery{
		CourseCuidIn: lo.Map(certificates, func(certificate *models.Certificate, _ int) string {
			return certificate.CourseCuid
		}),
	}, nil)
	if pErr != nil {
		appG.ResponseAppError(e.NewError500(e.CertificateFindFailed, pErr.Error()))
		return
	}

	courses, pErr := models.Repository.Course(context.TODO()).FindMany(&models.CourseQuery{
		IDIn: lo.Map(publishCourses, func(item *models.PublishCourse, _ int) string {
			return item.CourseID
		}),
	}, &models.FindManyOptions{})
	if pErr != nil {
		appG.ResponseAppError(e.NewError500(e.CertificateFindFailed, pErr.Error()))
		return
	}

	enabledByCourseCuid := map[string]bool{}
	for _, course := range courses {
		if course.Props.MintCertNFTSettings != nil {
			enabledByCourseCuid[course.Cuid] = course.Props.MintCertNFTSettings.Enabled
		}
	}

	certificateResponse := lo.Map(certificates, func(certificate *models.Certificate, _ int) *dto.CertificateResponse {
		return &dto.CertificateResponse{
			ID:             certificate.ID,
			UserID:         certificate.UserID,
			CourseName:     certificate.CourseName,
			CompletedAt:    certificate.CompletedAt,
			Files:          certificate.Files,
			MintNFTEnabled: enabledByCourseCuid[certificate.CourseCuid],
			NftTokenID:     certificate.NftTokenID,
			NftTxHash:      certificate.NftTxHash,
			NftNetwork:     certificate.NftNetwork,
			Props:          certificate.Props,
		}
	})

	resp.Results = certificateResponse
	resp.Pagination = pagination
	appG.Response200(resp)
}

func DeleteCertificateById(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")
	if !user.IsSysAdmin() && user.IsOrgAdmin(appG.GetOrg().ID) {
		appG.Response400(e.Organization_owner_required, "sysadmin or org owner required")
		return
	}
	var query = models.CertificateQuery{}

	if user.IsSysAdmin() {
		query.ID = &id
	} else {
		query.ID = &id
		query.OrgID = &appG.GetOrg().ID
	}

	oErr := services.Certificate.Delete(&query)
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200("success")
}

// GetCertificateByID
//
// @Summary		Get Certificate By ID
// @Description	Get Certificate By ID
//
// @Tags		certificate
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101dev.com)
//
// @Param		id			path		string		true	"Certificate ID"
//
// @Success		200				{object}	app.ResponseT[dto.CertificateDetailResponse]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/certificates/{id} [GET]
func GetCertificateByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	certificate, appErr := services.Certificate.FindByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	user, appErr := services.User.FindByID(certificate.UserID, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	publishCourse, appErr := services.PublishCourse.FindOne(&models.PublishCourseQuery{
		CourseCuid: &certificate.CourseCuid,
	}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	org, appErr := services.Organization.FindOne(&models.OrganizationQuery{
		ID: &publishCourse.OrgID,
	}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	course, appErr := services.Course.FindOne(&models.CourseQuery{
		ID: &publishCourse.CourseID}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField, models.LevelsField},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	course.Org = org.ToSimple()
	// total points for all quiz for user
	totalPoint, appErr := services.QuizSubmission.FindTotalPointForUserByCourse(course.Cuid, user.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	enabled := false
	if course.Props.MintCertNFTSettings != nil {
		enabled = course.Props.MintCertNFTSettings.Enabled
	}
	result := &dto.CertificateDetailResponse{
		ID: certificate.ID,
		User: &dto.UserCertificateProfile{
			ID:          user.ID,
			Username:    user.Username,
			DisplayName: user.DisplayName,
			Avatar:      user.Avatar,
			CompletedAt: certificate.CompletedAt,
			TotalPoints: totalPoint,
		},
		Course:         course.Sanitize(),
		Files:          certificate.Files,
		MintNFTEnabled: enabled,
		NftTokenID:     certificate.NftTokenID,
		NftTxHash:      certificate.NftTxHash,
		NftNetwork:     certificate.NftNetwork,
		Props:          certificate.Props,
	}

	appG.Response200(result)
}

// GetCertificateImage
//
// @Summary		Get certificate image by ID
// @Description	Get certificate image by ID
//
// @Tags		certificate
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
// @Param		id				path		string		true	"Certificate ID"
//
// @Success 	200 			{file} 		file 							"Certificate image"
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/certificates/{id}/image [GET]
func GetCertificateImage(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(util.IDParamKey)

	certificate, appErr := services.Certificate.FindByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if certificate.Image == nil {
		appG.Response404(e.CertificateImageNotFound, "Certificate image not found")
		return
	}

	//fileData, appErr := services.Upload.GetFileData(certificate.Image)
	//if appErr != nil {
	//	appG.ResponseAppError(appErr)
	//	return
	//}
	//c.Header("Content-Disposition", "inline; filename="+certificate.Image.Name)
	//c.Data(200, certificate.Image.Mime, fileData)
	c.Redirect(http.StatusFound, certificate.Image.URL)
}

// MintNFTForCertificate
//
// @Summary		Mint NFT for the certificate
// @Description	Mint NFT for the certificate
//
// @Tags		certificate
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
// @Param		id				path		string		true	"Certificate ID"
//
// @Success		200				{object}	app.ResponseT[models.Transaction]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/certificates/{id}/nft [POST]
func MintNFTForCertificate(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param(util.IDParamKey)

	var req dto.MintCertificateNFTRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	certificate, appErr := services.Certificate.FindByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	req.User = user
	req.Certificate = certificate
	certificate, appErr = services.Certificate.MintNFT(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(certificate)
}

// EstimatedMintNFTFees
//
// @Summary		Estimate fees to mint NFT for the certificate
// @Description	Estimate fees to mint NFT for the certificate
//
// @Tags		Certificate
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"Certificate ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/certificates/{id}/nft/fees [GET]
func EstimatedMintNFTFees(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(models.PathParamKeyID)
	certificate, appErr := services.Certificate.FindByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	feesResponse, appErr := services.Certificate.EstimatedMintNFTFees(certificate)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(feesResponse)
}

// IssueCertificate
//
//	@Summary		Issue a certificate
//	@Description	Issue a certificate
//
//	@Tags			certificate
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
//	@Param			Input		body		dto.CertificateRequest	true	"Issue certificate input"
//
//	@Success		200			{object}	app.ResponseT[models.Certificate]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/certificates/issue [POST]
func IssueCertificate(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.IssueCertificateRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	user, appErr := services.User.FindByID(req.UserID, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	publishCourse, appErr := services.PublishCourse.FindOne(&models.PublishCourseQuery{
		CourseCuid: &req.CourseCuid,
	}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	course, appErr := services.Course.FindOne(&models.CourseQuery{
		ID: &publishCourse.CourseID}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	org, appErr := services.Organization.FindByID(publishCourse.OrgID, false, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	cert, appErr := services.Certificate.FindOneOptions(&models.CertificateQuery{
		CourseCuid: &req.CourseCuid,
		UserID:     &user.ID,
		OrgID:      &org.ID}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if cert != nil {
		appG.Response200(cert)
		return
	}

	// init certificate
	certificate := &models.Certificate{
		UserID:      user.ID,
		CourseCuid:  course.Cuid,
		CourseName:  course.Name,
		OrgID:       org.ID,
		OrgSchema:   org.Schema,
		CompletedAt: req.CompletedAt,
		Image:       req.Image,
		Files:       []*models.File{req.File},
	}

	certificateRequest, aErr := services.Certificate.Create(&dto.ClaimCertificateRequest{
		Org:                org,
		User:               user,
		Cert:               certificate,
		Course:             course,
		CheckCertCondition: false,
	})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(certificateRequest)
}
