package v1

import (
	"errors"
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/upload"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UploadFile godoc
//
//	@Summary		Upload files
//	@Description	upload files
//	@Tags			upload
//	@Accept			json
//	@Produce		json
//
//	@Param			Authorization	header		string	false	"Bearer"
//
//	@Param			X-referer		header		string	true	"X-referer"
//
//	@Param			files			formData	file	true	"Files to upload"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/uploads [post]
func UploadFile(c *gin.Context) {
	appG := app.Gin{C: c}

	var userID string
	if user := appG.GetLoggedUser(); user != nil {
		userID = user.ID
	} else {
		userID = ""
	}

	form, err := c.MultipartForm()
	if err != nil {
		log.Warn(err)
		appG.Response400(e.Error_upload_invalid_content_type, nil)
		return
	}

	fileHeaders := form.File["files"]
	if len(fileHeaders) == 0 {
		appG.Response400(e.Error_upload_files_empty, nil)
		return
	}

	files, err := upload.ParseMultipartFiles(fileHeaders)
	if err != nil {
		appG.Response500(e.Error_file_upload_fail, nil)
		return
	}

	public := len(form.Value["public"]) > 0 && form.Value["public"][0] == "true"
	for _, file := range files {
		file.Hash = util.GenerateId()
		file.Public = public
	}

	uploadedFiles, errU := services.Upload.UploadFiles(files, userID, "")
	if errU != nil {
		//appG.Response500(e.ERROR_FILE_UPLOAD_FAIL, nil)
		appG.ResponseAppError(errU)
		return
	}

	appG.Response201(uploadedFiles)
}

// GetFiles godoc
//
//	@Summary		Get uploaded files
//	@Description	get uploaded files
//	@Tags			upload
//	@Accept			json
//	@Produce		json
//
//	@Param			Authorization	header		string	false	"Bearer"
//
//	@Param			X-referer		header		string	true	"X-referer"
//
//	@Param			page			query		int		false	"Page number"
//	@Param			per_page		query		int		false	"Number of items on each page"
//	@Param			sort			query		string	false	"Sorting"
//
//	@Success		200				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/uploads/files [get]
func GetFiles(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}
	files, pagination, err := models.Repository.File.FindPage(&models.FileQuery{}, &options)
	if err != nil {
		appG.Response(http.StatusInternalServerError, e.ERROR, nil)
		return
	}

	appG.Response200(dto.ListFilesResponse{
		Results:    files,
		Pagination: pagination,
	})
}

// GetFile godoc
//
//	@Summary		Get file by ID
//	@Description	get file by ID
//	@Tags			upload
//	@Accept			json
//	@Produce		json
//
//	@Param			Authorization	header		string	false	"Bearer"
//
//	@Param			X-referer		header		string	true	"X-referer"
//
//	@Param			id				path		string	true	"File ID"
//
//	@Success		200				{object}	app.Response
//	@Failure		404				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/uploads/files/{id} [get]
func GetFile(c *gin.Context) {
	appG := app.Gin{C: c}
	fileID := c.Param("id")
	file, err := models.Repository.File.FindByID(fileID, nil)
	if err != nil {
		switch {
		case errors.Is(err, gorm.ErrRecordNotFound):
			appG.Response404(e.Error_file_not_found, nil)
			return
		default:
			appG.Response500(e.ERROR, nil)
			return
		}
	}

	appG.Response200(map[string]interface{}{
		"file": file,
	})
}

// DeleteFile godoc
//
//	@Summary		Delete file by ID
//	@Description	delete file by ID
//	@Tags			upload
//	@Accept			json
//	@Produce		json
//
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			X-referer		header		string	true	"X-referer"
//
//	@Param			id				path		string	true	"File ID"
//
//	@Success		200				{object}	app.Response
//	@Failure		404				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/uploads/files/{id} [delete]
func DeleteFile(c *gin.Context) {
	appG := app.Gin{C: c}
	fileID := c.Param("id")
	file, err := models.Repository.File.FindByID(fileID, nil)
	if err != nil {
		switch {
		case errors.Is(err, gorm.ErrRecordNotFound):
			appG.Response404(e.Error_file_not_found, nil)
			return
		default:
			appG.Response500(e.ERROR, nil)
			return
		}
	}

	if err := services.Upload.DeleteFiles([]*models.File{file}); err != nil {
		//appG.Response500(e.ERROR_FILE_DELETE_FAIL, nil)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(dto.DeleteFileResponse{
		Success: true,
	})
}

// GetStatusVideoUploadFromBunny godoc
//
//	@Summary		Get status video upload from bunny
//	@Description	get status video upload from bunny
//	@Tags			upload
//	@Accept			json
//	@Produce		json
//
//	@Param			Authorization	header		string	false	"Bearer"
//
//	@Param			X-referer		header		string	true	"X-referer"
//
//	@Param			id				path		string	true	"File ID"
//
//	@Success		200				{object}	app.Response
//	@Failure		404				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/uploads/videos/{id}/status [get]
func GetStatusVideoUploadFromBunny(c *gin.Context) {
	appG := app.Gin{C: c}
	fileID := c.Param("id")
	file, err := models.Repository.File.FindByID(fileID, nil)
	if err != nil {
		switch {
		case errors.Is(err, gorm.ErrRecordNotFound):
			appG.Response404(e.Error_file_not_found, nil)
			return
		default:
			appG.Response500(e.ERROR, nil)
			return
		}
	}

	status, appErr := services.Upload.
		GetStatusUploadFromBunny(file)
	if appErr != nil {
		log.Error("Api::GetStatusUploadFromBunny failed ", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(dto.StatusVideoUploadResponse{
		Status: status,
	})
}
