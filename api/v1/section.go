package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Create section
// @Description	Create section
//
// @Tags			section
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.LessonContentRequest	true	"Create section input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sections [POST]
func CreateSection(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CreateSectionRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	if course, cErr := services.Course.FindById(data.CourseID, false, nil); cErr != nil {
		appG.ResponseAppError(cErr)
		return
	} else {
		data.Course = course
	}

	data.OrgID = appG.GetOrg().ID
	section, cErr := services.Section.Create(appG.GetLoggedUser(), &data, false)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(section)
}

// @Summary		Update section
// @Description	Update section
//
// @Tags			section
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"id"
//
// @Param			Input			body		dto.UpdateSectionRequest	true	"Update section request"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sections/{id} [PUT]
func UpdateSection(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.UpdateSectionRequest{}
	id := c.Param("id")
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	query := &models.SectionQuery{
		ID:             util.NewString(id),
		IncludeDeleted: util.NewBool(false),
	}
	section, fErr := services.Section.FindOne(query, nil)
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	course, cErr := services.Course.FindById(section.CourseID, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	data.User = appG.GetLoggedUser()
	data.Course = course
	_, cErr = services.Section.Update(section, &data)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	updatedSection, fErr := services.Section.FindOne(query, nil)
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	appG.Response200(updatedSection)
}

// @Summary		Find section by ID
// @Description	Find section by ID
//
// @Tags			section
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sections/{id} [GET]
func GetSection(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	query := models.SectionQuery{
		ID:             util.NewString(id),
		IncludeDeleted: util.NewBool(false),
	}
	section, cErr := services.Section.FindOne(&query, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(section)
}

// @Summary		FindPage section
// @Description	FindPage section
//
// @Tags			section
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Params			Input query																																																																																					models.FindPageOptions "find page options"																																																		query 		models.FindPageOptions 		 false  	"find page options"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sections [GET]
func FindSection(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.SectionQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	resp := dto.ListSectionResponse{
		Results:    []*models.Section{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	sections, pagination, err := services.Section.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(sections, func(c *models.Section, _ int) *models.Section {
		return c
	})

	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Delete section
// @Description	Delete section
//
// @Tags			section
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"section id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sections/{id} [DELETE]
func DeleteSection(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")
	section, fErr := services.Section.FindOne(&models.SectionQuery{ID: util.NewString(id)}, nil)
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	course, appErr := services.Course.FindOne(&models.CourseQuery{
		ID: &section.CourseID}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	// check permission partner
	if perErr := services.Course.CanUpdateCourse(course, user, models.CoursePerUpdate); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	if appErr := services.Section.CheckLessonIfExistInCondition(course, section.UID); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if dErr := services.Section.Delete(section); dErr != nil {
		appG.ResponseAppError(dErr)
		return
	}
	appG.Response200("success")
}

// @Summary		Bulk Update section
// @Description	Bulk Update section
//
// @Tags			section
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			Input			body		dto.BulkUpdateSectionRequest	true	"Update section request"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sections/bulk [PUT]
func BulkUpdateSection(c *gin.Context) {
	appG := app.Gin{C: c}
	reqBody := dto.BulkUpdateSectionRequest{}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	sections, uErr := services.Section.BulkUpdate(&reqBody, appG.GetLoggedUser())
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200(sections)
}
