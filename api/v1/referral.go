package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// Find referrals by user
//
// @Summary		 Find referrals by user
// @Description	 Find referrals by user
//
// @Tags			Referral
// @Accept			json
// @Produce		json
// @Security		BearerAuth
//
// @Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param			X-referrer	header		string					true	"X-referrer"	default(openedu101.com)
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			query		query		models.ReferralQuery	true	"query"
//
// @Success		201				{object}	app.Response
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/users/me/referrals [GET]
func FindReferralByUser(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.ReferralQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	resp := dto.ListReferralResponse{
		Results:    []*models.Referral{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	query.Relative2UserID = util.NewString(user.ID)
	referrals, pagination, cErr := services.Referral.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = referrals
	resp.Pagination = pagination
	appG.Response200(resp)
}

// Find referrals
//
// @Summary		 Find referrals
// @Description	 Find referrals
//
// @Tags			Referral
// @Accept			json
// @Produce		json
// @Security		BearerAuth
//
// @Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param			X-referrer	header		string					true	"X-referrer"	default(openedu101.com)
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			query		query		models.ReferralQuery	true	"query"
//
// @Success		201				{object}	app.Response
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/referrals [GET]
func FindReferrals(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.ReferralQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if query.CampaignOwnerID == nil {
		query.CampaignOwnerID = util.NewString(appG.GetLoggedUser().ID)
	}

	resp := dto.ListReferralResponse{
		Results:    []*models.Referral{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	referrals, pagination, cErr := services.Referral.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = referrals
	resp.Pagination = pagination
	appG.Response200(resp)
}

// Find referrals
//
// @Summary		 Find referrals
// @Description	 Find referrals
//
// @Tags			Referral
// @Accept			json
// @Produce		json
// @Security		BearerAuth
//
// @Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param			X-referrer	header		string					true	"X-referrer"	default(openedu101.com)
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			query		query		models.ReferralQuery	true	"query"
//
// @Success		201				{object}	app.Response
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1//referrals/summaries [GET]
func ReferralSummaryReport(c *gin.Context) {
	appG := app.Gin{C: c}
	var request models.ReferralSummaryReport
	if err := appG.C.ShouldBindQuery(&request); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	report, rErr := services.Referral.SummaryReport(&request)
	if rErr != nil {
		appG.ResponseAppError(rErr)
		return
	}

	appG.Response200(report)
}

// Referral Report By User
//
// @Summary		 Referral Report By User
// @Description	 Referral Report By User
//
// @Tags			Referral
// @Accept			json
// @Produce		json
// @Security		BearerAuth
//
// @Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param			X-referrer	header		string					true	"X-referrer"	default(openedu101.com)
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			query		query		models.ReferralReportByUserQuery	true	"query"
//
// @Success		201				{object}	app.Response
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/referrals/user-reports [GET]
func ReferralReportByUsers(c *gin.Context) {
	appG := app.Gin{C: c}
	var query models.ReferralReportByUserQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if query.UserID == nil {
		appG.Response400(e.INVALID_PARAMS, "user_id required")
		return
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	resp := dto.ListReferralReportByUser{
		Results:    []*models.ReferralReportByUser{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}
	reports, pagination, rErr := services.Referral.GetUserSummaryReport(*query.UserID, &query, &options)
	if rErr != nil {
		appG.ResponseAppError(rErr)
		return
	}

	resp.Results = reports
	resp.Pagination = pagination
	appG.Response200(resp)
}
