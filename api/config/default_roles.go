package bootstrap

import "openedu-core/models"

var defaultRoles = [...]*models.Role{
	{
		Name:        "System Admin",
		Description: "System administrator who can access everything in system",
		ID:          models.SystemAdminRoleType,
	},
	{
		Name:        "Admin",
		Description: "OpenEdu Administrator who can access OpenEdu admin center",
		ID:          models.AdminRoleType,
	},
	{
		Name:        "Moderator",
		Description: "OpenEdu moderator who can access OpenEdu admin center",
		ID:          models.ModeratorRoleType,
	},
	{
		Name:        "Organization admin",
		Description: "Organization admin who can access course management center",
		ID:          models.OrgAdminRoleType,
	},
	{
		Name:        "Organization moderator",
		Description: "Organization moderator who can access course management center",
		ID:          models.OrgModeratorRoleType,
	},
	{
		Name:        "Organization moderator 2",
		Description: "Organization moderator 2 who can access course management center",
		ID:          models.OrgModerator2RoleType,
	},
	{
		Name:        "Organization editor",
		Description: "Organization editor who can access blog management center",
		ID:          models.OrgEditorRoleType,
	},
	{
		Name:        "Organization writer",
		Description: "Organization writer who can access blog management center",
		ID:          models.OrgWriterRoleType,
	},
	{
		Name:        "Creator",
		Description: "Creator",
		ID:          models.PartnerRoleType,
	},
	{
		Name:        "Learner",
		Description: "Learner",
		ID:          models.LearnerRoleType,
	},

	{
		Name:        "Guest",
		Description: "The guest access the system with public data",
		ID:          models.GuestRoleType,
	},
}

func MigrateRoles() error {
	roles := []*models.Role{}
	for _, rl := range defaultRoles {
		role, err := models.Repository.Role.CreateOrUpdate(rl)
		if err != nil {
			return err
		}
		roles = append(roles, role)
	}

	return nil
}
