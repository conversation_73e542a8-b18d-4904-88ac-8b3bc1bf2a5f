global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'openedu-core'
    static_configs:
      - targets: ['localhost:8000']
        labels:
          env: 'dev'
          service: 'core'
      - targets: ['localhost:8001']
        labels:
          env: 'staging'
          service: 'core'
      - targets: ['localhost:8002']
        labels:
          env: 'demo'
          service: 'core'
      - targets: ['localhost:8003']
        labels:
          env: 'test'
          service: 'core'
      - targets: ['localhost:8011']
        labels:
          env: 'phuong'
          service: 'core'

  - job_name: 'openedu-communication'
    static_configs:
      - targets: ['localhost:8100']
        labels:
          env: 'dev'
          service: 'communication'
      - targets: ['localhost:8101']
        labels:
          env: 'staging'
          service: 'communication'
      - targets: ['localhost:8102']
        labels:
          env: 'demo'
          service: 'communication'
      - targets: ['localhost:8103']
        labels:
          env: 'test'
          service: 'communication'
      - targets: ['localhost:8111']
        labels:
          env: 'phuong'
          service: 'core'
