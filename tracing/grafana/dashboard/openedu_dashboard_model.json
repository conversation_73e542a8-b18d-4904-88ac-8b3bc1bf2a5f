{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2, "links": [], "panels": [{"datasource": {"default": true, "type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "Down"}, "1": {"color": "green", "index": 0, "text": "Up"}}, "type": "value"}, {"options": {"match": "null", "result": {"color": "red", "index": 2, "text": "Down"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "service_health_status{service=\"$service\", stage=\"$stage\"}", "instant": true, "range": false, "refId": "A"}], "title": "Service Status", "type": "stat"}, {"datasource": {"default": true, "type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepAfter", "lineStyle": {"fill": "solid"}, "lineWidth": 0, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "bool_on_off"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "timezone": ["browser"], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "code", "expr": "service_health_status{service=\"$service\", stage=\"$stage\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Service Status Timeline", "type": "timeseries"}, {"datasource": {"default": true, "type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dtdhms"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "service_uptime_seconds{service=\"$service\", stage=\"$stage\"}", "instant": true, "range": false, "refId": "A"}], "title": "Uptime", "transformations": [{"id": "formatTime", "options": {"fields": {"Value": {"format": "days hours minutes seconds"}}, "unit": "humanize"}}], "type": "stat"}, {"datasource": {"default": true, "type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 500}, {"color": "red", "value": 1000}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "service_goroutine_count{service=\"$service\", stage=\"$stage\"}", "instant": true, "range": false, "refId": "A"}], "title": "Number Of Goroutines", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.1", "targets": [{"editorMode": "code", "expr": "service_memory_usage_bytes{service=\"$service\", stage=\"$stage\", type=\"rss\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "RSS Memory", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "service_memory_usage_bytes{service=\"$service\", stage=\"$stage\", type=\"system_total\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "System Total Memory", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "service_memory_usage_bytes{service=\"$service\", stage=\"$stage\", type=\"system_used\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "System Used Memory", "range": true, "refId": "C"}], "title": "Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "service_cpu_usage_percent{service=\"$service\", stage=\"$stage\"}", "legendFormat": "CPU Usage Percent", "range": true, "refId": "A"}], "title": "CPU Utilization", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Connections", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 12}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.1", "targets": [{"editorMode": "code", "expr": "sqldb_connections{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", state=\"idle\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Idle Connections", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sqldb_connections{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", state=\"in_use\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "In-Use Connections", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sqldb_connections{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", state=\"total\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Total Connections", "range": true, "refId": "C"}], "title": "Database Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Duration (seconds)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "orange", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 12}, "id": 12, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.1", "targets": [{"editorMode": "code", "expr": "rate(sqldb_query_duration_seconds_sum{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"query\"}[5m]) / rate(sqldb_query_duration_seconds_count{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"query\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Query Average Duration", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "rate(sqldb_query_duration_seconds_sum{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"update\"}[5m]) / rate(sqldb_query_duration_seconds_count{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"update\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Update Average Duration", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "rate(sqldb_query_duration_seconds_sum{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"delete\"}[5m]) / rate(sqldb_query_duration_seconds_count{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"delete\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Delete Average Duration", "range": true, "refId": "C"}], "title": "Database Query Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Operation Count", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 21}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.1", "targets": [{"editorMode": "code", "expr": "sqldb_query_duration_seconds_count{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"query\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Query Count", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sqldb_query_duration_seconds_count{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"update\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Update Count", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sqldb_query_duration_seconds_count{db_type=\"postgres\", service=\"$service\", stage=\"$stage\", operation=\"delete\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Delete Count", "range": true, "refId": "C"}], "title": "Database Operation Counts", "type": "timeseries"}], "preload": false, "refresh": "auto", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "core", "value": "core"}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(service)", "includeAll": false, "name": "service", "options": [], "query": {"qryType": 1, "query": "label_values(service)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "test", "value": "test"}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(stage)", "includeAll": false, "name": "stage", "options": [], "query": {"qryType": 1, "query": "label_values(stage)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "OpenEdu Monitoring Dashboard", "uid": "openedu-monitoring-dashboard", "version": 39, "weekStart": ""}