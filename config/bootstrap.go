package config

import (
	"context"
	"errors"
	"fmt"
	bootstrap "openedu-core/api/config"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"runtime"

	"github.com/samber/lo"

	"github.com/shopspring/decimal"
	"golang.org/x/sync/semaphore"
	"gorm.io/gorm"
)

const (
	AIGovEmail = "Pho cap AI <<EMAIL>>"
)

func InitSystem() {
	if err := bootstrap.MigrateRoles(); err != nil {
		log.Fatalf("failed to migrate roles: %v", err)
	}

	if setting.AppSetting.InitDefaultRole {
		if err := bootstrap.MigratePermissions(); err != nil {
			log.Fatalf("failed to migrate permission: %v", err)
		} else {
			log.Debug("[info] permission migrations is successfully")
		}
	}

	master := InitDefaultAccount()

	vbiOrg := InitVbiOrg(master)
	aiGovOrg := InitAIGovernmentOrg(master)

	openeduOrg := InitOpenEduOrg(master)

	InitUsers(vbiOrg)
	InitUsers(aiGovOrg)

	InitUsers(openeduOrg)

	InitSystemConfigs()

	//InitSystemEmailTemplates(openeduOrg)

	InitPageConfig()

	if err := InitDefaultForms(openeduOrg); err != nil {
		log.Fatalf("failed to init default forms: %v", err)
	}

	InitFreePlan(master)
	InitInternalPlan(master)

	InitAISubscription(openeduOrg)

	InitDefaultCryptoPaymentMethods()

	InitLevels()
	InitDefaultAIModels()
	InitCategoryPrompt()
	InitDefaultInviteUserReferralSetting()
}

func InitDefaultAccount() *models.User {
	master, _ := models.Repository.Role.FindById(models.SystemAdminRoleType)
	masterUser, _ := models.Repository.User.FindByEmailWithOpts(setting.AppSetting.SystemAdminAccount, nil)
	log.Debug("Master User ", masterUser, master)
	if masterUser == nil {
		userData := &models.User{
			Email:    setting.AppSetting.SystemAdminAccount,
			Username: "administrator",
			Password: setting.AppSetting.SystemAdminPassword,
			Active:   true,
		}

		u, _ := services.User.Create(userData)
		masterUser = u
	}
	userRoleOrg := models.UserRoleOrg{
		UserID: masterUser.ID,
		RoleID: master.ID,
		OrgID:  "",
	}

	if uErr := models.Repository.User.UpdateRoles(&userRoleOrg); uErr != nil {
		log.Fatalf("Failed to update role org for default master account: %v", uErr)
	}

	return masterUser
}

func InitLevels() {
	existingLevels, err := models.Repository.Category.FindMany(&models.CategoryQuery{
		Type: util.NewT(models.TypeLevel),
	}, &models.FindManyOptions{})
	if err != nil {
		log.Fatalf("Failed to get list levels from DB: %v", err)
	}

	if len(existingLevels) > 0 {
		log.Infof("Course levels already initialized. Skip initializing...")
		return
	}

	levels := lo.Map(models.DefaultCourseLevels, func(level *models.Category, _ int) *models.Category {
		return &models.Category{
			Name:     level.Name,
			Active:   level.Active,
			ParentID: level.ParentID,
			Order:    level.Order,
			Type:     level.Type,
		}
	})
	if err = models.Repository.Category.UpsertMany(levels, nil); err != nil {
		log.Fatalf("Failed to init list course levels: %v", err)
	}
}

func InitCategoryPrompt() {
	existingCategories, err := models.Repository.Category.FindMany(&models.CategoryQuery{
		Type: util.NewT(models.TypeCategoryPrompt),
	}, &models.FindManyOptions{})
	if err != nil {
		log.Fatalf("Failed to get list categories prompt from DB: %v", err)
	}

	if len(existingCategories) > 0 {
		log.Infof("Categories prompts already initialized. Skip initializing...")
		return
	}

	cates := lo.Map(models.DefaultCategoryPrompt, func(cate *models.Category, _ int) *models.Category {
		return &models.Category{
			Name:   cate.Name,
			Active: cate.Active,
			Order:  cate.Order,
			Type:   cate.Type,
		}
	})
	if err = models.Repository.Category.UpsertMany(cates, nil); err != nil {
		log.Fatalf("Failed to init list categories prompt levels: %v", err)
	}
}

func InitUsers(org *models.Organization) {
	admin, _ := models.Repository.Role.FindById(models.AdminRoleType)
	mod, _ := models.Repository.Role.FindById(models.ModeratorRoleType)
	orgAdmin, _ := models.Repository.Role.FindById(models.OrgAdminRoleType)
	orgMod, _ := models.Repository.Role.FindById(models.OrgModeratorRoleType)
	creator, _ := models.Repository.Role.FindById(models.PartnerRoleType)
	learner, _ := models.Repository.Role.FindById(models.LearnerRoleType)
	orgWriter, _ := models.Repository.Role.FindById(models.OrgWriterRoleType)
	orgEditor, _ := models.Repository.Role.FindById(models.OrgEditorRoleType)

	//admin
	createUser(admin, "<EMAIL>", "admin", "")
	//mod
	createUser(mod, "<EMAIL>", "mod", "")
	//org admin
	createUser(orgAdmin, "<EMAIL>", "orgadmin", org.ID)
	//org mod
	createUser(orgMod, "<EMAIL>", "orgmod", org.ID)
	//instructor
	createUser(creator, "<EMAIL>", "creator", org.ID)
	//learner
	createUser(learner, "<EMAIL>", "learner", org.ID)
	//writer
	createUser(orgWriter, "<EMAIL>", "orgwriter", org.ID)
	//editor
	createUser(orgEditor, "<EMAIL>", "orgeditor", org.ID)
}

func createUser(role *models.Role, email string, username string, orgId string) {
	saved, _ := models.Repository.User.FindByEmailWithOpts(email, nil)
	if saved == nil {
		userData := &models.User{
			Email:    email,
			Username: username,
			Password: "Open12@4",
			Active:   true,
		}
		u, _ := services.User.Create(userData)
		saved = u
	}
	userRoleOrg := models.UserRoleOrg{
		UserID: saved.ID,
		RoleID: role.ID,
		OrgID:  orgId,
	}
	if uErr := models.Repository.User.UpdateRoles(&userRoleOrg); uErr != nil {
		log.Fatalf("Failed to update role org for default user: %v", uErr)
	}
}

func InitOpenEduOrg(user *models.User) *models.Organization {
	schema := models.SchemaOpenEdu
	saved, _ := models.Repository.Organization.FindOne(&models.OrganizationQuery{Schema: util.NewString(schema)}, nil)
	if saved == nil {
		org, appErr := services.Organization.Create(&dto.CreateOrgRequest{
			Name:       "Openedu Academy",
			Domain:     setting.AppSetting.BaseDomain,
			AltDomain:  &setting.AppSetting.BaseDomain,
			Schema:     schema,
			Email:      &user.Email,
			CreateByID: &user.ID,
		}, false)
		if appErr != nil {
			log.Fatalf("Init default OpenEdu org failed: %v", appErr)
		}

		saved = org
	}

	return saved
}

func InitVbiOrg(user *models.User) *models.Organization {
	schema := models.SchemaVBI
	saved, _ := models.Repository.Organization.FindOne(&models.OrganizationQuery{Schema: util.NewString(schema)}, nil)
	if saved == nil {
		org, appErr := services.Organization.Create(&dto.CreateOrgRequest{
			Name:       "VBI Academy",
			Domain:     "vbi." + setting.AppSetting.BaseDomain,
			Schema:     schema,
			AltDomain:  util.NewString("vbi." + setting.AppSetting.BaseDomain),
			Email:      &user.Email,
			CreateByID: &user.ID,
		}, false)
		if appErr != nil {
			log.Fatalf("Init default VBI org failed: %v", appErr)
		}
		saved = org
	}

	return saved
}

func InitSystemConfigs() {
	maxWorkers := runtime.GOMAXPROCS(0)
	sem := semaphore.NewWeighted(int64(maxWorkers))
	ctx := context.Background()
	for _, cfg := range GetDefaultSystemConfigsParams() {
		if err := sem.Acquire(ctx, 1); err != nil {
			log.Fatalf("Acquire semaphore to init config %s failed: %v", cfg.Key, err)
		}

		go func(cfg *InitConfigParams) {
			defer sem.Release(1)
			if err := models.InitConfig(cfg.Key, cfg.Val, cfg.Type, nil, nil); err != nil {
				log.Fatalf("Init default config key %s failed: %v", cfg.Key, err)
			}
		}(&cfg)
	}

	if err := sem.Acquire(ctx, int64(maxWorkers)); err != nil {
		log.Fatalf("Acquire semaphore to init default configs failed: %v", err)
	}

	if err := models.InitConfig(models.CertificateTemplateDefault, "", models.String, nil, nil); err != nil {
		log.Fatalf("Init default record certificate template default: %v", err)
	}

	// Organization configs
	orgs, err := models.Repository.Organization.FindMany(nil, &models.FindManyOptions{
		Preloads: []string{models.UserField},
	})
	if err != nil {
		log.Fatalf("failed to init system config: %v", err)
	}
	InitOrganizationConfig(orgs)
}

func InitOrganizationConfig(orgs []*models.Organization) {
	for _, org := range orgs {
		if appErr := services.Organization.InitOrgConfigs(org.User, org); appErr != nil {
			log.Fatalf("Init config for org %s failed: %v", org.ID, appErr)
		}
	}
}

func InitDefaultForms(openeduOrg *models.Organization) error {
	orgs, oErr := models.Repository.Organization.FindMany(&models.OrganizationQuery{}, &models.FindManyOptions{})
	if oErr != nil {
		return oErr
	}

	if err := initRegisterOrgForm(openeduOrg); err != nil {
		return err
	}
	if err := initNewUserForm(openeduOrg); err != nil {
		return err
	}
	if err := initRegisterCreatorForms(orgs); err != nil {
		return err
	}
	if err := initRegisterWriterForms(orgs); err != nil {
		return err
	}
	if err := initContactOrgForm(orgs); err != nil {
		return err
	}
	if err := initAIGovernmentFormTemplate(); err != nil {
		return err
	}

	if err := initEmptyForm(); err != nil {
		return err
	}
	return nil
}

func initRegisterOrgForm(vbiOrg *models.Organization) error {
	// check whether register organization form exists
	query := &models.FormQuery{
		Event:          util.NewT(models.FormEventRegisterOrg),
		IncludeDeleted: util.NewBool(false),
	}
	if _, err := models.Repository.Form.FindOne(query, nil); err != nil {
		// register organization form not exists, create new
		if errors.Is(err, gorm.ErrRecordNotFound) {
			form := models.GetDefaultRegisterOrgForm()
			form.OrgID = vbiOrg.ID
			return createForm(form)
		}
		return err
	}
	return nil
}

func initNewUserForm(org *models.Organization) error {
	// check whether register organization form exists
	query := &models.FormQuery{
		Event:          util.NewT(models.FormEventNewUser),
		IncludeDeleted: util.NewBool(false),
	}
	if _, err := models.Repository.Form.FindOne(query, nil); err != nil {
		// register organization form not exists, create new
		if errors.Is(err, gorm.ErrRecordNotFound) {
			form := models.GetDefaultNewUserForm()
			form.OrgID = org.ID
			return createForm(form)
		}
		return err
	}
	return nil
}

func initRegisterCreatorForms(orgs []*models.Organization) error {
	for _, org := range orgs {
		query := &models.FormQuery{
			OrgID:          &org.ID,
			Event:          util.NewT(models.FormEventRegisterCreator),
			IncludeDeleted: util.NewBool(false),
		}
		if _, err := models.Repository.Form.FindOne(query, nil); err != nil {
			// register instructor form not exists, create new
			if errors.Is(err, gorm.ErrRecordNotFound) {
				form := models.GetDefaultRegisterCreatorForm()
				form.OrgID = org.ID
				if cErr := createForm(form); cErr != nil {
					return cErr
				}
			} else {
				return err
			}
		}
	}

	return nil
}

func initRegisterWriterForms(orgs []*models.Organization) error {
	for _, org := range orgs {
		query := &models.FormQuery{
			OrgID:          &org.ID,
			Event:          util.NewT(models.FormEventRegisterWriter),
			IncludeDeleted: util.NewBool(false),
		}
		if _, err := models.Repository.Form.FindOne(query, nil); err != nil {
			// register instructor form not exists, create new
			if errors.Is(err, gorm.ErrRecordNotFound) {
				form := models.GetDefaultRegisterWriterForm()
				form.OrgID = org.ID
				if cErr := createForm(form); cErr != nil {
					return cErr
				}
			} else {
				return err
			}
		}
	}

	return nil
}

func initContactOrgForm(orgs []*models.Organization) error {
	for _, org := range orgs {
		query := &models.FormQuery{
			OrgID:          &org.ID,
			Event:          util.NewT(models.FormEventContactOrg),
			IncludeDeleted: util.NewBool(false),
		}
		if _, err := models.Repository.Form.FindOne(query, nil); err != nil {
			// contact org form not exists, create new
			if errors.Is(err, gorm.ErrRecordNotFound) {
				form := models.GetDefaultContactOrgForm()
				form.OrgID = org.ID
				if cErr := createForm(form); cErr != nil {
					return cErr
				}
			} else {
				return err
			}
		}
	}

	return nil
}

func initEmptyForm() error {
	query := &models.FormQuery{
		Event:          util.NewT(models.FormEventEmpty),
		IncludeDeleted: util.NewBool(false),
	}
	if _, err := models.Repository.Form.FindOne(query, nil); err != nil {
		// contact org form not exists, create new
		if errors.Is(err, gorm.ErrRecordNotFound) {
			form := models.GetDefaultEmptyForm()
			if cErr := createForm(form); cErr != nil {
				return cErr
			}
		} else {
			return err
		}
	}
	return nil
}

func initAIGovernmentFormTemplate() error {
	query := &models.FormQuery{
		Event:          util.NewT(models.FormEventAIGovernment),
		IncludeDeleted: util.NewBool(false),
	}
	if _, err := models.Repository.Form.FindOne(query, nil); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			form := models.GetDefaultAIGovernmentForm()
			form.IsTemplate = true
			if cErr := createForm(form); cErr != nil {
				return cErr
			}
		} else {
			return err
		}
	}

	return nil
}

func createForm(form *models.Form) error {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := models.Repository.Form.Create(form, tx); err != nil {
		tx.Rollback()
		return err
	}

	for idx, question := range form.Questions {
		if question.UID == "" {
			question.UID = util.GenerateId()
		}
		question.FormID = form.ID
		question.Order = idx
		for subQuesIdx, subQuestion := range question.SubQuestions {
			if subQuestion.UID == "" {
				subQuestion.UID = util.GenerateId()
			}
			subQuestion.FormID = form.ID
			subQuestion.Order = subQuesIdx
		}

		for _, option := range question.Options {
			if option.UID == "" {
				option.UID = util.GenerateId()
			}
		}
	}
	if err := models.Repository.FormQuestion.CreateMany(form.Questions, tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

func InitPageConfig() {
	actions := []string{"create", "read", "update", "delete"}
	initPageConfig := []*dto.PageConfigRequest{
		{
			ID:          "create",
			Name:        "create",
			Type:        "action",
			Description: util.NewString("can create"),
		},
		{
			ID:          "read",
			Name:        "read",
			Type:        "action",
			Description: util.NewString("can read"),
		},
		{
			ID:          "update",
			Name:        "update",
			Type:        "action",
			Description: util.NewString("can update"),
		},
		{
			ID:          "delete",
			Name:        "delete",
			Type:        "action",
			Description: util.NewString("can delete"),
		},
		{
			ID:          "course",
			Name:        "course",
			Type:        "entity",
			Actions:     actions,
			Description: util.NewString("course"),
		},
		{
			ID:          "section",
			Name:        "section",
			Type:        "entity",
			Actions:     actions,
			Description: util.NewString("section"),
		},
		{
			ID:          "lesson",
			Name:        "lesson",
			Type:        "entity",
			Actions:     actions,
			Description: util.NewString("lesson"),
		},
	}
	for _, pageCfg := range initPageConfig {
		if _, err := models.Repository.PageConfig.FindOne(&models.PageConfigQuery{ID: util.NewString(pageCfg.ID)}, nil); err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				if _, appErr := services.PageConfig.Create(pageCfg); appErr != nil {
					log.Fatalf("Init page config failed: %v", appErr)
				}
			} else {
				log.Fatalf("Init page config failed: %v", err)
			}
		}
	}
}

func InitFreePlan(user *models.User) {
	plan, err := models.Repository.PricingPlan.FindOne(&models.PricingPlanQuery{
		Tier: util.NewT(models.FreePlanTier),
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Fatalf("Check free pricing plan exist failed: %v", err)
		return
	}

	if plan != nil {
		log.Infof("Internal pricing plan already exist. Skip initializing.")
		return
	}

	aiModels, err := models.Repository.AIModel.FindMany(&models.AIModelQuery{
		Enabled:        util.NewBool(true),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindManyOptions{})
	if err != nil {
		log.Fatalf("Get list AI models to initialize free pricing plan failed: %v", err)
		return
	}

	var aiLimitModels []*models.AILimitModel
	for _, m := range aiModels {
		requestLimit := int64(0)
		balanceLimit := decimal.NewFromInt(0)
		switch m.Name {
		case string(models.ModelGPT4oMini),
			string(models.ModelGemini2):
			requestLimit = 621
			balanceLimit = decimal.NewFromInt(1)
			break
		case string(models.ModelClaude3Sonnet):
		case string(models.ModelO3Mini):
		case string(models.ModelGPT4o):
		case string(models.ModelMetaLlama3):
			break
		}
		aiLimitModels = append(aiLimitModels, &models.AILimitModel{
			AiModelID:     m.ID,
			ModelName:     m.Name,
			RequestAmount: requestLimit,
			BalanceAmount: balanceLimit,
		})
	}
	plan = &models.PricingPlan{
		Tier:         models.FreePlanTier,
		Name:         "Free",
		Description:  "Free",
		MonthlyPrice: models.PlanPrice{},
		AnnualPrice:  models.PlanPrice{},
		Enable:       true,
		Order:        1,
		Period:       0,
		PeriodUnit:   models.TimePeriodUnLimit,
		Cycle:        models.PlanCycleOverall,
		AiLimitation: models.AiLimitation{
			Cycle:     models.PlanCycleMonthly,
			Type:      models.AiFeeTypeBalance,
			Analytics: true,
			Models:    aiLimitModels,
		},
		UserID: user.ID,
	}

	if err = models.Repository.PricingPlan.Create(plan, nil); err != nil {
		log.Fatalf("Create free pricing plan failed: %v", err)
	}
	return
}

func InitInternalPlan(user *models.User) {
	plan, err := models.Repository.PricingPlan.FindOne(&models.PricingPlanQuery{
		Tier: util.NewT(models.InternalPlanTier),
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Fatalf("Check internal pricing plan exist failed: %v", err)
		return
	}

	if plan != nil {
		log.Infof("Internal pricing plan already exist. Skip initializing.")
		return
	}

	aiModels, err := models.Repository.AIModel.FindMany(&models.AIModelQuery{
		Enabled:        util.NewBool(true),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindManyOptions{})
	if err != nil {
		log.Fatalf("Get list AI models to initialize interal pricing plan failed: %v", err)
		return
	}

	var aiLimitModels []*models.AILimitModel
	for _, m := range aiModels {
		aiLimitModels = append(aiLimitModels, &models.AILimitModel{
			ModelName:     m.Name,
			RequestAmount: util.DefaultRequestLimit,
			BalanceAmount: decimal.NewFromInt(util.DefaultWhitelistBalanceLimit),
		})

		requestLimit := int64(0)
		balanceLimit := decimal.NewFromInt(0)
		switch m.Name {
		case string(models.ModelGPT4oMini):
			requestLimit = 2500
			balanceLimit = decimal.NewFromInt(util.DefaultWhitelistBalanceLimit)
			break
		case string(models.ModelClaude3Sonnet):
			requestLimit = 147
			balanceLimit = decimal.NewFromInt(util.DefaultWhitelistBalanceLimit)
			break
		case string(models.ModelGPT4o):
			requestLimit = 185
			balanceLimit = decimal.NewFromInt(util.DefaultWhitelistBalanceLimit)
			break
		case string(models.ModelMetaLlama3):
			requestLimit = 455
			balanceLimit = decimal.NewFromInt(util.DefaultWhitelistBalanceLimit)
			break
		case string(models.ModelGemini2):
			requestLimit = 250
			balanceLimit = decimal.NewFromInt(util.DefaultWhitelistBalanceLimit)
			break
		}
		aiLimitModels = append(aiLimitModels, &models.AILimitModel{
			AiModelID:     m.ID,
			ModelName:     m.Name,
			RequestAmount: requestLimit,
			BalanceAmount: balanceLimit,
		})
	}
	plan = &models.PricingPlan{
		Tier:         models.InternalPlanTier,
		Name:         "Internal",
		Description:  "Internal",
		MonthlyPrice: models.PlanPrice{},
		AnnualPrice:  models.PlanPrice{},
		Enable:       true,
		Order:        1,
		PeriodUnit:   models.TimePeriodUnLimit,
		Cycle:        models.PlanCycleOverall,
		AiLimitation: models.AiLimitation{
			Cycle:     models.PlanCycleMonthly,
			Type:      models.AiFeeTypeBalance,
			Analytics: true,
			Models:    aiLimitModels,
		},
		UserID: user.ID,
	}

	if err = models.Repository.PricingPlan.Create(plan, nil); err != nil {
		log.Fatalf("Create internal pricing plan failed: %v", err)
	}
	return
}

func InitAISubscription(org *models.Organization) {
	if err := services.Subscription.MigrateWhitelistAISubscription([]string{}, org.ID); err != nil {
		log.Errorf("InitWhitelistAISubscription Subscription Failed: %v", err)
	}
	return
}

func InitDefaultCryptoPaymentMethods() {
	paymentMethods := []*models.PaymentMethod{
		{
			Type:        models.PaymentMethodTypeOpenEduWallet,
			Service:     models.PaymentServiceCrypto,
			Network:     models.BlockchainNetworkNEAR,
			PaymentType: models.CryptoCurrencyUSDT,
			Enable:      true,
		},
		{
			Type:        models.PaymentMethodTypeOpenEduWallet,
			Service:     models.PaymentServiceCrypto,
			Network:     models.BlockchainNetworkNEAR,
			PaymentType: models.CryptoCurrencyUSDC,
			Enable:      true,
		},
	}

	for _, paymentMethod := range paymentMethods {
		_, err := models.Repository.PaymentMethod.FindOne(&models.PaymentMethodQuery{
			Type:        &paymentMethod.Type,
			Service:     &paymentMethod.Service,
			Network:     &paymentMethod.Network,
			PaymentType: &paymentMethod.PaymentType,
		}, &models.FindOneOptions{})
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Fatalf("Check payment method type=%s service=%s network=%s payment_type=%s exists failed: %v",
					paymentMethod.Type,
					paymentMethod.Service,
					paymentMethod.Network,
					paymentMethod.PaymentType,
					err)
			}

			if cErr := models.Repository.PaymentMethod.Create(paymentMethod, nil); cErr != nil {
				log.Fatalf("Create payment method type=%s service=%s network=%s payment_type=%s exists failed: %v",
					paymentMethod.Type,
					paymentMethod.Service,
					paymentMethod.Network,
					paymentMethod.PaymentType,
					cErr)
			}
		}
	}
}

func InitDefaultAIModels() {
	if appErr := services.AIModel.MigrateAIModel(); appErr != nil {
		log.Errorf("Migrate AI Models failed: %v", appErr)
	}
}

func InitDefaultInviteUserReferralSetting() {
	config := models.ReferralInviteUserProgram{
		ReferrerReward: models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(1)},
		RefereeReward:  models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(1)},
		Trigger:        models.CompleteRegisterTrigger,
		MilestoneBonus: true,
		RefCountBonus: []models.RefCountBonus{
			{
				ReachCount: 5,
				Reward:     models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(5)},
				Enable:     true,
				Order:      1,
			},
			{
				ReachCount: 10,
				Reward:     models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(10)},
				Enable:     true,
				Order:      2,
			},
			{
				ReachCount: 20,
				Reward:     models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(15)},
				Enable:     true,
				Order:      3,
			},
		},
		CompleteCourseBonus: models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(1)},
		DepositFiatBonus:    models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(2)},
		DepositTokenBonus:   models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(5)},
		TimeBased:           true,
		TimeBasedBonus:      models.TimeBasedBonus{Reward: models.PointReward{Type: models.PercentageValue, Amount: decimal.NewFromInt(100)}},
		StreakReward:        true,
		WeeklyStreakBonus: models.RefStreakBonus{
			Threshold: 10,
			Reward:    models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(3)},
		},
		MonthlyStreakBonus: models.RefStreakBonus{
			Threshold: 50,
			Reward:    models.PointReward{Type: models.FixedValue, Amount: decimal.NewFromInt(5)},
		},
	}

	jsonData, _ := util.StructToMap(config)
	campaign := models.OEPointCampaign{
		Program:   models.RefUserProgram,
		Name:      "Referral program: new user",
		Scope:     models.ReferralScopeGlobal,
		Enabled:   true,
		Entities:  models.ReferralCampaignEntity{},
		StartDate: 0,
		EndDate:   0,
		Setting:   jsonData,
	}
	ctx := context.Background()
	existed, _ := models.Repository.OEPointCampaign(ctx).FindOne(&models.OEPointCampaignQuery{
		Program: util.NewT(models.RefUserProgram),
		Scope:   util.NewT(models.ReferralScopeGlobal),
	}, &models.FindOneOptions{})

	if existed == nil {
		cErr := models.Repository.OEPointCampaign(ctx).Create(&campaign, nil)
		if cErr != nil {
			fmt.Println("models.Repository.OEPointCampaign(ctx).Create(&campaign, nil)", cErr)
		}
	}
}

func InitAIGovernmentOrg(user *models.User) *models.Organization {
	schema := models.SchemaAIGov
	saved, _ := models.Repository.Organization.FindOne(&models.OrganizationQuery{Schema: util.NewString(schema)}, nil)
	if saved == nil {
		org, appErr := services.Organization.Create(&dto.CreateOrgRequest{
			Name:       "AI Government",
			Domain:     "aigov." + setting.AppSetting.BaseDomain,
			Schema:     schema,
			AltDomain:  util.NewString("aigov." + setting.AppSetting.BaseDomain),
			Email:      &user.Email,
			CreateByID: &user.ID,
			Settings: &models.OrgSetting{
				SenderEmail: AIGovEmail,
			},
		}, false)
		if appErr != nil {
			log.Fatalf("Init default AI Government org failed: %v", appErr)
		}
		saved = org
	}

	if err := models.InitConfig(models.AIGovVN2025Campaign, &models.AiGovVn2025Campaign{
		CampaignKey: "ai-gov-campaign",
		Courses:     []*models.AiCampaignCourseItem{},
		OrgID:       saved.ID,
		FormEvent:   models.FormEventAIGovernment,
	}, models.JsonB, nil, nil); err != nil {
		log.Fatalf("Init default config key %s failed: %v", models.AIGovVN2025Campaign, err)
	}

	//services.OEReferralLeaderBoard(context.TODO()).FakeChutData("ai-government-campaign")
	services.OEReferralLeaderBoard(context.TODO()).InitProvinceByCampaign()
	return saved
}
