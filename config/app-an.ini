[server]
#debug or release
RunMode = debug
Host = 0.0.0.0
HttpPort = 8010
ReadTimeout = 900
WriteTimeout = 900
HostDomain = https://api-an.openedu101.com
BuildName = 1.0.0

[database]
Type = postgres
User = open_user
Password = open_password
Host = **************
Port = 5432
Name = vbi_dev
TablePrefix = openedu_
IdSize = 16
SSLMode = disable

[app]
Name = OpenEdu Core
InitDefaultRole = true
DefaultPerPage = 10
JwtAccessSecret = Sjv7ZpEWhQjen8lOsV7CwXGukNbxllUtkHOkObNPRBzPhb69m6
JwtRefreshSecret = Sjv7ZpEWhQjen8lOsV7CwXGukNbxllUtkHOkObNPRBzPhb69m6
JwtAccessExpireIn = 24
JwtRefreshExpireIn = 168
ApiKey = uU1UqUthMCkZ6keRgnTO1QdHZqC6AAYP
AIApiKey = uU1UqUthMCkZ6keRgnTO1QdHZqC6AAYP
AllowOrigin = http://localhost:8000,http://localhost:8001,http://localhost:3000,https://openedu101dev.com,https://admin.openedu101dev.com

AdminHost = sysadmin.openedu101dev.com
BaseDomain = openedu101dev.com
OrgAdminPath = /admin
PartnerPath = /creator
LearnerPath = /learner
BlogPath=/organization-blog/my-blog
AffiliatePath=/affiliate/campaigns

# System account
SystemAdminAccount = <EMAIL>
SystemAdminPassword = Open12@4

RuntimeRootPath = runtime/

ImageSavePath = upload/images/
# MB
ImageMaxSize = 100
ImageAllowExts = .jpg,.jpeg,.png

LogMode=debug
LogSavePath = runtime/logs/
LogSaveName = log
LogFileExt = log
TimeFormat = ********

CacheService=redis

EnableAPIFailureAlerts = true
MsTeamWebHookURL = https://gfiventures.webhook.office.com/webhookb2/1ec596d2-7de9-410a-8381-ce866cc1869a@42bc7246-a47f-47d8-bfa4-409aa66e8357/IncomingWebhook/f14de19787534d5581c8439538825e1b/1ae0fdd8-8069-43ab-beb7-ec9de156acc4/V2Ba5MhaCfyDl5aOhKRzrD6yOE4PZouK53g4YIl3-soTs1
MsTeamMentionedEmails = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

CouponReserveDuration = 15m

[redis]
Host = 127.0.0.1:6379
Password = OpenEdu2024
MaxIdle = 30
MaxActive = 30
IdleTimeout = 200
PrefixChannel = an_

[aws]
AccessKey = ********************
SecretKey = GHctZBnijgSQTikp3zCFzWnxnwPi1ph6Q/h4JhrX
Region = ap-southeast-1

[email]
Provider=brevo
From=<EMAIL>
FromName=Openedu.net
ReplyTo=<EMAIL>
RedirectClientURI=https://openedu101dev.com
TokenExpireIn=24
ResendAfter=60
ApiKey=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

[upload]
UploadProvider = s3
UploadLocalDir = upload
UploadS3BucketName = openedu.net-dev
UploadS3PathPrefix = images
UploadBunnyApiURI = https://video.bunnycdn.com/library
UploadBunnyIFrameURL = https://iframe.mediadelivery.net/embed

UploadPrivateBunnyAPIKey = 63cd7456-95a9-46d0-916c76e3ed1a-4c24-410a
UploadPrivateBunnyLibraryID = 260160
UploadPrivateBunnyIFrameAuthKey = 6b3d23bf-8f95-40b2-a3f7-a067c90480e4
UploadBunnyIFrameTokenExpireIn = 24

UploadPublicBunnyAPIKey = c86de365-9b69-4bc6-902ea281a10c-7d21-4e33
UploadPublicBunnyLibraryID = 309312

[google]
ClientID = 497507097735-287b7l59o3l8ocj526tsfehlih502qd3.apps.googleusercontent.com
ClientSecret = 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
RedirectURI = https://openedu101dev.com/callback
TokenURL = https://oauth2.googleapis.com/token?
UserDataURL = https://www.googleapis.com/oauth2/v3/userinfo

[facebook]
ClientID = ****************
ClientSecret = f7143ca7e4e452485e00a0c7b799c223
RedirectURI = https://openedu101dev.com/callback
TokenURL = https://graph.facebook.com/v17.0/oauth/access_token?
UserDataURL = https://graph.facebook.com/v17.0/me
DebugTokenURL = https://graph.facebook.com/debug_token?

[payment]
SepayURI = "https://qr.sepay.vn/img?acc=%s&bank=%s&amount=%.0f&des=%s"


[external-service]
AIDomain = https://aicore.southeastasia.cloudapp.azure.com/api
XAPIKey = oe-aicore--HPHppzWFF3kWck6vP1Rm-DfnsWsAfNynFqxevLdTziG1ctlNZ43xMjHw6B7_C6poygbVhIGMMmq1ACqk__UMZR6C-K2OHhoNjq7AJ-SNeC7BZPzzOGpXJYkksTl_1HSzgXRSGBEu4Bw3DSlDFSyn-HsMAg20DYs5zLm87x-Eto
AvailGameLeaderboardURL = https://test-dev-battle-server.weminal.com/v1/leaderboard?courseIdOpenEdu=bBfpgoYNpV2bl4hK

[openedu-communication]
CommunicationDomain = https://api-an.openedu101.com/api/com-internal-v1
CommunicationAPIKey = uU1UqUthMCkZ6keRgnTO1QdHZqC6AAYP

[rabbitmq]
URL = amqp://open_user:open_password@localhost:5672
Prefix = an_

[openedu-chain]
BaseURL = https://api-an.openedu101.com
ApiKey = uU1UqUthMCkZ6keRgnTO1QdHZqC6AAYP
IsMainnet = false

[coin-gecko]
BaseURL = https://api.coingecko.com/api/v3
ApiKey =

[openexchangerates]
AppID = ********************************
BaseURL = https://openexchangerates.org/api

[coinmarketcap]
BaseURL = https://pro-api.coinmarketcap.com
ApiKey = 16e0e44c-7204-41df-87a0-63b549596690

[openedu-scheduler]
Domain = https://api-an.openedu101.com/api/scheduler-v1
ApiKey = XNAIfTTAoVAujK8KhDKJbOwKDmtGgXruGKwIMFEvtKtUObAaCkgQAvDntLGcIIJj
