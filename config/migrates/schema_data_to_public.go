package migrates

import (
	"fmt"
	"github.com/samber/lo"
	"openedu-core/models"
	"strings"
)

func MigrateSchemaData2Public() {
	orgs := mFindAllOrgs()
	fmt.Println("dang co nhieu day orgs ne: ", len(orgs))
	fmt.Println("1======================COURSE==========================")
	for i, org := range orgs {
		courses := mFindAllEntities(org.Schema, "openedu_courses")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || Courses: ", len(courses), org.ID)
		lo.ForEach(courses, func(item map[string]interface{}, index int) {
			item["org_id"] = org.ID
			delete(item, "currency")
			delete(item, "is_pay")
			delete(item, "discount_price")
			delete(item, "price")
		})

		added := BatchInsertLarge("openedu_courses", courses, 20)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || Courses: ", len(courses), " added: ", added)
	}

	fmt.Println("2======================SECTION==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_sections")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || Sections: ", len(entities))
		lo.ForEach(entities, func(item map[string]interface{}, index int) {
			item["org_id"] = org.ID
			delete(item, "version")
		})
		added := BatchInsertLarge("openedu_sections", entities, 50)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || Sections: ", len(entities), " added: ", added)
	}

	fmt.Println("3======================LessonContent==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_lesson_contents")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || LessonContent: ", len(entities))
		lo.ForEach(entities, func(item map[string]interface{}, index int) {
			item["org_id"] = org.ID
		})
		added := BatchInsertLarge("openedu_lesson_contents", entities, 50)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || LessonContent: ", len(entities), " added: ", added)
	}

	fmt.Println("4======================CoursePartner==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_course_partners")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || CoursePartner: ", len(entities))
		lo.ForEach(entities, func(item map[string]interface{}, index int) {
			item["org_id"] = org.ID
		})
		added := BatchInsertLarge("openedu_course_partners", entities, 20)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || CoursePartner: ", len(entities), " added: ", added)
	}

	fmt.Println("5======================Quiz==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_quizzes")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || Quiz: ", len(entities))
		lo.ForEach(entities, func(item map[string]interface{}, index int) {
			item["org_id"] = org.ID
		})
		added := BatchInsertLarge("openedu_quizzes", entities, 20)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || Quiz: ", len(entities), " added: ", added)
	}

	fmt.Println("6======================Blog==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_blogs")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || Blog: ", len(entities))
		lo.ForEach(entities, func(item map[string]interface{}, index int) {
			item["org_id"] = org.ID
			delete(item, "category_id")
			delete(item, "vote_count")
			delete(item, "comment_count")
			delete(item, "view_count")
		})
		added := BatchInsertLarge("openedu_blogs", entities, 20)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || Blog: ", len(entities), " added: ", added)
	}

	fmt.Println("7======================CoursePrices==========================")
	for i, org := range orgs {
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema)
		mCoursePrices(org.Schema)
	}

	fmt.Println("9======================QuizQuestions==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_quiz_questions")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || QuizQuestions: ", len(entities))
		added := BatchInsertLarge("openedu_quiz_questions", entities, 50)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || QuizQuestions: ", len(entities), " added: ", added)
	}

	fmt.Println("8======================QuizAnswer==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_quiz_answers")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || QuizAnswer: ", len(entities))
		added := BatchInsertLarge("openedu_quiz_answers", entities, 20)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || QuizAnswer: ", len(entities), " added: ", added)
	}

	fmt.Println("10======================QuizRelation==========================")
	for i, org := range orgs {
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema)
		mQuizRelations(org.Schema)
	}

	fmt.Println("11======================QuizSubmission==========================")
	for i, org := range orgs {
		entities := mFindAllEntities(org.Schema, "openedu_quiz_submissions")
		fmt.Println("org: ", i+1, org.Name, "===> ", org.Schema, " || QuizSubmission: ", len(entities))
		added := BatchInsertLarge("openedu_quiz_submissions", entities, 20)
		fmt.Println("org: ", org.Name, "===> ", org.Schema, " || QuizSubmission: ", len(entities), " added: ", added)
	}
}

func mFindAllOrgs() []models.Organization {
	var orgs []models.Organization
	err := models.DB.Raw("SELECT * FROM openedu_organizations").Scan(&orgs).Error
	if err != nil {
		fmt.Println("find orgs loi nay: ", err)
	}
	return orgs
}

func mFindAllEntities(schema string, table string) []map[string]interface{} {
	var courses []map[string]interface{}
	query := fmt.Sprintf("SELECT * FROM %s.%s", schema, table)
	err := models.DB.Raw(query).Scan(&courses).Error
	if err != nil {
		fmt.Println("find loi nay: ", schema, "--", table, " <<>>", err)
	}
	return courses
}

func mQuizRelations(schema string) {
	query := fmt.Sprintf(`
	INSERT INTO "public".openedu_quiz_relations (quiz_id, related_entity_type, related_entity_id, relation_type, trigger_conditions)
SELECT DISTINCT quiz_id, related_entity_type, related_entity_id, relation_type, trigger_conditions
FROM %[1]s.openedu_quiz_relations
WHERE NOT EXISTS (
    SELECT 1 
    FROM "public".openedu_quiz_relations n 
    WHERE n.quiz_id = %[1]s.openedu_quiz_relations.quiz_id
    AND n.related_entity_type = %[1]s.openedu_quiz_relations.related_entity_type
    AND n.related_entity_id = %[1]s.openedu_quiz_relations.related_entity_id
    AND n.relation_type = %[1]s.openedu_quiz_relations.relation_type
    AND n.trigger_conditions = %[1]s.openedu_quiz_relations.trigger_conditions
)
`, schema)
	results := models.DB.Exec(query)
	if err := results.Error; err != nil {
		fmt.Println("Migrate loi ne!!!!: ", err)
		return
	} else {
		fmt.Println("khong co loi")
	}
}

func mCoursePrices(schema string) {
	query := fmt.Sprintf(`
	INSERT INTO "public".openedu_course_prices (course_id, is_pay, fiat_currency, fiat_price, fiat_discount_price, crypto_payment_enabled, crypto_currency, crypto_price, crypto_discount_price)
SELECT DISTINCT course_id, is_pay, fiat_currency, fiat_price, fiat_discount_price, crypto_payment_enabled, crypto_currency, crypto_price, crypto_discount_price
FROM %[1]s.openedu_course_prices
WHERE NOT EXISTS (
    SELECT 1 
    FROM "public".openedu_course_prices n 
    WHERE n.course_id = %[1]s.openedu_course_prices.course_id
    AND n.is_pay = %[1]s.openedu_course_prices.is_pay
    AND n.fiat_currency = %[1]s.openedu_course_prices.fiat_currency
    AND n.fiat_price = %[1]s.openedu_course_prices.fiat_price
    AND n.fiat_discount_price = %[1]s.openedu_course_prices.fiat_discount_price
    AND n.crypto_payment_enabled = %[1]s.openedu_course_prices.crypto_payment_enabled
    AND n.crypto_currency = %[1]s.openedu_course_prices.crypto_currency
    AND n.crypto_price = %[1]s.openedu_course_prices.crypto_price
    AND n.crypto_discount_price = %[1]s.openedu_course_prices.crypto_discount_price
)
`, schema)
	results := models.DB.Debug().Exec(query)
	if err := results.Error; err != nil {
		fmt.Println("Migrate loi ne!!!!: ", err)
		return
	} else {
		fmt.Println("khong co loi")
	}
}

func BatchInsertLarge(table string, entities []map[string]interface{}, batchSize int) int {
	if len(entities) == 0 {
		return 0
	}

	// Lấy columns từ map đầu tiên
	var columns []string
	var targetColumns []string
	for key := range entities[0] {
		columns = append(columns, key)
		targetColumns = append(targetColumns, `"`+key+`"`)
	}

	// Tạo placeholders cho một row
	rowPlaceholders := fmt.Sprintf("(%s)", strings.Join(lo.Map(columns, func(_ string, _ int) string {
		return "?"
	}), ","))
	add := 0
	// Process theo batch
	for i := 0; i < len(entities); i += batchSize {
		end := i + batchSize
		if end > len(entities) {
			end = len(entities)
		}

		batch := entities[i:end]
		batchPlaceholders := make([]string, len(batch))
		var values []interface{}

		// Build values và placeholders cho batch hiện tại
		for j, entity := range batch {
			batchPlaceholders[j] = rowPlaceholders
			for _, col := range columns {
				values = append(values, entity[col])
			}
		}

		query := fmt.Sprintf(`
            INSERT INTO public.%s (%s)
            VALUES %s
            ON CONFLICT (id) DO NOTHING
        `, table, strings.Join(targetColumns, ","), strings.Join(batchPlaceholders, ","))

		results := models.DB.Exec(query, values...)
		if err := results.Error; err != nil {
			fmt.Println("Migrate loi ne!!!!: ", err)
			return add
		}

		fmt.Println("added: ", i+1, batchSize, results.RowsAffected)
		add = add + int(results.RowsAffected)
	}

	return add
}
