[server]
#debug or release
RunMode = release
Host = 0.0.0.0
HttpPort = 8000
ReadTimeout = 900
WriteTimeout = 900
Name = OpenEdu Core
HostDomain =
BuildName = 1.0.0

[database]
Type = postgres
User = postgres
Password = uxsn9K%KH*+ExS(4
Host = openeduprod.cgghxrcljyao.ap-southeast-1.rds.amazonaws.com
Port = 5432
Name = openedu
TablePrefix = openedu_
IdSize = 16
#disable or require
SSLMode = require

[app]
Name = OpenEdu Core
InitDefaultRole = true
DefaultPerPage = 10
JwtAccessSecret =Atab9B02MJjKdJKthQXb19VR1Gl18nSnN0vECGCxE9hnrdDdZ4NJDv4WlJ1AxT9qLiV3OQPZjdLdMip0jWAbJbNNwPH5kYW8McOrMsBZzMdGCRYp8x4f5FkyqAL9SjdK
JwtRefreshSecret =0QiOqiqHlUEM7ebkOZtMOFEY3XNutiBoQ7ZBM5HwXtrifnoL372aVXfHUzQCxcIGl2rkbAGhTxYrVN83NGMGYMQ7DFUBmvOKl0dosL9rCJI4lILMMPIJ1xXbD1FRMPLR
JwtAccessExpireIn = 24
JwtRefreshExpireIn = 168
ApiKey =
AIApiKey =
AllowOrigin = http://localhost:8000,http://localhost:8001,http://localhost:3000,https://openedu.net,https://sysadmin.openedu101dev.com

AdminHost=sysadmin.openedu101dev.com
BaseDomain=openedu.net
OrgAdminPath=/admin
PartnerPath=/creator
LearnerPath=/learner
BlogPath=/organization-blog/my-blog
AffiliatePath=/affiliate/campaigns

# System account
SystemAdminAccount = <EMAIL>
SystemAdminPassword=BvAAFcA+qReW^KmN

RuntimeRootPath = ../runtime/

# Images
ImageSavePath = upload/images/
# MB
ImageMaxSize = 100
ImageAllowExts = .jpg,.jpeg,.png

LogMode=debug
LogSavePath = ../logs/
LogSaveName = log
LogFileExt = log
TimeFormat = ********

CacheService=redis

EnableAPIFailureAlerts = true
MsTeamWebHookURL = https://gfiventures.webhook.office.com/webhookb2/1ec596d2-7de9-410a-8381-ce866cc1869a@42bc7246-a47f-47d8-bfa4-409aa66e8357/IncomingWebhook/f14de19787534d5581c8439538825e1b/1ae0fdd8-8069-43ab-beb7-ec9de156acc4/V2Ba5MhaCfyDl5aOhKRzrD6yOE4PZouK53g4YIl3-soTs1
MsTeamMentionedEmails = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

CouponReserveDuration = 15m

[redis]
Host =
Password =
MaxIdle = 30
MaxActive = 30
IdleTimeout = 200
PrefixChannel = prod_

[aws]
AccessKey=********************
SecretKey=tWF8zQ5IHkq17IDlQhHZ8Fk8mtGSp/5+6CHkebmt
Region = ap-southeast-1

[email]
Provider=sendgrid
From=<EMAIL>
ReplyTo=<EMAIL>
RedirectClientURI=https://openedu101dev.com
TokenExpireIn=24
ResendAfter=60
ApiKey=*********************************************************************

[upload]
UploadProvider = s3
UploadLocalDir = upload
UploadS3BucketName = openedu.net-production
UploadS3PathPrefix = images

UploadBunnyApiURI = https://video.bunnycdn.com/library
UploadBunnyIFrameURL = https://iframe.mediadelivery.net/embed

UploadPrivateBunnyAPIKey =
UploadPrivateBunnyLibraryID =
UploadPrivateBunnyIFrameAuthKey =
UploadBunnyIFrameTokenExpireIn =

UploadPublicBunnyAPIKey =
UploadPublicBunnyLibraryID =

[google]
ClientID =708108284650-jgu6m5uusr3je4qkrnac3m3hpcmtj6da.apps.googleusercontent.com
ClientSecret =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
RedirectURI = https://openedu.net/callback
TokenURL = https://oauth2.googleapis.com/token?
UserDataURL = https://www.googleapis.com/oauth2/v3/userinfo

[facebook]
ClientID =
ClientSecret =
RedirectURI = https://openedu.net/callback
TokenURL = https://graph.facebook.com/v17.0/oauth/access_token?
UserDataURL = https://graph.facebook.com/v17.0/me
DebugTokenURL = https://graph.facebook.com/debug_token?

[payment]
SepayURI = "https://qr.sepay.vn/img?acc=%s&bank=%s&amount=%.0f&des=%s"

[external-service]
AIDomain = https://aicore.vbi-server.com/api
XAPIKey = oe-aicore-W0VqiMKrMpEFNjFciIR8HvHrVBVG3lFTSY7mDnvm2CroJj4V1DDTtEEKJ1TlvR4qlg0Nsn-gcK-KaAXbKKNXt79SMq64KxEmdpTDqnNw9CYSnNOiI6y7WLBDXd5v_NW0XJSnmHFAlsaM2CGwUE77gF7X1U1wQh6fb9MrFixvIj4
AvailGameLeaderboardURL = https://test-dev-battle-server.weminal.com/v1/leaderboard?courseIdOpenEdu=bBfpgoYNpV2bl4hK

[openedu-communication]
CommunicationDomain = http://localhost:8001/api/com-internal-v1
CommunicationAPIKey = uU1UqUthMCkZ6keRgnTO1QdHZqC6AAYP


[rabbitmq]
URL =
Prefix = prod_

[openedu-chain]
BaseURL =
ApiKey =
IsMainnet = true

[coin-gecko]
BaseURL =
ApiKey =

[openexchangerates]
AppID =
BaseURL = https://openexchangerates.org/api

[coinmarketcap]
BaseURL = https://pro-api.coinmarketcap.com
ApiKey =

[openedu-scheduler]
Domain = http://localhost:8000/api/scheduler-v1
ApiKey = XNAIfTTAoVAujK8KhDKJbOwKDmtGgXruGKwIMFEvtKtUObAaCkgQAvDntLGcIIJj