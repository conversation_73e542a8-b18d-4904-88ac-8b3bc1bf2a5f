package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	FormCacheTTL = 30 * time.Minute
)

func (c *FormCache) SetFormByID(id string, form interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, id)
	err := cache.Client.Set(cacheKey, form, FormCacheTTL)
	if err != nil {
		return err
	}
	return nil
}

func (c *FormCache) SetFormBySlug(slug string, form interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, slug)
	err := cache.Client.Set(cacheKey, form, FormCacheTTL)
	if err != nil {
		return err
	}
	return nil
}

func (c *FormCache) SetFormByKey(key string, form interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	err := cache.Client.Set(cacheKey, form, FormCacheTTL)
	if err != nil {
		return err
	}
	return nil
}

func (c *FormCache) GetByFormID(formId string, form interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, formId)
	return cache.Client.Get(cacheKey, form)
}

func (c *FormCache) GetByFormSlug(slug string, form interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, slug)
	return cache.Client.Get(cacheKey, form)
}

func (c *FormCache) GetByKey(key string, form interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Get(cacheKey, form)
}

func (c *FormCache) DeleteByFormID(formID string) error {
	key := makeCacheKey(c.Prefix, formID)
	return cache.Client.Delete(key)
}

func (c *FormCache) DeleteByFormSlug(slug string) error {
	key := makeCacheKey(c.Prefix, slug)
	return cache.Client.Delete(key)
}

func (c *FormCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *FormCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
