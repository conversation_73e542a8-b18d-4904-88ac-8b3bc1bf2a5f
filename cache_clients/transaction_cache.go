package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	TransactionTTL = 2 * time.Minute
)

func (c *TransactionCache) Set(key string, tx interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Set(cacheKey, tx, TransactionTTL)
}

func (c *TransactionCache) Get(key string, tx interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Get(cacheKey, tx)
}

func (c *TransactionCache) DeleteByKey(key string) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Delete(cacheKey)
}

func (c *TransactionCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
