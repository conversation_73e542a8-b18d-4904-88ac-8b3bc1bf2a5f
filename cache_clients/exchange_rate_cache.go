package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	FiatExchangeRateTTl   = 1 * time.Hour
	CryptoExchangeRateTTL = 5 * time.Minute

	FiatExchangeCacheKey   = "fiat_exchange_rates"
	CryptoExchangeCacheKey = "crypto_exchange_rates"
)

func (c *ExchangeRateCache) SetFiatExchangeRates(exchangeRates interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, FiatExchangeCacheKey)
	err := cache.Client.Set(cacheKey, exchangeRates, FiatExchangeRateTTl)
	if err != nil {
		return err
	}
	return nil
}

func (c *ExchangeRateCache) GetFiatExchangeRates(exchangeRates interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, FiatExchangeCacheKey)
	return cache.Client.Get(cacheKey, exchangeRates)
}

func (c *ExchangeRateCache) SetCryptoExchangeRates(exchangeRates interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, CryptoExchangeCacheKey)
	err := cache.Client.Set(cacheKey, exchangeRates, CryptoExchangeRateTTL)
	if err != nil {
		return err
	}
	return nil
}

func (c *ExchangeRateCache) GetCryptoExchangeRates(exchangeRates interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, CryptoExchangeCacheKey)
	return cache.Client.Get(cacheKey, exchangeRates)
}

func (c *ExchangeRateCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}

func (c *ExchangeRateCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}
