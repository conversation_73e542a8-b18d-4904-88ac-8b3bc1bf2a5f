package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	SubscriptionTTL = 24 * time.Hour
)

func (c *SubscriptionCache) Set(userID string, pricingPlan interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Set(key, pricingPlan, SubscriptionTTL)
	return err
}

func (c *SubscriptionCache) Get(userID string, pricingPlan interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	return cache.Client.Get(key, pricingPlan)
}

func (c *SubscriptionCache) DeleteByKey(userID string) error {
	key := makeCacheKey(c.Prefix, userID)
	return cache.Client.Delete(key)
}

func (c *SubscriptionCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
