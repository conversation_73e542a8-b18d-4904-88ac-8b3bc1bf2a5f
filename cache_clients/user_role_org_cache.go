package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	UserRoleTTL = 1 * time.Hour
)

func (c *UserRoleCache) SetUserRole(userID string, roles []interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Set(key, roles, UserRoleTTL)
	return err
}

func (c *UserRoleCache) GetByUserID(userID string, roles *[]interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	return cache.Client.Get(key, roles)
}

func (c *UserRoleCache) DeleteByUserID(userID string) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Delete(key)
	return err
}

func (c *UserRoleCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *UserRoleCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
