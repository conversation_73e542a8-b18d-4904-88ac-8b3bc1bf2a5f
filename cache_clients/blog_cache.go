package cache_clients

import (
	"openedu-core/pkg/cache"
	"openedu-core/pkg/log"
	"time"
)

const (
	BlogTTl = 24 * time.Hour
)

func (c *BlogCache) Set(key string, blog interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	err := cache.Client.Set(cacheKey, blog, BlogTTl)
	if err != nil {
		return err
	}
	return nil
}

func (c *BlogCache) Get(key string, blog interface{}) {
	cacheKey := makeCacheKey(c.Prefix, key)
	cache.Client.Get(cacheKey, blog)
}

func (c *BlogCache) SetManyBlogByCategory(blogByCategory map[string][]interface{}) error {
	for key, value := range blogByCategory {
		redisKey := makeCacheKey(c.Prefix, "categories", key)
		err := cache.Client.Set(redisKey, value, BlogTTl)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *BlogCache) GetManyBlogByCategory(categoryIDs []string, blogs map[string][]interface{}) {
	for _, id := range categoryIDs {
		cacheKey := makeCacheKey(c.Prefix, "categories", id)
		var res []interface{}
		cache.Client.Get(cacheKey, &res)
		if res != nil {
			blogs[id] = res
		}
	}
}

func (c *BlogCache) DeleteBlogByManyCategory(categoryIDs []string) error {
	for _, id := range categoryIDs {
		key := makeCacheKey(c.Prefix, "categories", id)
		log.Debugf("DeleteBlogByManyCategory key: %s", key)
		err := cache.Client.Delete(key)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *BlogCache) DeleteAllBlogByCategory() error {
	key := makeCacheKey(c.Prefix, "categories_")
	log.Debugf("DeleteAllBlogByCategory key: %s", key)
	return cache.Client.DeleteByPrefix(key)
}

func (c *BlogCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *BlogCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
