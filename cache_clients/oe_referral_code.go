package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	ReferralCodeTTL = 7 * 24 * time.Hour
)

func (c *OEReferralCodeCache) Set(cKey string, code interface{}) error {
	key := makeCacheKey(c.Prefix, c<PERSON>ey)
	err := cache.Client.Set(key, code, ReferralCodeTTL)
	return err
}

func (c *OEReferralCodeCache) Get(cKey string, code interface{}) error {
	key := makeCacheKey(c.Prefix, cKey)
	return cache.Client.Get(key, code)
}

func (c *OEReferralCodeCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *OEReferralCodeCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
