package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	QuizCacheTTL = 30 * time.Minute
)

func (c *QuizCache) SetQuizByID(id string, quiz interface{}) error {
	key := makeCacheKey(c.Prefix, id)
	err := cache.Client.Set(key, quiz, QuizCacheTTL)
	return err
}

func (c *QuizCache) GetByQuizID(quizID string, quiz interface{}) error {
	key := makeCacheKey(c.Prefix, quizID)
	return cache.Client.Get(key, quiz)
}

func (c *QuizCache) DeleteByQuizID(quizID string) error {
	key := makeCacheKey(c.Prefix, quizID)
	return cache.Client.Delete(key)
}

func (c *QuizCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *QuizCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
