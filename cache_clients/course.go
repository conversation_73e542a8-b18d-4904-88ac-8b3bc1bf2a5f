package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	CourseTTL        = 3 * time.Minute
	CourseItemTTL    = 30 * time.Minute
	CourseItemPrefix = "item_"
)

func (c *CourseCache) Set(id string, course interface{}) error {
	key := makeCacheKey(c.Prefix, id)
	err := cache.Client.Set(key, course, CourseTTL)
	return err
}

func (c *CourseCache) Get(id string, course interface{}) error {
	key := makeCacheKey(c.Prefix, id)
	return cache.Client.Get(key, course)
}

func (c *CourseCache) SetListItem(id string, courseItem interface{}) error {
	key := makeCacheKey(c.Prefix+CourseItemPrefix, id)
	err := cache.Client.Set(key, courseItem, CourseItemTTL)
	return err
}

func (c *CourseCache) GetListItem(id string, courseItem interface{}) error {
	key := makeCacheKey(c.Prefix+CourseItemPrefix, id)
	return cache.Client.Get(key, courseItem)
}

func (c *CourseCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
