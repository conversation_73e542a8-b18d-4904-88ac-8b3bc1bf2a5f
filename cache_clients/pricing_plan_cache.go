package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	PricingPlanTTL         = 7 * 24 * time.Hour
	PricingPlanCacheKeyAll = "all"
)

func (c *PricingPlanCache) SetAll(plans []interface{}) error {
	key := makeCacheKey(c.Prefix, PricingPlanCacheKeyAll)
	err := cache.Client.Set(key, plans, PricingPlanTTL)
	return err
}

func (c *PricingPlanCache) GetAll(plans *[]interface{}) error {
	key := makeCacheKey(c.Prefix, PricingPlanCacheKeyAll)
	err := cache.Client.Get(key, plans)
	if err != nil {
		return err
	}
	return nil
}

func (c *PricingPlanCache) DeleteKeyAll() error {
	key := makeCacheKey(c.Prefix, PricingPlanCacheKeyAll)
	return cache.Client.Delete(key)
}

func (c *PricingPlanCache) Flush() error {
	return cache.Client.Flush()
}
