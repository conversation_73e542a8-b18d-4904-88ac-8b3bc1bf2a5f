version: '3.8'

services:
  postgres:
    container_name: postgres_openedu
    image: postgres:latest
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-open_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-open_password}
      POSTGRES_DB: ${POSTGRES_DB:-openedu2}
      PGDATA: /data/postgres
    volumes:
      - ./.data_storage/postgres:/data/postgres
    ports:
      - "5432:5432"
    networks:
      - app_network
    restart: unless-stopped

  redis:
    image: redis:latest
    container_name: redis_openedu
    ports:
      - "${REDIS_PORT:-6379}:6379"
    restart: unless-stopped
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-OpenEdu2024}
    networks:
      - app_network
    command: [ "redis-server", "--requirepass", "${REDIS_PASSWORD}" ]
    volumes:
      - ./.data_storage/redis:/data

  mongodb:
    image: mongo:latest
    container_name: mongodb_openedu
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGODB_ROOT_USER:-openedu_admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_ROOT_PASSWORD:-OpenEdu2024}
      MONGO_INITDB_DATABASE: openedu
    ports:
      - "${MONGODB_PORT:-27017}:27017"
    networks:
      - app_network
    volumes:
      - ./.data_storage/mongodb:/data/db

  rabbitmq:
    container_name: rabbitmq_openedu
    # There is a prebuilt RabbitMQ image; see
    # https://hub.docker.com/_/rabbitmq/ for details.
    # This variant is built on Alpine Linux (it's smaller) and includes
    # the management UI.
    image: 'rabbitmq:4.0.2-management-alpine'
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-open_user}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-open_password}
    volumes:
      - ./.data_storage/rabbitmq:/var/lib/rabbitmq

    # These ports are exposed on the host; 'hostport:containerport'.
    # You could connect to this server from outside with the *host's*
    # DNS name or IP address and port 5672 (the left-hand side of the
    # colon).
    ports:
      # The standard AMQP protocol port
      - "5672:5672"
      # HTTP management UI
      - "15672:15672"

    restart: unless-stopped

    # Run this container on a private network for this application.
    # This is necessary for magic Docker DNS to work: other containers
    # also running on this network will see a host name "rabbitmq"
    # (the name of this section) and the internal port 5672, even though
    # that's not explicitly published above.
    networks:
      - app_network

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./tracing/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--web.enable-lifecycle'
      - '--enable-feature=remote-write-receiver'
    network_mode: "host"


  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    network_mode: "host"
    environment:
      - GF_SERVER_HTTP_PORT=3001
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_LOG_LEVEL=debug
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./tracing/grafana/provisioning:/etc/grafana/provisioning

  jaeger:
    image: jaegertracing/all-in-one:1.22
    container_name: tracing
    network_mode: "host"
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - COLLECTOR_DEBUG=true
      - LOG_LEVEL=debug
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
      - "9411:9411"

networks:
  app_network:
    driver: bridge

volumes:
  grafana-storage: