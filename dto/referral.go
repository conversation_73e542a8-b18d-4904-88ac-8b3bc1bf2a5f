package dto

import (
	"github.com/shopspring/decimal"
	"openedu-core/models"
)

type CreateReferralLink struct {
	CommissionID string `json:"commission_id"`
}

type UpdateReferralLink struct {
	ShareRate float32 `json:"share_rate"`
}

type GetReferralLinkResponse struct {
	ReferralLink       *models.ReferralLink `json:"referral_link"`
	ReferralExtendLink *models.ReferralLink `json:"referral_extend_link"`
}

type CreateReferralResponse struct {
	Referral        *models.Referral
	RefLinkLevel1   *models.ReferralLink
	RefLinkLevel2   *models.ReferralLink
	RefLinkLevel3   *models.ReferralLink
	Commission      *models.Commission
	Ref1Amount      decimal.Decimal
	Ref1ShareAmount decimal.Decimal
	Ref2Amount      decimal.Decimal
	Ref3Amount      decimal.Decimal
}

type PrecomputeReferralResponse struct {
	Referral        *models.Referral
	RefLink         *models.ReferralLink
	RefLinkLevel1   *models.ReferralLink
	RefLinkLevel2   *models.ReferralLink
	RefLinkLevel3   *models.ReferralLink
	Commission      *models.Commission
	Ref1Amount      decimal.Decimal
	Ref1ShareAmount decimal.Decimal
	Ref2Amount      decimal.Decimal
	Ref3Amount      decimal.Decimal
}

type ListReferralResponse struct {
	Results    []*models.Referral `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}

type ListReferralReportByUser struct {
	Results    []*models.ReferralReportByUser `json:"results"`
	Pagination *models.Pagination             `json:"pagination"`
}
