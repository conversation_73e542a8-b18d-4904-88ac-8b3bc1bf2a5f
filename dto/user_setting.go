package dto

import "openedu-core/models"

type UserSettingRequest struct {
	ID     *string                `json:"id"`
	UserID string                 `json:"user_id"`
	Type   models.UserSettingType `json:"type"`
	Enable bool                   `json:"enable"`
	Value  models.JSONB           `json:"value"`
}

type CreateSettingRequest struct {
	Settings []UserSettingRequest `json:"settings"`
}

type DeleteUserSettingRequest struct {
	IDs []string `json:"ids" form:"ids"`
}

type ListUserSettingResponse struct {
	Results    []*models.UserSetting `json:"results"`
	Pagination *models.Pagination    `json:"pagination"`
}

type CertificateSettingResponse struct {
	*models.Certificate
	IsShow bool `json:"is_show"`
}

type CourseSettingResponse struct {
	*models.SimpleCourse
	IsShow bool `json:"is_show"`
}

type BlogSettingResponse struct {
	*models.SimpleBlog
	IsShow bool `json:"is_show"`
}

type SettingProfileResponse struct {
	Settings   []any              `json:"settings"`
	Pagination *models.Pagination `json:"pagination"`
	Enable     bool               `json:"enable"`
}

func MakeSettingProfileResponse[T any](input []T, pagination *models.Pagination, enable bool) *SettingProfileResponse {
	var result []any
	for _, item := range input {
		result = append(result, item)
	}

	if len(result) <= 0 {
		result = []any{}
	}


	return &SettingProfileResponse{
		Settings:   result,
		Pagination: pagination,
		Enable:     enable,
	}
}

type UpdateSettingProfileRequest struct {
	Type   models.UserSettingType `json:"type" validate:"required"`
	Enable *bool                  `json:"enable"`
	Value  *models.JSONB          `json:"value"`
}
