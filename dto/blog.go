package dto

import (
	"openedu-core/models"

	communicationdto "openedu-core/pkg/communication/dto"
)

type BlogHashTagRequest struct {
	Name string `json:"name"`
}

type UpdateBlogRequest struct {
	Description      string                 `json:"description,omitempty"`
	ImageDescription string                 `json:"image_description,omitempty"`
	Title            string                 `json:"title,omitempty" validate:"required"`
	BannerID         string                 `json:"banner_id,omitempty"`
	TimeRead         int16                  `json:"time_read,omitempty"`
	Content          string                 `json:"content,omitempty" gorm:"type:text"`
	CategoryIDs      []DefaultEntityRequest `json:"category_ids,omitempty"`
	HashTagID        []BlogHashTagRequest   `json:"hashtag_names,omitempty"`
	JsonContent      models.JSONB           `json:"json_content"`
	IsPin            bool                   `json:"is_pin,omitempty"`
	ScheduleAt       int64                  `json:"schedule_at,omitempty" `
	SendAsLetter     bool                   `json:"send_as_letter,omitempty"`
	Locale           string                 `json:"locale,omitempty"`
	AIBlogID         string                 `json:"ai_blog_id,omitempty"`
}

type PublishBlogRequest struct {
	Note string `json:"note"`

	Blog      *models.Blog
	Org       *models.Organization
	Requester *models.User
}

type ListBlogsResponse struct {
	Results    []*models.SimpleBlog `json:"results"`
	Pagination *models.Pagination   `json:"pagination"`
}

type PinBlogRequest struct {
	Pin bool `json:"pin"`
}

type AddRoleWriterRequest struct {
	AddIds    []string `json:"add_ids"`
	RemoveIds []string `json:"remove_ids"`
}

type AddRoleEditorRequest struct {
	AddIds    []string `json:"add_ids"`
	RemoveIds []string `json:"remove_ids"`
}

type BlogTreeByCateResponse struct {
	CategoryName string               `json:"category_name"`
	Blogs        []*models.SimpleBlog `json:"blogs"`
}

type BlogTreeByCateOrgResponse struct {
	Result   map[string]*BlogTreeByCateResponse `json:"result"`
	Organize *models.Organization               `json:"organize"`
}

type UserPersonBlogResponse struct {
	ListBlogsResponse
	Organize *models.Organization `json:"organize"`
}

type UserOrgBlogResponse struct {
	ListBlogsResponse
	Organize *models.Organization `json:"organize"`
}

type HomePageBlogResponse struct {
	ListBlogsResponse
	Organize *models.Organization `json:"organize"`
}

type TopViewBlogResponse struct {
	Results    []*communicationdto.BlogResp `json:"results"`
	Pagination *communicationdto.Pagination `json:"pagination"`
}

type TopUserBlogViewResponse struct {
	Results    []*communicationdto.UserResp `json:"results"`
	Pagination *communicationdto.Pagination `json:"pagination"`
}
