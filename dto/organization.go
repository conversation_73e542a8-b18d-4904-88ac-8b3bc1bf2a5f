package dto

import (
	"openedu-core/models"

	"github.com/shopspring/decimal"
)

type CreateOrgRequest struct {
	FormSessionID *string `json:"form_session_id"`
	// org info
	Name        string             `json:"name" validate:"required"`
	Domain      string             `json:"domain" validate:"required"`
	Schema      string             `json:"-"`
	AltDomain   *string            `json:"alt_domain"`
	ThumbnailID *string            `json:"thumbnail_id"`
	BannerID    *string            `json:"banner_id"`
	Settings    *models.OrgSetting `json:"settings"`

	// org owner info
	Email    *string `json:"email"`
	FullName *string `json:"full_name"`
	Phone    *string `json:"phone"`

	// base info
	DefaultOrgID *string `json:"default_org_id"`
	CreateByID   *string `json:"create_by_id"`
}

type UpdateOrgRequest struct {
	Name        string  `json:"name"`
	UserID      string  `json:"user_id"`
	ThumbnailID *string `json:"thumbnail_id"`
	BannerID    *string `json:"banner_id"`
	Active      bool    `json:"active"`
}

type ListOrgsResponse struct {
	Results    []*models.SimpleOrganization `json:"results"`
	Pagination *models.Pagination           `json:"pagination"`
}

type CheckOrgValidationRequest struct {
	OrgID  *string `json:"org_id"`
	Domain string  `json:"domain"`
}

type CalcRetroactiveForAvailRequest struct {
	AirdropAmount decimal.Decimal `json:"airdrop_amount"`
}

type CalcRetroactiveForAvailResponse struct {
	TotalScoreAllUsers decimal.Decimal            `json:"total_score_of_all_users"`
	AirdropAmount      decimal.Decimal            `json:"airdrop_amount"`
	Distributions      []*RetroactiveDistribution `json:"distributions"`
	FileURL            string                     `json:"file_url"`
}

type RetroactiveDistribution struct {
	UserID               string                   `json:"user_id" excel:"User ID"`
	UsernameInOpenEdu    string                   `json:"username_in_openedu" excel:"Username In OpenEdu"`
	UsernameInGame       string                   `json:"username_in_game" excel:"Username In Game"`
	Email                string                   `json:"email" excel:"Email"`
	Address              string                   `json:"address" excel:"Wallet Address"`
	Network              models.BlockchainNetwork `json:"network" excel:"-"`
	Amount               decimal.Decimal          `json:"amount" excel:"Amount"`
	Rank                 int                      `json:"rank" excel:"Rank"`
	TotalScore           decimal.Decimal          `json:"total_score" excel:"Total Score"`
	TotalRefScore        decimal.Decimal          `json:"total_ref_score" excel:"Total Ref Score"`
	TotalSocialTaskScore decimal.Decimal          `json:"total_social_task_score" excel:"Total Social Task Score"`
	TotalGameScore       decimal.Decimal          `json:"total_game_score" excel:"Total Score"`
}

type AvailLeaderboardResponse[T any] struct {
	Data T      `json:"data"`
	Msg  string `json:"msg"`
	Code int    `json:"code"`
}

type AvailLeaderboardUserInfo struct {
	Email    string `json:"email"`
	Username string `json:"username"`
}

type AvailLeaderboardRecord struct {
	User                 *AvailLeaderboardUserInfo `json:"user"`
	TotalScore           decimal.Decimal           `json:"totalScore"`
	Rank                 int                       `json:"rank"`
	Course               string                    `json:"course"`
	TotalRefScore        decimal.Decimal           `json:"totalRefScore"`
	TotalSocialTaskScore decimal.Decimal           `json:"totalSocialTaskScore"`
	TotalGameScore       decimal.Decimal           `json:"totalGameScore"`
}
