package dto

import (
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/go-playground/validator/v10"
)

var validationMap = map[string]func(fl validator.FieldLevel) bool{
	ValidateTypeCoupon:        validateCouponStruct,
	ValidateFlatConstraint:    validateMaximumCouponStruct,
	ValidateCourseListExists:  validateCourseListExists,
	ValidateOrgListExists:     validateOrgListExists,
	ValidateCourseExists:      validateCourseExists,
	ValidateOrgExists:         validateOrgExists,
	ValidateEntityType:        validateEntityType,
	ValidateApproveType:       validateApproveType,
	ValidateApproveStatus:     validateApproveStatus,
	ValidateCreateBlogType:    validateBlogType,
	ValidateCategoriesExists:  validateCategoriesExists,
	ValidateHashtagExists:     validateHashtagExists,
	ValidateUserExists:        validateUserExists,
	ValidateAIBlogRequest:     validateBlogAIRequest,
	ValidateReportType:        validateReportType,
	ValidateEventLessonLP:     validateLessonForLearningProgress,
	ValidateBlockchainNetwork: validateBlockchainNetwork,
}

func Setup() {
	var err error

	for k, v := range validationMap {
		if err = util.Validator.RegisterValidation(k, v); err != nil {
			log.Fatalf("error registering validation: %v", err)
		}
	}
}
