package dto

import (
	"openedu-core/dto"
	"openedu-core/models"

	"github.com/go-playground/validator/v10"
	"github.com/samber/lo"
)

func validateBlogType(fl validator.FieldLevel) bool {
	if fl.Field().String() == "" {
		return true
	}
	approveType, ok := fl.Field().Interface().(models.BlogType)
	if !ok {
		return false
	}

	if !lo.Contains(models.CreateBlogType, approveType) {
		return false
	}

	return true
}

func validateBlogAIRequest(fl validator.FieldLevel) bool {
	req := fl.Parent().Interface().(dto.AIBlogRequest)
	switch req.AIBlogRequestType {
	case models.AIBlogGenerateBlog:
		if len(req.Blogs) == 0 {
			return false
		}
	case models.AIBlogRewriteParagraph:
		if req.Text == "" {
			return false
		}
	case models.AIBlogRewriteFromLink:
		if req.Link == "" {
			return false
		}
	default:
		return false
	}
	return true
}
