package dto

import (
	"context"
	"openedu-core/models"
	"openedu-core/services"

	"github.com/go-playground/validator/v10"
)

func validateCourseListExists(fl validator.FieldLevel) bool {
	courseCUIDs, ok := fl.Field().Interface().([]string)
	if !ok {
		return false
	}
	if exists, err := models.Repository.Course(context.TODO()).FindMany(&models.CourseQuery{CuidIn: courseCUIDs}, &models.FindManyOptions{}); err != nil {
		return false
	} else {
		coursesByCUIDs := map[string]*models.Course{}
		for _, course := range exists {
			coursesByCUIDs[course.Cuid] = course
		}

		for _, courseCUID := range courseCUIDs {
			if _, found := coursesByCUIDs[courseCUID]; !found {
				return false
			}
		}

		return true
	}
}

func validateOrgListExists(fl validator.FieldLevel) bool {
	orgIDs, ok := fl.Field().Interface().([]string)
	if !ok {
		return false
	}
	if exists, err := models.Repository.Organization.FindMany(&models.OrganizationQuery{IDIn: orgIDs}, &models.FindManyOptions{}); err != nil {
		return false
	} else {
		orgsByIDs := map[string]*models.Organization{}
		for _, org := range exists {
			orgsByIDs[org.ID] = org
		}

		for _, orgID := range orgIDs {
			if _, found := orgsByIDs[orgID]; !found {
				return false
			}
		}

		return true
	}
}

func validateCourseExists(fl validator.FieldLevel) bool {
	courseID, ok := fl.Field().Interface().(string)
	if !ok {
		return false
	}
	if exists, err := models.Repository.Course(context.TODO()).FindByID(courseID, &models.FindOneOptions{}); err != nil {
		return false
	} else {
		return exists != nil
	}
}

func validateOrgExists(fl validator.FieldLevel) bool {
	orgID, ok := fl.Field().Interface().(string)
	if !ok {
		return false
	}
	if exists, err := models.Repository.Organization.FindByID(orgID, &models.FindOneOptions{}); err != nil {
		return false
	} else {
		return exists != nil
	}

}

func validateCategoriesExists(fl validator.FieldLevel) bool {
	categoryIDs, ok := fl.Field().Interface().([]string)
	if !ok {
		return false
	}
	if len(categoryIDs) == 0 {
		return true
	}

	if exists, err := services.Category.FindMany(&models.CategoryQuery{IDIn: categoryIDs}, &models.FindManyOptions{}); err != nil {
		return false
	} else {
		return len(exists) == len(categoryIDs)
	}
}

func validateHashtagExists(fl validator.FieldLevel) bool {
	hashtagIDs, ok := fl.Field().Interface().([]string)
	if !ok {
		return false
	}

	if len(hashtagIDs) == 0 {
		return true
	}

	if exists, err := services.Hashtag.FindMany(&models.HashtagQuery{HashIn: hashtagIDs}, &models.FindManyOptions{}); err != nil {
		return false
	} else {
		return len(exists) == len(hashtagIDs)
	}
}

func validateUserExists(fl validator.FieldLevel) bool {
	userIDs, ok := fl.Field().Interface().([]string)
	if !ok {
		return false
	}

	if exists, err := services.User.FindMany(&models.UserQuery{IDIn: &userIDs}, &models.FindManyOptions{}); err != nil {
		return false
	} else {
		return len(exists) == len(userIDs)
	}
}
