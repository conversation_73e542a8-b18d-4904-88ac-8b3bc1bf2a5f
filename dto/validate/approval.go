package dto

import (
	"openedu-core/models"

	"github.com/go-playground/validator/v10"
	"github.com/samber/lo"
)

func validateEntityType(fl validator.FieldLevel) bool {
	entityType, ok := fl.Field().Interface().(models.ModelName)
	if !ok {
		return false
	}

	if !lo.Contains(models.ModelNames, entityType) {
		return false
	}

	return true
}

func validateApproveType(fl validator.FieldLevel) bool {
	approveType, ok := fl.Field().Interface().(models.ApproveType)
	if !ok {
		return false
	}

	if !lo.Contains(models.ApprovalTypes, approveType) {
		return false
	}

	return true
}

func validateApproveStatus(fl validator.FieldLevel) bool {
	approveStatus, ok := fl.Field().Interface().(models.ApprovalStatus)
	if !ok {
		return false
	}

	if !lo.Contains(models.ApprovalStatuses, approveStatus) {
		return false
	}

	return true
}
