package dto

import (
	"openedu-core/models"
)

type GenerateCourseFromYoutubeParams struct {
	Org              *models.Organization
	User             *models.User
	PlaylistLink     string
	Tone             models.AITone
	Language         string
	QuizIncluded     bool
	QuizType         models.QuizQuestionType
	NumberOfQuestion int16
	SummaryIncluded  bool
}

type GenerateCourseFromAIRequest struct {
	PlaylistLink     string                  `json:"playlist_link"`
	Tone             models.AITone           `json:"tone"`
	Language         string                  `json:"language"`
	Type             models.AIOfferType      `json:"type" validate:"required"`
	QuizIncluded     bool                    `json:"quiz_included"`
	QuizType         models.QuizQuestionType `json:"quiz_type"`
	NumberOfQuestion int16                   `json:"number_of_question"`
	SummaryIncluded  bool                    `json:"summary_included"`

	LearnerInfo  string                      `json:"learner_info"`
	ContentInfo  string                      `json:"content_info"`
	MaterialID   string                      `json:"material_id"`
	LevelID      string                      `json:"level_id"`
	Duration     int16                       `json:"duration"`
	DurationType models.AICourseDurationType `json:"duration_type"`
	StudyLoad    int16                       `json:"study_load"`
	CurrentStep  models.AIGenerateStep       `json:"current_step"`

	ThumbnailDescription string                  `json:"thumbnail_description"`
	ThumbnailQuantity    int16                   `json:"thumbnail_quantity"`
	ThumbnailStyle       models.AIThumbnailStyle `json:"thumbnail_style"`
	CourseCuid           string                  `json:"course_cuid"`
	Title                string                  `json:"title"`
	Description          string                  `json:"description"`
	ThumbnailID          string                  `json:"thumbnail_id"`
}

type GenerateCourseFromLearnerDescriptionParams struct {
	Org          *models.Organization
	User         *models.User
	LearnerInfo  string
	ContentInfo  string
	MaterialID   string
	LevelID      string
	Language     string
	Duration     int16
	DurationType models.AICourseDurationType
	StudyLoad    int16
	CourseCuid   string
}

type GenerateCourseThumbnailParams struct {
	ThumbnailDescription string
	ThumbnailQuantity    int16
	ThumbnailStyle       models.AIThumbnailStyle
	CourseCuid           string
}

type GenerateOutlineFromLearnerDescriptionParams struct {
	CourseCuid  string
	Title       string
	Description string
	ThumbnailID string
}

type CreateAIHistoryParams struct {
	ID           string
	RequestIDs   []string
	RequestType  models.ModelName
	UserID       string
	EntityID     string
	EntityType   models.ModelName
	GenerateID   string
	GenerateType models.AIOfferType
	Step         models.AIGenerateStep
	Status       models.AIStatus
	Cost         float64
	Request      models.JSONB
	Response     models.JSONB
	Error        *models.DetailAIError
	StartDate    int64
	EndDate      int64
	OrgID        string
	OrgSchema    string
	AIOfferID    string

	RequestIDsEmpty bool
}

type RewriteBlogFromAIData struct {
	Metadata *BlogMetadata   `json:"metadata"`
	Status   models.AIStatus `json:"status"`
}

type BlogMetadata struct {
	Content string `json:"content"`
}

type RewriteBlogByAIResponse struct {
	Content string `json:"content"`

	BlogID      string                `json:"blog_id"`
	BlogCuid    string                `json:"blog_cuid"`
	AIBlogID    string                `json:"ai_blog_id"`
	RewriteID   string                `json:"rewrite_id"`
	Status      models.AIStatus       `json:"status"`
	CurrentStep models.AIGenerateStep `json:"current_step"`
	AuthorID    string                `json:"author_id"`
}

type CreateBlogRequest struct {
	AIBlogID         *string                `json:"ai_blog_id,omitempty"`
	Title            string                 `json:"title,omitempty" validate:"required"`
	BannerID         string                 `json:"banner_id,omitempty"`
	ImageDescription string                 `json:"image_description,omitempty"`
	CategoryIDs      []DefaultEntityRequest `json:"category_ids,omitempty"`
	HashTagNames     []BlogHashTagRequest   `json:"hashtag_names,omitempty"`
	TimeRead         int16                  `json:"time_read,omitempty"`
	Description      string                 `json:"description,omitempty"`
	Content          string                 `json:"content,omitempty" `
	JsonContent      models.JSONB           `json:"json_content"`
	BlogType         models.BlogType        `json:"blog_type,omitempty" validate:"createBlogType,required"`
	IsPublish        bool                   `json:"is_publish"`
	ScheduleAt       int64                  `json:"schedule_at,omitempty" `
	SendAsLetter     bool                   `json:"send_as_letter,omitempty"`
	IsAIGenerated    bool                   `json:"is_ai_generated,omitempty"`
	Locale           string                 `json:"locale,omitempty"`
}

type AIBlogRequest struct {
	// indicate the type of request
	AIBlogRequestType models.AIOfferType `json:"ai_blog_request_type" validate:"required,validateBlogAIRequest"`
	// For rewrite paragraph & from link
	BlogCuid string `json:"blog_cuid"`
	Link     string `json:"link"`
	Text     string `json:"text"`
	// For create blog
	Blogs  []CreateBlogByAI `json:"blogs"`
	Locale string           `json:"locale"`
	Tone   models.AITone    `json:"tone"`
}

type CreateBlogByAI struct {
	Link     string          `json:"link"`
	BlogType models.BlogType `json:"blog_type"`
}

type OfferRewriteBlogDataResponse struct {
	BlogID      string                `json:"blog_id"`
	BlogCuid    string                `json:"blog_cuid"`
	AIBlogID    string                `json:"ai_blog_id"`
	RewriteID   string                `json:"rewrite_id"`
	Status      models.AIStatus       `json:"status"`
	CurrentStep models.AIGenerateStep `json:"current_step"`
	AuthorID    string                `json:"author_id"`
}

type ListAIPromptResponse struct {
	Results    []*models.AIPrompt `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}

type AIPromptRequest struct {
	Text        string             `json:"text" validate:"required"`
	Enable      bool               `json:"enable"`
	AIAgentType models.AIAgentType `json:"ai_agent_type" validate:"required"`
	CategoryID  string             `json:"category_id"`
	Order       int                `json:"order"`
	UserID      string             `json:"user_id"`
}

type AIPromptsRequest struct {
	Prompts []*AIPromptRequest `json:"prompts"`
}
