package dto

import (
	"github.com/shopspring/decimal"
	"openedu-core/models"
)

type TransactionSyncRequest struct {
	CoreTxIDs      []string                 `json:"core_tx_ids"`
	NftTokenID     string                   `json:"nft_token_id"`
	BlockchainTxID string                   `json:"blockchain_tx_id"`
	TxHash         string                   `json:"tx_hash"`
	Status         models.TransactionStatus `json:"status"`
	ErrorCode      int                      `json:"error_code"`
	DecimalAmount  *decimal.Decimal         `json:"decimal_amount"`
}
