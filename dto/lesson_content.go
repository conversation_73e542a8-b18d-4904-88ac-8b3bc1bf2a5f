package dto

import "openedu-core/models"

type LessonContentRequest struct {
	ID          *string                  `json:"id"`
	UID         *string                  `json:"-"`
	OrgID       string                   `json:"org_id"`
	CourseID    string                   `json:"course_id"`
	SectionID   string                   `json:"section_id"`
	LessonID    string                   `json:"lesson_id"`
	Title       string                   `json:"title"`
	Content     string                   `json:"content"`
	Duration    int                      `json:"duration"`
	Type        models.LessonContentType `json:"type"`
	Order       int                      `json:"order"`
	Files       []*models.File           `json:"files"`
	JsonContent models.JSONB             `json:"json_content"`
	Quizzes     []*QuizRequest           `json:"quizzes"`
}

type ListLessonResponse struct {
	Results    []*models.SimpleLessonContent `json:"results"`
	Pagination *models.Pagination            `json:"pagination"`
}
