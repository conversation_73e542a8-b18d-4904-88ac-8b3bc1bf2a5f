package dto

import "openedu-core/models"

type AffiliateCampaignRequest struct {
	ID        *string `json:"id"`
	Name      string  `json:"name"`
	StartDate int     `json:"start_date"`
	EndDate   int     `json:"end_date"`
	Enable    bool    `json:"enable"`
	//CourseID  string  `json:"course_id"`

	Org  *models.Organization
	User *models.User
}

type ListAffiliateCampaignResponse struct {
	Results    []*models.AffiliateCampaign `json:"results"`
	Pagination *models.Pagination          `json:"pagination"`
}

type ListUserCampaignResponse struct {
	Results    []*models.UserCampaign `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type AffiliateCampaignCourse struct {
	CourseCuids []string `json:"course_cuids"`
	Org         *models.Organization
	User        *models.User
}

type FindCourseCampaignRequest struct {
	Org      *models.Organization
	Campaign *models.AffiliateCampaign
	//IsCurrentOrg bool
	IsPublish bool
}

type ListCourseByCampaignRequest struct {
	Results    []*models.CourseCampaign `json:"results"`
	Pagination *models.Pagination       `json:"pagination"`
}
