package dto

import (
	"openedu-core/models"

	communicationdto "openedu-core/pkg/communication/dto"

	"github.com/shopspring/decimal"
)

type SubscriptionRequest struct {
	ID          *string `json:"id"`
	AutoRenew   bool    `json:"auto_renew"`
	Enable      bool    `json:"enable"`
	PlanOwnerID string  `json:"plan_owner_id"`
	IsGroup     bool    `json:"is_group"`
}

type ListSubscriptionResponse struct {
	Results    []*models.Subscription `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type CheckLimitResourceRequest struct {
	UserID    string              `json:"user_id" form:"user_id"`
	Type      models.ResourceType `json:"type" form:"type"`
	AIModelID string              `json:"ai_model_id" form:"ai_model_id"`
	OrgID     string              `json:"org_id" form:"org_id"`
}

type AddAiUsage struct {
	UserID      string          `json:"user_id"`
	Amount      decimal.Decimal `json:"amount"`
	AIModelName string          `json:"ai_model_name"`
	OrgID       string          `json:"org_id" form:"org_id"`
}

type ValidUsageResponse struct {
	AiUsage *LimitResourceResponse `json:"ai_usage"`
}

type LimitResourceResponse struct {
	BalanceUsed      decimal.Decimal `json:"balance_used"`
	BalanceLimit     decimal.Decimal `json:"balance_limit"`
	Bonus            decimal.Decimal `json:"bonus"`
	BalanceRemaining decimal.Decimal `json:"balance_remaining"`
	RequestUsed      int64           `json:"request_used"`
	RequestLimit     int64           `json:"request_limit"`
	RequestRemaining int64           `json:"request_remaining"`
}

type GetSubscriptionInfo struct {
	Subscription   *models.SimpleSubscription `json:"subscription"`
	ResourceUsages []*models.AiResourceUsage  `json:"resource_usages"`
}

type SubscribeUsersRequest struct {
	Event       *communicationdto.EventType `json:"event"`
	EnableEmail bool                        `json:"enable_email"`
	UserIDs     []string                    `json:"user_ids"`
}

type SubscribePlanRequest struct {
	Event       *communicationdto.EventType `json:"event"`
	EnableEmail bool                        `json:"enable_email"`
	User        *models.User
	Org         *models.Organization
	Plan        *models.PricingPlan
}
