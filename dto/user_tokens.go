package dto

import "openedu-core/models"

type UserTokenInvitationResponse struct {
	models.Model
	User        *models.User     `json:"user" `
	UserID      *string          `json:"user_id"`
	OrgID       string           `json:"org_id"`
	Email       string           `json:"email"`
	Token       string           `json:"token"`
	Event       models.EventType `json:"event"`
	IsExpired   bool             `json:"is_expired"`
	IsVerified  bool             `json:"is_verified"`
	SendEmail   int              `json:"send_email"`
	RedirectUrl string           `json:"redirect_url"`
}

type ListUserTokenInvitationResponse struct {
	Results    []*UserTokenInvitationResponse `json:"results"`
	Pagination *models.Pagination             `json:"pagination"`
}

type ResendMailInvitationRequest struct {
	UserTokenIDs []string `json:"user_token_ids" binding:"required"`
}
