package dto

import "openedu-core/models"

type CategoryRequest struct {
	ID     string              `json:"id"`
	Name   string              `json:"name" validate:"required"`
	Type   models.CategoryType `json:"type" validate:"required,oneof=blog course level"`
	Active bool                `json:"active"`
	Order  int                 `json:"order" validate:"required"`
	Child  []*CategoryRequest  `json:"child"`
}

type UpsertCategoriesRequest struct {
	Categories []*CategoryRequest `json:"categories" validate:"required,dive"`
}

type CreateCategoriesParams struct {
	Categories []*CategoryRequest
	OrgID      string
}

type ListCategoryResponse struct {
	Results    []*models.SimpleCategory `json:"results"`
	Pagination *models.Pagination       `json:"pagination"`
}

type DeleteCategoryRequest struct {
	IDs []string `json:"ids" form:"ids" validate:"required"`
}

type CreateCategoryRelationRequest struct {
	CategoryRelations []*CreateCategoryRelationParams `json:"category_relations" validate:"required"`
}

type CreateCategoryRelationParams struct {
	CategoryIDs []string         `json:"category_ids"`
	RelatedID   string           `json:"related_id"`
	RelatedType models.ModelName `json:"related_type"`
	Field       string           `json:"field"`
	Order       int              `json:"order"`
}

type GetTreeCategoryRequest struct {
	Active bool                 `json:"active" form:"active" validate:"required"`
	Type   *models.CategoryType `json:"type" form:"type" validate:"oneof=blog course"`
}
