package dto

import "openedu-core/models"

type GenerateOEReferralReportsRequest struct {
	From      int `json:"from"`
	To        int `json:"to"`
	BatchSize int `json:"batch_size"`
}

type ListOEReferralLeaderBoardResponse struct {
	Results    []*models.OEReferralLeaderBoard `json:"results"`
	Pagination *models.Pagination              `json:"pagination"`
}

type OEReferralStatisticResponse struct {
	RegisterCount int64 `json:"register_count"`
	RefCount      int64 `json:"ref_count"`
	CertCount     int64 `json:"cert_count"`
}

type OEReferralLocalResponse struct {
	models.OEReferralLeaderBoard
	RefCode   string `json:"ref_code"`
	UnitCount int64  `json:"unit_count"`
}

type OERefWidgetStatisticRequest struct {
	CourseCUIDs []string `form:"course_cuids" json:"course_cuids"` // List of course IDs
	FromDate    int64    `form:"from_date" json:"from_date"`       // Start date (Unix millisecond timestamp)
	ToDate      int64    `form:"to_date" json:"to_date"`           // End date (Unix millisecond timestamp)
}

type OERefWidgetStatisticResponse struct {
	TotalRegisteredUsers int64   `json:"total_registered_users"` // Total users who registered an account
	TotalEnrolledUsers   int64   `json:"total_enrolled_users"`   // Total users enrolled in courses based on filters
	TotalCompletedUsers  int64   `json:"total_completed_users"`  // Total users who completed courses and received certificates
	CompletionRate       float64 `json:"completion_rate"`        // Completion rate percentage
}

type OERefLearnerGrowthStatisticRequest struct {
	CourseCUIDs []string `form:"course_cuids" json:"course_cuids"` // List of course IDs
	FromDate    int64    `form:"from_date" json:"from_date"`       // Start date (Unix millisecond timestamp)
	ToDate      int64    `form:"to_date" json:"to_date"`           // End date (Unix millisecond timestamp)
	GroupBy     string   `form:"group_by" json:"group_by"`         // Available value: hour, day, month
	Timezone    string   `form:"timezone" json:"timezone"`
}

type OERefStudentGrowthDataPoint struct {
	Timestamp  string  `json:"timestamp"`
	TimeLabel  string  `json:"time_label"`
	Value      int64   `json:"value"`
	GrowthRate float64 `json:"growth_rate"`
}

type OERefLearnerGrowthStatisticResponse struct {
	GroupBy string                         `json:"group_by"`
	Points  []*OERefStudentGrowthDataPoint `json:"points"`
}

type OERefSectionCompletionRequest struct {
	CourseCUIDs []string `form:"course_cuids" validate:"required"`
	FromDate    int64    `form:"from_date" validate:"required"`
	ToDate      int64    `form:"to_date" validate:"required"`
}

type OERefSectionCompletionItem struct {
	SectionUID     string `json:"section_uid"`
	SectionName    string `json:"section_name"`
	Order          int    `json:"order"`
	CompletedCount int64  `json:"completed_count"`
}

type OERefSectionCompletionCourse struct {
	CourseCUID  string                       `json:"course_cuid"`
	CourseName  string                       `json:"course_name"`
	ModuleItems []OERefSectionCompletionItem `json:"module_items"`
}

type OERefSectionCompletionResponse []*OERefSectionCompletionCourse

type OERefLearnerCountByProvinceRequest struct {
	CourseCUIDs []string `form:"course_cuids" json:"course_cuids"`
	FromDate    int64    `form:"from_date" validate:"required"`
	ToDate      int64    `form:"to_date" validate:"required"`
	Provinces   []string `form:"provinces" json:"provinces"`
}

type OERefProvinceLearnerCount struct {
	Province            string  `json:"province"`
	LearnerCount        int64   `json:"learner_count"`
	LearnerPercent      float64 `json:"learner_percent"`
	EnrollCount         int64   `json:"enroll_count"`
	CompletionCount     int64   `json:"completion_count"`
	CertificateCount    int64   `json:"certificate_count"`
	CertOnEnrollPercent float64 `json:"cert_on_enroll_percent"`
}

type OERefLearnerCountByProvinceResponse []*OERefProvinceLearnerCount

type OERefDetailsByProvinceStatsRequest struct {
	CourseCUIDs []string `form:"course_cuids" json:"course_cuids"`
	FromDate    int64    `form:"from_date" validate:"required"`
	ToDate      int64    `form:"to_date" validate:"required"`
	Provinces   []string `form:"provinces" json:"provinces"`
}

type OERefByProvinceCourseItem struct {
	Name          string                        `json:"name"`
	ActiveSection int                           `json:"active_section"`
	Sections      []*OERefByProvinceSectionItem `json:"sections"`
}

type OERefByProvinceSectionItem struct {
	SectionUID     string `json:"section_uid"`
	SectionName    string `json:"section_name"`
	Order          int    `json:"order"`
	CompletedCount int64  `json:"completed_count"`
}

type OERefByProvinceStatItem struct {
	Province       string                       `json:"province"`
	RegisterCount  int64                        `json:"register_count"`
	EnrollCount    int64                        `json:"enroll_count"`
	CompleteCount  int64                        `json:"complete_count"`
	CompletionRate float64                      `json:"completion_rate"`
	Courses        []*OERefByProvinceCourseItem `json:"courses" gorm:"-"`
}

type OERefDetailsByProvinceStatsResponse []*OERefByProvinceStatItem

type OERefLearnersByCampaignRequest struct {
	CourseCUIDs []string `form:"course_cuids" json:"course_cuids"`
	FromDate    int64    `form:"from_date" validate:"required"`
	ToDate      int64    `form:"to_date" validate:"required"`
	Provinces   []string `form:"provinces" json:"provinces"`
	Page        int      `form:"page" json:"page"`
	PerPage     int      `form:"per_page" json:"per_page"`
}

type OERefLearnerCourseItem struct {
	Name                     string `json:"name"`
	ActiveSection            int    `json:"active_section"`
	EnrollDate               int64  `json:"enroll_date"`
	NumberOfCompletedSection int    `json:"number_of_completed_section"`
	CanClaimCert             bool   `json:"can_claim_cert"`
	ClaimCertDate            int64  `json:"claim_cert_date"`
}

type OERefLearner struct {
	ID       string                    `json:"id"`
	FullName string                    `json:"full_name"`
	Email    string                    `json:"email"`
	Job      string                    `json:"job"`
	AgeGroup string                    `json:"age_group"`
	School   string                    `json:"school"`
	Province string                    `json:"province"`
	Source   string                    `json:"source"`
	Courses  []*OERefLearnerCourseItem `json:"courses"`
}

type OERefLearnersByCampaignResponse struct {
	Results    []*OERefLearner    `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}
