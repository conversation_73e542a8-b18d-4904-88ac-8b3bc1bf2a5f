package dto

import "openedu-core/models"

type HtmlTemplateRequest struct {
	Name        string              `json:"name" validate:"required"`
	CourseCuid  *string             `json:"course_cuid,omitempty"`
	FileID      string              `json:"file_id"`
	Type        models.TemplateType `json:"type" validate:"required"`
	CreatorName *string             `json:"creator_name,omitempty"`
	CourseName  *string             `json:"course_name,omitempty"`
	Enable      *bool               `json:"enable,omitempty" gorm:"default:false"`
	Props       *models.JSONB       `json:"props"`
	Template    *models.JSONB       `json:"template"`
	IsDefault   *bool               `json:"is_default"`
}

type UpdateHtmlTemplateRequest struct {
	Name          *string       `json:"name"`
	FileID        *string       `json:"file_id"`
	CreatorName   *string       `json:"creator_name"`
	CourseName    *string       `json:"course_name"`
	Props         *models.JSONB `json:"props"`
	Template      *models.JSONB `json:"template"`
	IsDefault     *bool         `json:"is_default"`
	EnableProject *bool         `json:"enable_project"`
}

type ListHtmlTemplateResponse struct {
	Results    []*models.HtmlTemplate `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type FindPageHTMLTemplateRequest struct {
	Type     *string `json:"type" form:"type" validate:"required"`
	CourseID *string `json:"course_id" form:"course_id"`
}

type EnableCertificateRequest struct {
	CourseName    string                          `json:"course_name"`
	Organizations models.CertificateOrganizations `json:"organizations"`
	Signatures    models.Signatures               `json:"signatures"`
}

type GetFullTemplateRequest struct {
	IsFull *bool `json:"is_full" form:"is_full"`
}
