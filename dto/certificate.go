package dto

import (
	"github.com/shopspring/decimal"
	"openedu-core/models"
)

type CertificateRequest struct {
	CourseCuid  string       `json:"course_cuid" validate:"required"`
	File        *models.File `json:"file" validate:"required"`
	Image       *models.File `json:"image"`
	CompletedAt int          `json:"completed_at" validate:"required"`
}

type ListCertificateResponse struct {
	Results    []*CertificateResponse `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type CheckingCertificateConditionRequest struct {
	UserID string         `json:"user_id"`
	Course *models.Course `json:"course" validate:"required"`
	OrgID  string         `json:"org_id"`
}

type CertificateResponse struct {
	ID             string                   `json:"id"`
	UserID         string                   `json:"user_id"`
	CourseName     string                   `json:"course_name"`
	CompletedAt    int                      `json:"completed_at"`
	Files          []*models.File           `json:"files"`
	MintNFTEnabled bool                     `json:"mint_nft_enabled"`
	NftTokenID     string                   `json:"nft_token_id"`
	NftTxHash      string                   `json:"nft_tx_hash"`
	NftNetwork     models.BlockchainNetwork `json:"nft_network"`
	Props          models.CertificateProps  `json:"props"`
}

type CertificateDetailResponse struct {
	ID             string                   `json:"id"`
	User           *UserCertificateProfile  `json:"user"`
	Course         *models.SimpleCourse     `json:"course"`
	Files          []*models.File           `json:"files"`
	MintNFTEnabled bool                     `json:"mint_nft_enabled"`
	NftTokenID     string                   `json:"nft_token_id"`
	NftTxHash      string                   `json:"nft_tx_hash"`
	NftNetwork     models.BlockchainNetwork `json:"nft_network"`
	Props          models.CertificateProps  `json:"props"`
}

type UserCertificateProfile struct {
	ID          string `json:"id"`
	Username    string `json:"username"`
	DisplayName string `json:"display_name"`
	Avatar      string `json:"avatar"`
	TotalPoints int    `json:"total_points"`
	CompletedAt int    `json:"completed_at"`
}

type CertificateNFTFeesResponse struct {
	MintNFTEnabled       bool                     `json:"mint_nft_enabled"`
	GasFeePayerInSetting models.Participant       `json:"gas_fee_payer_in_setting"`
	ActualGasFeePayer    models.Participant       `json:"actual_gas_fee_payer"`
	EstimatedFee         decimal.Decimal          `json:"estimated_fee"`
	SponsorBalance       decimal.Decimal          `json:"sponsor_balance"`
	Network              models.BlockchainNetwork `json:"network"`
}

type MintCertificateNFTRequest struct {
	Certificate *models.Certificate 	 `json:"-"`
	User        *models.User        	 `json:"-"`
	Network     models.BlockchainNetwork `json:"network"`
	GasFeePayer models.Participant  	 `json:"gas_fee_payer"`
}

type ClaimCertificateRequest struct {
	Org                *models.Organization
	User               *models.User
	Cert               *models.Certificate
	Course             *models.Course
	CheckCertCondition bool
}

type IssueCertificateRequest struct {
	UserID      string       `json:"user_id" validate:"required"`
	CourseCuid  string       `json:"course_cuid" validate:"required"`
	File        *models.File `json:"file" validate:"required"`
	Image       *models.File `json:"image"`
	CompletedAt int          `json:"completed_at" validate:"required"`
}
