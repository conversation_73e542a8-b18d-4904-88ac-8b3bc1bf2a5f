package dto

import "openedu-core/models"

type CreateApprovalRequest struct {
	EntityType    models.ModelName    `json:"entity_type,omitempty" validate:"required,checkEntityType"`
	EntityID      string              `json:"entity_id,omitempty" validate:"required"`
	EntityUID     string              `json:"entity_uid"`
	Type          models.ApproveType  `json:"type,omitempty" validate:"required,checkApproveType"`
	Value         string              `json:"value"`
	Props         models.ApprovalProp `json:"props"`
	Note          string              `json:"note"`
	RequestUid    string              `json:"request_uid"`
	EntityVersion int                 `json:"entity_version"`
	PreVersion    *int                `json:"pre_version"`
	PreID         *string             `json:"pre_id"`
}

type UpdateApprovalRequest struct {
	Status         models.ApprovalStatus `json:"status,omitempty" validate:"required,checkApproveStatus"`
	ScheduleEntity *int64                `json:"schedule_entity,omitempty"`
}

type ApprovalRequest struct {
	Note  string         `json:"note"`
	Value *string        `json:"value"`
	Files []*models.File `json:"files"`

	Org *models.Organization
}

type ApprovalFeedback struct {
	ID              *string `json:"id"`
	Content         string  `json:"content"`
	IsAdminFeedback bool    `json:"is_admin_feedback"`
	EntityID        *string `json:"entity_id"`
	EntityVersion   *int    `json:"entity_version"`

	User *models.User
}

type ListApprovalResponse struct {
	Results    []*models.Approval `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}
