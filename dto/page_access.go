package dto

import "openedu-core/models"

type PageAccessRequest struct {
	ID     *string `json:"id"`
	Role   string  `json:"role" validate:"required"`
	Entity string  `json:"entity" validate:"required"`
	Action string  `json:"action" validate:"required"`
	Allow  bool    `json:"allow" validate:"required"`
	OrgID  string  `json:"org_id"`
}

type BulkCreatePageAccessRequest struct {
	PageAccess []PageAccessRequest `json:"page_access"`
}

type ListPageAccessResponse struct {
	Results    []*models.PageAccess `json:"results"`
	Pagination *models.Pagination   `json:"pagination"`
}
