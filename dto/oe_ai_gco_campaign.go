package dto

import "openedu-core/models"

type OELeaderBoardAddMod struct {
	CampaignKey string            `json:"campaign_key"`
	User        *models.User      `json:"user"`
	Parent      *models.User      `json:"creator"`
	LocalLevel  models.LocalLevel `json:"local_level"`
	DisplayName string            `json:"display_name"`
	RefCode     string            `json:"ref_code"`
	RefCodeID   string            `json:"ref_code_id"`

	OrgID  string `json:"org_id"`
	RoleID string `json:"role_id"`
}
