package dto

import (
	"openedu-core/models"
	communicationdto "openedu-core/pkg/communication/dto"
)

type CreateEmailTemplateRequest struct {
	Name    string               `json:"name" validate:"required"`
	Subject string               `json:"subject"`
	Html    string               `json:"html"`
	Json    string               `json:"json"`
	Code    models.EmailCodeType `json:"code"`
}

func (req *CreateEmailTemplateRequest) IntoComm(user *models.User, org *models.Organization) *communicationdto.CreateEmailTemplateRequest {
	return &communicationdto.CreateEmailTemplateRequest{
		Name:    req.Name,
		Subject: req.Subject,
		Html:    req.Html,
		Json:    req.Json,
		Code:    communicationdto.EmailCodeType(req.Code),
		User:    user.IntoComm(),
		Org:     org.IntoComm(),
	}
}

type UpdateEmailTemplateRequest struct {
	Name    string               `json:"name" `
	Subject string               `json:"subject"`
	Html    string               `json:"html"`
	Json    string               `json:"json"`
	Code    models.EmailCodeType `json:"code"`
}

func (req *UpdateEmailTemplateRequest) IntoComm(user *models.User, org *models.Organization) *communicationdto.UpdateEmailTemplateRequest {
	return &communicationdto.UpdateEmailTemplateRequest{
		Name:    req.Name,
		Subject: req.Subject,
		Html:    req.Html,
		Json:    req.Json,
		Code:    communicationdto.EmailCodeType(req.Code),
		User:    user.IntoComm(),
		Org:     org.IntoComm(),
	}
}

type PreviewEmailTemplateRequest struct {
	Emails []string                       `json:"emails" validate:"required"`
	Data   models.EmailTemplateDataParams `json:"data" validate:"required"`
}
