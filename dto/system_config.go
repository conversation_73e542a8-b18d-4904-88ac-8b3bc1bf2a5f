package dto

import "openedu-core/models"

type SystemConfigRequest struct {
	Key             string `json:"key" validate:"required"`
	Locale          string `json:"locale"`
	Value           string `json:"value" validate:"required"`
	DataType        string `json:"data_type"`
	OrgID           string `json:"org_id"`
	Domain          string `json:"domain"`
	AltDomain       string `json:"alt_domain"`
	IsStorageInFile bool   `json:"is_storage_in_file"`
}

type ListSystemConfigResponse struct {
	Results    []*models.SystemConfig `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type QuerySystemConfig struct {
	Keys    []*string `json:"keys" form:"keys"`
	Locales []*string `json:"locales" form:"locales"`
	OrgIds  []*string `json:"org_ids" form:"org_ids"`
	Domains []*string `json:"domains" form:"domains"`
}
