package dto

import "openedu-core/models"

type CreateUser struct {
	Email       string  `json:"email"`
	Username    string  `json:"username"`
	Password    string  `json:"password"`
	DisplayName string  `json:"display_name"`
	Phone       string  `json:"phone"`
	Active      string  `json:"active"`
	Role        string  `json:"role"`
	OrgID       *string `json:"org_id"`
}

type UpdateProfileRequest struct {
	Headline    *string                    `json:"headline"`
	About       *string                    `json:"about"`
	Skills      *[]string                  `json:"skill"`
	Username    *string                    `json:"username"`
	DisplayName *string                    `json:"display_name"`
	Phone       *string                    `json:"phone"`
	Avatar      *string                    `json:"avatar"`
	CoverPhoto  *string                    `json:"cover_photo"`
	Props       *models.UserSettingsOption `json:"props"`
}

type GetUserProfileByIDParams struct {
	User           *models.User
	TargetUsername string
}

type UserProfileResponse struct {
	ID           string                       `json:"id"`
	Username     string                       `json:"username"`
	Email        string                       `json:"email"`
	Active       bool                         `json:"active"`
	Blocked      bool                         `json:"blocked"`
	DisplayName  string                       `json:"display_name"`
	Avatar       string                       `json:"avatar"`
	Roles        []*models.SimpleUserRole     `json:"roles"`
	CoverPhoto   string                       `json:"cover_photo"`
	Skills       []string                     `json:"skills"`
	Headline     string                       `json:"headline"`
	About        string                       `json:"about"`
	Phone        string                       `json:"phone"`
	Position     string                       `json:"position"`
	Following    int64                        `json:"following"`
	Followers    int64                        `json:"followers"`
	Props        models.UserSettingsOption    `json:"props"`
	TotalCourses int64                        `json:"total_courses"`
	Status       models.ActionType            `json:"status"`
	Certificate  CertificateProfile           `json:"certificate"`
	Course       CourseProfile                `json:"course"`
	Blog         BlogProfile                  `json:"blog"`
	TotalBlogs   int64                        `json:"total_blogs"`
	WriterInOrgs []*models.SimpleOrganization `json:"writer_in_orgs"`
}

type ChangePasswordRequest struct {
	NewPassword string `json:"new_password" validate:"required,min=8"`
	OldPassword string `json:"old_password" validate:"required"`
}

type ChangePasswordParams struct {
	User        *models.User
	NewPassword string
	OldPassword string
}

type CreateCreatorRequest struct {
	FormSessionID *string `json:"form_session_id"`

	Email       string `json:"email" validate:"required"`
	DisplayName string `json:"display_name" validate:"required"`
	Phone       string `json:"phone" validate:"required"`
}

type CreateWriterRequest struct {
	FormSessionID *string `json:"form_session_id"`

	Email       string `json:"email" validate:"required"`
	DisplayName string `json:"display_name" validate:"required"`
	Phone       string `json:"phone" validate:"required"`
}

type InviteCreatorRequest struct {
	CreatorEmails   []string `json:"creator_emails" validate:"required"`
	AllowFieldsData map[string]interface{}
}

type ListCreatorsResponse struct {
	Results    []*models.User     `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}

type AcceptInviteRequest struct {
	Token string       `json:"token" validate:"required"`
	User  *models.User `json:"-"`
	Role  string       `json:"role"`
}

type UpdateUserRequest struct {
	DisplayName string `json:"display_name"`
	Phone       string `json:"phone"`
	Active      bool   `json:"active"`
	Avatar      string `json:"avatar"`
	Blocked     bool   `json:"blocked"`
	Password    string `json:"password"`
}

type ListUserResponse struct {
	Results    []*models.SimpleProfile `json:"results"`
	Pagination *models.Pagination      `json:"pagination"`
}

type AddRemoveRoleRequest struct {
	AddIds    []RoleOrgParamsRequest `json:"add_ids"`
	RemoveIds []RoleOrgParamsRequest `json:"remove_ids"`
	OrgID     *string                `json:"org_id"`
}

type RoleOrgParamsRequest struct {
	UserID string   `json:"user_id"`
	RoleID []string `json:"role_id"`
}

type InviteUserRequest struct {
	UserEmails      []string          `json:"user_emails" validate:"required"`
	Event           models.EventType  `json:"event"`
	ObjectType      *models.ModelName `json:"object_type"`
	ObjectID        *string           `json:"object_id"`
	AllowFieldsData map[string]interface{}
}

type AcceptInviteUserRequest struct {
	Token string       `json:"token" validate:"required"`
	User  *models.User `json:"-"`
}

type CertificateProfile struct {
	IsShow  bool                  `json:"is_show" default:"false"`
	Count   int                   `json:"count"`
	Results []*models.Certificate `json:"results"`
}

type CourseProfile struct {
	IsShow  bool                   `json:"is_show" default:"false"`
	Count   int                    `json:"count"`
	Results []*models.SimpleCourse `json:"results"`
}

type BlogProfile struct {
	IsShow  bool                 `json:"is_show" default:"false"`
	Count   int                  `json:"count"`
	Results []*models.SimpleBlog `json:"results"`
}

type RoleOrgRequest struct {
	UserID string `json:"user_id"`
	OrgID  string `json:"org_id"`
}

type AdminCreateUserWithRole struct {
	CampaignKey string                   `json:"campaign_key"`
	LocalLevel  models.LocalLevel        `json:"local_level"`
	RoleID      string                   `json:"role_id" validate:"required"`
	OrgID       string                   `json:"org_id" validate:"required"`
	Users       []AdminCreateUserRequest `json:"users"`
}

type AdminCreateUserRequest struct {
	DisplayName  string `json:"display_name"`
	Email        string `json:"email"`
	ReferralCode string `json:"referral_code"`
	Phone        string `json:"phone"`
	Password     string `json:"password"`

	ID           *string                `json:"id"`
	TempPassword *string                `json:"temp_password"`
	Error        *string                `json:"error"`
	ErrorCode    *int                   `json:"error_code"`
	RefCode      *models.OEReferralCode `json:"ref_code"`
}
