package dto

import (
	"openedu-core/models"

	"github.com/shopspring/decimal"
)

type RefUserConfigRequest struct {
	ID        *string                       `json:"id"`
	Name      string                        `json:"name"`
	Scope     models.ReferralScope          `json:"scope"`
	Enabled   bool                          `json:"enabled"`
	Entities  models.ReferralCampaignEntity `json:"entities"`
	StartDate int                           `json:"start_date"`
	EndDate   int                           `json:"end_date"`

	Setting models.ReferralInviteUserProgram `json:"setting"`
}

type CreatePointCampaignRequest struct {
	Name             string            `json:"name"`
	Point            decimal.Decimal   `json:"point"`
	Currency         models.Currency   `json:"currency"`
	Rate             decimal.Decimal   `json:"rate"` // 1P = rate USD
	Enable           bool              `json:"enable"`
	ExpirationPeriod models.TimePeriod `json:"expiration_period"`
	ExpirationVal    int               `json:"expiration_val"`
	GracePeriod      models.TimePeriod `json:"grace_period"`
	GraceVal         int               `json:"grace_val"`
}

type ListPointCampaignResponse struct {
	Results    []*models.OEPointCampaign `json:"results"`
	Pagination *models.Pagination        `json:"pagination"`
}

type UpdatePointCampaignRequest struct {
	Name             string            `json:"name"`
	Point            decimal.Decimal   `json:"point"`
	Currency         models.Currency   `json:"currency"`
	Rate             decimal.Decimal   `json:"rate"` // 1P = rate USD
	Enable           bool              `json:"enable"`
	ExpirationPeriod models.TimePeriod `json:"expiration_period"`
	ExpirationVal    int               `json:"expiration_val"`
	GracePeriod      models.TimePeriod `json:"grace_period"`
	GraceVal         int               `json:"grace_val"`
}
