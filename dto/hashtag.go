package dto

import "openedu-core/models"

type CreateHashtagRequest struct {
	Names []*string `json:"names" validate:"required"`
}

type CreateHashtagRelationParams struct {
	HashtagID   string           `json:"hashtag_id"`
	RelatedID   string           `json:"related_id"`
	RelatedType models.ModelName `json:"related_type"`
}

type DeleteHashtagRequest struct {
	IDs []string `json:"ids" validate:"required"`
}

type ListHashtagResponse struct {
	Results    []*models.SimpleHashtag `json:"results"`
	Pagination *models.Pagination      `json:"pagination"`
}

type ListHashtagRelationResponse struct {
	Results    []*models.SimpleHashtagRelation `json:"results"`
	Pagination *models.Pagination              `json:"pagination"`
}
