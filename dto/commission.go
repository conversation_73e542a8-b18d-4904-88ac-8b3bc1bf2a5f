package dto

import "openedu-core/models"

type CommissionRequest struct {
	CampaignID string  `json:"campaign_id"`
	Ref1Rate   float32 `json:"ref1_rate"`
	Qty1       int     `json:"qty1"`
	Ref2Rate   float32 `json:"ref2_rate"`
	Qty2       int     `json:"qty2"`
	Ref3Rate   float32 `json:"ref3_rate"`
	Qty3       int     `json:"qty3"`
	Enable     bool    `json:"enable"`
	IsBaseRate bool    `json:"is_base_rate"`
	Order      int     `json:"order"`

	ReferrerIDs   models.StringArray        `json:"referrer_ids"`
	ReferrerTypes models.StringArray        `json:"referrer_types"`
	Type          models.CommissionType     `json:"type"`
	Bonuses       []*CommissionBonusRequest `json:"bonuses"`

	Org  *models.Organization
	User *models.User
}

type CommissionBonusRequest struct {
	ID       *string               `json:"id"`
	Type     models.CommissionType `json:"type"`
	Ref1Rate float32               `json:"ref1_rate"`
	Qty1     int                   `json:"qty1"`
	Enable   bool                  `json:"enable"`
	Order    int                   `json:"order"`
}

type CommissionDeleteRequest struct {
	CampaignID string   `json:"campaign_id" validate:"required" form:"campaign_id"`
	IDs        []string `json:"ids" form:"ids"`
}

type ListCommissionResponse struct {
	Results    []*models.Commission `json:"results"`
	Pagination *models.Pagination   `json:"pagination"`
}
