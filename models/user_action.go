package models

import (
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ActionType string

const (
	Followed ActionType = "followed"
	Blocked  ActionType = "blocked"
	Reported ActionType = "reported"
)

type ActionRelationType string

const (
	Followers ActionRelationType = "followers"
	Following ActionRelationType = "following"
	Block     ActionRelationType = "block"
	Report    ActionRelationType = "report"
	Unblock   ActionRelationType = "unblock"
)

type UserAction struct {
	Model
	UserID       string     `json:"user_id" validate:"required"`
	User         *User      `json:"user"`
	TargetUserID string     `json:"target_user_id" validate:"required"`
	TargetUser   *User      `json:"target_user"`
	Action       ActionType `json:"action" validate:"required"`
	Reason       string     `json:"reason"`
}

type SimpleUserAction struct {
	Model
	UserID       string      `json:"user_id"`
	User         *SimpleUser `json:"user"`
	TargetUserID string      `json:"target_user_id"`
	TargetUser   *SimpleUser `json:"target_user"`
	Action       ActionType  `json:"action"`
	Reason       string      `json:"reason"`
}

type UserActionQuery struct {
	ID             *string `json:"id" form:"id"`
	UserID         *string `json:"user_id"`
	User           *User   `json:"user"`
	TargetUserID   *string `json:"target_user_id"`
	TargetUserIDIn []string
	TargetUser     *User       `json:"target_user"`
	Action         *ActionType `json:"action"`
	ActionNe       *ActionType `json:"action_ne"`
	Reason         *string     `json:"reason"`
	IDIn           []string
	IncludeDeleted *bool
}

func (ua *UserAction) Sanitize() *SimpleUserAction {
	user := &SimpleUserAction{
		Model:        ua.Model,
		UserID:       ua.UserID,
		TargetUserID: ua.TargetUserID,
		Action:       ua.Action,
		Reason:       ua.Reason,
	}

	if ua.User != nil {
		user.User = ua.User.ToSimpleUser()
	}

	if ua.TargetUser != nil {
		user.TargetUser = ua.TargetUser.ToSimpleUser()
	}

	return user
}

func (query *UserActionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.TargetUserID != nil {
		qb = qb.Where("target_user_id = ?", *query.TargetUserID)
	}

	if query.ActionNe != nil {
		qb = qb.Where("action <> ?", *query.ActionNe)
	}

	if query.Action != nil {
		qb = qb.Where("action = ?", *query.Action)
	}

	if query.Reason != nil {
		qb = qb.Where("reason = ?", *query.Reason)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if len(query.TargetUserIDIn) > 0 {
		qb = qb.Where("target_user_id IN (?)", query.TargetUserIDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a UserAction to database, transaction is optional
func (r *UserActionRepository) Create(ua *UserAction, trans *gorm.DB) error {
	return create(UserActionTbl, ua, trans)
}

func (r *UserActionRepository) CreateMany(uas []*UserAction, trans *gorm.DB) error {
	return createMany(UserActionTbl, uas, trans)
}

// Update updates a UserAction by ID in database, transaction is optional
func (r *UserActionRepository) Update(ua *UserAction, trans *gorm.DB) error {
	return update(UserActionTbl, ua, trans)
}

// Update upsert a UserAction by ID in database, transaction is optional
func (r *UserActionRepository) Upsert(ua *UserAction, trans *gorm.DB) (*bool, error) {
	var tx *gorm.DB
	// Begin trans
	if trans != nil {
		tx = trans
	} else {
		tx = GetDb(UserActionTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	isUpdated := false

	result := tx.Where("user_id = ? AND action = ? AND target_user_id = ?", ua.UserID, ua.Action, ua.TargetUserID).
		FirstOrCreate(&ua)

	if err := result.Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if result.RowsAffected == 0 {
		isUpdated = true
	}

	// Commit tx
	return &isUpdated, tx.Commit().Error
}

// FindByID finds a UserAction by ID with given find options, transaction is optional
func (r *UserActionRepository) FindByID(id string, options *FindOneOptions) (*UserAction, error) {
	return findByID[UserAction](UserActionTbl, id, options)
}

// FindOne finds one UserAction with given find queries and options, transaction is optional
func (r *UserActionRepository) FindOne(query *UserActionQuery, options *FindOneOptions) (*UserAction, error) {
	return findOne[UserAction](UserActionTbl, query, options)
}

// FindMany finds UserActions by query conditions with give find options
func (r *UserActionRepository) FindMany(query *UserActionQuery, options *FindManyOptions) ([]*UserAction, error) {
	return findMany[UserAction](UserActionTbl, query, options)
}

// FindPage returns UserActions and pagination by query conditions and find options, transaction is optional
func (r *UserActionRepository) FindPage(query *UserActionQuery, options *FindPageOptions) ([]*UserAction, *Pagination, error) {
	return findPage[UserAction](UserActionTbl, query, options)
}

// Delete perform soft deletion to a UserActions by ID, transaction is optional
func (r *UserActionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[UserAction](UserActionTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *UserActionRepository) DeleteMany(query *UserActionQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[UserAction](UserActionTbl, query, trans)
}

// Delete perform deletion to a UserActions by ID, transaction is optional
func (r *UserActionRepository) DeleteHard(id string, trans *gorm.DB) error {
	var tx *gorm.DB
	// Begin trans
	if trans != nil {
		tx = trans
	} else {
		tx = GetDb(UserActionTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	result := tx.Delete(&UserAction{}, "id = ?", id)

	if err := result.Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit tx
	return tx.Commit().Error
}

// DeleteHardMultiple deletes multiple UserActions by their IDs, transaction is optional
func (r *UserActionRepository) DeleteHardMultiple(ids []string, trans *gorm.DB) error {
	var tx *gorm.DB
	// Begin transaction
	if trans != nil {
		tx = trans
	} else {
		tx = GetDb(UserActionTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	// Perform the deletion for all provided IDs
	result := tx.Delete(&UserAction{}, "id IN ?", ids)

	if err := result.Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	return tx.Commit().Error
}

// Count returns number of UserActions by query conditions, transaction is optional
func (r *UserActionRepository) Count(query *UserActionQuery) (int64, error) {
	return count[UserAction](UserActionTbl, query)
}

func (r *UserActionRepository) UpsertMany(userActions []*UserAction, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "target_user_id"}, {Name: "action"}},
		DoUpdates: clause.AssignmentColumns([]string{"update_at", "delete_at"}),
	}).Create(&userActions).Error; err != nil {
		return err
	}

	return nil
}
