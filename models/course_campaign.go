package models

import (
	"gorm.io/gorm"
)

type CourseCampaign struct {
	Model
	UserID     string `json:"user_id" gorm:"type:varchar(20);not null"`
	OrgID      string `json:"org_id" gorm:"type:varchar(20);not null"`
	OrgSchema  string `json:"org_schema" gorm:"type:varchar(50);not null"`
	CourseCuid string `json:"course_cuid" gorm:"type:varchar(20);not null"`
	CampaignID string `json:"campaign_id" gorm:"type:varchar(20);not null"`
	Enable     bool   `json:"enable" gorm:"default:true"`
	StartDate  int    `json:"start_date" gorm:"type:int8;default:0"`
	EndDate    int    `json:"end_date" gorm:"type:int8;default:0"`
	CourseName string `json:"course_name"`
	CourseSlug string `json:"course_slug"`

	Campaign      *AffiliateCampaign   `json:"campaign"`
	Course        *Course              `json:"course" gorm:"-"`
	PublishCourse *SimplePublishCourse `json:"publish_course" gorm:"-"`
}

type CourseCampaignQuery struct {
	ID             *string   `json:"id" form:"id"`
	IDIn           []*string `json:"id_in" form:"id_in"`
	UserID         *string   `json:"user_id" form:"user_id"`
	CourseCuid     *string   `json:"course_cuid" form:"course_cuid"`
	CampaignID     *string   `json:"campaign_id" form:"campaign_id"`
	OrgID          *string   `json:"org_id" form:"org_id"`
	OrgSchema      *string   `json:"org_schema" form:"org_schema"`
	Enable         *bool     `json:"enable" form:"enable"`
	IncludeDeleted *bool     `form:"include_deleted"`
	GteStartDate   *int      `json:"gte_start_date" form:"gte_start_date"`
	LteEndDate     *int      `json:"lte_end_date" form:"lte_end_date"`
}

func (query *CourseCampaignQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *query.CampaignID)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *query.CourseCuid)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.GteStartDate != nil {
		qb = qb.Where("start_date <= ? ", *query.GteStartDate)
	}

	if query.LteEndDate != nil {
		qb = qb.Where("end_date >= ? ", *query.LteEndDate)
	}

	return qb
}

func (r *CourseCampaignRepository) Create(e *CourseCampaign, trans *gorm.DB) error {
	return create(CourseCampaignTbl, e, trans)
}

func (r *CourseCampaignRepository) CreateMany(ts []*CourseCampaign, trans *gorm.DB) error {
	return createMany(CourseCampaignTbl, ts, trans)
}

func (r *CourseCampaignRepository) Update(f *CourseCampaign, trans *gorm.DB) error {
	return update(CourseCampaignTbl, f, trans)
}

func (r *CourseCampaignRepository) FindOne(query *CourseCampaignQuery, options *FindOneOptions) (*CourseCampaign, error) {
	return findOne[CourseCampaign](CourseCampaignTbl, query, options)
}

func (r *CourseCampaignRepository) FindPage(query *CourseCampaignQuery, options *FindPageOptions) ([]*CourseCampaign, *Pagination, error) {
	return findPage[CourseCampaign](CourseCampaignTbl, query, options)
}

func (r *CourseCampaignRepository) FindMany(query *CourseCampaignQuery, options *FindManyOptions) ([]*CourseCampaign, error) {
	return findMany[CourseCampaign](CourseCampaignTbl, query, options)
}

func (r *CourseCampaignRepository) Count(query *CourseCampaignQuery) (int64, error) {
	return count[CourseCampaign](CourseCampaignTbl, query)
}

func (r *CourseCampaignRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[CourseCampaign](CourseCampaignTbl, id, trans)
}

func (r *CourseCampaignRepository) DeleteMany(query *CourseCampaignQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[CourseCampaign](CourseCampaignTbl, query, trans)
}
