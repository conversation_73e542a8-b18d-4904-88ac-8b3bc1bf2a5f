package models

import (
	"errors"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type StatusVotingLaunchpad string

const (
	StatusVotingApprove     StatusVotingLaunchpad = "approve"
	StatusVotingReject      StatusVotingLaunchpad = "reject"
	StatusVotingAutoApprove StatusVotingLaunchpad = "auto_approve"
)

type ClpVotingLaunchpad struct {
	Model
	ClpInvestmentID      string                `json:"clp_investment_id"`
	ClpInvestment        *ClpInvestment        `json:"clp_investment" gorm:"-"`
	ClpLaunchpadID       string                `json:"clp_launchpad_id"`
	ClpVotingMilestoneID string                `json:"clp_voting_milestone_id"`
	Status               StatusVotingLaunchpad `json:"status"`
	Note                 string                `json:"note"`
}

type ClpVotingLaunchpadQuery struct {
	ID                   *string   `json:"id" form:"id"`
	IDIn                 []*string `json:"id_in" form:"id_in"`
	ClpInvestmentID      *string   `json:"clp_investment_id" form:"clp_investment_id"`
	ClpLaunchpadID       *string   `json:"clp_launchpad_id" form:"clp_launchpad_id"`
	ClpVotingMilestoneID *string   `json:"clp_voting_milestone_id" form:"clp_voting_milestone_id"`
	Status               *string   `json:"status" form:"status"`
	IncludeDeleted       *bool     `json:"include_deleted" form:"include_deleted"`
}

func (query *ClpVotingLaunchpadQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.ClpInvestmentID != nil {
		qb = qb.Where("clp_investment_id = ?", *query.ClpInvestmentID)
	}

	if query.ClpLaunchpadID != nil {
		qb = qb.Where("clp_launchpad_id = ?", *query.ClpLaunchpadID)
	}

	if query.ClpVotingMilestoneID != nil {
		qb = qb.Where("clp_voting_milestone_id = ?", *query.ClpVotingMilestoneID)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

type VotingLaunchpadPreload struct {
	Investment bool
}

func (r *ClpVotingLaunchpadRepository) getExPreloadVotingLaunchpad(preloads []string) (*VotingLaunchpadPreload, []string) {
	shouldPreloadInvestment := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, InvestmentField) {
		shouldPreloadInvestment = true
		newPreloads = util.RemoveElement(newPreloads, InvestmentField)
	}

	return &VotingLaunchpadPreload{
		Investment: shouldPreloadInvestment,
	}, newPreloads
}

func (r *ClpVotingLaunchpadRepository) preloadInvestments(votingLaunchpads []*ClpVotingLaunchpad) error {
	ids := lo.Map(votingLaunchpads, func(item *ClpVotingLaunchpad, _ int) string {
		return item.ClpInvestmentID
	})
	if investments, pErr := Repository.ClpInvestment(r.ctx).FindMany(
		&ClpInvestmentQuery{IDIn: ids},
		&FindManyOptions{}); pErr != nil {
		if !errors.Is(pErr, gorm.ErrRecordNotFound) {
			return pErr
		}
	} else {
		investmentMap := make(map[string]*ClpInvestment)
		for _, investment := range investments {
			investmentMap[investment.ID] = investment
		}

		for _, votingLaunchpad := range votingLaunchpads {
			if investment, exists := investmentMap[votingLaunchpad.ClpInvestmentID]; exists {
				votingLaunchpad.ClpInvestment = investment
			}
		}
	}
	return nil
}

func (r *ClpVotingLaunchpadRepository) Create(e *ClpVotingLaunchpad, trans *gorm.DB) error {
	return create(ClpVotingLaunchpadTbl, e, trans)
}

func (r *ClpVotingLaunchpadRepository) CreateMany(ts []*ClpVotingLaunchpad, trans *gorm.DB) error {
	return createMany(ClpVotingLaunchpadTbl, ts, trans)
}

func (r *ClpVotingLaunchpadRepository) Update(f *ClpVotingLaunchpad, trans *gorm.DB) error {
	return update(ClpVotingLaunchpadTbl, f, trans)
}

func (r *ClpVotingLaunchpadRepository) FindOne(query *ClpVotingLaunchpadQuery, options *FindOneOptions) (*ClpVotingLaunchpad, error) {
	return findOne[ClpVotingLaunchpad](ClpVotingLaunchpadTbl, query, options)
}

func (r *ClpVotingLaunchpadRepository) FindPage(query *ClpVotingLaunchpadQuery, options *FindPageOptions) ([]*ClpVotingLaunchpad, *Pagination, error) {
	return findPage[ClpVotingLaunchpad](ClpVotingLaunchpadTbl, query, options)
}

func (r *ClpVotingLaunchpadRepository) FindMany(query *ClpVotingLaunchpadQuery, options *FindManyOptions) ([]*ClpVotingLaunchpad, error) {
	if options == nil {
		options = &FindManyOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreloadVotingLaunchpad(preloads)
	options.Preloads = newPreloads

	clpVotingLaunchpads, err := findMany[ClpVotingLaunchpad](ClpVotingLaunchpadTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Investment {
		if err := r.preloadInvestments(clpVotingLaunchpads); err != nil {
			return nil, err
		}
	}

	return clpVotingLaunchpads, nil
}

func (r *ClpVotingLaunchpadRepository) Count(query *ClpVotingLaunchpadQuery) (int64, error) {
	return count[ClpVotingLaunchpad](ClpVotingLaunchpadTbl, query)
}

func (r *ClpVotingLaunchpadRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ClpVotingLaunchpad](ClpVotingLaunchpadTbl, id, trans)
}
