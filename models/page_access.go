package models

import (
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type PageAccess struct {
	ID     string `json:"id" gorm:"primaryKey;type:varchar(50);unique"`
	Role   string `json:"role" gorm:"not null"`
	Entity string `json:"entity" gorm:"not null"`
	Action string `json:"action" gorm:"not null"`
	Allow  bool   `json:"allow" gorm:"not null;default:true"`
	OrgID  string `json:"org_id" gorm:"not null"`
}

type SimplePageAccess struct {
	Entity string `json:"entity" gorm:"not null"`
	Action string `json:"action" gorm:"not null"`
	Allow  bool   `json:"allow" gorm:"not null"`
	OrgID  string `json:"org_id" gorm:"not null"`
}

type PageAccessQuery struct {
	ID     *string   `form:"id" json:"id,omitempty"`
	IDIn   []string  `form:"id_in" json:"id_in,omitempty"`
	RoleIn []*string `form:"role_in" json:"role_in,omitempty"`
	Role   *string   `form:"role" json:"role"`
	Entity *string   `form:"entity" json:"entity"`
	Action *string   `form:"action" json:"action"`
	Allow  *bool     `form:"allow" json:"allow"`
	OrgID  *string   `json:"org_id" form:"org_id"`
}

func (query *PageAccessQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}
	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if len(query.RoleIn) > 0 {
		qb = qb.Where("role IN (?)", *&query.RoleIn)
	}

	if query.Role != nil {
		qb = qb.Where("role = ?", *query.Role)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Entity != nil {
		qb = qb.Where("entity = ?", *query.Entity)
	}

	if query.Action != nil {
		qb = qb.Where("action = ?", *query.Action)
	}

	if query.Allow != nil {
		qb = qb.Where("allow = ?", *query.Allow)
	}

	return qb
}
func (r *PageAccessRepository) Create(p *PageAccess, trans *gorm.DB) error {
	Cache.PageAccess.DeleteAll()
	return create(PageAccessTbl, p, trans)
}

func (r *PageAccessRepository) CreateMany(entities []*PageAccess, trans *gorm.DB) error {
	Cache.PageAccess.DeleteAll()
	if err := createMany(PageAccessTbl, entities, trans); err != nil {
		return err
	}
	return nil
}

func (r *PageAccessRepository) FindByID(id string, options *FindOneOptions) (*PageAccess, error) {
	return findByID[PageAccess](PageAccessTbl, id, options)
}

func (r *PageAccessRepository) Update(p *PageAccess, trans *gorm.DB) error {
	Cache.PageAccess.DeleteAll()
	return update(PageAccessTbl, p, trans)
}

func (r *PageAccessRepository) FindOne(query *PageAccessQuery, options *FindOneOptions) (*PageAccess, error) {
	return findOne[PageAccess](PageAccessTbl, query, options)
}

func (r *PageAccessRepository) FindMany(query *PageAccessQuery, options *FindManyOptions) ([]*PageAccess, error) {
	return findMany[PageAccess](PageAccessTbl, query, options)
}

func (r *PageAccessRepository) FindPage(query *PageAccessQuery, options *FindPageOptions) ([]*PageAccess, *Pagination, error) {
	return findPage[PageAccess](PageAccessTbl, query, options)
}

func (r *PageAccessRepository) Delete(id string, trans *gorm.DB) error {
	Cache.PageAccess.DeleteAll()
	return deleteByID[PageAccess](PageAccessTbl, id, trans)
}

func (r *PageAccessRepository) DeleteMany(query *PageAccessQuery, trans *gorm.DB) (int64, error) {
	Cache.PageAccess.DeleteAll()
	return deleteMany[PageAccess](PageAccessTbl, query, trans)
}

func (r *PageAccessRepository) FindAll() ([]*PageAccess, error) {
	var pageAccess []*PageAccess
	var results []interface{}
	Cache.PageAccess.GetAll(results)
	if len(results) > 0 {
		lo.ForEach(results, func(item interface{}, _ int) {
			pageAccess = append(pageAccess, item.(*PageAccess))
		})
		return pageAccess, nil
	}

	// cache new if null
	pageAccess, _ = findMany[PageAccess](PageAccessTbl, &PageAccessQuery{}, &FindManyOptions{})
	pageAccessCache := lo.Map(pageAccess, func(category *PageAccess, _ int) interface{} {
		return category
	})
	Cache.PageAccess.SetAll(pageAccessCache)

	return pageAccess, nil
}

func (p *PageAccess) Sanitize() *SimplePageAccess {
	return &SimplePageAccess{
		Entity: p.Entity,
		Action: p.Action,
		Allow:  p.Allow,
		OrgID:  p.OrgID,
	}
}

func (r *PageAccessRepository) UpdateMany(entities []*PageAccess, trans *gorm.DB) error {
	var tx *gorm.DB
	// Begin trans
	if trans != nil {
		tx = trans

	} else {
		tx = GetDb(PageAccessTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	for _, c := range entities {
		if err := tx.Model(&PageAccess{}).Where("id = ?", c.ID).Updates(map[string]interface{}{
			"role":   c.Role,
			"entity": c.Entity,
			"action": c.Action,
			"allow":  c.Allow,
			"org_id": c.OrgID,
		}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	Cache.PageAccess.DeleteAll()

	return tx.Commit().Error
}
