package models

import (
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
)

type Subscription struct {
	Model
	UserID       string             `json:"user_id" gorm:"type:varchar(20)"`
	PlanID       string             `json:"plan_id" gorm:"type:varchar(20)"`
	StartDate    int                `json:"start_date" gorm:"type:int8;not null;default:0"`
	EndDate      int                `json:"end_date" gorm:"type:int8;not null;default:0"`
	BillingCycle PlanCycle          `json:"billing_cycle"`
	Status       SubscriptionStatus `json:"status"`
	AutoRenew    bool               `json:"auto_renew" gorm:"default:false"`
	Enable       bool               `json:"enable" gorm:"default:true"`
	PlanOwnerID  string             `json:"plan_owner_id"`
	IsGroup      bool               `json:"is_group" gorm:"default:false"`

	User      *User        `json:"user"`
	PlanOwner *User        `json:"plan_owner"`
	Plan      *PricingPlan `json:"plan"`
}

type SimpleSubscription struct {
	Model
	UserID       string             `json:"user_id" gorm:"type:varchar(20)"`
	StartDate    int                `json:"start_date" gorm:"type:int8;not null;default:0"`
	EndDate      int                `json:"end_date" gorm:"type:int8;not null;default:0"`
	BillingCycle PlanCycle          `json:"billing_cycle"`
	AutoRenew    bool               `json:"auto_renew" gorm:"default:false"`
	Status       SubscriptionStatus `json:"status"`
	Enable       bool               `json:"enable" gorm:"default:true"`
	Plan         *SimplePricingPlan `json:"plan"`
}

func (s *Subscription) Santinize() *SimpleSubscription {
	return &SimpleSubscription{
		Model:        s.Model,
		StartDate:    s.StartDate,
		EndDate:      s.EndDate,
		Status:       s.Status,
		Enable:       s.Enable,
		BillingCycle: s.BillingCycle,
		AutoRenew:    s.AutoRenew,
		Plan:         s.Plan.Santinize(),
	}
}

type SubscriptionQuery struct {
	ID      *string             `json:"id" form:"id"`
	UserID  *string             `json:"user_id" form:"user_id"`
	PlanID  *string             `json:"plan_id" form:"plan_id"`
	Enable  *bool               `json:"enable" form:"enable"`
	Status  *SubscriptionStatus `json:"status" form:"status"`
	Deleted *bool               `json:"deleted" form:"deleted"`
	Active  *bool               `json:"active" form:"active"`
}

func (query *SubscriptionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.PlanID != nil {
		qb = qb.Where("plan_id = ?", *query.PlanID)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.Deleted == nil || !*query.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.Active != nil {
		now := time.Now().UnixMilli()
		if *query.Active {
			qb = qb.Where("start_date <= ? and end_date >= ?", int(now), int(now))
		} else {
			qb = qb.Where("end_date <= ?", int(now))
		}
	}

	return qb
}

// Create inserts a Subscription to database, transaction is optional
func (r *SubscriptionRepository) Create(q *Subscription, trans *gorm.DB) error {
	if cErr := Cache.Subscription.DeleteByKey(q.UserID); cErr != nil {
		log.ErrorWithAlertf("Create::Delete subscription cache error: %v", cErr)
	}

	err := create(SubscriptionTbl, q, trans)
	if err != nil {
		return err
	}

	return nil
}

func (r *SubscriptionRepository) CreateMany(qs []*Subscription, trans *gorm.DB) error {
	return createMany(SubscriptionTbl, qs, trans)
}

// Update updates a Subscription by ID in database, transaction is optional
func (r *SubscriptionRepository) Update(q *Subscription, trans *gorm.DB) error {
	return update(SubscriptionTbl, q, trans)
}

// FindByID finds a Subscription by ID with given find options, transaction is optional
func (r *SubscriptionRepository) FindByID(id string, options *FindOneOptions) (*Subscription, error) {
	return findByID[Subscription](SubscriptionTbl, id, options)
}

// FindOne finds one Subscription with given find queries and options, transaction is optional
func (r *SubscriptionRepository) FindOne(query *SubscriptionQuery, options *FindOneOptions) (*Subscription, error) {
	return findOne[Subscription](SubscriptionTbl, query, options)
}

// FindMany finds Subscriptions by query conditions with give find options
func (r *SubscriptionRepository) FindMany(query *SubscriptionQuery, options *FindManyOptions) ([]*Subscription, error) {
	return findMany[Subscription](SubscriptionTbl, query, options)
}

// FindPage returns Subscriptions and pagination by query conditions and find options, transaction is optional
func (r *SubscriptionRepository) FindPage(query *SubscriptionQuery, options *FindPageOptions) ([]*Subscription, *Pagination, error) {
	return findPage[Subscription](SubscriptionTbl, query, options)
}

// Delete perform soft deletion to a FormRelation by ID, transaction is optional
func (r *SubscriptionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Subscription](SubscriptionTbl, id, trans)
}

func (r *SubscriptionRepository) GetExpirationByPeriod(period int, batchSize int) ([]*Subscription, error) {
	var subscriptions []*Subscription

	tx := DB.Begin()

	now := util.GetCurrentTime()
	startTime := util.AddCurrentTimeWithDay(period - 1)
	endTime := util.AddCurrentTimeWithDay(period)

	query := fmt.Sprintf(`
        SELECT s.*
        FROM %s s
        LEFT JOIN %s r ON
            r.entity_type = '%s' AND
            r.entity_id = s.id AND
            r.days_before = %d AND
            r.expired = false
        WHERE
            s.end_date BETWEEN %d AND %d
            AND s.delete_at = 0
            AND s.enable = true
            AND s.status = '%s'
            AND r.id IS NULL
        LIMIT %d
        FOR UPDATE SKIP LOCKED`,
		GetTblName(SubscriptionTbl),
		GetTblName(ReminderTbl),
		util.SubscriptionEntity,
		period,
		startTime,
		endTime,
		SubscriptionStatusActive,
		batchSize)

	if err := tx.Raw(query).Scan(&subscriptions).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if len(subscriptions) > 0 {
		reminders := make([]*Reminder, len(subscriptions))
		for i, subscription := range subscriptions {
			reminders[i] = &Reminder{
				UserID:     subscription.UserID,
				EntityType: util.SubscriptionEntity,
				EntityID:   subscription.ID,
				RemindeAt:  now,
				DaysBefore: period,
				Expired:    false,
			}
		}

		if err := tx.Table(GetTblName(ReminderTbl)).Create(&reminders).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	return subscriptions, nil
}

func (r *SubscriptionRepository) GetExpiration(batchSize int) ([]*Subscription, error) {
	var subscriptions []*Subscription

	tx := DB.Begin()

	now := util.GetCurrentTime()

	query := fmt.Sprintf(`
        SELECT s.*
        FROM %s s
        LEFT JOIN %s r ON
            r.entity_type = '%s' AND
            r.entity_id = s.id AND
            r.days_before = 0 AND
            r.expired = true
        WHERE
            s.end_date < %d
            AND s.delete_at = 0
            AND s.enable = true
            AND s.status = '%s'
            AND r.id IS NULL
        LIMIT %d
        FOR UPDATE SKIP LOCKED`,
		GetTblName(SubscriptionTbl),
		GetTblName(ReminderTbl),
		util.SubscriptionEntity,
		now,
		SubscriptionStatusActive,
		batchSize)

	if err := tx.Raw(query).Scan(&subscriptions).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if len(subscriptions) > 0 {
		reminders := make([]*Reminder, len(subscriptions))
		for i, subscription := range subscriptions {
			reminders[i] = &Reminder{
				UserID:     subscription.UserID,
				EntityType: util.SubscriptionEntity,
				EntityID:   subscription.ID,
				RemindeAt:  now,
				DaysBefore: 0,
				Expired:    true,
			}
		}

		if err := tx.Table(GetTblName(ReminderTbl)).Create(&reminders).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	return subscriptions, nil
}

func (r *SubscriptionRepository) ExpiredManySubscriptions(entities []*Subscription, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	ids := lo.Map(entities, func(subscription *Subscription, _ int) string {
		if cErr := Cache.Subscription.DeleteByKey(subscription.UserID); cErr != nil {
			log.ErrorWithAlertf("ExpiredManySubscriptions::Delete subscription cache error: %v", cErr)
		}
		return subscription.ID
	})

	result := DB.Table(GetTblName(SubscriptionTbl)).
		Where("id IN ? AND delete_at = 0", ids).
		Updates(map[string]interface{}{
			"status":    SubscriptionStatusExpired,
			"enable":    false,
			"update_at": time.Now().UnixMilli(),
		})

	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to expired many subscriptions: %w", result.Error)
	}

	// Check if any records were affected
	if result.RowsAffected == 0 {
		tx.Rollback()
		return fmt.Errorf("no matching subscriptions need edxpired")
	}

	return nil
}
