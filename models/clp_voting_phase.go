package models

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ClpVotingPhase struct {
	Model
	ClpLaunchpadID       string          `json:"clp_launchpad_id"`
	ClpVotingMilestoneID string          `json:"clp_voting_milestone_id"`
	Status               string          `json:"status"`
	Amount               decimal.Decimal `json:"amount"`
	TotalApprove         int             `json:"total_approve"`
	TotalReject          int             `json:"total_reject"`
}

type ClpVotingPhaseQuery struct {
	ID                   *string   `json:"id" form:"id"`
	IDIn                 []*string `json:"id_in" form:"id_in"`
	ClpLaunchpadID       *string   `json:"clp_launchpad_id" form:"clp_launchpad_id"`
	ClpVotingMilestoneID *string   `json:"clp_voting_milestone_id" form:"clp_voting_milestone_id"`
	IncludeDeleted       *bool     `json:"include_deleted" form:"include_deleted"`
}

func (query *ClpVotingPhaseQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.ClpLaunchpadID != nil {
		qb = qb.Where("clp_launchpad_id = ?", *query.ClpLaunchpadID)
	}

	if query.ClpVotingMilestoneID != nil {
		qb = qb.Where("clp_voting_milestone_id = ?", *query.ClpVotingMilestoneID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *ClpVotingPhaseRepository) Create(e *ClpVotingPhase, trans *gorm.DB) error {
	return create(ClpVotingPhaseTbl, e, trans)
}

func (r *ClpVotingPhaseRepository) CreateMany(ts []*ClpVotingPhase, trans *gorm.DB) error {
	return createMany(ClpVotingPhaseTbl, ts, trans)
}

func (r *ClpVotingPhaseRepository) Update(f *ClpVotingPhase, trans *gorm.DB) error {
	return update(ClpVotingPhaseTbl, f, trans)
}

func (r *ClpVotingPhaseRepository) FindOne(query *ClpVotingPhaseQuery, options *FindOneOptions) (*ClpVotingPhase, error) {
	return findOne[ClpVotingPhase](ClpVotingPhaseTbl, query, options)
}

func (r *ClpVotingPhaseRepository) FindPage(query *ClpVotingPhaseQuery, options *FindPageOptions) ([]*ClpVotingPhase, *Pagination, error) {
	return findPage[ClpVotingPhase](ClpVotingPhaseTbl, query, options)
}

func (r *ClpVotingPhaseRepository) FindMany(query *ClpVotingPhaseQuery, options *FindManyOptions) ([]*ClpVotingPhase, error) {
	return findMany[ClpVotingPhase](ClpVotingPhaseTbl, query, options)
}

func (r *ClpVotingPhaseRepository) Count(query *ClpVotingPhaseQuery) (int64, error) {
	return count[ClpVotingPhase](ClpVotingPhaseTbl, query)
}

func (r *ClpVotingPhaseRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ClpVotingPhase](ClpVotingPhaseTbl, id, trans)
}
