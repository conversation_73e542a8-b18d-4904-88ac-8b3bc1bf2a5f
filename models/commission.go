package models

import (
	"gorm.io/gorm"
	"strings"
)

type CommissionType string

const (
	CommissionTypeNoLimitQuantity CommissionType = "no_limit_qty"
	CommissionTypeMinQuantity     CommissionType = "min_quantity"
)

type Commission struct {
	Model
	UserID        string         `json:"user_id" gorm:"type:varchar(20);not null"`
	OrgID         string         `json:"org_id" gorm:"type:varchar(20);not null"`
	CampaignID    string         `json:"campaign_id" gorm:"type:varchar(20);not null"`
	Ref1Rate      float32        `json:"ref1_rate" gorm:"type:float4;default:0"`
	Qty1          int            `json:"qty1" gorm:"type:int8;default:0"`
	Ref2Rate      float32        `json:"ref2_rate" gorm:"type:float4;default:0"`
	Qty2          int            `json:"qty2" gorm:"type:int8;default:0"`
	Ref3Rate      float32        `json:"ref3_rate" gorm:"type:float4;default:0"`
	Qty3          int            `json:"qty3" gorm:"type:int8;default:0"`
	ParentID      string         `json:"parent_id" gorm:"type:varchar(20)"`
	ReferrerTypes StringArray    `json:"referrer_types" gorm:"type:jsonb"`
	ReferrerIDs   StringArray    `json:"referrer_ids" gorm:"type:jsonb"`
	Enable        bool           `json:"enable" gorm:"default:true"`
	Type          CommissionType `json:"type"`
	IsBaseRate    bool           `json:"is_base_rate" gorm:"default:false"`
	Order         int            `json:"order" gorm:"default:1"`

	Bonuses            []*Commission `json:"bonuses" gorm:"-"`
	ReferralLinkByUser *ReferralLink `json:"referral_link_by_user" gorm:"-"`
}

type CommissionQuery struct {
	ID                   *string   `json:"id" form:"id"`
	IDIn                 []*string `json:"id_in" form:"id_in"`
	UserID               *string   `json:"user_id" form:"user_id"`
	OrgID                *string   `json:"org_id" form:"org_id"`
	CampaignID           *string   `json:"campaign_id" form:"campaign_id"`
	CampaignIDIn         []string  `json:"campaign_id_in" form:"campaign_id_in"`
	ParentID             *string   `json:"parent_id" form:"parent_id"`
	ParentIDIn           []*string `json:"parent_id_in" form:"parent_id_in"`
	ParentIDNull         *bool     `json:"parent_id_null" form:"parent_id_null"`
	ReferrerID           *string   `json:"referrer_id" form:"referrer_id"`
	ReferrerIDNull       *bool     `json:"referrer_id_null" form:"referrer_id_null"`
	Enable               *bool     `json:"enable" form:"enable"`
	Type                 *bool     `json:"type" form:"type"`
	IncludeDeleted       *bool     `json:"include_deleted" form:"include_deleted"`
	IncludeReferrerIDs   *[]string `json:"include_referrer_ids" form:"include_referrer_ids"`
	IncludeReferrerTypes *[]string `json:"include_referrer_types" form:"include_referrer_types"`
	IsBaseRate           *bool     `json:"is_base_rate" form:"is_base_rate"`
}

func (query *CommissionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *query.CampaignID)
	}

	if len(query.CampaignIDIn) > 0 {
		qb = qb.Where("campaign_id IN (?)", query.CampaignIDIn)
	}

	if query.ParentID != nil {
		qb = qb.Where("parent_id = ?", *query.ParentID)
	}

	if len(query.ParentIDIn) > 0 {
		qb = qb.Where("parent_id IN (?)", query.ParentIDIn)
	}

	if query.ParentIDNull != nil {
		if *query.ParentIDNull {
			qb = qb.Where("parent_id = ''")
		} else {
			qb = qb.Where("parent_id <> ''")
		}
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.IsBaseRate != nil {
		qb = qb.Where("is_base_rate = ?", *query.IsBaseRate)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.IncludeReferrerIDs != nil {
		qb = qb.Where("referrer_ids ?| ARRAY['" + strings.Join(*query.IncludeReferrerIDs, "','") + "']")
	}

	if query.IncludeReferrerTypes != nil {
		qb = qb.Where("referrer_types ?| ARRAY['" + strings.Join(*query.IncludeReferrerTypes, "','") + "']")
	}

	return qb
}

func (r *CommissionRepository) Create(e *Commission, trans *gorm.DB) error {
	return create(CommissionTbl, e, trans)
}

func (r *CommissionRepository) CreateMany(ts []*Commission, trans *gorm.DB) error {
	return createMany(CommissionTbl, ts, trans)
}

func (r *CommissionRepository) Update(f *Commission, trans *gorm.DB) error {
	return update(CommissionTbl, f, trans)
}

func (r *CommissionRepository) FindOne(query *CommissionQuery, options *FindOneOptions) (*Commission, error) {
	return findOne[Commission](CommissionTbl, query, options)
}

func (r *CommissionRepository) FindPage(query *CommissionQuery, options *FindPageOptions) ([]*Commission, *Pagination, error) {
	return findPage[Commission](CommissionTbl, query, options)
}

func (r *CommissionRepository) FindMany(query *CommissionQuery, options *FindManyOptions) ([]*Commission, error) {
	return findMany[Commission](CommissionTbl, query, options)
}

func (r *CommissionRepository) Count(query *CommissionQuery) (int64, error) {
	return count[Commission](CommissionTbl, query)
}

func (r *CommissionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Commission](CommissionTbl, id, trans)
}
