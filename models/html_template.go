package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type TemplateType string

const (
	CertificateTemplate TemplateType = "certificate_template"
	CertificateLayer    TemplateType = "certificate_layer"
)

type HtmlTemplate struct {
	Model
	Name          string                   `json:"name" gorm:"type:varchar(50)"`
	Type          TemplateType             `json:"type" gorm:"not null"`
	UserID        string                   `json:"user_id" gorm:"type:varchar(20)"`
	CourseCuid    string                   `json:"course_cuid" gorm:"type:varchar(20)"`
	CourseName    string                   `json:"course_name" gorm:"type:text"`
	CreatorName   string                   `json:"creator_name" gorm:"type:text"`
	OrgID         string                   `json:"org_id" gorm:"type:varchar(50)"`
	Enable        bool                     `json:"enable" gorm:"not null; default:false"`
	Files         []*File                  `json:"files" gorm:"-"`
	Props         JSONB                    `json:"props" gorm:"type:jsonb"`
	Template      JSONB                    `json:"template" gorm:"type:jsonb"`
	LearnerName   string                   `json:"learner_name" gorm:"-"`
	ProjectName   string                   `json:"project_name" gorm:"-"`
	EnableProject bool                     `json:"enable_project" gorm:"default:false"`
	IssueDate     int                      `json:"issue_date" gorm:"-"`
	Organizations CertificateOrganizations `json:"organizations" gorm:"type:jsonb"`
	Signatures    Signatures               `json:"signatures" gorm:"type:jsonb"`
}

type HtmlTemplateQuery struct {
	ID             *string `form:"id" json:"id,omitempty"`
	Name           *string `form:"name" json:"name"`
	Type           *string `form:"type" json:"type"`
	UserID         *string `form:"user_id" json:"user_id"`
	OrgID          *string `form:"org_id" json:"org_id"`
	CourseCuid     *string `form:"course_cuid" json:"course_cuid"`
	Enable         *bool   `form:"enable" json:"enable"`
	IncludeDeleted *bool
	CourseIDIn     []string `json:"course_id_in,omitempty" form:"course_id_in"`
}
type Signature struct {
	CreatorName string      `json:"creator_name"`
	Signature   *SimpleFile `json:"signature"`
	Position    string      `json:"position"`
}

type CertificateOrganization struct {
	Name string      `json:"name"`
	Logo *SimpleFile `json:"logo"`
}

func (s Signature) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *Signature) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

func (s CertificateOrganization) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *CertificateOrganization) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

type CertificateOrganizations []CertificateOrganization

func (orgs CertificateOrganizations) Value() (driver.Value, error) {
	val, err := json.Marshal(orgs)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (orgs *CertificateOrganizations) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, orgs)
}

type Signatures []Signature

func (sigs Signatures) Value() (driver.Value, error) {
	val, err := json.Marshal(sigs)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (sigs *Signatures) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, sigs)
}

func (query *HtmlTemplateQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *query.CourseCuid)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if len(query.CourseIDIn) > 0 {
		qb = qb.Where("course_id IN (?)", query.CourseIDIn)
	}

	return qb
}

func (r *HtmlTemplateRepository) Create(template *HtmlTemplate, trans *gorm.DB) error {
	if err := create(HtmlTemplateTbl, template, trans); err != nil {
		return err
	}

	if template.Files != nil && len(template.Files) > 0 {
		if err := Repository.FileRelation.AddFiles(HTMLTemplateModelName, template.ID, util.FilesField, template.Files); err != nil {
			return err
		}
	}

	return nil
}

func (r *HtmlTemplateRepository) FindOne(query *HtmlTemplateQuery, options *FindOneOptions) (*HtmlTemplate, error) {
	hasPreloadFiles := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	template, err := findOne[HtmlTemplate](HtmlTemplateTbl, query, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadFiles {
		templateIDs := []string{template.ID}
		if filesByTemplateIDs, err := Repository.FileRelation.GetFilesByEntities(HTMLTemplateModelName, templateIDs, util.FilesField); err != nil {
			return nil, err
		} else {
			template.Files = filesByTemplateIDs[template.ID]
		}
	}

	return template, nil
}

func (r *HtmlTemplateRepository) FindPage(query *HtmlTemplateQuery, options *FindPageOptions) ([]*HtmlTemplate, *Pagination, error) {
	shouldPreloadContents := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		shouldPreloadContents = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	htmlTemplates, pagination, err := findPage[HtmlTemplate](HtmlTemplateTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if shouldPreloadContents {
		htmlTemplateIDs := lo.Map(htmlTemplates, func(htmlTemplate *HtmlTemplate, _ int) string {
			return htmlTemplate.ID
		})
		if filesByTemplateIDs, mErr := Repository.FileRelation.GetFilesByEntities(HTMLTemplateModelName, htmlTemplateIDs, util.FilesField); mErr != nil {
			return nil, nil, mErr
		} else {
			lo.ForEach(htmlTemplates, func(htmlTemplate *HtmlTemplate, _ int) {
				htmlTemplate.Files = filesByTemplateIDs[htmlTemplate.ID]
			})
		}
	}

	return htmlTemplates, pagination, err
}

func (r *HtmlTemplateRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[HtmlTemplate](HtmlTemplateTbl, id, trans)
}

// FindByID finds a Html Template by ID with full preloads
func (r *HtmlTemplateRepository) FindByID(id string) (*HtmlTemplate, error) {
	options := &FindOneOptions{}
	htmlTemplate, err := findByID[HtmlTemplate](HtmlTemplateTbl, id, options)
	if err != nil {
		return nil, err
	}

	//Preload field Files
	htmlTemplateIDs := []string{htmlTemplate.ID}
	if filesByTemplateIDs, err := Repository.FileRelation.GetFilesByEntities(HTMLTemplateModelName, htmlTemplateIDs, util.FilesField); err != nil {
		return nil, err
	} else {
		htmlTemplate.Files = filesByTemplateIDs[htmlTemplate.ID]
	}

	return htmlTemplate, err
}

func (r *HtmlTemplateRepository) Update(template *HtmlTemplate, trans *gorm.DB) error {
	return update(HtmlTemplateTbl, template, trans)
}

func (r *HtmlTemplateRepository) FindPageCertificateTemplateForCourse(courseCuid string, templateRootID string, org *Organization, options *FindPageOptions) ([]*HtmlTemplate, *Pagination, error) {
	shouldPreloadContents := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		shouldPreloadContents = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	templateID := ""
	if !org.IsRoot() {
		sysConfig, err := Repository.System.FindOne(&SystemConfigQuery{
			Key:   util.NewT(CertificateTemplateDefault),
			OrgID: &org.ID}, nil)
		if err != nil && !IsRecordNotFound(err) {
			return nil, nil, err
		}
		if sysConfig != nil {
			templateID = sysConfig.Value
		}
	}

	entitiesChan := make(chan []*HtmlTemplate)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		close(entitiesChan)
		close(countChan)
		close(errorChan)
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		var entities []*HtmlTemplate
		qb := DB
		qb = qb.Table(GetTblName(HtmlTemplateTbl) + " as c").Debug()
		qb = qb.Where("c.type = ?", CertificateTemplate).
			Where("c.delete_at = ?", 0)

		query := qb.Where("c.org_id = ?", org.ID)

		query = query.Or("(c.type = ? AND c.course_cuid = ? AND c.delete_at = ?)",
			CertificateLayer, courseCuid, 0)

		if templateRootID != "" {
			query = query.Or("c.id = ? AND c.delete_at = ?", templateRootID, 0)
		}

		if templateID != "" && templateID != templateRootID {
			query = query.Or("c.id = ? AND c.delete_at = ?", templateID, 0)
		}

		orderCaseStatement := "CASE "

		orderCaseStatement += fmt.Sprintf("WHEN (c.type = '%s' AND c.course_cuid = '%s') THEN 1 ",
			CertificateLayer, courseCuid)

		if templateRootID != "" {
			orderCaseStatement += fmt.Sprintf("WHEN c.id = '%s' THEN 2 ", templateRootID)
			if templateID != "" && templateID != templateRootID {
				orderCaseStatement += fmt.Sprintf("WHEN c.id = '%s' THEN 3 ELSE 4 ", templateID)
			} else {
				orderCaseStatement += "ELSE 3 "
			}
		} else if templateID != "" {
			orderCaseStatement += fmt.Sprintf("WHEN c.id = '%s' THEN 2 ELSE 3 ", templateID)
		}
		orderCaseStatement += "END"
		query = query.Order(orderCaseStatement)
		lo.ForEach(options.Sort, func(clause string, _ int) {
			query = query.Order("c." + clause)
		})
		query = query.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)
		result := query.Debug().Find(&entities)

		if err := result.Error; err != nil {
			errorChan <- err
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		var c int64
		var entity HtmlTemplate
		qb := DB
		qb = qb.Table(GetTblName(HtmlTemplateTbl) + " as c").Debug()

		countQuery := qb.Where("c.type = ?", CertificateTemplate).
			Where("c.delete_at = ?", 0).
			Where("c.org_id = ?", org.ID)

		countQuery = countQuery.Or("(c.type = ? AND c.course_cuid = ? AND c.delete_at = ?)",
			CertificateLayer, courseCuid, 0)

		if templateRootID != "" {
			countQuery = countQuery.Or("c.id = ? AND c.delete_at = ?", templateRootID, 0)
		}

		if templateID != "" && templateID != templateRootID {
			countQuery = countQuery.Or("c.id = ? AND c.delete_at = ?", templateID, 0)
		}

		result := countQuery.Model(&entity).Count(&c)
		if err := result.Error; err != nil {
			errorChan <- err
			return
		}

		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*HtmlTemplate
	var entitiesCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entitiesCount = <-countChan:
		case err := <-errorChan:
			return nil, nil, err
		}
	}

	if shouldPreloadContents {
		htmlTemplateIDs := lo.Map(entities, func(htmlTemplate *HtmlTemplate, _ int) string {
			return htmlTemplate.ID
		})
		if filesByTemplateIDs, mErr := Repository.FileRelation.GetFilesByEntities(HTMLTemplateModelName, htmlTemplateIDs, util.FilesField); mErr != nil {
			return nil, nil, mErr
		} else {
			lo.ForEach(entities, func(htmlTemplate *HtmlTemplate, _ int) {
				htmlTemplate.Files = filesByTemplateIDs[htmlTemplate.ID]
			})
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entitiesCount)), nil
}

func (r *HtmlTemplateRepository) FindPageCertificateTemplate(templateRootID string, org *Organization, options *FindPageOptions) ([]*HtmlTemplate, *Pagination, error) {
	shouldPreloadContents := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		shouldPreloadContents = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	templateID := ""
	if !org.IsRoot() {
		sysConfig, err := Repository.System.FindOne(&SystemConfigQuery{
			Key:   util.NewT(CertificateTemplateDefault),
			OrgID: &org.ID}, nil)
		if err != nil && !IsRecordNotFound(err) {
			return nil, nil, err
		}
		if sysConfig != nil {
			templateID = sysConfig.Value
		}
	}

	entitiesChan := make(chan []*HtmlTemplate)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		close(entitiesChan)
		close(countChan)
		close(errorChan)
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		var entities []*HtmlTemplate
		qb := DB
		qb = qb.Table(GetTblName(HtmlTemplateTbl) + " as c").Debug()
		qb = qb.Where("c.type = ?", CertificateTemplate).
			Where("c.delete_at = ?", 0)

		query := qb.Where("c.org_id = ?", org.ID)

		if templateRootID != "" {
			query = query.Or("c.id = ? AND c.delete_at = ?", templateRootID, 0)
		}

		if templateID != "" && templateID != templateRootID {
			query = query.Or("c.id = ? AND c.delete_at = ?", templateID, 0)
		}

		orderCaseStatement := "CASE "
		if templateRootID != "" {
			orderCaseStatement += fmt.Sprintf("WHEN c.id = '%s' THEN 1 ", templateRootID)
			if templateID != "" && templateID != templateRootID {
				orderCaseStatement += fmt.Sprintf("WHEN c.id = '%s' THEN 2 ELSE 3 ", templateID)
			} else {
				orderCaseStatement += "ELSE 2 "
			}
		} else if templateID != "" {
			orderCaseStatement += fmt.Sprintf("WHEN c.id = '%s' THEN 1 ELSE 2 ", templateID)
		}
		orderCaseStatement += "END"
		query = query.Order(orderCaseStatement)
		lo.ForEach(options.Sort, func(clause string, _ int) {
			query = query.Order("c." + clause)
		})
		query = query.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)
		result := query.Debug().Find(&entities)

		if err := result.Error; err != nil {
			errorChan <- err
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		var c int64
		var entity HtmlTemplate
		qb := DB
		qb = qb.Table(GetTblName(HtmlTemplateTbl) + " as c").Debug()

		countQuery := qb.Where("c.type = ?", CertificateTemplate).
			Where("c.delete_at = ?", 0).
			Where("c.org_id = ?", org.ID)

		if templateRootID != "" {
			countQuery = countQuery.Or("c.id = ? AND c.delete_at = ?", templateRootID, 0)
		}

		if templateID != "" && templateID != templateRootID {
			countQuery = countQuery.Or("c.id = ? AND c.delete_at = ?", templateID, 0)
		}

		result := countQuery.Model(&entity).Count(&c)
		if err := result.Error; err != nil {
			errorChan <- err
			return
		}

		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*HtmlTemplate
	var entitiesCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entitiesCount = <-countChan:
		case err := <-errorChan:
			return nil, nil, err
		}
	}

	if shouldPreloadContents {
		htmlTemplateIDs := lo.Map(entities, func(htmlTemplate *HtmlTemplate, _ int) string {
			return htmlTemplate.ID
		})
		if filesByTemplateIDs, mErr := Repository.FileRelation.GetFilesByEntities(HTMLTemplateModelName, htmlTemplateIDs, util.FilesField); mErr != nil {
			return nil, nil, mErr
		} else {
			lo.ForEach(entities, func(htmlTemplate *HtmlTemplate, _ int) {
				htmlTemplate.Files = filesByTemplateIDs[htmlTemplate.ID]
			})
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entitiesCount)), nil
}
