package models

type ExchangeRates struct {
	VND2USD             float64 `json:"VND_USD"`
	VND2USDLastUpdateAt int64   `json:"VND_USD_last_update_at"`
	VND2USDNextUpdateAt int64   `json:"VND_USD_next_update_at"`

	USD2VND             float64 `json:"USD_VND"`
	USD2VNDLastUpdateAt int64   `json:"USD_VND_last_update_at"`
	USD2VNDNextUpdateAt int64   `json:"USD_VND_next_update_at"`

	NEAR2USD  float64 `json:"NEAR_USD"`
	AVAIL2USD float64 `json:"AVAIL_USD"`
	USDT2USD  float64 `json:"USDT_USD"`
	USDC2USD  float64 `json:"USDC_USD"`
	ETH2USD   float64 `json:"ETH_USD"`
}
