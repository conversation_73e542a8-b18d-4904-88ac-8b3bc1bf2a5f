package models

import (
	"gorm.io/gorm"
)

type OEReferralCode struct {
	Model
	Code       string  `json:"code" gorm:"not null;unique"`
	UserID     string  `json:"user_id"`
	Type       string  `json:"type" gorm:"default:'referral'"`
	ReferrerID *string `json:"referrer_id,omitempty" gorm:"default:null"`
	CampaignID *string `json:"campaign_id" gorm:"default:null"`
	Props      JSONB   `json:"props" gorm:"type:jsonb"`

	Referrer *User            `json:"referrer"`
	User     *User            `json:"user"`
	Campaign *OEPointCampaign `json:"campaign"`
}

type OEReferralCodeQuery struct {
	ID         *string `json:"id" form:"id"`
	Code       *string `json:"code" form:"code"`
	UserID     *string `json:"user_id" form:"user_id"`
	Deleted    *bool   `json:"deleted" form:"deleted"`
	ReferrerID *string `json:"referrer_id" form:"referrer_id"`
}

func (q *OEReferralCodeQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if q.ID != nil {
		qb = qb.Where("id=?", *q.Code)
	}

	if q.UserID != nil {
		qb = qb.Where("user_id = ?", *q.UserID)
	}

	if q.Code != nil {
		qb = qb.Where("code=?", *q.Code)
	}

	if q.ReferrerID != nil {
		qb = qb.Where("referrer_id=?", *q.ReferrerID)
	}

	if q.Deleted == nil || *q.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *OEReferralCodeRepository) Create(ah *OEReferralCode, trans *gorm.DB) error {
	return create(OEReferralCodeTbl, ah, trans)
}

func (r *OEReferralCodeRepository) CreateMany(ahs []*OEReferralCode, trans *gorm.DB) error {
	return createMany(OEReferralCodeTbl, ahs, trans)
}

func (r *OEReferralCodeRepository) Update(ah *OEReferralCode, trans *gorm.DB) error {
	return update(OEReferralCodeTbl, ah, trans)
}

func (r *OEReferralCodeRepository) FindByID(id string, options *FindOneOptions) (*OEReferralCode, error) {
	return findByID[OEReferralCode](OEReferralCodeTbl, id, options)
}

func (r *OEReferralCodeRepository) FindOne(query *OEReferralCodeQuery, options *FindOneOptions) (*OEReferralCode, error) {
	return findOne[OEReferralCode](OEReferralCodeTbl, query, options)
}

func (r *OEReferralCodeRepository) FindMany(query *OEReferralCodeQuery, options *FindManyOptions) ([]*OEReferralCode, error) {
	return findMany[OEReferralCode](OEReferralCodeTbl, query, options)
}

func (r *OEReferralCodeRepository) FindPage(query *OEReferralCodeQuery, options *FindPageOptions) ([]*OEReferralCode, *Pagination, error) {
	return findPage[OEReferralCode](OEReferralCodeTbl, query, options)
}

func (r *OEReferralCodeRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OEReferralCode](OEReferralCodeTbl, id, trans)
}

func (r *OEReferralCodeRepository) DeleteMany(query *OEReferralCodeQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[OEReferralCode](OEReferralCodeTbl, query, trans)
}
