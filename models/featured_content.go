package models

import "gorm.io/gorm"

type FeaturedContentType string

const (
	FeaturedContentTypePopular  FeaturedContentType = "popular"
	FeaturedContentTypeFeatured FeaturedContentType = "featured"
	FeaturedContentTypeTrending FeaturedContentType = "trending"
)

type FeaturedContent struct {
	Model
	OrgID      string              `json:"org_id"`
	EntityID   string              `json:"entity_id"`
	EntityType ModelName           `json:"entity_type"`
	Enabled    bool                `json:"enabled" gorm:"default:true"`
	Order      int                 `json:"order" gorm:"default:0"`
	Type       FeaturedContentType `json:"type"`

	Entity interface{} `json:"entity" gorm:"-"`
}

type FeaturedContentQuery struct {
	ID         *string              `json:"id" form:"id"`
	IDIn       []string             `json:"id_in" form:"id_in"`
	OrgID      *string              `json:"org_id" form:"org_id"`
	EntityType *ModelName           `json:"entity_type" form:"entity_type"`
	EntityID   *string              `json:"entity_id" form:"entity_id"`
	Enabled    *bool                `json:"enabled" form:"enabled"`
	Order      *int                 `json:"order" form:"order"`
	Type       *FeaturedContentType `json:"type" form:"type"`
	Deleted    *bool                `json:"deleted" form:"deleted"`
}

func (query *FeaturedContentQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.EntityID != nil {
		qb = qb.Where("entity_id = ?", *query.EntityID)
	}

	if query.EntityType != nil {
		qb = qb.Where("entity_type = ?", *query.EntityType)
	}

	if query.Enabled != nil {
		qb = qb.Where("enabled = ?", *query.Enabled)
	}

	if query.Order != nil {
		qb = qb.Where("order = ?", *query.Order)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.Deleted == nil || !*query.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *FeaturedContentRepository) Create(p *FeaturedContent, trans *gorm.DB) error {
	return create(FeaturedContentTbl, p, trans)
}

func (r *FeaturedContentRepository) CreateMany(entities []*FeaturedContent, trans *gorm.DB) error {
	if err := createMany(FeaturedContentTbl, entities, trans); err != nil {
		return err
	}
	return nil
}

func (r *FeaturedContentRepository) FindByID(id string, options *FindOneOptions) (*FeaturedContent, error) {
	return findByID[FeaturedContent](FeaturedContentTbl, id, options)
}

func (r *FeaturedContentRepository) Update(p *FeaturedContent, trans *gorm.DB) error {
	return update(FeaturedContentTbl, p, trans)
}

func (r *FeaturedContentRepository) FindOne(query *FeaturedContentQuery, options *FindOneOptions) (*FeaturedContent, error) {
	return findOne[FeaturedContent](FeaturedContentTbl, query, options)
}

func (r *FeaturedContentRepository) FindMany(query *FeaturedContentQuery, options *FindManyOptions) ([]*FeaturedContent, error) {
	return findMany[FeaturedContent](FeaturedContentTbl, query, options)
}

func (r *FeaturedContentRepository) FindPage(query *FeaturedContentQuery, options *FindPageOptions) ([]*FeaturedContent, *Pagination, error) {
	return findPage[FeaturedContent](FeaturedContentTbl, query, options)
}

func (r *FeaturedContentRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[FeaturedContent](FeaturedContentTbl, id, trans)
}

func (r *FeaturedContentRepository) DeleteMany(query *FeaturedContentQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[FeaturedContent](FeaturedContentTbl, query, trans)
}
