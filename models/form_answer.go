package models

import (
	"fmt"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type FormAnswer struct {
	Model
	SessionID      string              `json:"session_id" gorm:"type:varchar(20);not null"`
	FormID         string              `json:"form_id" gorm:"type:varchar(20)"`
	FormUID        string              `json:"form_uid" gorm:"type:varchar(20)"`
	UserID         *string             `json:"user_id" gorm:"type:varchar(20)"`
	Key            FormQuestionKey     `json:"key"`
	QuestionID     string              `json:"question_id" gorm:"type:varchar(20);not null"`
	QuestionUID    string              `json:"question_uid" gorm:"type:varchar(20)"`
	Question       *FormQuestion       `json:"question"`
	SubQuestionID  *string             `json:"sub_question_id" gorm:"type:varchar(20)"`
	SubQuestionUID *string             `json:"sub_question_uid" gorm:"type:varchar(20)"`
	SubQuestion    *FormQuestion       `json:"sub_question"`
	OptionID       *string             `json:"option_id" gorm:"type:varchar(20)"`
	OptionUID      *string             `json:"option_uid" gorm:"type:varchar(20)"`
	Option         *FormQuestionOption `json:"option"`
	Text           string              `json:"answer_text"`
	Files          []*File             `json:"files" gorm:"-"`
}

type FormQuestionSummary struct {
	QuestionUID          string                    `json:"question_uid"`
	Title                string                    `json:"title"`
	Description          string                    `json:"description"`
	Type                 FormQuestionType          `json:"type"`
	TotalCount           int64                     `json:"total_count"`
	SubQuestionSummaries []*FormSubQuestionSummary `json:"sub_question_summaries,omitempty"`
}

type FormSubQuestionSummary struct {
	ID         string `json:"id"`
	Title      string `json:"title"`
	TotalCount int64  `json:"total_count"`
}

type FormSummary struct {
	TotalResponses    int64                  `json:"total_responses"`
	QuestionSummaries []*FormQuestionSummary `json:"question_summaries"`
}

type FormAnswerCount struct {
	QuestionID    string `json:"question_id"`
	SubQuestionID string `json:"sub_question_id"`
	OptionID      string `json:"option_id,omitempty"`
	OptionText    string `json:"option_text,omitempty"`
	Text          string `json:"text"`
	Count         int64  `json:"count"`
}

type SimpleFormAnswer struct {
	Model
	SessionID      string                    `json:"session_id"`
	FormID         string                    `json:"form_id"`
	Key            FormQuestionKey           `json:"key,omitempty"`
	QuestionID     string                    `json:"question_id"`
	QuestionUID    string                    `json:"question_uid"`
	QuestionType   FormQuestionType          `json:"question_type"`
	SubQuestionID  *string                   `json:"sub_question_id"`
	SubQuestionUID *string                   `json:"sub_question_uid"`
	SubQuestion    *SimpleFormQuestion       `json:"sub_question,omitempty"`
	OptionID       *string                   `json:"option_id,omitempty"`
	OptionUID      *string                   `json:"option_uid,omitempty"`
	OptionText     string                    `json:"option_text"`
	Option         *SimpleFormQuestionOption `json:"option,omitempty"`
	Text           string                    `json:"answer_text"`
	Files          []*File                   `json:"files"`
}

type VerySimpleFormAnswer struct {
	UserID string `json:"user_id"`
	Text   string `json:"answer_text"`
}

func (a *FormAnswer) Sanitize() *SimpleFormAnswer {
	simpleAnswer := &SimpleFormAnswer{
		Model:          a.Model,
		SessionID:      a.SessionID,
		FormID:         a.FormID,
		Key:            a.Key,
		QuestionID:     a.QuestionID,
		QuestionUID:    a.QuestionUID,
		SubQuestionID:  a.SubQuestionID,
		SubQuestionUID: a.SubQuestionUID,
		OptionID:       a.OptionID,
		OptionUID:      a.OptionUID,
		Text:           a.Text,
		Files:          a.Files,
	}
	if a.Question != nil {
		simpleAnswer.QuestionType = a.Question.QuestionType
		if a.OptionUID != nil {
			for _, option := range a.Question.Options {
				if *a.OptionUID == option.UID {
					simpleAnswer.OptionText = option.Text
				}
			}
		}
	}

	if a.SubQuestion != nil {
		simpleAnswer.SubQuestion = a.SubQuestion.Sanitize()
	}

	if a.Option != nil {
		simpleAnswer.Option = a.Option.Sanitize()
	}

	if a.Text != "" && simpleAnswer.OptionText == "" {
		simpleAnswer.OptionText = a.Text
	}

	return simpleAnswer
}

func (a *FormAnswer) IsEmptyAnswerText() bool {
	return a.Text == ""
}

func (a *FormAnswer) IsEmptyAnswerFile() bool {
	return len(a.Files) == 0
}

func (a *FormAnswer) IsKeyFullName() bool {
	return a.Key == FormQuestionKeyFullName
}

func (a *FormAnswer) IsKeyEmail() bool {
	return a.Key == FormQuestionKeyEmail
}

func (a *FormAnswer) IsKeyPhone() bool {
	return a.Key == FormQuestionKeyPhone
}

func (a *FormAnswer) IsKeyCompanyName() bool {
	return a.Key == FormQuestionKeyCompanyName
}

type FormAnswerQuery struct {
	ID            *string  `json:"id,omitempty" form:"id"`
	IDIn          []string `json:"id_in,omitempty" form:"id_in"`
	UserID        *string  `json:"user_id,omitempty" form:"user_id"`
	QuestionUID   *string  `form:"question_uid" json:"question_uid"`
	FormUID       *string  `form:"form_uid" json:"form_uid"`
	QuestionUIDIn []string `form:"question_uid_in" json:"question_uid_in"`
	SessionIDIn   []string `form:"session_uid_in" json:"session_uid_in"`
	FormUIDIn     []string `form:"form_uid_in" json:"form_uid_in"`

	FormID         *string  `json:"form_id,omitempty" form:"form_id"`
	FormIDIn       []string `json:"form_id_in,omitempty" form:"form_id_in"`
	SessionID      *string  `json:"session_id,omitempty" form:"session_id"`
	IncludeDeleted *bool    `json:"include_deleted,omitempty" form:"include_deleted"`
}

func (query *FormAnswerQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.QuestionUID != nil {
		qb = qb.Where("question_uid = ?", *query.QuestionUID)
	}
	if query.FormUID != nil {
		qb = qb.Where("form_uid = ?", *query.FormUID)
	}

	if len(query.QuestionUIDIn) > 0 {
		qb = qb.Where("question_uid IN (?)", query.QuestionUIDIn)
	}
	if len(query.FormUIDIn) > 0 {
		qb = qb.Where("form_uid IN (?)", query.FormUIDIn)
	}

	if len(query.SessionIDIn) > 0 {
		qb = qb.Where("session_id IN (?)", query.SessionIDIn)
	}

	if len(query.FormIDIn) > 0 {
		qb = qb.Where("form_id IN (?)", query.FormIDIn)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.FormID != nil {
		qb = qb.Where("form_id = ?", *query.FormID)
	}

	if query.SessionID != nil {
		qb = qb.Where("session_id = ?", *query.SessionID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *FormAnswerRepository) Create(answer *FormAnswer, trans *gorm.DB) error {
	if err := create(FormAnswerTbl, answer, trans); err != nil {
		return err
	}
	if answer.Files != nil && len(answer.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(FormAnswerModelName, answer.ID, util.FilesField, answer.Files); fErr != nil {
			return fErr
		}
	}
	return nil
}

func (r *FormAnswerRepository) CreateMany(answers []*FormAnswer, trans *gorm.DB) error {
	if err := createMany(FormAnswerTbl, answers, trans); err != nil {
		return err
	}
	for _, answer := range answers {
		if answer.Files != nil && len(answer.Files) > 0 {
			if fErr := Repository.FileRelation.AddFiles(FormAnswerModelName, answer.ID, util.FilesField, answer.Files); fErr != nil {
				return fErr
			}
		}
	}
	return nil
}

func (r *FormAnswerRepository) Update(o *FormAnswer, trans *gorm.DB) error {
	return update(FormAnswerTbl, o, trans)
}

func (r *FormAnswerRepository) FindByID(id string, options *FindOneOptions) (*FormAnswer, error) {
	return findByID[FormAnswer](FormAnswerTbl, id, options)
}

func (r *FormAnswerRepository) FindOne(query *FormAnswerQuery, options *FindOneOptions) (*FormAnswer, error) {
	return findOne[FormAnswer](FormAnswerTbl, query, options)
}

func (r *FormAnswerRepository) FindMany(query *FormAnswerQuery, options *FindManyOptions) ([]*FormAnswer, error) {
	shouldPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		shouldPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}
	answers, err := findMany[FormAnswer](FormAnswerTbl, query, options)
	if err != nil {
		return nil, err
	}
	if shouldPreloadFiles {
		answerIDs := lo.Map(answers, func(answer *FormAnswer, _ int) string {
			return answer.ID
		})
		if filesByAnswerIDs, mErr := Repository.FileRelation.GetFilesByEntities(FormAnswerModelName, answerIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			lo.ForEach(answers, func(answer *FormAnswer, _ int) {
				answer.Files = filesByAnswerIDs[answer.ID]
			})
		}
	}
	return answers, err
}

func (r *FormAnswerRepository) FindPage(query *FormAnswerQuery, options *FindPageOptions) ([]*FormAnswer, *Pagination, error) {
	return findPage[FormAnswer](FormAnswerTbl, query, options)
}

func (r *FormAnswerRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[FormAnswer](FormAnswerTbl, id, trans)
}

func (r *FormAnswerRepository) DeleteMany(query *FormAnswerQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[FormAnswer](FormAnswerTbl, query, trans)
}

func (r *FormAnswerRepository) FindAnswerCountsByForm(formID string) ([]*FormAnswerCount, error) {
	var answerCounts []*FormAnswerCount
	query := fmt.Sprintf(
		`
        SELECT a.question_id, a.sub_question_id, a.text, a.option_id, o.text, COUNT(a.id) AS count
        FROM %[1]s a
        LEFT JOIN %[2]s s ON a.session_id = s.id
        LEFT JOIN %[3]s o ON a.option_id = o.id
        WHERE s.form_id = ?
        GROUP BY a.question_id, a.sub_question_id, a.text, a.option_id, o.text
    `,
		GetTblName(FormAnswerTbl),
		GetTblName(FormSessionTbl),
		GetTblName(FormQuestionOptionTbl),
	)

	result := DB.Raw(query, formID).Scan(&answerCounts)
	if err := result.Error; err != nil {
		return nil, err
	}

	return answerCounts, nil
}

func (r *FormAnswerRepository) FindAnswerCountsByQuestion(questionID string) ([]*FormAnswerCount, error) {
	var answerCounts []*FormAnswerCount
	query := fmt.Sprintf(
		`
        SELECT a.question_id, a.sub_question_id, a.text, a.option_id, o.text, COUNT(a.id) AS count
        FROM %[1]s a
        LEFT JOIN %[2]s o ON a.option_id = o.id
        WHERE a.question_id = ?
        GROUP BY a.question_id, a.sub_question_id, a.text, a.option_id, o.text
    `,
		GetTblName(FormAnswerTbl),
		GetTblName(FormQuestionOptionTbl),
	)

	result := DB.Raw(query, questionID).Scan(&answerCounts)
	if err := result.Error; err != nil {
		return nil, err
	}

	return answerCounts, nil
}

func (r *FormAnswerRepository) FindSimpleQuestionAnwser(query *FormAnswerQuery) ([]*VerySimpleFormAnswer, error) {
	var entities []*VerySimpleFormAnswer
	qb := ApplyQueryOptions(DB, query)
	err := qb.Table(GetTblName(TableName(FormAnswerTbl))).Debug().Find(&entities).Error
	if err != nil {
		return nil, err
	}

	return entities, nil

}

func (r *FormAnswerRepository) FindFormAnswerGroupByFormID(formUID string) (map[string][]*FormAnswer, error) {
	formAnwsers, err := r.FindMany(&FormAnswerQuery{FormUID: &formUID}, &FindManyOptions{})
	if err != nil {
		return nil, err
	}

	mFormAwnser := lo.GroupBy(formAnwsers, func(item *FormAnswer) string { return item.SessionID })

	return mFormAwnser, nil
}
