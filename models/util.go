package models

import (
	"fmt"
	"net/url"
	"openedu-core/pkg/setting"

	"gorm.io/gorm"
)

func IsValidID(id string) error {
	if len(id) != setting.DatabaseSetting.IdSize {
		return fmt.Errorf("id must have length %d", setting.DatabaseSetting.IdSize)
	}
	return nil
}

func IsUrl(str string) bool {
	u, err := url.Parse(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}

func GetTableName(obj any, DB *gorm.DB) (string, error) {
	stmt := &gorm.Statement{DB: DB}
	err := stmt.Parse(obj)
	if err != nil {
		return "", err
	}

	tableName := stmt.Schema.Table

	return tableName, nil
}

// ExtractStringSliceFromJSONB is a utility function to extract a slice of strings from a JSONB map.
func ExtractStringSliceFromJSONB(v JSONB, key string) ([]string, error) {
	// Retrieve the value associated with the provided key from the JSONB map
	value, ok := v[key]
	if !ok {
		return nil, nil // Key not found, return nil without error
	}

	// Assert and convert to []interface{}
	interfaceSlice, ok := value.([]interface{})
	if !ok {
		return nil, fmt.Errorf("value under key %s is not a slice", key)
	}

	// Convert the slice of interfaces to a slice of strings
	stringSlice := make([]string, len(interfaceSlice))
	for i, v := range interfaceSlice {
		str, ok := v.(string)
		if !ok {
			return nil, fmt.Errorf("value at index %d under key %s is not a string", i, key)
		}
		stringSlice[i] = str
	}

	return stringSlice, nil
}
