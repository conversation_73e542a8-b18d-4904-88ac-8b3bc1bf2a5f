package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"openedu-core/pkg/util"
	"slices"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CourseRevenuePoint struct {
	Model
	CourseCuid string             `json:"course_cuid" gorm:"not null;type:varchar(20)"`
	Timestamp  int64              `json:"timestamp" gorm:"not null;type:varchar(20)"`
	Revenue    CourseRevenueArray `json:"revenues" gorm:"type:jsonb"`
	Period     TimePeriod         `json:"period" gorm:"not null;type:varchar(20)"`
}

type CourseRevenuePointQuery struct {
	CourseCuids []string `json:"course_cuid" form:"course_cuid"`
	UserID      *string  `json:"user_id" form:"user_id"`
	StartDate   *int64   `json:"start_date" form:"start_date"`
	EndDate     *int64   `json:"end_date" form:"end_date"`
	Period      *string  `json:"period" form:"period"`
}

func (query *CourseRevenuePointQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if len(query.CourseCuids) > 0 {
		qb = qb.Where("course_cuid IN ?", query.CourseCuids)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.StartDate != nil {
		qb = qb.Where("timestamp >= ?", *query.StartDate)
	}

	if query.EndDate != nil {
		qb = qb.Where("timestamp <= ?", *query.EndDate)
	}

	if query.Period != nil {
		qb = qb.Where("period = ?", *query.Period)
	}

	return qb
}

type CourseRevenueSummary struct {
	StartDate           *int64                 `json:"start_date,omitempty" form:"start_date"`
	EndDate             *int64                 `json:"end_date,omitempty" form:"end_date"`
	CourseRevenueReport []*CourseRevenueReport `json:"course_revenue_report" form:"course_revenue_report"`
}

type CourseRevenueSummaryQuery struct {
	UserID    *string  `json:"user_id" form:"user_id"`
	CuidIn    []string `json:"cuid_in" form:"cuid_in" validate:"required"`
	PartnerId *string  `json:"partner_id" form:"partner_id"`
	StartTime *int64   `json:"start_time" form:"start_time"`
	EndTime   *int64   `json:"end_time" form:"end_time"`
}

type CourseRevenueReport struct {
	Amount   decimal.Decimal `json:"amount"`
	Currency Currency        `json:"currency"`
}

type CourseRevenueArray []*CourseRevenueReport

func (r CourseRevenueArray) Value() (driver.Value, error) {
	valueString, err := json.Marshal(r)
	return string(valueString), err
}

func (r *CourseRevenueArray) Scan(value any) error {
	if err := json.Unmarshal(value.([]byte), &r); err != nil {
		return err
	}
	return nil
}

type CourseRevenueDetailQuery struct {
	UserID     *string  `json:"user_id" form:"user_id"`
	CuidIn     []string `json:"cuid_in" form:"cuid_in" validate:"required"`
	PartnerId  *string  `json:"partner_id" form:"partner_id"`
	StartTime  *int64   `json:"start_time" form:"start_time"`
	EndTime    *int64   `json:"end_time" form:"end_time"`
	CourseName *string  `json:"course_name" form:"course_name"`

	BaseSearchQuery
}

type CourseRevenueOrderDetail struct {
	Model
	UserID          string          `json:"user_id"`
	PaymentMethodID string          `json:"payment_method_id"`
	Amount          decimal.Decimal `json:"amount"`
	ActualAmount    decimal.Decimal `json:"actual_amount"`
	MissingAmount   decimal.Decimal `json:"missing_amount"`
	DiscountAmount  decimal.Decimal `json:"discount_amount"`
	Paid            decimal.Decimal `json:"paid"`
	Currency        Currency        `json:"currency"`

	Status      OrderStatus `json:"status"`
	OrderNumber int64       `json:"order_number"`

	OrderItems []*SimpleOrderItem `json:"order_items" gorm:"-"`
	Coupons    []*SimpleCoupon    `json:"coupons" gorm:"-"`
	Payment    *SimplePayment     `json:"payment" gorm:"-"`
}

func (r *CourseRepository) GetCoursesRevenueSummary(query *CourseRevenueSummaryQuery) (*CourseRevenueSummary, error) {
	var results []*CourseRevenueReport

	filter := DB.Table(GetTblName(OrderItemTbl)+" oi").
		Select("SUM(oi.actual_amount) as amount, oi.currency").
		Joins("JOIN "+GetTblName(OrderTbl)+" o ON o.id = oi.order_id").
		Joins("JOIN "+GetTblName(CourseTbl)+" c ON c.cuid = oi.entity_cuid AND c.latest = true").
		Joins("JOIN "+GetTblName(PaymentTbl)+" p ON p.order_id = o.id").
		Where("oi.entity_type = ?", CourseModelName).
		Where("o.status = ?", OrderStatusSuccess).
		Where("oi.delete_at = 0")

	if query.UserID != nil {
		filter = filter.Where("c.user_id = ?", *query.UserID)
	}
	if query.StartTime != nil {
		filter = filter.Where("p.create_at >= ?", *query.StartTime)
	}
	if query.EndTime != nil {
		filter = filter.Where("p.create_at <= ?", *query.EndTime)
	}
	if len(query.CuidIn) > 0 {
		filter = filter.Where("oi.entity_cuid IN ?", query.CuidIn)
	}
	if query.PartnerId != nil {
		filter = filter.Where("c.partner_id = ?", *query.PartnerId)
	}

	filter = filter.Group("oi.currency")

	if err := filter.Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get courses revenue by date range: %v", err)
	}

	results = lo.Filter(results, func(report *CourseRevenueReport, _ int) bool {
		return r.isValidCurrency(report.Currency)
	})

	summary := &CourseRevenueSummary{
		StartDate:           query.StartTime,
		EndDate:             query.EndTime,
		CourseRevenueReport: results,
	}

	return summary, nil
}

func (r *CourseRepository) GetCourseRevenueDetail(query *CourseRevenueDetailQuery, options *FindPageOptions) ([]*CourseRevenueOrderDetail, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{
			Page:    1,
			PerPage: 10,
			Sort:    []string{"o.create_at desc"},
		}
	}
	limit := options.PerPage
	offset := (options.Page - 1) * options.PerPage
	var reports []*CourseRevenueOrderDetail
	var totalCount int64
	var err error

	orderTbl := GetTblName(OrderTbl)
	orderItemTbl := GetTblName(OrderItemTbl)
	courseTbl := GetTblName(CourseTbl)
	paymentTbl := GetTblName(PaymentTbl)

	entitiesChan := make(chan []*CourseRevenueOrderDetail)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	optionWhereStatement := ""
	if query.UserID != nil {
		optionWhereStatement += fmt.Sprintf(" AND c.user_id = '%s' ", *query.UserID)
	}

	if len(query.CuidIn) > 0 {
		optionWhereStatement += fmt.Sprintf(" AND oi.entity_cuid IN ('%s') ", strings.Join(query.CuidIn, "','"))
	}

	if query.PartnerId != nil {
		optionWhereStatement += fmt.Sprintf(" AND c.partner_id = '%s' ", *query.PartnerId)
	}

	if query.StartTime != nil {
		optionWhereStatement += fmt.Sprintf(" AND p.create_at >= %d ", *query.StartTime)
	}

	if query.EndTime != nil {
		optionWhereStatement += fmt.Sprintf(" AND p.create_at <= %d ", *query.EndTime)
	}

	if query.CourseName != nil {
		optionWhereStatement += fmt.Sprintf(" AND c.name = '%s' ", *query.CourseName)
	}

	if query.GetSearchTerm() != nil && query.GetSearchCategories() != nil {
		conditions, err := Search.buildSearchConditions(query, Course{}, util.NewString("c"))
		if err != nil {
			return nil, nil, err
		}

		if len(conditions.Conditions) > 0 {
			optionWhereStatement += fmt.Sprintf(" AND (%s) ", strings.Join(conditions.Conditions, " OR "))
		}
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		sortStr := ""
		if options.Sort != nil && len(options.Sort) > 0 {
			sortStr = "ORDER BY"
			lo.ForEach(options.Sort, func(item string, _ int) {
				sortStr += " " + item
			})
		}

		mainQuery := fmt.Sprintf(`
            SELECT
                o.id,
                o.user_id,
                o.payment_method_id,
                o.amount,
                o.actual_amount,
                o.missing_amount,
                o.discount_amount,
                o.paid,
                o.currency,
                o.status,
                o.order_number,
                o.create_at,
                o.update_at,
                o.delete_at
            FROM
                %[1]s as o
                INNER JOIN %[2]s as oi ON oi.order_id = o.id
                INNER JOIN %[3]s as c ON c.cuid = oi.entity_cuid AND c.latest = true
                INNER JOIN %[10]s as p ON p.order_id = o.id
            WHERE
                oi.entity_type = '%[4]s'
                AND oi.status = '%[5]s'
                AND oi.delete_at = 0
                %[6]s
            %[7]s
            LIMIT %[8]d OFFSET %[9]d;`,
			orderTbl, orderItemTbl, courseTbl,
			CourseModelName, OrderStatusSuccess,
			optionWhereStatement, sortStr,
			limit, offset, paymentTbl,
		)

		if err := DB.Debug().Raw(mainQuery).Scan(&reports).Error; err != nil {
			errorChan <- err
			return
		}
		entitiesChan <- reports
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		countQuery := fmt.Sprintf(`
            SELECT COUNT(DISTINCT o.id)
            FROM
                %[1]s as o
                INNER JOIN %[2]s as oi ON oi.order_id = o.id
                INNER JOIN %[3]s as c ON c.cuid = oi.entity_cuid AND c.latest = true
                INNER JOIN %[7]s as p ON p.order_id = o.id
            WHERE
                oi.entity_type = '%[4]s'
                AND o.status = '%[5]s'
                AND oi.delete_at = 0
                %[6]s;`,
			orderTbl, orderItemTbl, courseTbl,
			CourseModelName, OrderStatusSuccess,
			optionWhereStatement, paymentTbl,
		)

		if err := DB.Debug().Raw(countQuery).Count(&totalCount).Error; err != nil {
			errorChan <- err
			return
		}
		countChan <- totalCount
	}()

	var entityCount int64
	for range 2 {
		select {
		case reports = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	if len(reports) > 0 {
		orderIDs := lo.Map(reports, func(r *CourseRevenueOrderDetail, _ int) string {
			return r.ID
		})

		orderItems, err := Repository.OrderItem.FindMany(&OrderItemQuery{
			OrderIDIn:  orderIDs,
			EntityType: util.NewT(CourseModelName),
		}, &FindManyOptions{})
		if err != nil {
			return nil, nil, fmt.Errorf("failed to load order items: %v", err)
		}

		orderItemsByOrderID := make(map[string][]*OrderItem)
		for _, item := range orderItems {
			orderItemsByOrderID[item.OrderID] = append(orderItemsByOrderID[item.OrderID], item)
		}

		for _, report := range reports {
			if items, ok := orderItemsByOrderID[report.ID]; ok {
				report.OrderItems = lo.Map(items, func(item *OrderItem, _ int) *SimpleOrderItem {
					return item.Sanitize()
				})
			}
		}

		payments, err := Repository.Payment.FindMany(&PaymentQuery{
			OrderIDIn: orderIDs,
		}, &FindManyOptions{})
		if err != nil {
			return nil, nil, fmt.Errorf("failed to load payments: %v", err)
		}

		paymentsByOrderID := make(map[string]*Payment)
		for _, payment := range payments {
			paymentsByOrderID[payment.OrderID] = payment
		}

		for _, report := range reports {
			if payment, ok := paymentsByOrderID[report.ID]; ok {
				report.Payment = payment.Sanitize()
			}
		}

		couponHistories, err := Repository.CouponHistory.FindMany(&CouponHistoryQuery{
			OrderIDIn: orderIDs,
		}, &FindManyOptions{
			Preloads: []string{"Coupon"},
		})
		if err != nil {
			return nil, nil, fmt.Errorf("failed to load coupon histories: %v", err)
		}

		couponHistoriesByOrderID := make(map[string][]*SimpleCouponHistory)
		for _, history := range couponHistories {
			couponHistoriesByOrderID[history.OrderID] = append(
				couponHistoriesByOrderID[history.OrderID],
				history.ToSimple(),
			)
		}

		for _, report := range reports {
			if histories, ok := couponHistoriesByOrderID[report.ID]; ok {
				report.Coupons = lo.Map(histories, func(history *SimpleCouponHistory, _ int) *SimpleCoupon {
					return history.Coupon.ToSimple()
				})
			} else {
				report.Coupons = []*SimpleCoupon{}
			}
		}

	}

	return reports, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

func (r *CourseRevenuePointRepository) Create(c *CourseRevenuePoint, trans *gorm.DB) error {
	return create(CourseRevenuePointTbl, c, trans)
}

func (r *CourseRevenuePointRepository) CreateMany(points []*CourseRevenuePoint, trans *gorm.DB) error {
	return createMany(CourseRevenuePointTbl, points, trans)
}

func (r *CourseRevenuePointRepository) Update(c *CourseRevenuePoint, trans *gorm.DB) error {
	return update(CourseRevenuePointTbl, c, trans)
}

func (r *CourseRevenuePointRepository) FindByID(id string, options *FindOneOptions) (*CourseRevenuePoint, error) {
	return findByID[CourseRevenuePoint](CourseRevenuePointTbl, id, options)
}

func (r *CourseRevenuePointRepository) FindOne(query *CourseRevenuePointQuery, options *FindOneOptions) (*CourseRevenuePoint, error) {
	return findOne[CourseRevenuePoint](CourseRevenuePointTbl, query, options)
}

func (r *CourseRevenuePointRepository) FindMany(query *CourseRevenuePointQuery, options *FindManyOptions) ([]*CourseRevenuePoint, error) {
	return findMany[CourseRevenuePoint](CourseRevenuePointTbl, query, options)
}

func (r *CourseRevenuePointRepository) FindPage(query *CourseRevenuePointQuery, options *FindPageOptions) ([]*CourseRevenuePoint, *Pagination, error) {
	return findPage[CourseRevenuePoint](CourseRevenuePointTbl, query, options)
}

func (r *CourseRevenuePointRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[CourseRevenuePoint](CourseRevenuePointTbl, id, trans)
}

func (r *CourseRevenuePointRepository) AddCourseRevenuePoint(timestamp int64, period TimePeriod) error {
	var distinctCourse []struct {
		CourseCuid string
	}

	if err := DB.Table(GetTblName(CourseTbl)).
		Select("DISTINCT cuid as course_cuid").
		Where("delete_at = 0").
		Where("latest = ?", true).
		Scan(&distinctCourse).Error; err != nil {
		return fmt.Errorf("failed to get distinct course: %v", err)
	}

	startTimestamp := timestamp
	switch period {
	case TimePeriodDay:
		startTimestamp = timestamp - int64(time.Hour*24)
	case TimePeriodWeek:
		startTimestamp = timestamp - int64(time.Hour*24*7)
	case TimePeriodMonth:
		startTimestamp = timestamp - int64(time.Hour*24*30)
	}

	points := make([]*CourseRevenuePoint, 0, len(distinctCourse))

	for _, cu := range distinctCourse {
		courseCuid := cu.CourseCuid
		summary, err := Repository.Course(r.ctx).GetCoursesRevenueSummary(&CourseRevenueSummaryQuery{
			CuidIn:    []string{courseCuid},
			StartTime: &startTimestamp,
			EndTime:   &timestamp,
		})
		if err != nil {
			return err
		}

		if summary == nil {
			continue
		}

		if len(summary.CourseRevenueReport) == 0 {
			continue
		}

		revenuePoint := &CourseRevenuePoint{
			CourseCuid: courseCuid,
			Timestamp:  timestamp,
			Revenue:    summary.CourseRevenueReport,
			Period:     period,
		}

		points = append(points, revenuePoint)

	}
	if err := r.CreateMany(points, nil); err != nil {
		return err
	}
	return nil
}

func (r *CourseRepository) isValidCurrency(currency Currency) bool {
	validCurrency := []Currency{
		FiatCurrencyUSD,
		FiatCurrencyVND,
		CryptoCurrencyNEAR,
		CryptoCurrencyUSDT,
		CryptoCurrencyUSDC,
		CryptoCurrencyOpenEdu,
		CryptoCurrencyAVAIL,
	}

	return slices.Contains(validCurrency, currency)
}
