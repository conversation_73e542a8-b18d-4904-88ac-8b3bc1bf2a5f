package models

import (
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Hashtag struct {
	Hash             string `json:"hash" validate:"required" gorm:"primaryKey;type:varchar(50);unique"`
	Name             string `json:"name" validate:"required"`
	CreateAt         int    `gorm:"type:int8;not null" json:"create_at"`
	UpdateAt         int    `gorm:"type:int8;not null" json:"update_at"`
	DeleteAt         int    `gorm:"type:int8;not null" json:"delete_at"`
	UseCount         int    `json:"use_count" gorm:"default:0"`
	FormattedHashtag string `json:"formatted_hashtag"`
	OrgID            string `json:"org_id"`
}

type HashtagQuery struct {
	Hash               *string  `json:"id" form:"id"`
	Name               *string  `json:"name" form:"name"`
	OrgID              *string  `json:"org_id" form:"org_id"`
	UseCount           *int     `json:"use_count" form:"use_count"`
	FormattedHashtag   *string  `json:"formatted_hashtag" form:"formatted_hashtag"`
	HashIn             []string `json:"hash_in" form:"hash_in"`
	FormattedHashtagIn []string `json:"formatted_hashtag_in" form:"formatted_hashtag_in"`
	IncludeDeleted     *bool
	NameIn             []string `json:"name_in" form:"name_in"`
}

type SimpleHashtag struct {
	Hash             string `json:"hash"`
	Name             string `json:"name"`
	OrgID            string `json:"org_id"`
	UseCount         int    `json:"use_count"`
	FormattedHashtag string `json:"formatted_hashtag"`
}

func (h *Hashtag) Sanitize() *SimpleHashtag {
	return &SimpleHashtag{
		Name:             h.Name,
		OrgID:            h.OrgID,
		Hash:             h.Hash,
		UseCount:         h.UseCount,
		FormattedHashtag: h.FormattedHashtag,
	}
}

func (query *HashtagQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Hash != nil {
		qb = qb.Where("hash = ?", *query.Hash)
	}

	if query.UseCount != nil {
		qb = qb.Where("use_count = ?", *query.UseCount)
	}

	if query.FormattedHashtag != nil {
		qb = qb.Where("formatted_hashtag = ?", *query.FormattedHashtag)
	}

	if len(query.HashIn) > 0 {
		qb = qb.Where("hash IN (?)", query.HashIn)
	}

	if len(query.FormattedHashtagIn) > 0 {
		qb = qb.Where("formatted_hashtag IN (?)", query.FormattedHashtagIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if len(query.NameIn) > 0 {
		qb = qb.Where("name IN (?)", query.NameIn)
	}

	return qb
}

// Create inserts a Hashtag to database, transaction is optional
func (r *HashtagRepository) Create(h *Hashtag, trans *gorm.DB) error {
	return create(HashtagTbl, h, trans)
}

func (r *HashtagRepository) CreateMany(hs []*Hashtag, trans *gorm.DB) error {
	return createMany(HashtagTbl, hs, trans)
}

// Update updates a Hashtag by ID in database, transaction is optional
func (r *HashtagRepository) Update(h *Hashtag, trans *gorm.DB) error {
	return update(HashtagTbl, h, trans)
}

func (r *HashtagRepository) UpsertMany(hs []*Hashtag, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "name"}, {Name: "org_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"name", "formatted_hashtag", "update_at", "delete_at"}),
	}).Create(&hs).Error; err != nil {
		return err
	}

	return nil
}

// FindOne finds one Hashtag with given find queries and options, transaction is optional
func (r *HashtagRepository) FindOne(query *HashtagQuery, options *FindOneOptions) (*Hashtag, error) {
	return findOne[Hashtag](HashtagTbl, query, options)
}

// FindMany finds Hashtags by query conditions with give find options
func (r *HashtagRepository) FindMany(query *HashtagQuery, options *FindManyOptions) ([]*Hashtag, error) {
	return findMany[Hashtag](HashtagTbl, query, options)
}

// FindPage returns Hashtags and pagination by query conditions and find options, transaction is optional
func (r *HashtagRepository) FindPage(query *HashtagQuery, options *FindPageOptions) ([]*Hashtag, *Pagination, error) {
	return findPage[Hashtag](HashtagTbl, query, options)
}

// Delete perform soft deletion to a Hashtags by ID, transaction is optional
func (r *HashtagRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Hashtag](HashtagTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *HashtagRepository) DeleteMany(query *HashtagQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Hashtag](HashtagTbl, query, trans)
}

// Count returns number of Hashtags by query conditions, transaction is optional
func (r *HashtagRepository) Count(query *HashtagQuery) (int64, error) {
	return count[Hashtag](HashtagTbl, query)
}

func (r *HashtagRepository) IncreaseHashtag(hashtag *Hashtag, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(HashtagTbl)).Debug().Where("hash = ?", hashtag.Hash).UpdateColumn(field, gorm.Expr("? + ?", gorm.Expr(field), amount)).Error

	return
}

func (r *HashtagRepository) DecreaseHashtag(hashtag *Hashtag, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(HashtagTbl)).Debug().Where("hash = ?", hashtag.Hash).UpdateColumn(field, gorm.Expr("? - ?", gorm.Expr(field), amount)).Error

	return
}
