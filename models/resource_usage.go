package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ResourceLimitCycle string

type ResourceUsage struct {
	Model
	UserID   string           `json:"user_id" gorm:"type:varchar(20)"`
	Cycle    PlanCycle        `json:"cycle"`
	Time     int              `json:"time" gorm:"type:int8;not null;default:0"`
	AiUsages AIResourceUsages `json:"ai_usages" gorm:"type:jsonb"`
}

type AiResourceUsage struct {
	Enable       bool            `json:"enable"`
	AIModelID    string          `json:"ai_model_id"`
	AIModelName  string          `json:"model_name"`
	RequestUsed  int64           `json:"request_used"`
	BalanceUsed  decimal.Decimal `json:"balance_used"`
	RequestLimit int64           `json:"request_limit"` // limit by plan
	BalanceLimit decimal.Decimal `json:"balance_limit"` // limit by plan
	Bonus        decimal.Decimal `json:"bonus"`
}

func (j AiResourceUsage) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *AiResourceUsage) Scan(value interface{}) error {
	source, ok := value.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &j)
	if err != nil {
		return err
	}

	return nil
}

type AIResourceUsages []*AiResourceUsage

func (j AIResourceUsages) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *AIResourceUsages) Scan(value interface{}) error {
	source, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &j)
	if err != nil {
		return err
	}

	return nil
}

type ResourceUsageQuery struct {
	ID      *string    `json:"id" form:"id"`
	UserID  *string    `json:"user_id" form:"user_id"`
	Time    *int       `json:"time" form:"time"`
	Deleted *bool      `json:"deleted" form:"deleted"`
	Cycle   *PlanCycle `json:"cycle" form:"cycle"`
}

func (query *ResourceUsageQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.Time != nil {
		qb = qb.Where("time = ?", *query.Time)
	}

	if query.Cycle != nil {
		qb = qb.Where("cycle = ?", *query.Cycle)
	}

	if query.Deleted == nil || !*query.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a ResourceUsage to database, transaction is optional
func (r *ResourceUsageRepository) Create(q *ResourceUsage, trans *gorm.DB) error {
	return create(ResourceUsageTbl, q, trans)
}

func (r *ResourceUsageRepository) CreateMany(qs []*ResourceUsage, trans *gorm.DB) error {
	return createMany(ResourceUsageTbl, qs, trans)
}

// Update updates a ResourceUsage by ID in database, transaction is optional
func (r *ResourceUsageRepository) Update(q *ResourceUsage, trans *gorm.DB) error {
	return update(ResourceUsageTbl, q, trans)
}

// FindByID finds a ResourceUsage by ID with given find options, transaction is optional
func (r *ResourceUsageRepository) FindByID(id string, options *FindOneOptions) (*ResourceUsage, error) {
	return findByID[ResourceUsage](ResourceUsageTbl, id, options)
}

// FindOne finds one ResourceUsage with given find queries and options, transaction is optional
func (r *ResourceUsageRepository) FindOne(query *ResourceUsageQuery, options *FindOneOptions) (*ResourceUsage, error) {
	return findOne[ResourceUsage](ResourceUsageTbl, query, options)
}

// FindMany finds ResourceUsage by query conditions with give find options
func (r *ResourceUsageRepository) FindMany(query *ResourceUsageQuery, options *FindManyOptions) ([]*ResourceUsage, error) {
	return findMany[ResourceUsage](ResourceUsageTbl, query, options)
}

// FindPage returns ResourceUsage and pagination by query conditions and find options, transaction is optional
func (r *ResourceUsageRepository) FindPage(query *ResourceUsageQuery, options *FindPageOptions) ([]*ResourceUsage, *Pagination, error) {
	return findPage[ResourceUsage](ResourceUsageTbl, query, options)
}

// Delete perform soft deletion to a ResourceUsage by ID, transaction is optional
func (r *ResourceUsageRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ResourceUsage](ResourceUsageTbl, id, trans)
}
