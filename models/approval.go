package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/util"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type ApprovalStatus string

const (
	ApprovalStatusNew      ApprovalStatus = "new"
	ApprovalStatusPending  ApprovalStatus = "pending"
	ApprovalStatusApprove  ApprovalStatus = "approved"
	ApprovalStatusReject   ApprovalStatus = "rejected"
	ApprovalStatusCancel   ApprovalStatus = "cancelled"
	ApprovalStatusFeedback ApprovalStatus = "feedback"
)

var ApprovalStatuses = []ApprovalStatus{
	ApprovalStatusNew, ApprovalStatusApprove, ApprovalStatusReject,
}

type ApproveType string

const (
	ApproveTypePublish              ApproveType = "publish"
	ApproveTypePublishOrg           ApproveType = "publish_org"
	ApproveTypePublishAll           ApproveType = "publish_all"
	ApproveTypePublishRoot          ApproveType = "publish_root"
	ApproveTypePublishInvestment    ApproveType = "publish_investment"
	ApproveTypeWalletFiatWithdraw   ApproveType = "wallet_fiat_withdrawal"
	ApproveTypeWalletCryptoWithdraw ApproveType = "wallet_crypto_withdrawal"
)

var ApprovalTypes = []ApproveType{
	ApproveTypePublishOrg, ApproveTypePublishAll, ApproveTypePublishRoot, ApproveTypePublishInvestment,
}

type Approval struct {
	Model
	OrgID         string          `json:"org_id,omitempty" gorm:"type: varchar(20)"`
	Org           *Organization   `json:"org,omitempty" `
	EntityType    ModelName       `json:"entity_type,omitempty"`
	EntityID      string          `json:"entity_id,omitempty" gorm:"type: varchar(20)"`
	EntityUID     string          `json:"entity_uid,omitempty"  gorm:"type: varchar(20)"`
	EntityVersion int             `json:"entity_version"`
	Entity        *EntityApproval `json:"entity,omitempty" gorm:"-"`
	EntitySchema  string          `json:"entity_schema,omitempty"`
	RequesterID   string          `json:"requester_id,omitempty" gorm:"type: varchar(20)"`
	Requester     *User           `json:"requester,omitempty" `
	RequestDate   int64           `json:"request_date,omitempty"`
	ConfirmByID   string          `json:"confirm_by_id,omitempty" gorm:"type: varchar(20)"`
	ConfirmBy     *User           `json:"confirm_by,omitempty" gorm:"-" `
	ConfirmDate   int64           `json:"confirm_date,omitempty"`
	Status        ApprovalStatus  `json:"status,omitempty" gorm:"default:new"`
	Type          ApproveType     `json:"type,omitempty"`
	Note          string          `json:"note"`
	Props         ApprovalProp    `json:"props" gorm:"type:jsonb"`
	RequestValue  string          `json:"request_value"`
	ApproveValue  string          `json:"approve_value"`
	Files         []*File         `json:"files" gorm:"-"`
	RequestUid    string          `json:"request_uid" gorm:"type:varchar(20)"` // request session uid
}

type SimpleApproval struct {
	Model
	OrgID         string              `json:"org_id,omitempty" gorm:"type: varchar(20)"`
	Org           *SimpleOrganization `json:"org"`
	EntityType    ModelName           `json:"entity_type,omitempty"`
	EntityID      string              `json:"entity_id,omitempty" gorm:"type: varchar(20)"`
	EntityUID     string              `json:"entity_uid"`
	EntityVersion int                 `json:"entity_version"`
	RequesterID   string              `json:"requester_id,omitempty" gorm:"type: varchar(20)"`
	RequestDate   int64               `json:"request_date,omitempty"`
	ConfirmID     string              `json:"confirm_id,omitempty" gorm:"type: varchar(20)"`
	ConfirmDate   int64               `json:"confirm_date,omitempty"`
	Status        ApprovalStatus      `json:"status,omitempty"`
	Type          ApproveType         `json:"type,omitempty"`
	Note          string              `json:"note"`
	Props         ApprovalProp        `json:"props" gorm:"type:jsonb"`
	RequestValue  string              `json:"request_value"`
	ApproveValue  string              `json:"approve_value"`
	Files         []*File             `json:"files"`
	RequestUid    string              `json:"request_uid"`
}

type ApprovalProp struct {
	IsAdminFeedback bool                  `json:"is_admin_feedback"`
	IsIncludeChange bool                  `json:"is_include_change"`
	AdminID         string                `json:"admin_id"`
	Discussion      []*ApprovalSingleSend `json:"discussion"`
	SettingID       string                `json:"setting_id"`
	SettingValue    JSONB                 `json:"setting_value"`
	PreVersion      int                   `json:"pre_version"`
	PreID           string                `json:"pre_id"`
}

type ApprovalSingleSend struct {
	ID            string         `json:"id"`
	Email         string         `json:"email"`
	Username      string         `json:"username"`
	Content       string         `json:"content"`
	SendDate      int64          `json:"send_date"`
	EntityID      string         `json:"entity_id"`
	EntityVersion *int           `json:"entity_version" gorm:"default:0"`
	Action        ApprovalStatus `json:"action"`
}

func (s ApprovalProp) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	return string(val), err
}

func (s *ApprovalProp) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &s)
	if err != nil {
		return err
	}

	return nil
}

type ApprovalQuery struct {
	ID             *string          `form:"id" form:"id"`
	EntityID       *string          `form:"entity_id" form:"entity_id"`
	EntityType     *string          `json:"entity_type" form:"entity_type"`
	EntityUID      *string          `json:"entity_uid" form:"entity_uid"`
	OrgID          *string          `form:"org_id" form:"org_id"`
	RequesterID    *string          `form:"requester_id" form:"requester_id"`
	ConfirmID      *string          `form:"confirm_id" form:"confirm_id"`
	Status         *ApprovalStatus  `form:"status" form:"status"`
	StatusIn       []ApprovalStatus `form:"status_in" form:"status_in"`
	Type           *ApproveType     `form:"type" form:"type"`
	TypeIn         []ApproveType    `form:"type_in" form:"type_in"`
	IncludeDeleted *bool            `form:"include_deleted"`
	RequestUid     *string          `json:"request_uid" form:"request_uid"`
	RequestUidIn   []string         `json:"request_uid_in" form:"request_uid_in"`

	SearchTerm       *string `json:"search_term" form:"search_term"`
	SearchCategories *string `json:"search_categories" form:"search_categories"`
}

func (a *Approval) Sanitize() *SimpleApproval {
	var org *SimpleOrganization
	if a.Org != nil {
		org = a.Org.ToSimple()
	}
	return &SimpleApproval{
		Model:         a.Model,
		OrgID:         a.OrgID,
		Org:           org,
		EntityType:    a.EntityType,
		EntityID:      a.EntityID,
		EntityUID:     a.EntityUID,
		EntityVersion: a.EntityVersion,
		RequesterID:   a.RequesterID,
		RequestDate:   a.RequestDate,
		ConfirmID:     a.ConfirmByID,
		ConfirmDate:   a.ConfirmDate,
		Status:        a.Status,
		Type:          a.Type,
		Note:          a.Note,
	}
}

func (query *ApprovalQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}
	if query.EntityID != nil {
		qb = qb.Where("entity_id = ?", *query.EntityID)
	}

	if query.EntityType != nil {
		qb = qb.Where("entity_type = ?", *query.EntityType)
	}

	if query.EntityUID != nil {
		qb = qb.Where("entity_uid = ?", *query.EntityUID)
	}

	if query.RequesterID != nil {
		qb = qb.Where("requester_id = ?", *query.RequesterID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.ConfirmID != nil {
		qb = qb.Where("confirm_id = ?", *query.ConfirmID)
	}
	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", query.StatusIn)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.RequestUid != nil {
		qb = qb.Where("request_uid = ?", *query.RequestUid)
	}

	if len(query.RequestUidIn) > 0 {
		qb = qb.Where("request_uid IN (?)", query.RequestUidIn)
	}

	if len(query.TypeIn) > 0 {
		qb = qb.Where("type IN (?)", query.TypeIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (query *ApprovalQuery) ApplyJoin(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("a.id = ?", *query.ID)
	}
	if query.EntityID != nil {
		qb = qb.Where("a.entity_id = ?", *query.EntityID)
	}

	if query.EntityType != nil {
		qb = qb.Where("a.entity_type = ?", *query.EntityType)
	}

	if query.EntityUID != nil {
		qb = qb.Where("a.entity_uid = ?", *query.EntityUID)
	}

	if query.RequesterID != nil {
		qb = qb.Where("a.requester_id = ?", *query.RequesterID)
	}

	if query.OrgID != nil {
		qb = qb.Where("a.org_id = ?", *query.OrgID)
	}

	if query.ConfirmID != nil {
		qb = qb.Where("a.confirm_id = ?", *query.ConfirmID)
	}
	if query.Status != nil {
		qb = qb.Where("a.status = ?", *query.Status)
	}

	if query.Type != nil {
		qb = qb.Where("a.type = ?", *query.Type)
	}

	if len(query.TypeIn) > 0 {
		qb = qb.Where("a.type IN (?)", query.TypeIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("a.delete_at = 0")
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		for _, category := range categories {
			switch category {
			case "requester_email":
				qb = qb.Where("rqu.email ~* ?", *query.SearchTerm)
			case "confirmer_email":
				qb = qb.Where("cfu.email ~* ?", *query.SearchTerm)
			default:
				break
			}
		}
	}

	return qb
}

func (r *ApprovalRepository) Create(a *Approval, trans *gorm.DB) error {
	err := create(ApprovalTbl, a, trans)
	if err != nil {
		return err
	}
	if a.Files != nil && len(a.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(ApprovalModelName, a.ID, FilesField, a.Files); fErr != nil {
			return fErr
		}
	}
	return nil
}

func (r *ApprovalRepository) CreateMany(a []*Approval, trans *gorm.DB) error {
	return createMany(ApprovalTbl, a, trans)
}

func (r *ApprovalRepository) Update(a *Approval, trans *gorm.DB) error {
	err := update(ApprovalTbl, a, trans)
	if err != nil {
		return err
	}
	if a.Files != nil && len(a.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(ApprovalModelName, a.ID, FilesField, a.Files); fErr != nil {
			return fErr
		}
	}
	return nil
}

func (r *ApprovalRepository) FindByID(id string, options *FindOneOptions) (*Approval, error) {
	return findByID[Approval](ApprovalTbl, id, options)
}

func (r *ApprovalRepository) FindOne(query *ApprovalQuery, options *FindOneOptions) (*Approval, error) {
	approval, err := findOne[Approval](ApprovalTbl, query, options)
	if err != nil {
		return nil, err
	}

	if files, mErr := Repository.FileRelation.GetFiles(ApprovalModelName, approval.ID, FilesField); mErr != nil {
		return approval, mErr
	} else {
		approval.Files = files
	}
	return approval, nil
}

func (r *ApprovalRepository) FindMany(query *ApprovalQuery, options *FindManyOptions) ([]*Approval, error) {
	return findMany[Approval](ApprovalTbl, query, options)
}

func findPageApprovalWithJoin(query *ApprovalQuery, options *FindPageOptions) ([]*Approval, *Pagination, error) {
	tbl := GetTblName(ApprovalTbl)
	entitiesChan := make(chan []*Approval)
	countChan := make(chan int64)
	errorChan := make(chan error)
	var err error
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		var entities []*Approval

		qb := query.ApplyJoin(DB)
		lo.ForEach(options.Sort, func(clause string, _ int) {
			qb = qb.Order("a." + clause)
		})
		qb = qb.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)
		qb = qb.Table(tbl + " as a").Debug().Select("a.*")
		qb = qb.Joins("left join " + GetTblName(UserTbl) + " as rqu on rqu.id = a.requester_id")
		qb = qb.Joins("left join " + GetTblName(UserTbl) + " as cfu on cfu.id = a.confirm_by_id")
		result := qb.Find(&entities)

		if serr := result.Error; serr != nil {
			errorChan <- serr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		var c int64
		var entity Approval
		qb := query.ApplyJoin(DB)
		result := qb.Table(tbl + " as a").Debug().
			Joins("left join " + GetTblName(UserTbl) + " as rqu on rqu.id = a.requester_id").
			Joins("left join " + GetTblName(UserTbl) + " as cfu on cfu.id = a.confirm_by_id").
			Model(&entity).Count(&c)
		if cerr := result.Error; cerr != nil {
			errorChan <- cerr
			return
		}

		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*Approval
	var entitiesCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entitiesCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entitiesCount)), nil
}

func (r *ApprovalRepository) FindPage(query *ApprovalQuery, options *FindPageOptions) ([]*Approval, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}

	shouldPreloadConfirmer := false
	shouldPreloadRequester := false
	shouldPreloadEntity := false
	shouldPreloadFiles := false

	if lo.Contains(options.Preloads, ConfirmByField) {
		shouldPreloadConfirmer = true
		options.Preloads = util.RemoveElement(options.Preloads, ConfirmByField)
	}

	if lo.Contains(options.Preloads, RequesterField) {
		shouldPreloadRequester = true
		options.Preloads = util.RemoveElement(options.Preloads, RequesterField)
	}

	if lo.Contains(options.Preloads, EntityField) {
		shouldPreloadEntity = true
		options.Preloads = util.RemoveElement(options.Preloads, EntityField)
	}

	if lo.Contains(options.Preloads, FilesField) {
		shouldPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, FilesField)
	}

	approvals, pagination, aErr := findPageApprovalWithJoin(query, options)
	if aErr != nil {
		return nil, nil, aErr
	}

	if len(approvals) == 0 {
		return approvals, pagination, nil
	}

	mapUserIDUser := map[string]*User{}
	mapEntityIDEntity := map[string]*EntityApproval{}

	if shouldPreloadConfirmer {
		confirmIDs := lo.Map(approvals, func(a *Approval, _ int) string { return a.ConfirmByID })
		confirmers, fmErr := Repository.User.FindMany(&UserQuery{IDIn: &confirmIDs}, nil)
		if fmErr != nil {
			return approvals, pagination, fmErr
		}
		lo.ForEach(confirmers, func(u *User, _ int) { mapUserIDUser[u.ID] = u })
		lo.ForEach(approvals, func(a *Approval, _ int) {
			confirmer, ok := mapUserIDUser[a.ConfirmByID]
			if ok {
				a.ConfirmBy = confirmer.Sanitize()
			} else {
				a.ConfirmBy = nil
			}
		})
	}

	if shouldPreloadRequester {
		requesterIDs := lo.Map(approvals, func(a *Approval, _ int) string { return a.RequesterID })
		requesterIDs = lo.Uniq(requesterIDs)
		requesters, fmErr := Repository.User.FindMany(&UserQuery{IDIn: &requesterIDs}, nil)
		if fmErr != nil {
			return approvals, pagination, fmErr
		}
		lo.ForEach(requesters, func(u *User, _ int) { mapUserIDUser[u.ID] = u })
		lo.ForEach(approvals, func(a *Approval, _ int) {
			requester, ok := mapUserIDUser[a.RequesterID]
			if ok {
				a.Requester = requester.Sanitize()
			} else {
				a.Requester = nil
			}
		})
	}

	if shouldPreloadEntity {
		mGroupSchema := make(map[string][]string)
		lo.ForEach(approvals, func(a *Approval, _ int) {
			mGroupSchema[a.EntitySchema] = append(mGroupSchema[a.EntitySchema], a.EntityID)
		})
		entities, fmErr := r.FindManyEntityRelation(ModelName(approvals[0].EntityType), mGroupSchema)
		if fmErr != nil {
			return approvals, pagination, fmErr
		}
		lo.ForEach(entities, func(e EntityApproval, _ int) { mapEntityIDEntity[e.GetID()] = &e })
		lo.ForEach(approvals, func(a *Approval, _ int) {
			entity, ok := mapEntityIDEntity[a.EntityID]
			if ok {
				a.Entity = entity
			} else {
				a.Entity = nil
			}
		})
	}

	if shouldPreloadFiles {
		ids := lo.Map(approvals, func(a *Approval, _ int) string {
			return a.ID
		})
		if files, mErr := Repository.FileRelation.GetFilesByEntities(ApprovalModelName, ids, FilesField); mErr != nil {
			return nil, nil, mErr
		} else {
			lo.ForEach(approvals, func(a *Approval, _ int) {
				a.Files = files[a.ID]
			})
		}
	}

	lo.ForEach(approvals, func(item *Approval, _ int) {
		org, _ := Repository.Organization.FindByID(item.OrgID, nil)
		item.Org = org
	})

	return approvals, pagination, aErr
}
func (r *ApprovalRepository) FindEntityRelation(entityType ModelName, entityID string) (EntityApproval, error) {
	switch entityType {
	case BlogModelName:
		return Repository.Blog.FindByID(entityID, &FindOneOptions{})
	case CourseModelName:
		return Repository.Course(r.ctx).FindByID(entityID, &FindOneOptions{})
	case WalletModelName:
		return Repository.Wallet.FindByID(entityID, &FindOneOptions{})
	case ClpLaunchpadModelName:
		return Repository.ClpLaunchpad(r.ctx).FindByID(entityID, &FindOneOptions{Preloads: []string{CoursesField, OwnerField}})
	default:
		return nil, errors.New("entity not found")
	}
}

func (r *ApprovalRepository) FindManyEntityRelation(entityType ModelName, mapEntityIDs map[string][]string) ([]EntityApproval, error) {
	switch entityType {
	case BlogModelName:
		// TODO: implement find many blog by schema
		var blogs []*Blog
		for _, entityIDs := range mapEntityIDs {
			blog, err := Repository.Blog.FindMany(&BlogQuery{IDIn: entityIDs}, &FindManyOptions{Preloads: []string{BlogPreloadsHashTag, BlogPreloadsCategories}})
			if err != nil {
				return nil, err
			}
			blogs = append(blogs, blog...)
		}
		result := make([]EntityApproval, len(blogs))
		for i, blog := range blogs {
			result[i] = EntityApproval(blog)
		}

		return result, nil
	case CourseModelName:
		var courses []*Course
		for _, IDs := range mapEntityIDs {
			course, err := Repository.Course(r.ctx).FindMany(&CourseQuery{IDIn: IDs}, &FindManyOptions{})
			if err != nil {
				return nil, err
			}
			courses = append(courses, course...)
		}

		result := make([]EntityApproval, len(courses))
		for i, course := range courses {
			result[i] = EntityApproval(course)
		}

		return result, nil
	case WalletModelName:
		var wallets []*Wallet
		for _, IDs := range mapEntityIDs {
			wls, err := Repository.Wallet.FindMany(&WalletQuery{IDIn: IDs}, &FindManyOptions{})
			if err != nil {
				return nil, err
			}
			wallets = append(wallets, wls...)
		}

		result := make([]EntityApproval, len(wallets))
		for i, w := range wallets {
			result[i] = EntityApproval(w)
		}

		return result, nil
	case ClpLaunchpadModelName:
		var launchpads []*ClpLaunchpad
		for _, IDs := range mapEntityIDs {
			clp, err := Repository.ClpLaunchpad(r.ctx).FindMany(&LaunchpadQuery{IDIn: IDs}, &FindManyOptions{
				Preloads: []string{CoursesField, OwnerField},
			})
			if err != nil {
				return nil, err
			}
			launchpads = append(launchpads, clp...)
		}

		result := make([]EntityApproval, len(launchpads))
		for i, launchpad := range launchpads {
			result[i] = EntityApproval(launchpad)
		}

		return result, nil

	default:
		return nil, errors.New("entity type not handle")
	}
}

func (r *ApprovalRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Approval](ApprovalTbl, id, trans)
}

func (r *ApprovalRepository) DeleteMany(query *ApprovalQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Approval](ApprovalTbl, query, trans)
}

func (r *ApprovalRepository) CancelRequestByEntity(newID string, entityType ModelName, entityID string) (err error) {
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	result := tx.Model(&Approval{}).Debug().
		Where("entity_type = ? AND entity_uid = ? and status in (?) and id <> ?",
			entityType, entityID,
			[]ApprovalStatus{ApprovalStatusNew, ApprovalStatusPending},
			newID,
		).
		Update("status", ApprovalStatusCancel)

	return result.Error
}
