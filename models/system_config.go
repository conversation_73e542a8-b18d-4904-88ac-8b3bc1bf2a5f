package models

import (
	"encoding/json"
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strings"

	"github.com/shopspring/decimal"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type SystemPointType string

const (
	AllSystemConfig                      = "all_system_config"
	AllowOriginConfig                    = "allow_origin_domain"
	LearnMethodConfig                    = "learn_method"
	CourseLessonContentType              = "course_lesson_content_type"
	CurrencyConfig                       = "currency"
	LocaleConfig                         = "locale"
	OrgLandingPageConfig                 = "org_landing_page"
	EmailTemplateVarsConfig              = "email_template_variables"
	SupportContactConfig                 = "support_contact"
	VerifyEmailPathConfig                = "verify_email_path"
	ResetPasswordPathConfig              = "reset_password_path"
	SetPasswordPathConfig                = "set_password_path"
	InviteCreatorPathConfig              = "invite_creator_path"
	InviteUserPathConfig                 = "invite_user_path"
	PolicyUrlConfig                      = "policy_url"
	WalletFiatMinRemainAmount            = "wallet_fiat_min_remain_amount"
	WalletCryptoMinRemainAmount          = "wallet_crypto_min_remain_amount"
	VerifyOTP                            = "verify_otp"
	BlockDisposableEmailsEnabled         = "block_disposable_emails_enabled"
	DisposableEmailDomains               = "disposable_email_domains"
	CertificateTemplateDefault           = "certificate_template_default"
	AILanguages                          = "ai_languages"
	AIResourceUsageEmailConfig           = "ai_resource_usage_email_config"
	I18nConfig                           = "i18n_config"
	I18nTranslations                     = "i18n_translations"
	ThemeSystem                          = "theme_system"
	CourseLaunchpadMinSections           = "course_launchpad_min_sections"
	CourseLaunchpadVotingPhaseRule       = "course_launchpad_voting_phase_rule"
	CourseLaunchpadMinApprovalPercentage = "course_launchpad_min_approval_percentage"
	CourseLaunchpadMinRejectPercentage   = "course_launchpad_min_reject_percentage"
	CourseLaunchPadMinPledgeOptions      = "course_launchpad_min_pledge_options"
	CourseLaunchpadVotingDuration        = "course_launchpad_voting_duration"
	FundingWaitingPeriodDays             = "funding_waiting_period_days"
	CourseLaunchpadTestIDs               = "course_launchpad_test_ids"
	WaitingSettingFundingTimeDays        = "waiting_setting_funding_time_days"
	ClpWaitingProcessFundingDays         = "clp_waiting_process_funding_days"
	ClpMaximumPledgePercentage           = "clp_maximum_pledge_percentage"
	AIModelsConfig                       = "ai_models_config"
	AISubscribePlanForOrgEnable          = "ai_subscribe_plan_for_org_enable"
	AISubscribePlanForOrgConfig          = "ai_subscribe_plan_for_org_config"
	AIGovVN2025Campaign                  = "ai_gov_vn_2025_campaign"
	PaymasterAllowedCoursesConfig        = "paymaster_allowed_courses"

	PointSystemSetting           = "point_system_setting"
	FieldAllowInRequest          = "field_allow_in_request"
	ConfirmInvitationPathConfig  = "confirm_invitation_path"
	NormalizeEmailEnabled        = "normalize_email_enabled"
	NormalizeEmailRules          = "normalize_email_rules"
	PlatformSponsorNFTGasCourses = "platform_sponsor_nft_gas_courses"
)

type CourseLaunchpadVotingPhaseRuleConfig struct {
	FundingGoalGte decimal.Decimal `json:"funding_goal_gte"`
	FundingGoalLt  decimal.Decimal `json:"funding_goal_lt"`
	MaxPhases      int             `json:"max_phases"`
}

type SystemConfig struct {
	Model
	Key       string `json:"key"`
	Locale    string `json:"locale" gorm:"type:varchar(10)"`
	DataType  string `json:"data_type"`
	Value     string `json:"value" gorm:"type:text"`
	OrgID     string `json:"org_id" gorm:"type:varchar(20)"`
	Domain    string `json:"domain"`
	AltDomain string `json:"alt_domain"`

	Files []*File `json:"files" gorm:"-"`
}

type SimpleConfig struct {
	Model
	Key      string `json:"key"`
	Locale   string `json:"locale"`
	Value    any    `json:"value"`
	OrgID    string `json:"org_id"`
	DataType string `json:"data_type"`
	Domain   string `json:"domain"`

	Files []*File `json:"files,omitempty"`
}

type CurrencyInfo struct {
	Name   string    `json:"name"`
	Symbol Currency  `json:"symbol"`
	Type   AssetType `json:"type"`
}

type ResourceUsageEmailConfig struct {
	WhitelistEmail        StringArray `json:"white_list_email"`
	WhitelistEmailPattern string      `json:"white_list_email_pattern"`
	BlacklistEmailPattern string      `json:"black_list_email_pattern"`
}

type I18nConfigInfo struct {
	Files   map[string]string  `json:"files"`
	Locale  string             `json:"locale"`
	Locales []string           `json:"locales"`
	Stats   []*I18nConfigStats `json:"stats"`
}

type I18nConfigStats struct {
	Language     string `json:"language"`
	Locale       string `json:"locale"`
	Total        int    `json:"total"`
	Translated   int    `json:"translated"`
	Untranslated int    `json:"untranslated"`
}

type SubscribeAIPlanInfo struct {
	OrgSchema   string `json:"org_schema"`
	PlanID      string `json:"plan_id"`
	Event       string `json:"event"`
	EmailEnable bool   `json:"email_enable"`
}

type AiGovVn2025Campaign struct {
	CampaignKey string                  `json:"campaign_key"`
	Courses     []*AiCampaignCourseItem `json:"courses"`
	OrgID       string                  `json:"org_id"`
	FormEvent   FormEvent               `json:"form_event"`
}

type AiCampaignCourseItem struct {
	CourseCuid string      `json:"course_cuid"`
	Name       string      `json:"name"`
	Thumbnail  *SimpleFile `json:"Thumbnail"`
}

func (cfg *SystemConfig) GetKey() string {
	return cfg.Key
}

func (cfg *SystemConfig) IsI18nConfig() bool {
	return cfg.Key == I18nConfig
}

func (cfg *SystemConfig) IsI18nTranslations() bool {
	return cfg.Key == I18nTranslations
}

func (cfg *SystemConfig) IsEmailTemplateVarsConfig() bool {
	return cfg.Key == EmailTemplateVarsConfig
}

func (cfg *SystemConfig) IsSupportContactConfig() bool {
	return cfg.Key == SupportContactConfig
}

func (cfg *SystemConfig) IsPolicyUrl() bool {
	return cfg.Key == PolicyUrlConfig
}

func (cfg *SystemConfig) IsVerifyEmailPathConfig() bool {
	return cfg.Key == VerifyEmailPathConfig
}

func (cfg *SystemConfig) IsResetPasswordPathConfig() bool {
	return cfg.Key == ResetPasswordPathConfig
}

func (cfg *SystemConfig) IsSetPasswordPathConfig() bool {
	return cfg.Key == SetPasswordPathConfig
}

func (cfg *SystemConfig) IsInviteCreatorPathConfig() bool {
	return cfg.Key == InviteCreatorPathConfig
}

func (cfg *SystemConfig) IsInviteUserPathConfig() bool {
	return cfg.Key == InviteUserPathConfig
}

func (cfg *SystemConfig) GetCurrentFile() (*File, bool) {
	return lo.Find(cfg.Files, func(file *File) bool {
		return strings.HasPrefix(file.Name, CurrentPrefix)
	})
}

func (cfg *SystemConfig) GetBackupFile() (*File, bool) {
	return lo.Find(cfg.Files, func(file *File) bool {
		return strings.HasPrefix(file.Name, BackupPrefix)
	})
}

type SystemConfigQuery struct {
	ID             *string `form:"id"`
	Key            *string `form:"key"`
	Locale         *string `form:"locale"`
	OrgID          *string `form:"org_id"`
	IncludeDeleted *bool   `form:"include_deleted"`
	Domain         *string `json:"domain" form:"domain"`
}

func (query SystemConfigQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Domain != nil {
		qb = qb.Where("domain = ?", *query.Domain)
	}

	if query.Key != nil {
		qb = qb.Where("key = ?", *query.Key)
	}

	if query.Locale != nil {
		qb = qb.Where("locale = ?", *query.Locale)
	}

	if (query.IncludeDeleted != nil) && !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

type SystemConfigPreload struct {
	Files bool
}

func (r *SystemConfigRepository) Upsert(c *SystemConfig) error {
	var s *SystemConfig
	q := GetDb(SystemConfigTbl).
		Where(SystemConfig{Key: c.Key, Locale: c.Locale, OrgID: c.OrgID}).
		Assign(SystemConfig{Value: c.Value, OrgID: c.OrgID, DataType: c.DataType}).
		FirstOrCreate(&s)
	if q.Error != nil {
		return q.Error
	}
	if cErr := Cache.System.Flush(); cErr != nil {
		log.Errorf("SystemConfigRepository::Upsert Flush all system config cache failed: %v", cErr)
	}
	return nil
}

func (r *SystemConfigRepository) getExPreload(preloads []string) (*SystemConfigPreload, []string) {
	shouldPreloadFiles := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, FilesField) {
		shouldPreloadFiles = true
		newPreloads = util.RemoveElement(newPreloads, FilesField)
	}

	return &SystemConfigPreload{
		Files: shouldPreloadFiles,
	}, newPreloads
}

func (r *SystemConfigRepository) preloadFiles(sysConfigs []*SystemConfig) error {
	if len(sysConfigs) == 0 {
		return nil
	}
	sysConfigIDs := lo.Map(sysConfigs, func(sysConfig *SystemConfig, _ int) string {
		return sysConfig.ID
	})
	if filesBySysConfigIDs, mErr := Repository.FileRelation.GetFilesByEntities(SystemConfigModelName, sysConfigIDs, FilesField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(sysConfigs, func(sysConfig *SystemConfig, _ int) {
			sysConfig.Files = filesBySysConfigIDs[sysConfig.ID]
		})
	}
	return nil
}

func (r *SystemConfigRepository) FindAll() ([]*SystemConfig, error) {
	var systemConfigs []*SystemConfig
	var configCache []interface{}
	if cErr := Cache.System.GetAll(AllSystemConfig, &configCache); cErr == nil && len(configCache) > 0 {
		if err := Cache.Convert(configCache, &systemConfigs); err != nil {
			log.Errorf("SystemConfigRepository.FindAll::Convert cache result to system configs error: %v", err)
		}
	}

	if len(systemConfigs) > 0 {
		return systemConfigs, nil
	}

	// If not found in cache, retrieve from database
	configs, err := r.FindMany(&SystemConfigQuery{}, &FindManyOptions{Preloads: []string{FilesField}})
	if err != nil {
		return nil, err
	}

	if err = addSuffixToI18nConfigFileNames(configs); err != nil {
		log.Errorf("SystemConfigRepository.FindAll::Add suffix to i18n config file names error: %v", err)
	}
	// Update cache with the retrieved data
	cacheItems := lo.Map(configs, func(sysCfg *SystemConfig, _ int) interface{} {
		return sysCfg
	})

	if cErr := Cache.System.SetAll(AllSystemConfig, cacheItems); cErr != nil {
		log.Errorf("SystemConfigRepository.FindAll::Set cache all system config error: %v", cErr)
	}
	return configs, nil
}

func addSuffixToI18nConfigFileNames(configs []*SystemConfig) error {
	updateAtByLocales := map[string]int{}
	for _, config := range configs {
		if !config.IsI18nTranslations() {
			continue
		}

		updateAtByLocales[config.Locale] = config.UpdateAt
	}

	for _, config := range configs {
		if !config.IsI18nConfig() {
			continue
		}

		var i18nConfig I18nConfigInfo
		if err := json.Unmarshal([]byte(config.Value), &i18nConfig); err != nil {
			return fmt.Errorf("unmarshal the system config value failed: %w", err)
		}

		for locale, fileName := range i18nConfig.Files {
			if updateAt, found := updateAtByLocales[locale]; found {
				i18nConfig.Files[locale] = fmt.Sprintf("%s?update_at=%d", fileName, updateAt)
			}
		}

		b, err := json.Marshal(&i18nConfig)
		if err != nil {
			return fmt.Errorf("marshal i18n config to bytes failed: %w", err)
		}

		config.Value = string(b)
	}
	return nil
}

func (r *SystemConfigRepository) FindByKey(key string) (*SystemConfig, error) {
	// Try to get from cache
	var sysConfigCache interface{}
	err := Cache.System.Get(key, &sysConfigCache)
	if err == nil && sysConfigCache != nil {
		var sysConfig *SystemConfig
		if err = Cache.Convert(sysConfigCache, &sysConfig); err != nil {
			log.Errorf("SystemConfigRepository::FindByKey Convert system config (key=%s) cache failed: %v", key, err)
		} else {
			return sysConfig, nil
		}
	}

	// If not found in cache, get role accesses from DB
	sysConfig, err := r.FindOne(&SystemConfigQuery{Key: &key}, &FindOneOptions{})
	if err != nil {
		return nil, err
	}

	// Set system config to cache
	if cErr := Cache.System.Set(key, sysConfig); cErr != nil {
		log.Errorf("SystemConfigRepository::FindByKey Set system config (key=%s) cache failed: %v", key, err)
	}

	return sysConfig, nil
}

func (r *SystemConfigRepository) FindOne(query *SystemConfigQuery, options *FindOneOptions) (*SystemConfig, error) {
	if options == nil {
		options = &FindOneOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	sysConfig, err := findOne[SystemConfig](SystemConfigTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles([]*SystemConfig{sysConfig}); fErr != nil {
			return sysConfig, fErr
		}
	}

	return sysConfig, nil
}

func InitConfig[T any](key string, value T, dataType string, org *Organization, locale *string) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}
	var orgID *string
	if org != nil {
		orgID = &org.ID
	}

	if f, _ := Repository.System.FindOne(&SystemConfigQuery{
		Key:    util.NewString(key),
		OrgID:  lo.If(orgID != nil, orgID).Else(nil),
		Locale: locale,
	}, nil); f == nil {

		log.Debug("string(jsonData): ", key, string(jsonData))
		sysConfig := &SystemConfig{Value: string(jsonData), Key: key, DataType: dataType}
		if dataType == String {
			if str, ok := any(value).(string); ok {
				sysConfig.Value = str
			}
		}
		if org != nil {
			sysConfig.OrgID = org.ID
			sysConfig.Domain = org.Domain
		}
		if locale != nil {
			sysConfig.Locale = *locale
		}
		if sErr := Repository.System.Upsert(sysConfig); sErr != nil {
			return sErr
		}
	}
	return nil
}

func GetConfig[T any](key string) T {
	var result T
	if sysConfig, err := Repository.System.FindByKey(key); err != nil {
		return result
	} else {
		// Handle different types based on T
		switch any(result).(type) {
		case string:
			// Return the config value directly as string
			return any(sysConfig.Value).(T)
		default:
			// Try to unmarshal into the expected type
			uErr := json.Unmarshal([]byte(sysConfig.Value), &result)
			if uErr != nil {
				return result
			}
			return result
		}
	}
}

func GetCurrencyInfo(currency Currency) (*CurrencyInfo, bool) {
	currencyInfos := GetConfig[[]*CurrencyInfo](CurrencyConfig)
	return lo.Find(currencyInfos, func(cInfo *CurrencyInfo) bool {
		return cInfo.Symbol == currency
	})
}

func UpdateAllowOrigin(value []string) error {
	oldValue := GetConfig[[]string](AllowOriginConfig)
	newValue := append(oldValue, value...)
	newValue = lo.Uniq(newValue)
	jsonData, err := json.Marshal(newValue)
	if err != nil {
		return err
	}
	if uErr := Repository.System.Upsert(&SystemConfig{Value: string(jsonData), Key: AllowOriginConfig}); uErr != nil {
		return uErr
	}
	if cErr := Cache.System.Flush(); cErr != nil {
		log.Errorf("SystemConfigRepository::UpdateAllowOrigin Flush all system config cache failed: %v", cErr)
	}
	return nil
}

func InitResourceUsage() ResourceUsageEmailConfig {
	return ResourceUsageEmailConfig{
		WhitelistEmail:        util.WhitelistEmailAIResourceUsage,
		WhitelistEmailPattern: util.WhitelistEmailPattern,
		BlacklistEmailPattern: util.BlacklistEmailPattern,
	}
}

func InitSubscibePlanForOrgConfig() SubscribeAIPlanInfo {
	return SubscribeAIPlanInfo{
		OrgSchema:   SchemaAIGov,
		PlanID:      "",
		Event:       "",
		EmailEnable: false,
	}
}

func (r *SystemConfigRepository) Create(c *SystemConfig, trans *gorm.DB) error {
	if cErr := Cache.System.Flush(); cErr != nil {
		log.Errorf("SystemConfigRepository::Create Flush all system config cache failed: %v", cErr)
	}

	if err := create(SystemConfigTbl, c, trans); err != nil {
		return err
	}

	if c.Files != nil && len(c.Files) > 0 {
		if err := Repository.FileRelation.AddFiles(SystemConfigModelName, c.ID, FilesField, c.Files); err != nil {
			return err
		}
	}
	return nil
}

func (r *SystemConfigRepository) CreateMany(c []*SystemConfig, trans *gorm.DB) error {
	return createMany(SystemConfigTbl, c, trans)
}

func (r *SystemConfigRepository) FindByID(id string, options *FindOneOptions) (*SystemConfig, error) {
	if options == nil {
		options = &FindOneOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	sysConfig, err := findByID[SystemConfig](SystemConfigTbl, id, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles([]*SystemConfig{sysConfig}); fErr != nil {
			return sysConfig, fErr
		}
	}

	return sysConfig, nil
}

func (r *SystemConfigRepository) FindMany(query *SystemConfigQuery, options *FindManyOptions) ([]*SystemConfig, error) {
	if options == nil {
		options = &FindManyOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	sysConfigs, err := findMany[SystemConfig](SystemConfigTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles(sysConfigs); fErr != nil {
			return sysConfigs, fErr
		}
	}

	return sysConfigs, err

}

func (r *SystemConfigRepository) FindPage(query *SystemConfigQuery, options *FindPageOptions) ([]*SystemConfig, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	sysConfigs, pagination, err := findPage[SystemConfig](SystemConfigTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles(sysConfigs); fErr != nil {
			return sysConfigs, pagination, fErr
		}
	}
	return sysConfigs, pagination, err
}

func (r *SystemConfigRepository) Update(c *SystemConfig, trans *gorm.DB) error {
	if cErr := Cache.System.Flush(); cErr != nil {
		log.Errorf("SystemConfigRepository::Update Flush all system config cache failed: %v", cErr)
	}

	if err := update(SystemConfigTbl, c, trans); err != nil {
		return err
	}

	if c.Files != nil && len(c.Files) > 0 {
		if err := Repository.FileRelation.AddFiles(SystemConfigModelName, c.ID, FilesField, c.Files); err != nil {
			return err
		}
	}
	return nil
}

func (r *SystemConfigRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[SystemConfig](SystemConfigTbl, id, trans)
}

func (r *SystemConfigRepository) DeleteMany(query *SystemConfigQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[SystemConfig](SystemConfigTbl, query, trans)
}
