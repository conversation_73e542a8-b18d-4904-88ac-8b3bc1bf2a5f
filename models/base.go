package models

import (
	"fmt"
	"reflect"
	"strings"
	"time"
	"unicode"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type QueryOptions interface {
	Apply(tx *gorm.DB) *gorm.DB
}

type EntityApproval interface {
	GetID() string
	GetUserID() string
}

type FindOneOptions struct {
	Joins          []string                           `json:"joins" form:"joins"`
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort" form:"sort"`
}

func (o *FindOneOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Joins, func(clause string, _ int) {
		qb = qb.Joins(clause)
	})

	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	return qb
}

func (o *FindOneOptions) ToCacheKey() string {
	key := ""
	if len(o.Joins) > 0 {
		key += "_" + strings.Join(o.Joins, "_")
	}
	if len(o.Preloads) > 0 {
		key += "_" + strings.Join(o.Preloads, "_")
	}
	if len(o.Sort) > 0 {
		key += "_" + strings.Join(o.Sort, "_")
	}
	if len(o.CustomPreloads) > 0 {
		key += fmt.Sprintf("_%v", o.CustomPreloads)
	}
	return key
}

type FindManyOptions struct {
	Joins          []string                           `json:"joins,omitempty" form:"joins"`
	Preloads       []string                           `json:"preloads,omitempty" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads,omitempty" form:"-"`
	Sort           []string                           `json:"sort,omitempty" form:"sort"`
	Limit          *int                               `json:"limit,omitempty" form:"limit"`
	Offset         *int                               `json:"offset,omitempty" form:"offset"`
}

func (o *FindManyOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Joins, func(clause string, _ int) {
		qb = qb.Joins(clause)
	})

	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	if o.Limit != nil {
		qb = qb.Limit(*o.Limit)
	}

	if o.Offset != nil {
		qb = qb.Offset(*o.Offset)
	}

	return qb
}

func (o *FindManyOptions) ToCacheKey() string {
	key := ""
	if len(o.Joins) > 0 {
		key += "_" + strings.Join(o.Joins, "_")
	}

	if len(o.Preloads) > 0 {
		key += "_" + strings.Join(o.Preloads, "_")
	}

	if len(o.Sort) > 0 {
		key += "_" + strings.Join(o.Sort, "_")
	}

	if len(o.CustomPreloads) > 0 {
		key += fmt.Sprintf("_%v", o.CustomPreloads)
	}

	if o.Limit != nil {
		key += fmt.Sprintf("_limit:%d", *o.Limit)
	}

	if o.Offset != nil {
		key += fmt.Sprintf("_offset:%d", *o.Offset)
	}

	return key
}

type FindPageOptions struct {
	Joins          []string                           `json:"joins" form:"joins"`
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort"  form:"sort"`
	Page           int                                `json:"page" form:"page"`
	PerPage        int                                `json:"per_page" form:"per_page"`
}

func (o *FindPageOptions) GetPage() int64 {
	return int64(o.Page)
}

func (o *FindPageOptions) GetPerPage() int64 {
	return int64(o.PerPage)
}

func (o *FindPageOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Joins, func(clause string, _ int) {
		qb = qb.Joins(clause)
	})

	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	limit := o.PerPage
	offset := (o.Page - 1) * o.PerPage
	qb = qb.Limit(limit).Offset(offset)
	return qb
}

func ApplyQueryOptions(tx *gorm.DB, queries ...QueryOptions) *gorm.DB {
	qb := tx
	for _, query := range queries {
		if !reflect.ValueOf(query).IsNil() {
			qb = query.Apply(qb)
		}
	}
	return qb
}

func createWithOmitFields[T any](tblName TableName, entity T, omitFields []string, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	err = tx.Table(GetTblName(tblName)).Debug().Omit(omitFields...).Create(&entity).Error
	return
}

func create[T any](tblName TableName, entity T, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	err = tx.Table(GetTblName(tblName)).Debug().Create(&entity).Error
	return
}

func createMany[T any](tbl TableName, entities []T, trans *gorm.DB) (err error) {
	if len(entities) == 0 {
		return nil
	}

	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(tbl)).Debug().Create(&entities).Error
	return
}

func createManyWithSchema[T any](schema string, tbl TableName, entities []T, trans *gorm.DB) (err error) {
	if len(entities) == 0 {
		return nil
	}

	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblNameWithSchema(schema, tbl)).Debug().Create(&entities).Error
	return
}

func findByID[T any](tbl TableName, id string, options *FindOneOptions) (*T, error) {
	var entity T
	qb := ApplyQueryOptions(DB, options)
	err := qb.Table(GetTblName(tbl)).Debug().Where("id = ? AND delete_at = 0", id).First(&entity).Error
	if err != nil {
		return nil, err
	}

	return &entity, nil
}

func findOne[T any](tbl TableName, query QueryOptions, options *FindOneOptions) (*T, error) {
	var entity T
	qb := ApplyQueryOptions(DB, query, options)
	err := qb.Table(GetTblName(tbl)).Debug().First(&entity).Error
	if err != nil {
		return nil, err
	}

	return &entity, nil
}

func findMany[T any](tbl TableName, query QueryOptions, options *FindManyOptions) ([]*T, error) {
	var entities []*T
	qb := ApplyQueryOptions(DB, query, options)
	err := qb.Table(GetTblName(tbl)).Debug().Find(&entities).Error
	if err != nil {
		return nil, err
	}

	return entities, nil
}

func findPageWithSchema[T any](schema string, tbl TableName, query QueryOptions, options *FindPageOptions) (entities []*T, pagination *Pagination, err error) {
	entitiesChan := make(chan []*T)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		qb := ApplyQueryOptions(DB, query, options)
		result := qb.Table(GetTblNameWithSchema(schema, tbl)).Debug().Find(&entities)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		c, cErr := count[T](tbl, query)
		if cErr != nil {
			errorChan <- cErr
			return
		}
		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

func findManyWithSchema[T any](schema string, tbl TableName, query QueryOptions, options *FindManyOptions) ([]*T, error) {
	if IsSharedTable(tbl) {
		return nil, fmt.Errorf("table %s is shared", tbl)
	}

	var entities []*T
	qb := ApplyQueryOptions(DB, query, options)
	err := qb.Table(GetTblNameWithSchema(schema, tbl)).Debug().Find(&entities).Error
	if err != nil {
		return nil, err
	}

	return entities, nil
}

func findManyWithSchemas[T any](schemas []string, tbl TableName, query QueryOptions, options *FindManyOptions) ([]*T, error) {
	if IsSharedTable(tbl) {
		return nil, fmt.Errorf("table %s is shared", tbl)
	}

	if len(schemas) == 0 {
		return []*T{}, nil
	}

	var entities []*T
	dbTables := lo.Map(schemas, func(schema string, _ int) interface{} {
		return ApplyQueryOptions(DB, query, options).Table(GetTblNameWithSchema(schema, tbl))
	})

	unionClause := strings.Join(lo.Map(schemas, func(schema string, _ int) string {
		return "?"
	}), " UNION ALL ")

	tableClause := fmt.Sprintf("(%s) as %s", unionClause, tbl)

	err := DB.Table(tableClause, dbTables...).Debug().Find(&entities).Error
	if err != nil {
		return nil, err
	}

	return entities, nil
}

func findPage[T any](tbl TableName, query QueryOptions, options *FindPageOptions) (entities []*T, pagination *Pagination, err error) {
	entitiesChan := make(chan []*T)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		qb := ApplyQueryOptions(DB, query, options)
		result := qb.Table(GetTblName(tbl)).Debug().Find(&entities)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		c, cErr := count[T](tbl, query)
		if cErr != nil {
			errorChan <- cErr
			return
		}
		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

func update[T any](tbl TableName, entity T, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(tbl)).Debug().Select("*").Omit("id", "create_at").Updates(&entity).Error
	return
}

func updateWithOmitFields[T any](tbl TableName, entity T, omitFields []string, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	omits := append(omitFields, "id", "create_at")
	err = tx.Table(GetTblName(tbl)).Debug().Select("*").Omit(omits...).Updates(&entity).Error
	return
}

func updateWithSchema[T any](schema string, tbl TableName, entity T, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblNameWithSchema(schema, tbl)).Debug().Select("*").Omit("id", "create_at").Updates(&entity).Error
	return

}

func getFields(t reflect.Type) ([]string, error) {
	var fields []string
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		if field.Anonymous && field.Type.Kind() == reflect.Struct {
			// Recursive in embeded struct
			embeddedFields, err := getFields(field.Type)
			if err != nil {
				return nil, err
			}
			fields = append(fields, embeddedFields...)
		} else if field.Name != "ID" && field.Name != "CreatedAt" {
			if field.Tag.Get("gorm") != "" {
				fields = append(fields, field.Name)
			}
		}
	}
	return fields, nil
}

func camelToSnake(s string) string {
	var result []rune
	runes := []rune(s)

	for i, r := range runes {
		if unicode.IsUpper(r) {
			if i > 0 && unicode.IsLower(runes[i-1]) {
				result = append(result, '_')
			}
			result = append(result, unicode.ToLower(r))
		} else {
			result = append(result, r)
		}
	}

	return string(result)
}

func updateMany[T any](tbl TableName, entities []T, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(tbl)).Debug().Select("*").Omit("id", "create_at").Updates(&entities).Error

	return
}

func deleteByID[T any](tbl TableName, id string, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	var entity T
	now := time.Now().UnixMilli()
	result := tx.Table(GetTblName(tbl)).Debug().
		Model(&entity).
		Where("id = ? AND delete_at = 0", id).
		Updates(map[string]interface{}{
			"delete_at": now,
			//"update_at": now,
		})

	err = result.Error
	if result.RowsAffected == 0 {
		err = lo.If(err == nil, gorm.ErrRecordNotFound).Else(err)
	}
	return
}

func deleteMany[T any](tbl TableName, query QueryOptions, trans *gorm.DB) (count int64, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	var entity T
	now := time.Now().UnixMilli()
	result := qb.Table(GetTblName(tbl)).Debug().
		Model(&entity).
		Updates(map[string]interface{}{
			"delete_at": now,
			//"update_at": now,
		})

	err = result.Error
	count = result.RowsAffected
	return
}

func hardDeleteMany[T any](tbl TableName, query QueryOptions, trans *gorm.DB) (count int64, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	var entity T
	result := qb.Table(GetTblName(tbl)).Debug().
		Delete(&entity)

	err = result.Error
	count = result.RowsAffected
	return
}

func count[T any](tbl TableName, query QueryOptions) (c int64, err error) {
	if r := recover(); r != nil {
		err = fmt.Errorf("panic: %v", r)
	}

	var entity T
	qb := ApplyQueryOptions(DB, query)
	result := qb.Table(GetTblName(tbl)).Debug().Model(&entity).Count(&c)
	if err = result.Error; err != nil {
		return -1, err
	}
	return c, nil
}

func countWithSchema[T any](schema string, tbl TableName, query QueryOptions) (c int64, err error) {
	if r := recover(); r != nil {
		err = fmt.Errorf("panic: %v", r)
	}

	var entity T
	qb := ApplyQueryOptions(DB, query)
	result := qb.Table(GetTblNameWithSchema(schema, tbl)).Model(&entity).Count(&c)
	if err = result.Error; err != nil {
		return -1, err
	}
	return c, nil
}

func increment[T any](tbl TableName, entityID string, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(tbl)).Debug().Where("id = ?", entityID).UpdateColumn(field, gorm.Expr("? + ?", gorm.Expr(field), amount)).Error

	return
}

func decrement[T any](tbl TableName, entityID string, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(tbl)).Debug().Where("id = ?", entityID).UpdateColumn(field, gorm.Expr("? - ?", gorm.Expr(field), amount)).Error

	return
}

func decrementMultiple[T any](tbl TableName, entityIDs []string, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not using external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	// Update the field for all users in the entityIDs list
	err = tx.Table(GetTblName(tbl)).Debug().Where("id IN ?", entityIDs).UpdateColumn(field, gorm.Expr("? - ?", gorm.Expr(field), amount)).Error

	return
}

func getAllFieldsWithSnakeCase(t reflect.Type, omitFields []string) ([]string, error) {
	var fields []string
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		if field.Anonymous && field.Type.Kind() == reflect.Struct {
			// Recursive in embeded struct
			embeddedFields, err := getFields(field.Type)
			if err != nil {
				return nil, err
			}
			fields = append(fields, embeddedFields...)
		} else {
			if !lo.Contains(omitFields, camelToSnake(field.Name)) {
				fields = append(fields, camelToSnake(field.Name))
			}
		}
	}
	return fields, nil
}
