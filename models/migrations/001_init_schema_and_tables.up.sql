create sequence if not exists openedu_orders_order_number_seq;

create table if not exists openedu_affiliate_campaigns
(
    id         varchar(20)       not null
    constraint uni_openedu_affiliate_campaigns_id
    primary key,
    create_at  bigint  default 0 not null,
    update_at  bigint  default 0 not null,
    delete_at  bigint  default 0 not null,
    user_id    varchar(20)       not null,
    org_id     varchar(20)       not null,
    name       text,
    start_date bigint  default 0,
    end_date   bigint  default 0,
    enable     boolean default true
    );

create table if not exists openedu_ai_blog_rewrites
(
    id               varchar(20)           not null
    constraint uni_openedu_ai_blog_rewrites_id
    primary key,
    create_at        bigint      default 0 not null,
    update_at        bigint      default 0 not null,
    delete_at        bigint      default 0 not null,
    link             text,
    old_content      text,
    blog_id          text,
    status           text,
    ai_payload       jsonb,
    title            text,
    new_content      text,
    cost             numeric,
    meta_data        text,
    thumbnail        text,
    org_id           text,
    author_id        text,
    schema           text,
    blog_type        text,
    error            jsonb,
    offer_id         text,
    current_step     varchar(50),
    blog_cuid        varchar(20),
    language         varchar(20) default 'English'::character varying,
    rewrite_offer_id text,
    tone             varchar(20) default 'normal'::character varying
    );

create table if not exists openedu_ai_courses
(
    id                       varchar(20)           not null
    constraint uni_openedu_ai_courses_id
    primary key,
    create_at                bigint      default 0 not null,
    update_at                bigint      default 0 not null,
    delete_at                bigint      default 0 not null,
    playlist_id              varchar(50),
    playlist_link            varchar(1000),
    offer_id                 varchar(50),
    course_id                varchar(20),
    course_cuid              varchar(20),
    status                   varchar(20) default 'pending'::character varying,
    total_cost               numeric(20, 2),
    language                 varchar(20) default 'English'::character varying,
    tone                     varchar(20) default 'normal'::character varying,
    org_id                   varchar(20),
    org_schema               varchar(20),
    thumbnail_id             varchar(20),
    user_id                  varchar(20),
    retry_count              smallint    default 0,
    offer_type               varchar(50),
    course_title             varchar(100),
    course_description       text,
    slug                     varchar(255),
    summary_included         boolean     default false,
    quiz_included            boolean     default false,
    quiz_type                varchar(50),
    quiz_status              varchar(20) default 'manual'::character varying,
    quiz_details             jsonb,
    number_of_question       smallint    default 0,
    error                    jsonb,
    current_step             varchar(50),
    general_info_status      varchar(20) default 'manual'::character varying,
    learner_info             text,
    content_info             text,
    material_id              varchar(20),
    level_id                 varchar(20),
    duration_type            varchar(20),
    duration                 smallint    default 0,
    study_load               smallint    default 0,
    thumbnail_generate_count smallint    default 0,
    thumbnail_status         varchar(20),
    thumbnail_error          jsonb,
    thumbnail_description    text,
    thumbnail_style          varchar(50) default 'general'::character varying,
    thumbnail_quantity       smallint    default 0,
    generated_thumbnail_ids  jsonb
    );

create table if not exists openedu_ai_histories
(
    id            varchar(20)           not null
    constraint uni_openedu_ai_histories_id
    primary key,
    create_at     bigint      default 0 not null,
    update_at     bigint      default 0 not null,
    delete_at     bigint      default 0 not null,
    request_ids   jsonb,
    request_type  varchar(50),
    user_id       varchar(20),
    entity_id     varchar(20),
    entity_type   varchar(50),
    generate_id   varchar(20),
    generate_type varchar(50),
    status        varchar(20) default 'pending'::character varying,
    cost          numeric(20, 2),
    request       jsonb,
    response      jsonb,
    start_date    bigint      default 0,
    end_date      bigint      default 0,
    run_duration  smallint    default 0,
    wait_duration smallint    default 0,
    error         jsonb,
    org_id        varchar(20),
    org_schema    varchar(50),
    step          varchar(50),
    ai_offer_id   varchar(50)
    );

create table if not exists openedu_ai_models
(
    id            varchar(20)      not null
    constraint uni_openedu_ai_models_id
    primary key,
    create_at     bigint default 0 not null,
    update_at     bigint default 0 not null,
    delete_at     bigint default 0 not null,
    name          text,
    description   text,
    enabled       boolean,
    configs       jsonb,
    display_name  text,
    thumbnail_url text,
    org_id        text,
    org_schema    text,
    "order"       smallint
    );

create table if not exists openedu_ai_prompts
(
    id            varchar(20)      not null
    constraint uni_openedu_ai_prompts_id
    primary key,
    create_at     bigint default 0 not null,
    update_at     bigint default 0 not null,
    delete_at     bigint default 0 not null,
    user_id       varchar(20),
    text          text,
    enable        boolean,
    ai_agent_type text,
    category_id   text,
    "order"       bigint default 0
    );

create table if not exists openedu_bookmarks
(
    id          varchar(20)      not null
    constraint uni_openedu_bookmarks_id
    primary key,
    create_at   bigint default 0 not null,
    update_at   bigint default 0 not null,
    delete_at   bigint default 0 not null,
    name        varchar(255),
    entity_id   varchar(20),
    entity_type varchar(255),
    user_id     varchar(20)      not null,
    parent_id   varchar(20),
    link        varchar(255)
    );

create table if not exists openedu_categories
(
    id        varchar(20)       not null
    constraint uni_openedu_categories_id
    primary key,
    create_at bigint  default 0 not null,
    update_at bigint  default 0 not null,
    delete_at bigint  default 0 not null,
    name      varchar(50)       not null,
    active    boolean default true,
    parent_id varchar(20),
    "order"   bigint  default 1,
    type      varchar(20),
    org_id    varchar(20),
    use_count bigint  default 0,
    formatted varchar(50)
    );

create index if not exists idx_openedu_categories_parent_id
    on openedu_categories (parent_id);

create table if not exists openedu_category_relations
(
    id           varchar(20)      not null
    constraint uni_openedu_category_relations_id
    primary key,
    create_at    bigint default 0 not null,
    update_at    bigint default 0 not null,
    delete_at    bigint default 0 not null,
    category_id  varchar(20)      not null
    constraint fk_openedu_category_relations_category
    references openedu_categories,
    related_id   varchar(20)      not null,
    related_type varchar(20)      not null,
    field        varchar(20),
    "order"      bigint default 0 not null
    );

create index if not exists idx_openedu_category_relations_category_id
    on openedu_category_relations (category_id);

create unique index if not exists idx_openedu_category_relations_category_id_related_id_field
    on openedu_category_relations (category_id, related_id, field);

create index if not exists idx_openedu_category_relations_composite
    on openedu_category_relations (related_type, field, related_id, delete_at);

create index if not exists idx_openedu_category_relations_covering
    on openedu_category_relations (related_type, field, related_id, delete_at, "order", category_id) include (id, create_at, update_at);

create table if not exists openedu_certificates
(
    id           varchar(20)      not null
    constraint uni_openedu_certificates_id
    primary key,
    create_at    bigint default 0 not null,
    update_at    bigint default 0 not null,
    delete_at    bigint default 0 not null,
    user_id      varchar(20)      not null,
    course_id    text,
    org_id       text             not null,
    course_name  text,
    completed_at bigint,
    org_schema   text,
    course_cuid  text,
    nft_tx_hash  text,
    nft_network  text,
    nft_token_id text,
    props        jsonb
    );

create table if not exists openedu_clp_course_launchpads
(
    id               varchar(20)      not null
    constraint uni_openedu_clp_course_launchpads_id
    primary key,
    create_at        bigint default 0 not null,
    update_at        bigint default 0 not null,
    delete_at        bigint default 0 not null,
    clp_launchpad_id text             not null,
    course_cuid      text             not null,
    course_id        text,
    enable           boolean
    );

create table if not exists openedu_clp_investments
(
    id               varchar(20)                         not null
    constraint uni_openedu_clp_investments_id
    primary key,
    create_at        bigint         default 0            not null,
    update_at        bigint         default 0            not null,
    delete_at        bigint         default 0            not null,
    user_id          text,
    clp_launchpad_id text,
    amount           numeric(19, 4) default '0'::numeric not null,
    revenue_amount   numeric(19, 4) default '0'::numeric not null,
    refunded_amount  numeric(19, 4) default '0'::numeric not null,
    currency         text,
    status           text
    );

create table if not exists openedu_clp_launchpads
(
    id                    varchar(20)       not null
    constraint uni_openedu_clp_launchpads_id
    primary key,
    create_at             bigint  default 0 not null,
    update_at             bigint  default 0 not null,
    delete_at             bigint  default 0 not null,
    org_id                text,
    org_schema            text,
    name                  text,
    description           text,
    slug                  text,
    learner_outcome       text,
    preview_video_id      text,
    thumbnail_id          text,
    status                text    default 'draft'::text,
    props                 jsonb,
    enable                boolean default true,
    voting_start_date     bigint,
    voting_end_date       bigint,
    estimate_funding_days bigint,
    funding_start_date    bigint,
    funding_end_date      bigint,
    user_id               text              not null,
    funding_goal          jsonb,
    total_refunded        text    default '0'::text
    );

create table if not exists openedu_clp_voting_launchpads
(
    id                      varchar(20)      not null
    constraint uni_openedu_clp_voting_launchpads_id
    primary key,
    create_at               bigint default 0 not null,
    update_at               bigint default 0 not null,
    delete_at               bigint default 0 not null,
    clp_investment_id       text,
    clp_launchpad_id        text,
    clp_voting_milestone_id text,
    status                  text,
    note                    text
    );

create table if not exists openedu_clp_voting_milestones
(
    id                       varchar(20)      not null
    constraint uni_openedu_clp_voting_milestones_id
    primary key,
    create_at                bigint default 0 not null,
    update_at                bigint default 0 not null,
    delete_at                bigint default 0 not null,
    clp_launchpad_id         text,
    title                    text,
    estimated_open_vote_date bigint,
    estimated_end_vote_date  bigint,
    open_vote_date           bigint,
    end_vote_date            bigint,
    "order"                  bigint,
    target_section           bigint,
    status                   text,
    props                    jsonb
    );

create table if not exists openedu_clp_voting_phases
(
    id                      varchar(20)      not null
    constraint uni_openedu_clp_voting_phases_id
    primary key,
    create_at               bigint default 0 not null,
    update_at               bigint default 0 not null,
    delete_at               bigint default 0 not null,
    clp_launchpad_id        text,
    clp_voting_milestone_id text,
    status                  text,
    amount                  text,
    total_approve           bigint,
    total_reject            bigint
    );

create table if not exists openedu_commissions
(
    id             varchar(20)       not null
    constraint uni_openedu_commissions_id
    primary key,
    create_at      bigint  default 0 not null,
    update_at      bigint  default 0 not null,
    delete_at      bigint  default 0 not null,
    user_id        varchar(20)       not null,
    org_id         varchar(20)       not null,
    campaign_id    varchar(20)       not null,
    ref1_rate      real    default 0,
    qty1           bigint  default 0,
    ref2_rate      real    default 0,
    qty2           bigint  default 0,
    ref3_rate      real    default 0,
    qty3           bigint  default 0,
    parent_id      varchar(20),
    referrer_types jsonb,
    referrer_ids   jsonb,
    enable         boolean default true,
    type           text,
    is_base_rate   boolean default false,
    "order"        bigint  default 1
    );

create table if not exists openedu_coupons
(
    id                            varchar(20)                         not null
    constraint uni_openedu_coupons_id
    primary key,
    create_at                     bigint         default 0            not null,
    update_at                     bigint         default 0            not null,
    delete_at                     bigint         default 0            not null,
    org_id                        varchar(20),
    created_by                    varchar(20),
    name                          text,
    coupon_code                   text,
    type                          varchar(100),
    method                        varchar(100),
    scope                         varchar(50),
    start_date                    bigint,
    end_date                      bigint,
    discount_amount               numeric(19, 4) default '0'::numeric not null,
    discount_percentage           numeric(19, 4) default '0'::numeric not null,
    allow_maximum_discount        numeric(19, 4) default '0'::numeric not null,
    maximum_total_usage           smallint,
    min_amount_to_use             numeric(19, 4) default '0'::numeric not null,
    description                   text,
    allow_courses                 jsonb,
    allow_teams                   jsonb,
    is_active                     boolean        default false,
    total_used                    smallint       default 0,
    fiat_discount_enabled         boolean        default false,
    fiat_discount_percentage      numeric(19, 4) default '0'::numeric not null,
    fiat_min_amount_to_use        numeric(19, 4) default '0'::numeric not null,
    fiat_allow_maximum_discount   numeric(19, 4) default '0'::numeric not null,
    crypto_discount_enabled       boolean        default false,
    crypto_discount_percentage    numeric(19, 4) default '0'::numeric not null,
    crypto_min_amount_to_use      numeric(19, 4) default '0'::numeric not null,
    crypto_allow_maximum_discount numeric(19, 4) default '0'::numeric not null,
    fiat_discount_amount          numeric(19, 4) default '0'::numeric not null,
    crypto_discount_amount        numeric(19, 4) default '0'::numeric not null
    );

create table if not exists openedu_coupon_histories
(
    id                      varchar(20)                         not null
    constraint uni_openedu_coupon_histories_id
    primary key,
    create_at               bigint         default 0            not null,
    update_at               bigint         default 0            not null,
    delete_at               bigint         default 0            not null,
    user_id                 varchar(20),
    org_id                  varchar(20),
    coupon_id               varchar(20)
    constraint fk_openedu_coupon_histories_coupon
    references openedu_coupons,
    discount_amount         numeric(19, 4) default '0'::numeric not null,
    discount_percent        numeric(19, 4) default '0'::numeric not null,
    status                  text,
    order_id                varchar(20),
    crypto_discount_amount  numeric(19, 4) default '0'::numeric not null,
    crypto_discount_percent numeric(19, 4) default '0'::numeric not null
    );

create table if not exists openedu_course_campaigns
(
    id          varchar(20)       not null
    constraint uni_openedu_course_campaigns_id
    primary key,
    create_at   bigint  default 0 not null,
    update_at   bigint  default 0 not null,
    delete_at   bigint  default 0 not null,
    user_id     varchar(20)       not null,
    org_id      varchar(20)       not null,
    org_schema  varchar(50)       not null,
    course_cuid varchar(20)       not null,
    campaign_id varchar(20)       not null
    constraint fk_openedu_course_campaigns_campaign
    references openedu_affiliate_campaigns,
    enable      boolean default true,
    start_date  bigint  default 0,
    end_date    bigint  default 0,
    course_name text,
    course_slug text
    );

create table if not exists openedu_course_enrollments
(
    id             varchar(20)       not null
    constraint uni_openedu_course_enrollments_id
    primary key,
    create_at      bigint  default 0 not null,
    update_at      bigint  default 0 not null,
    delete_at      bigint  default 0 not null,
    course_cuid    varchar(20),
    user_id        varchar(20),
    org_id         varchar(20),
    blocked        boolean default false,
    blocked_reason text
    );

create index if not exists idx_openedu_course_enrollments_composite
    on openedu_course_enrollments (course_cuid, blocked, delete_at, user_id);

create unique index if not exists uniq_idx_openedu_course_enrollments_user_id_course_cuid
    on openedu_course_enrollments (user_id, course_cuid);

create table if not exists openedu_course_partners
(
    org_id      text,
    course_id   text              not null,
    partner_id  text              not null,
    roles       jsonb,
    is_active   boolean default true,
    permissions jsonb,
    id          varchar(20)       not null
    constraint uni_openedu_course_partners_id
    primary key,
    create_at   bigint  default 0 not null,
    update_at   bigint  default 0 not null,
    delete_at   bigint  default 0 not null
    );

create table if not exists openedu_course_prices
(
    course_id              varchar(20)                         not null
    primary key,
    is_pay                 boolean        default false,
    fiat_currency          text,
    fiat_price             numeric(19, 4) default '0'::numeric not null,
    fiat_discount_price    numeric(19, 4) default '0'::numeric not null,
    crypto_payment_enabled boolean,
    crypto_currency        text,
    crypto_price           numeric(19, 4) default '0'::numeric not null,
    crypto_discount_price  numeric(19, 4) default '0'::numeric not null
    );

create index if not exists idx_openedu_course_prices_course_id
    on openedu_course_prices (course_id);

create table if not exists openedu_course_revenue_points
(
    id          varchar(20)      not null
    constraint uni_openedu_course_revenue_points_id
    primary key,
    create_at   bigint default 0 not null,
    update_at   bigint default 0 not null,
    delete_at   bigint default 0 not null,
    course_cuid varchar(20)      not null,
    timestamp   varchar(20)      not null,
    revenue     jsonb,
    period      varchar(20)      not null
    );

create table if not exists openedu_courses
(
    org_id               varchar(20)       not null,
    cuid                 varchar(20)       not null,
    version              bigint  default 0,
    latest               boolean default true,
    name                 text              not null,
    slug                 text,
    description          text,
    thumbnail_id         text,
    learn_method         text,
    user_id              text              not null,
    status               text    default 'draft'::text,
    props                jsonb,
    pub_date             bigint  default 0,
    pub_reject_date      bigint  default 0,
    pub_root_date        bigint  default 0,
    pub_root_reject_date bigint  default 0,
    enable               boolean default true,
    start_date           bigint  default 0,
    end_date             bigint  default 0,
    mark_as_completed    boolean default false,
    course_id_v2         varchar(20),
    is_ai_generated      boolean default false,
    ai_generate_status   text    default 'manual'::text,
    ai_course_id         varchar(20),
    id                   varchar(20)       not null
    constraint uni_openedu_courses_id
    primary key,
    create_at            bigint  default 0 not null,
    update_at            bigint  default 0 not null,
    delete_at            bigint  default 0 not null,
    section_count        bigint  default 0,
    lesson_count         bigint  default 0,
    active_lesson        bigint  default 0,
    active_section       bigint  default 0,
    video_count          bigint  default 0,
    quiz_count           bigint  default 0,
    accesses             text,
    has_certificate      boolean default false
    );

create table if not exists openedu_featured_contents
(
    id          varchar(20)       not null
    constraint uni_openedu_featured_contents_id
    primary key,
    create_at   bigint  default 0 not null,
    update_at   bigint  default 0 not null,
    delete_at   bigint  default 0 not null,
    org_id      text,
    entity_id   text,
    entity_type text,
    enabled     boolean default true,
    "order"     bigint  default 0,
    type        text
    );

create table if not exists openedu_files
(
    name             text,
    mime             text,
    ext              text,
    url              text,
    thumbnail_url    text,
    id               varchar(20)      not null
    constraint uni_openedu_files_id
    primary key,
    create_at        bigint default 0 not null,
    update_at        bigint default 0 not null,
    delete_at        bigint default 0 not null,
    width            bigint,
    height           bigint,
    size             bigint,
    bunny_video_id   text,
    bunny_library_id text,
    duration         bigint,
    lesson_id_v2     varchar(100),
    course_id        varchar(100),
    user_id          text   default ''::text
    );

create table if not exists openedu_file_relations
(
    file_id      varchar(20) not null
    constraint fk_openedu_file_relations_file
    references openedu_files,
    related_id   text        not null,
    related_type text        not null,
    field        text        not null,
    "order"      bigint,
    primary key (file_id, related_id, related_type, field)
    );

create table if not exists openedu_form_answer_stats
(
    form_id          varchar(20)      not null,
    form_uid         varchar(20)      not null,
    file_ids         text             not null,
    text             text             not null,
    count            bigint,
    create_at        bigint default 0 not null,
    update_at        bigint default 0 not null,
    question_uid     varchar(20)      not null,
    sub_question_uid varchar(20)      not null,
    option_uid       varchar(20)      not null,
    question_id      varchar(20),
    sub_question_id  varchar(20),
    option_id        varchar(20),
    constraint openedu_form_answer_stats_pk
    primary key (form_id, form_uid, question_uid, sub_question_uid, option_uid, file_ids, text)
    );

create table if not exists openedu_forms
(
    id            varchar(20)       not null
    constraint uni_openedu_forms_id
    primary key,
    create_at     bigint  default 0 not null,
    update_at     bigint  default 0 not null,
    delete_at     bigint  default 0 not null,
    uid           text,
    latest        boolean default true,
    title         text,
    description   text,
    slug          text,
    event         varchar(100),
    type          varchar(100),
    status        varchar(100),
    start_date    bigint,
    end_date      bigint,
    org_id        varchar(20),
    creator_id    varchar(20),
    auth_required boolean,
    is_template   boolean default false
    );

create table if not exists openedu_form_questions
(
    id              varchar(20)      not null
    constraint uni_openedu_form_questions_id
    primary key,
    create_at       bigint default 0 not null,
    update_at       bigint default 0 not null,
    delete_at       bigint default 0 not null,
    parent_id       varchar(20)
    constraint fk_openedu_form_questions_sub_questions
    references openedu_form_questions,
    form_id         varchar(20)
    constraint fk_openedu_forms_questions
    references openedu_forms,
    title           text,
    description     text,
    question_type   varchar(100),
    "order"         bigint,
    total_responses bigint,
    settings        jsonb,
    uid             varchar(20)
    );

create table if not exists openedu_form_question_options
(
    id          varchar(20) not null
    constraint uni_openedu_form_question_options_id
    primary key,
    question_id varchar(20)
    constraint fk_openedu_form_questions_options
    references openedu_form_questions,
    text        text,
    "order"     bigint,
    delete_at   bigint,
    uid         varchar(20)
    );

create table if not exists openedu_form_relations
(
    id                    varchar(20)      not null
    constraint uni_openedu_form_relations_id
    primary key,
    create_at             bigint default 0 not null,
    update_at             bigint default 0 not null,
    delete_at             bigint default 0 not null,
    form_id               varchar(20)      not null
    constraint fk_openedu_form_relations_form
    references openedu_forms,
    related_entity_id     varchar(20)      not null,
    related_entity_uid    varchar(20),
    related_entity_type   text,
    org_id                varchar(20),
    org_schema            text,
    start_when            jsonb,
    end_when              jsonb,
    confirmation_settings jsonb,
    enabled               boolean,
    type                  text   default 'form'::text,
    name                  text,
    add_date              bigint default 0 not null,
    form_uid              varchar(20)
    );

create table if not exists openedu_hashtags
(
    hash              varchar(50) not null
    constraint uni_openedu_hashtags_hash
    primary key,
    name              text,
    create_at         bigint      not null,
    update_at         bigint      not null,
    delete_at         bigint      not null,
    use_count         bigint default 0,
    formatted_hashtag text,
    org_id            text
    );

create table if not exists openedu_hashtag_relations
(
    id           varchar(20)      not null
    constraint uni_openedu_hashtag_relations_id
    primary key,
    create_at    bigint default 0 not null,
    update_at    bigint default 0 not null,
    delete_at    bigint default 0 not null,
    hashtag_id   varchar(50)
    constraint fk_openedu_hashtag_relations_hashtag
    references openedu_hashtags,
    related_id   text             not null,
    related_type text
    );

create unique index if not exists idx_openedu_hashtags_name_org_id
    on openedu_hashtags (name, org_id);

create table if not exists openedu_html_templates
(
    id             varchar(20)           not null
    constraint uni_openedu_html_templates_id
    primary key,
    create_at      bigint  default 0     not null,
    update_at      bigint  default 0     not null,
    delete_at      bigint  default 0     not null,
    name           varchar(50),
    type           text                  not null,
    object         jsonb,
    html           text,
    user_id        varchar(20),
    course_id      varchar(20),
    course_name    text,
    creater_name   text,
    enable         boolean default false not null,
    org_id         varchar(50),
    creator_name   text,
    props          jsonb,
    root_layer     jsonb,
    org_layer      jsonb,
    course_cuid    varchar(20),
    template       jsonb,
    organizations  jsonb,
    signatures     jsonb,
    enable_project boolean default false
    );

create table if not exists openedu_learning_progresses
(
    id                       varchar(20)           not null
    constraint uni_openedu_learning_progresses_id
    primary key,
    create_at                bigint      default 0 not null,
    update_at                bigint      default 0 not null,
    delete_at                bigint      default 0 not null,
    user_id                  varchar(20)           not null,
    course_cuid              varchar(20)           not null,
    section_uid              varchar(20)           not null,
    lesson_uid               varchar(20)           not null,
    lesson_content_uid       varchar(20),
    session_id               varchar(128),
    quizzes                  jsonb,
    org_id                   varchar(20)           not null,
    content_type             varchar(20),
    complete_at              bigint      default 0,
    pause_at                 bigint      default 0,
    start_at                 bigint      default 0,
    org_schema               text,
    event                    varchar(30) default 'lesson_content_progress'::character varying,
    learning_status_migrated boolean     default false
    );

create index if not exists idx_learning_progresses_user_event
    on openedu_learning_progresses (user_id, event, complete_at, lesson_content_uid, course_cuid);

create unique index if not exists idx_openedu_learning_progresses_session_id
    on openedu_learning_progresses (session_id);

create unique index if not exists uniq_idx_openedu_learning_progresses_user_id_course_cuid_lesson
    on openedu_learning_progresses (user_id, course_cuid, lesson_uid, event)
    where ((event)::text = 'latest_lesson_progress'::text);

create table if not exists openedu_lesson_contents
(
    org_id       text,
    uid          text,
    course_id    text,
    user_id      text,
    section_id   text,
    lesson_id    text,
    title        text,
    content      text,
    json_content jsonb,
    "order"      bigint,
    type         text,
    duration     bigint,
    id           varchar(20)      not null
    constraint uni_openedu_lesson_contents_id
    primary key,
    create_at    bigint default 0 not null,
    update_at    bigint default 0 not null,
    delete_at    bigint default 0 not null,
    lesson_id_v2 text
    );

create index if not exists idx_lesson_contents_lesson
    on openedu_lesson_contents (lesson_id, uid);

create table if not exists openedu_oe_point_campaigns
(
    id                varchar(20)       not null
    constraint uni_openedu_oe_point_campaigns_id
    primary key,
    create_at         bigint  default 0 not null,
    update_at         bigint  default 0 not null,
    delete_at         bigint  default 0 not null,
    name              text,
    point             text,
    currency          text,
    rate              text,
    enable            boolean default true,
    expiration_period text    default '0'::text,
    expiration_val    bigint  default 0,
    grace_period      text    default '0'::text,
    grace_val         bigint  default 0,
    program           text,
    scope             text,
    enabled           boolean,
    entities          jsonb,
    start_date        bigint  default 0 not null,
    end_date          bigint  default 0 not null,
    total_reward      bigint  default 0,
    setting           jsonb
    );

create table if not exists openedu_oe_referral_leader_boards
(
    id                     varchar(20)       not null
    constraint uni_openedu_oe_referral_leader_boards_id
    primary key,
    create_at              bigint  default 0 not null,
    update_at              bigint  default 0 not null,
    delete_at              bigint  default 0 not null,
    org_id                 varchar(20),
    user_id                varchar(20),
    parent_id              varchar(20),
    parent_name            text,
    local_level            bigint,
    display_name           text,
    email                  text,
    ref_code               text,
    ref_code_id            text,
    register_count         bigint  default 0,
    ref_count              bigint  default 0,
    cert_count             bigint  default 0,
    completed_course_count bigint  default 0,
    percent_cert_on_ref    numeric default 0,
    percent_cert_on_reg    numeric default 0,
    course_cuid            text,
    campaign_key           text
    );

create table if not exists openedu_oe_referral_reports
(
    id                          varchar(20)      not null
    constraint uni_openedu_oe_referral_reports_id
    primary key,
    create_at                   bigint default 0 not null,
    update_at                   bigint default 0 not null,
    delete_at                   bigint default 0 not null,
    org_id                      varchar(20)      not null,
    campaign_key                text,
    local_level                 bigint,
    province                    text,
    local_unit                  text,
    user_id                     text,
    user_props                  jsonb,
    course_c_uid                text,
    fill_form_date              bigint default 0,
    register_date               bigint default 0,
    enroll_date                 bigint default 0,
    complete_date               bigint default 0,
    claim_cert_date             bigint default 0,
    number_of_completed_section bigint default 0,
    course_cuid                 text,
    ref_by                      text   default ''::text
    );

create unique index if not exists uniq_idx_openedu_oe_referral_reports_user_id_course_cuid
    on openedu_oe_referral_reports (org_id, campaign_key, user_id, course_cuid);

create table if not exists openedu_orders
(
    id                       varchar(20)                         not null
    constraint uni_openedu_orders_id
    primary key,
    create_at                bigint         default 0            not null,
    update_at                bigint         default 0            not null,
    delete_at                bigint         default 0            not null,
    user_id                  varchar(20),
    amount                   numeric(19, 4) default '0'::numeric not null,
    actual_amount            numeric(19, 4) default '0'::numeric not null,
    paid                     numeric(19, 4) default '0'::numeric not null,
    missing_amount           numeric(19, 4) default '0'::numeric not null,
    discount_amount          numeric(19, 4) default '0'::numeric not null,
    code                     text,
    status                   text,
    currency                 text           default 'VND'::text,
    payment_method_id        varchar(20),
    order_number             bigint         default nextval('openedu_orders_order_number_seq'::regclass),
    referral_code            text,
    referral_discount_amount numeric(19, 4) default '0'::numeric not null,
    org_id                   varchar(20)                         not null,
    pay_from_org_id          varchar(20)                         not null,
    payment_id_v2            varchar(100),
    purchase_at              bigint         default 0            not null,
    props                    jsonb
    );

alter sequence openedu_orders_order_number_seq owned by openedu_orders.order_number;

create table if not exists openedu_order_items
(
    id              varchar(20)                         not null
    constraint uni_openedu_order_items_id
    primary key,
    create_at       bigint         default 0            not null,
    update_at       bigint         default 0            not null,
    delete_at       bigint         default 0            not null,
    order_id        varchar(20)                         not null
    constraint fk_openedu_order_items_order
    references openedu_orders,
    course_cuid     varchar(20),
    course_id       varchar(20),
    pay_from_org_id varchar(20)                         not null,
    org_id          varchar(20)                         not null,
    org_schema      text                                not null,
    user_id         varchar(20)                         not null,
    amount          numeric(19, 4) default '0'::numeric not null,
    discount_amount numeric(19, 4) default '0'::numeric not null,
    actual_amount   numeric(19, 4) default '0'::numeric not null,
    status          text,
    payment_id_v2   varchar(100),
    currency        text           default 'VND'::text,
    entity_id       varchar(20),
    entity_cuid     varchar(20),
    entity_type     varchar(255)
    );

create unique index if not exists idx_openedu_order_items_payment_id_v2
    on openedu_order_items (payment_id_v2);

create unique index if not exists idx_openedu_orders_payment_id_v2
    on openedu_orders (payment_id_v2);

create table if not exists openedu_page_accesses
(
    id     varchar(50)          not null
    constraint uni_openedu_page_accesses_id
    primary key,
    role   text                 not null,
    entity text                 not null,
    action text                 not null,
    allow  boolean default true not null,
    org_id text                 not null
    );

create table if not exists openedu_page_configs
(
    id          varchar(100) not null
    constraint uni_openedu_page_configs_id
    primary key,
    name        varchar(50)  not null,
    type        text         not null,
    actions     jsonb,
    description text
    );

create table if not exists openedu_payment_methods
(
    id                   varchar(20)      not null
    constraint uni_openedu_payment_methods_id
    primary key,
    create_at            bigint default 0 not null,
    update_at            bigint default 0 not null,
    delete_at            bigint default 0 not null,
    user_id              varchar(20),
    course_id            varchar(20),
    organize_id          varchar(20),
    service              text,
    account              text,
    account_number       text,
    account_name         text,
    network              text,
    payment_type         text,
    enable               boolean,
    payment_method_id_v2 varchar(100),
    type                 text
    );

create unique index if not exists idx_openedu_payment_methods_payment_id_v2
    on openedu_payment_methods (payment_method_id_v2);

create table if not exists openedu_payments
(
    id                varchar(20)                         not null
    constraint uni_openedu_payments_id
    primary key,
    create_at         bigint         default 0            not null,
    update_at         bigint         default 0            not null,
    delete_at         bigint         default 0            not null,
    user_id           varchar(20)                         not null,
    order_id          varchar(20)                         not null,
    payment_method_id varchar(20),
    payment_info      jsonb,
    amount            numeric(19, 4) default '0'::numeric not null,
    status            text           default 'pending'::text,
    invoice_id        text,
    payload           jsonb,
    org_id            varchar(20)                         not null,
    pay_from_org_id   varchar(20)                         not null,
    payment_id_v2     varchar(100),
    currency          text           default 'VND'::text
    );

create unique index if not exists idx_openedu_payments_payment_id_v2
    on openedu_payments (payment_id_v2);

create table if not exists openedu_publish_courses
(
    org_id            varchar(20)                         not null,
    org_schema        text,
    org_domain        text,
    user_id           text,
    course_cuid       varchar(20)                         not null,
    course_slug       text,
    name              text,
    is_pay            boolean        default false,
    price             numeric(19, 4) default '0'::numeric not null,
    description       text,
    thumbnail_id      text,
    course_id         varchar(20)                         not null,
    pub_date          bigint         default 0,
    is_root           boolean        default false,
    enable            boolean        default true,
    start_date        bigint         default 0,
    end_date          bigint         default 0,
    props             jsonb,
    id                varchar(20)                         not null
    constraint uni_openedu_publish_courses_id
    primary key,
    create_at         bigint         default 0            not null,
    update_at         bigint         default 0            not null,
    delete_at         bigint         default 0            not null,
    pub_root_date     bigint         default 0,
    enable_root       boolean        default false,
    version           bigint         default 1,
    mark_as_completed boolean        default false,
    crypto_price      numeric(19, 4) default '0'::numeric not null,
    scope             text
    );

create table if not exists openedu_quiz_relations
(
    quiz_id             text,
    related_entity_type text,
    related_entity_id   text,
    relation_type       text,
    trigger_conditions  jsonb
);

create table if not exists openedu_quiz_submissions
(
    id                                varchar(20)      not null
    constraint uni_openedu_quiz_submissions_id
    primary key,
    create_at                         bigint default 0 not null,
    update_at                         bigint default 0 not null,
    delete_at                         bigint default 0 not null,
    quiz_uid                          text,
    quiz_id                           varchar(20)      not null,
    user_id                           varchar(20),
    course_cuid                       varchar(20),
    status                            text,
    passed                            boolean,
    start_at                          bigint,
    end_at                            bigint,
    deadline_at                       bigint,
    time_to_complete_in_milli_seconds bigint,
    archived_points                   bigint,
    highest_points_on_single_question bigint,
    highest_streak                    bigint
    );

create table if not exists openedu_quizzes
(
    id          varchar(20)      not null
    constraint uni_openedu_quizzes_id
    primary key,
    create_at   bigint default 0 not null,
    update_at   bigint default 0 not null,
    delete_at   bigint default 0 not null,
    org_id      text,
    uid         text,
    version     bigint,
    title       varchar(255),
    description text,
    creator_id  varchar(20),
    settings    jsonb
    );

create table if not exists openedu_quiz_questions
(
    id                varchar(20)      not null
    constraint uni_openedu_quiz_questions_id
    primary key,
    create_at         bigint default 0 not null,
    update_at         bigint default 0 not null,
    delete_at         bigint default 0 not null,
    quiz_id           varchar(20)
    constraint fk_openedu_quizzes_questions
    references openedu_quizzes,
    title             text,
    description       text,
    text              text,
    type              text,
    items             jsonb,
    correct_item_sets jsonb,
    explanation       text,
    points            bigint,
    "order"           bigint,
    settings          jsonb
    );

create table if not exists openedu_quiz_answers
(
    id                 varchar(20)      not null
    constraint uni_openedu_quiz_answers_id
    primary key,
    create_at          bigint default 0 not null,
    update_at          bigint default 0 not null,
    delete_at          bigint default 0 not null,
    submission_id      varchar(20)      not null
    constraint fk_openedu_quiz_submissions_answers
    references openedu_quiz_submissions,
    quiz_id            varchar(20)      not null,
    user_id            varchar(20),
    question_id        varchar(20)      not null
    constraint fk_openedu_quiz_answers_question
    references openedu_quiz_questions,
    "order"            bigint,
    answered_item_sets jsonb,
    start_at           bigint,
    end_at             bigint,
    correct            boolean,
    archived_points    bigint
    );

create table if not exists openedu_referral_links
(
    id            varchar(20)                not null
    constraint uni_openedu_referral_links_id
    primary key,
    create_at     bigint           default 0 not null,
    update_at     bigint           default 0 not null,
    delete_at     bigint           default 0 not null,
    user_id       varchar(20)                not null,
    org_id        varchar(20)                not null,
    campaign_id   varchar(20)                not null
    constraint fk_openedu_referral_links_campaign
    references openedu_affiliate_campaigns,
    commission_id varchar(20)                not null
    constraint fk_openedu_referral_links_commission
    references openedu_commissions,
    ref_code      varchar(20)                not null,
    ref_level1    varchar(20),
    ref_level2    varchar(20),
    share_rate    double precision default 0,
    enable        boolean          default true,
    is_extend     boolean          default false
    );

create table if not exists openedu_referrers
(
    id                      varchar(20)       not null
    constraint uni_openedu_referrers_id
    primary key,
    create_at               bigint  default 0 not null,
    update_at               bigint  default 0 not null,
    delete_at               bigint  default 0 not null,
    user_id                 varchar(20),
    org_id                  varchar(20)       not null,
    campaign_id             varchar(20)       not null,
    type                    text    default 'user'::text,
    enable                  boolean default true,
    email                   text,
    invite_status           text,
    purchased_link_id       varchar(20)
    constraint fk_openedu_referrers_purchased_link
    references openedu_referral_links,
    purchased_commission_id varchar(20)
    constraint fk_openedu_referrers_purchased_commission
    references openedu_commissions
    );

create unique index if not exists uniq_idx_openedu_referrers_email_campaign_id_type_delete
    on openedu_referrers (email, campaign_id, type, delete_at);

create table if not exists openedu_reminders
(
    id          varchar(20)       not null
    constraint uni_openedu_reminders_id
    primary key,
    create_at   bigint  default 0 not null,
    update_at   bigint  default 0 not null,
    delete_at   bigint  default 0 not null,
    user_id     varchar(20),
    entity_type text,
    entity_id   varchar(20),
    reminde_at  bigint,
    days_before bigint,
    expired     boolean default false
    );

create table if not exists openedu_resource_usages
(
    id        varchar(20)      not null
    constraint uni_openedu_resource_usages_id
    primary key,
    create_at bigint default 0 not null,
    update_at bigint default 0 not null,
    delete_at bigint default 0 not null,
    user_id   varchar(20),
    cycle     text,
    time      bigint default 0 not null,
    ai_usage  jsonb,
    ai_usages jsonb
    );

create table if not exists openedu_roles
(
    id          varchar(50)           not null
    constraint uni_openedu_roles_id
    primary key,
    create_at   bigint                not null,
    update_at   bigint                not null,
    delete_at   bigint                not null,
    name        text,
    description text,
    level       bigint,
    org_id      text    default ''::text,
    "default"   boolean default false not null
    );

create table if not exists openedu_permissions
(
    method          text,
    path            text,
    controller_name text,
    role_id         varchar(50)
    constraint fk_openedu_permissions_role
    references openedu_roles,
    id              varchar(20)      not null
    constraint uni_openedu_permissions_id
    primary key,
    create_at       bigint default 0 not null,
    update_at       bigint default 0 not null,
    delete_at       bigint default 0 not null,
    allow           boolean
    );

create table if not exists openedu_scheduled_jobs
(
    id                    varchar(20)      not null
    constraint uni_openedu_scheduled_jobs_id
    primary key,
    create_at             bigint default 0 not null,
    update_at             bigint default 0 not null,
    delete_at             bigint default 0 not null,
    name                  text,
    key                   text,
    type                  text,
    args                  jsonb,
    schedule_at           bigint default 0 not null,
    cron_expression       text,
    active                boolean,
    last_execution_time   bigint default 0,
    last_execution_status text
    );

create table if not exists openedu_schedules
(
    id          varchar(20)      not null
    constraint uni_openedu_schedules_id
    primary key,
    create_at   bigint default 0 not null,
    update_at   bigint default 0 not null,
    delete_at   bigint default 0 not null,
    name        text,
    description text,
    start_at    bigint,
    end_at      bigint,
    org_id      text
    );

create table if not exists openedu_event_schedules
(
    id          varchar(20)      not null
    constraint uni_openedu_event_schedules_id
    primary key,
    create_at   bigint default 0 not null,
    update_at   bigint default 0 not null,
    delete_at   bigint default 0 not null,
    schedule_id varchar(20)
    constraint fk_openedu_schedules_events
    references openedu_schedules,
    name        text,
    description text,
    location    text,
    start_at    bigint,
    end_at      bigint,
    join_link   text,
    event_type  text
    );

create table if not exists openedu_sections
(
    uid                       text,
    org_id                    text,
    course_id                 text,
    user_id                   text,
    title                     text,
    note                      text,
    "order"                   bigint,
    free                      boolean default false,
    status                    text    default 'draft'::text,
    parent_id                 text,
    section_id_v2             text,
    lesson_id_v2              text,
    lesson_count              bigint,
    active_lesson             bigint,
    id                        varchar(20)       not null
    constraint uni_openedu_sections_id
    primary key,
    create_at                 bigint  default 0 not null,
    update_at                 bigint  default 0 not null,
    delete_at                 bigint  default 0 not null,
    count_text_lesson         bigint,
    count_video_lesson        bigint,
    count_pdf_lesson          bigint,
    count_pp_lesson           bigint,
    count_doc_lesson          bigint,
    count_quiz_lesson         bigint,
    count_embed_lesson        bigint,
    count_active_text_lesson  bigint,
    count_active_video_lesson bigint,
    count_active_pdf_lesson   bigint,
    count_active_pp_lesson    bigint,
    count_active_doc_lesson   bigint,
    count_active_quiz_lesson  bigint,
    count_active_embed_lesson bigint
    );

create index if not exists idx_sections_uid_course
    on openedu_sections (uid, course_id, delete_at);

create table if not exists openedu_sent_emails
(
    id          varchar(20)      not null
    constraint uni_openedu_sent_emails_id
    primary key,
    create_at   bigint default 0 not null,
    update_at   bigint default 0 not null,
    delete_at   bigint default 0 not null,
    sender      text,
    receiver    text,
    status      text,
    template_id varchar(20),
    params      jsonb
    );

create table if not exists openedu_sessions
(
    id           varchar(20)      not null
    constraint uni_openedu_sessions_id
    primary key,
    create_at    bigint default 0 not null,
    update_at    bigint default 0 not null,
    delete_at    bigint default 0 not null,
    org_id       varchar(20)      not null,
    url          varchar(256)     not null,
    user_id      varchar(20)      not null,
    token        varchar(1000),
    user_agent   varchar(256),
    expire_at    bigint,
    props        jsonb,
    otp_verified boolean
    );

create unique index if not exists idx_openedu_sessions_user_id_user_agent_org_id
    on openedu_sessions (user_id, user_agent, org_id);

create unique index if not exists idx_openedu_user_login_session_user_id_org_id_ua
    on openedu_sessions (user_id, user_agent, org_id);

create table if not exists openedu_sns_accounts
(
    id            varchar(20)       not null
    constraint uni_openedu_sns_accounts_id
    primary key,
    create_at     bigint  default 0 not null,
    update_at     bigint  default 0 not null,
    delete_at     bigint  default 0 not null,
    user_id       text,
    account_id    text,
    username      text,
    display_name  text,
    email         text,
    avatar        text,
    profile       jsonb,
    access_token  text,
    refresh_token text,
    provider      text,
    is_default    boolean default false,
    last_login    bigint
    );

create table if not exists openedu_system_configs
(
    id         varchar(20)      not null
    constraint uni_openedu_system_configs_id
    primary key,
    create_at  bigint default 0 not null,
    update_at  bigint default 0 not null,
    delete_at  bigint default 0 not null,
    key        text,
    data_type  text,
    value      text,
    org_id     varchar(20),
    domain     text,
    locale     varchar(10),
    alt_domain text
    );

create unique index if not exists uniq_idx_openedu_system_configs_key_org_id_locale
    on openedu_system_configs (key, org_id, locale, domain);

create table if not exists openedu_transactions
(
    user_id          varchar(20)                         not null,
    wallet_id        varchar(20)                         not null,
    sender_id        varchar(20),
    amount           numeric(19, 4) default '0'::numeric not null,
    type             text,
    status           text,
    payment_id       text,
    org_id           varchar(20),
    data             jsonb,
    entity_type      text,
    entity_id        text,
    id               varchar(20)                         not null
    constraint uni_openedu_transactions_id
    primary key,
    create_at        bigint         default 0            not null,
    update_at        bigint         default 0            not null,
    delete_at        bigint         default 0            not null,
    note             text,
    to_address       text,
    network          text,
    currency_type    text,
    currency         text,
    error_code       bigint,
    tx_hash          text,
    blockchain_tx_id text
    );

create table if not exists openedu_user_settings
(
    user_id   varchar(20)       not null,
    org_id    varchar(20)       not null,
    type      text    default 'agency'::text,
    enable    boolean default true,
    value     jsonb,
    id        varchar(20)       not null
    constraint uni_openedu_user_settings_id
    primary key,
    create_at bigint  default 0 not null,
    update_at bigint  default 0 not null,
    delete_at bigint  default 0 not null
    );

create table if not exists openedu_users
(
    id                        varchar(20)       not null
    constraint uni_openedu_users_id
    primary key,
    create_at                 bigint  default 0 not null,
    update_at                 bigint  default 0 not null,
    delete_at                 bigint  default 0 not null,
    username                  text
    constraint uni_openedu_users_username
    unique,
    email                     text
    constraint uni_openedu_users_email
    unique,
    phone                     text,
    password                  text,
    active                    boolean default false,
    blocked                   boolean default false,
    props                     jsonb,
    cover_photo               text,
    skills                    jsonb,
    avatar                    text,
    display_name              text,
    headline                  text,
    about                     text,
    position                  text,
    following                 bigint  default 0,
    followers                 bigint  default 0,
    require_set_password      boolean default false,
    user_id_v2                varchar(100),
    summary                   text,
    normalized_email          text,
    new_user_survey_completed boolean,
    created_by_id             varchar(20)
    constraint fk_openedu_users_created_by
    references openedu_users
    );

create table if not exists openedu_email_templates
(
    id        varchar(20)      not null
    constraint uni_openedu_email_templates_id
    primary key,
    create_at bigint default 0 not null,
    update_at bigint default 0 not null,
    delete_at bigint default 0 not null,
    org_id    varchar(20)      not null,
    user_id   varchar(20)
    constraint fk_openedu_email_templates_user
    references openedu_users,
    code      text,
    name      text,
    subject   varchar(1000),
    html      text,
    json      text
    );

create table if not exists openedu_form_sessions
(
    id               varchar(20)      not null
    constraint uni_openedu_form_sessions_id
    primary key,
    create_at        bigint default 0 not null,
    update_at        bigint default 0 not null,
    delete_at        bigint default 0 not null,
    user_id          varchar(20)
    constraint fk_openedu_form_sessions_user
    references openedu_users,
    form_id          varchar(20)      not null,
    form_relation_id varchar(20),
    status           varchar(100),
    note             text,
    form_uid         varchar(20)
    );

create table if not exists openedu_form_answers
(
    id               varchar(20)      not null
    constraint uni_openedu_form_answers_id
    primary key,
    create_at        bigint default 0 not null,
    update_at        bigint default 0 not null,
    delete_at        bigint default 0 not null,
    session_id       varchar(20)      not null
    constraint fk_openedu_form_sessions_answers
    references openedu_form_sessions,
    form_id          varchar(20),
    key              text,
    question_id      varchar(20)      not null
    constraint fk_openedu_form_answers_question
    references openedu_form_questions,
    sub_question_id  varchar(20)
    constraint fk_openedu_form_answers_sub_question
    references openedu_form_questions,
    option_id        varchar(20)
    constraint fk_openedu_form_answers_option
    references openedu_form_question_options,
    text             text,
    question_uid     varchar(20),
    sub_question_uid varchar(20),
    option_uid       varchar(20),
    form_uid         varchar(20),
    user_id          varchar(20)
    );

create table if not exists openedu_oe_campaign_accounts
(
    id          varchar(20)      not null
    constraint uni_openedu_oe_campaign_accounts_id
    primary key,
    create_at   bigint default 0 not null,
    update_at   bigint default 0 not null,
    delete_at   bigint default 0 not null,
    user_id     varchar(20)
    constraint fk_openedu_oe_campaign_accounts_user
    references openedu_users,
    campaign_id text,
    parent_id   varchar(20)
    constraint fk_openedu_oe_campaign_accounts_parent
    references openedu_users,
    active      boolean
    );

create table if not exists openedu_oe_point_histories
(
    id              varchar(20)                         not null
    constraint uni_openedu_oe_point_histories_id
    primary key,
    create_at       bigint         default 0            not null,
    update_at       bigint         default 0            not null,
    delete_at       bigint         default 0            not null,
    user_id         varchar(20)
    constraint fk_openedu_oe_point_histories_user
    references openedu_users,
    status          text,
    amount          numeric(19, 4) default '0'::numeric not null,
    used            numeric(19, 4) default '0'::numeric not null,
    remaining       numeric(19, 4) default '0'::numeric not null,
    expire_date     bigint         default 0,
    props           jsonb,
    entity_type     text,
    entity_id       text,
    note            text,
    org_id          text,
    notified_period bigint         default 0,
    source          text,
    claim_date      bigint         default 0,
    campaign_id     varchar(20)
    constraint fk_openedu_oe_point_histories_campaign
    references openedu_oe_point_campaigns
    );

create table if not exists openedu_oe_referral_codes
(
    id          varchar(20)           not null
    constraint uni_openedu_oe_referral_codes_id
    primary key,
    create_at   bigint      default 0 not null,
    update_at   bigint      default 0 not null,
    delete_at   bigint      default 0 not null,
    code        text                  not null
    constraint uni_openedu_oe_referral_codes_code
    unique,
    user_id     varchar(20)
    constraint fk_openedu_oe_referral_codes_user
    references openedu_users,
    type        text        default 'referral'::text,
    referrer_id varchar(20) default NULL::character varying
    constraint fk_openedu_oe_referral_codes_referrer
    references openedu_users,
    campaign_id varchar(20) default NULL::character varying
    constraint fk_openedu_oe_referral_codes_campaign
    references openedu_oe_point_campaigns,
    props       jsonb
    );

create table if not exists openedu_oe_referrals
(
    id             varchar(20)                         not null
    constraint uni_openedu_oe_referrals_id
    primary key,
    create_at      bigint         default 0            not null,
    update_at      bigint         default 0            not null,
    delete_at      bigint         default 0            not null,
    user_id        varchar(20)
    constraint fk_openedu_oe_referrals_user
    references openedu_users,
    referee_id     varchar(20),
    code           text,
    ref_code_id    varchar(20)
    constraint fk_openedu_oe_referrals_ref_code
    references openedu_oe_referral_codes,
    campaign_id    varchar(20)
    constraint fk_openedu_oe_referrals_campaign
    references openedu_oe_point_campaigns,
    campaign_type  text,
    point_reward   jsonb,
    amount         numeric(19, 4) default '0'::numeric not null,
    referee_reward jsonb,
    referee_amount numeric(19, 4) default '0'::numeric not null,
    trigger        text
    );

create table if not exists openedu_organizations
(
    user_id      varchar(20)
    constraint fk_openedu_organizations_user
    references openedu_users,
    thumbnail_id varchar(20) default NULL::character varying
    constraint fk_openedu_organizations_thumbnail
    references openedu_files,
    settings     jsonb,
    schema       text,
    name         text,
    domain       text,
    alt_domain   text,
    active       boolean     default true,
    create_by_id varchar(20)
    constraint fk_openedu_organizations_create_by
    references openedu_users,
    id           varchar(20)           not null
    constraint uni_openedu_organizations_id
    primary key,
    create_at    bigint      default 0 not null,
    update_at    bigint      default 0 not null,
    delete_at    bigint      default 0 not null,
    banner_id    varchar(20)
    constraint fk_openedu_organizations_banner
    references openedu_files
    );

create table if not exists openedu_approvals
(
    id             varchar(20)       not null
    constraint uni_openedu_approvals_id
    primary key,
    create_at      bigint  default 0 not null,
    update_at      bigint  default 0 not null,
    delete_at      bigint  default 0 not null,
    org_id         varchar(20)
    constraint fk_openedu_approvals_org
    references openedu_organizations,
    entity_type    text,
    entity_id      varchar(20),
    entity_schema  text,
    requester_id   varchar(20)
    constraint fk_openedu_approvals_requester
    references openedu_users,
    request_date   bigint,
    confirm_by_id  varchar(20),
    confirm_date   bigint,
    status         text    default 'new'::text,
    type           text,
    note           text,
    props          jsonb,
    request_value  text,
    approve_value  text,
    entity_version bigint,
    request_uid    varchar(20),
    re_review      boolean default false,
    entity_uid     varchar(20)
    );

create table if not exists openedu_blogs
(
    id                 varchar(20)       not null
    constraint uni_openedu_blogs_id
    primary key,
    create_at          bigint  default 0 not null,
    update_at          bigint  default 0 not null,
    delete_at          bigint  default 0 not null,
    org_id             varchar(20)
    constraint fk_openedu_blogs_org
    references openedu_organizations,
    author_id          varchar(20),
    banner_id          varchar(20),
    image_description  text,
    description        text,
    title              text,
    hash_tag_id        jsonb,
    slug               text,
    content            text,
    json_content       jsonb,
    time_read          smallint,
    status             text    default 'draft'::text,
    schedule_at        bigint  default 0 not null,
    blog_type          text,
    ai_blog_id         varchar(20),
    cuid               varchar(20)       not null,
    version            bigint  default 0,
    latest             boolean default true,
    props              jsonb,
    pub_date           bigint  default 0,
    pub_reject_date    bigint  default 0,
    is_pin             boolean,
    is_ai_generated    boolean,
    is_origin_draft    boolean default false,
    locale             text    default 'en'::text,
    ai_generate_status text    default 'manual'::text
    );

create table if not exists openedu_learning_statuses
(
    id                  varchar(20)      not null
    constraint uni_openedu_learning_statuses_id
    primary key,
    create_at           bigint default 0 not null,
    update_at           bigint default 0 not null,
    delete_at           bigint default 0 not null,
    org_id              varchar(20)      not null
    constraint fk_openedu_learning_statuses_org
    references openedu_organizations,
    user_id             varchar(20)      not null
    constraint fk_openedu_learning_statuses_user
    references openedu_users,
    course_cuid         varchar(20)      not null,
    start_at            bigint default 0 not null,
    completed_at        bigint default 0 not null,
    sections            jsonb,
    current_lesson_uid  varchar(20),
    current_section_uid varchar(20),
    latest_lesson_uid   varchar(20),
    latest_section_uid  varchar(20)
    );

create unique index if not exists uniq_idx_openedu_learning_statuses_user_id_course_cuid
    on openedu_learning_statuses (user_id, course_cuid);

create table if not exists openedu_pricing_plans
(
    id            varchar(20)       not null
    constraint uni_openedu_pricing_plans_id
    primary key,
    create_at     bigint  default 0 not null,
    update_at     bigint  default 0 not null,
    delete_at     bigint  default 0 not null,
    tier          text,
    name          text,
    description   text,
    monthly_price jsonb,
    annual_price  jsonb,
    enable        boolean default false,
    "order"       bigint  default 0,
    cycle         text,
    ai_limitation jsonb,
    user_id       varchar(20)
    constraint fk_openedu_pricing_plans_user
    references openedu_users,
    period        text    default 0,
    period_unit   varchar(50)
    );

create table if not exists openedu_publish_blogs
(
    id              varchar(20)      not null
    constraint uni_openedu_publish_blogs_id
    primary key,
    create_at       bigint default 0 not null,
    update_at       bigint default 0 not null,
    delete_at       bigint           not null,
    org_id          varchar(20)      not null,
    org_schema      text,
    blog_id         varchar(20)      not null,
    author_id       varchar(20)
    constraint fk_openedu_publish_blogs_author
    references openedu_users,
    blog_cuid       varchar(20)      not null
    constraint uni_openedu_publish_blogs_blog_cuid
    unique,
    title           text,
    slug            text,
    pub_date        bigint           not null,
    description     text,
    props           jsonb,
    is_pin          boolean,
    is_ai_generated boolean,
    locale          text   default 'en'::text,
    ai_blog_id      varchar(20)
    );

create table if not exists openedu_referrals
(
    id                  varchar(20)                           not null
    constraint uni_openedu_referrals_id
    primary key,
    create_at           bigint           default 0            not null,
    update_at           bigint           default 0            not null,
    delete_at           bigint           default 0            not null,
    org_id              varchar(20)                           not null,
    course_cuid         varchar(20)                           not null,
    course_id           varchar(20),
    campaign_id         varchar(20)                           not null
    constraint fk_openedu_referrals_campaign
    references openedu_affiliate_campaigns,
    commission_id       varchar(20)                           not null,
    order_id            varchar(20)                           not null
    constraint fk_openedu_referrals_order
    references openedu_orders,
    order_amount        numeric(19, 4)   default '0'::numeric not null,
    ref1_user_id        varchar(20)
    constraint fk_openedu_referrals_ref1_user
    references openedu_users,
    ref1_link_id        varchar(20)
    constraint fk_openedu_referrals_ref1_link
    references openedu_referral_links,
    ref1_rate           double precision default 0,
    ref1_amount         numeric(19, 4)   default '0'::numeric not null,
    share_rate          double precision default 0,
    share_amount        numeric(19, 4)   default '0'::numeric not null,
    bonus_commission_id varchar(20)      default NULL::character varying,
    bonus_rate          double precision default 0,
    bonus_amount        numeric(19, 4)   default '0'::numeric not null,
    ref2_user_id        varchar(20)      default NULL::character varying
    constraint fk_openedu_referrals_ref2_user
    references openedu_users,
    ref2_link_id        varchar(20)      default NULL::character varying
    constraint fk_openedu_referrals_ref2_link
    references openedu_referral_links,
    ref2_rate           double precision default 0,
    ref2_amount         numeric(19, 4)   default '0'::numeric not null,
    ref3_user_id        varchar(20)      default NULL::character varying
    constraint fk_openedu_referrals_ref3_user
    references openedu_users,
    ref3_link_id        varchar(20)      default NULL::character varying
    constraint fk_openedu_referrals_ref3_link
    references openedu_referral_links,
    ref3_rate           double precision default 0,
    ref3_amount         numeric(19, 4)   default '0'::numeric not null,
    currency            text             default 'VND'::text
    );

create table if not exists openedu_subscriptions
(
    id            varchar(20)       not null
    constraint uni_openedu_subscriptions_id
    primary key,
    create_at     bigint  default 0 not null,
    update_at     bigint  default 0 not null,
    delete_at     bigint  default 0 not null,
    user_id       varchar(20)
    constraint fk_openedu_subscriptions_user
    references openedu_users,
    plan_id       varchar(20)
    constraint fk_openedu_subscriptions_plan
    references openedu_pricing_plans,
    start_date    bigint  default 0 not null,
    end_date      bigint  default 0 not null,
    billing_cycle text,
    status        text,
    auto_renew    boolean default false,
    enable        boolean default true,
    plan_owner_id varchar(20)
    constraint fk_openedu_subscriptions_plan_owner
    references openedu_users,
    is_group      boolean default false
    );

create table if not exists openedu_user_actions
(
    id             varchar(20)      not null
    constraint uni_openedu_user_actions_id
    primary key,
    create_at      bigint default 0 not null,
    update_at      bigint default 0 not null,
    delete_at      bigint default 0 not null,
    user_id        varchar(20)
    constraint fk_openedu_user_actions_user
    references openedu_users,
    target_user_id varchar(20)
    constraint fk_openedu_user_actions_target_user
    references openedu_users,
    action         text,
    reason         text
    );

create unique index if not exists idx_openedu_user_actions_user_id_action_target_user_id
    on openedu_user_actions (user_id, action, target_user_id);

create table if not exists openedu_user_role_orgs
(
    user_id   varchar(20)
    constraint fk_openedu_user_role_orgs_user
    references openedu_users,
    role_id   varchar(50)
    constraint fk_openedu_user_role_orgs_role
    references openedu_roles,
    org_id    text,
    id        varchar(20)      not null
    constraint uni_openedu_user_role_orgs_id
    primary key,
    create_at bigint default 0 not null,
    update_at bigint default 0 not null,
    delete_at bigint default 0 not null
    );

create unique index if not exists idx_openedu_user_role_orgs_user_id_org_id_role_id
    on openedu_user_role_orgs (user_id, role_id, org_id);

create index if not exists idx_openedu_user_role_orgs_user_id_role_id
    on openedu_user_role_orgs (user_id, role_id);

create table if not exists openedu_user_summaries
(
    user_id       varchar(20) not null
    constraint uni_openedu_user_summaries_user_id
    primary key
    constraint fk_openedu_users_summary
    references openedu_users,
    following     integer default 0,
    followers     integer default 0,
    total_blogs   integer default 0,
    total_courses integer default 0
    );

create table if not exists openedu_user_tokens
(
    id            varchar(20)      not null
    constraint uni_openedu_user_tokens_id
    primary key,
    create_at     bigint default 0 not null,
    update_at     bigint default 0 not null,
    delete_at     bigint default 0 not null,
    user_id       varchar(20)
    constraint fk_openedu_user_tokens_user
    references openedu_users,
    org_id        text,
    email         text,
    token         text,
    event         text,
    expiry_time   bigint           not null,
    send_email    bigint           not null,
    verify_date   bigint default 0,
    redirect_url  text,
    props         jsonb,
    otp           text,
    otp_expire_at bigint
    );

create index if not exists idx_openedu_users_id
    on openedu_users (id);

create unique index if not exists idx_openedu_users_user_id_v2
    on openedu_users (user_id_v2);

create table if not exists openedu_wallets
(
    user_id              varchar(20)                         not null,
    balance              numeric(19, 4) default '0'::numeric not null,
    type                 varchar(20)                         not null,
    currency             text,
    id                   varchar(20)                         not null
    constraint uni_openedu_wallets_id
    primary key,
    create_at            bigint         default 0            not null,
    update_at            bigint         default 0            not null,
    delete_at            bigint         default 0            not null,
    available_balance    numeric(19, 4) default '0'::numeric not null,
    "default"            boolean,
    network              text,
    address              text,
    public_key           text,
    blockchain_wallet_id varchar(20),
    parent_id            varchar(20),
    earning_balance      numeric(19, 4) default '0'::numeric not null
    );

create or replace function generate_id(length integer default 16)
returns text
language plpgsql
as $$
declare
chars text := '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    result text := '';
    i int;
begin
for i in 1..length loop
        result := result || substr(chars, ceil(random() * length(chars))::int, 1);
end loop;
return result;
end;
$$;