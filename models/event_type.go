package models

type EventType string

const (
	EventRegister         EventType = "REGISTER"
	EventResetPassword    EventType = "RESET_PASSWORD"
	EventExternalRegister EventType = "EXTERNAL_REGISTER"
	EventInviteCreator    EventType = "INVITE_CREATOR"
	EventInviteUser       EventType = "INVITE_USER"
	EventInviteReferrer   EventType = "INVITE_REFERRER"
	EventSendOTP          EventType = "SEND_OTP"

	EventApproveRegisterOrgNewUser      EventType = "APPROVE_REGISTER_ORG_NEW_USER"
	EventApproveRegisterOrgExistingUser EventType = "APPROVE_REGISTER_ORG_EXISTING_USER"
	EventDeactivateOrg                  EventType = "DEACTIVATE_ORG"

	//APPROVE_CREATOR EventType = "APPROVE_CREATOR"
	// => Separate to EventApproveRegisterCreatorNewUser and EventApproveRegisterCreatorExistingUser for easier
	// to maintain and debugging

	//Writer
	EventApproveRegisterWriterNewUser      EventType = "APPROVE_REGISTER_WRITER_NEW_USER"
	EventApproveRegisterWriterExistingUser EventType = "APPROVE_REGISTER_WRITER_EXISTING_USER"

	//Creator
	EventApproveRegisterCreatorNewUser      EventType = "APPROVE_REGISTER_CREATOR_NEW_USER"
	EventApproveRegisterCreatorExistingUser EventType = "APPROVE_REGISTER_CREATOR_EXISTING_USER"
	EventInviteCreatorBeforeAccept          EventType = "INVITE_CREATOR_BEFORE_ACCEPT"
	EventAcceptInviteCreatorNewUser         EventType = "ACCEPT_INVITE_CREATOR_NEW_USER"
	EventAcceptInviteCreatorExistingUser    EventType = "ACCEPT_INVITE_CREATOR_EXISTING_USER"
	// Editor writer
	EventInviteOrgEditor EventType = "INVITE_ORG_EDITOR"
	EventInviteOrgWriter EventType = "INVITE_ORG_WRITER"

	// enroll success
	EnrollCourseSuccessEn EventType = "ENROLL_COURSE_SUCCESS_EN"
	EnrollCourseSuccessVi EventType = "ENROLL_COURSE_SUCCESS_VI"

	//USER
	EventInviteUserBeforeAccept       EventType = "INVITE_USER_BEFORE_ACCEPT"
	EventAcceptInviteUserNewUser      EventType = "ACCEPT_INVITE_USER_NEW_USER"
	EventAcceptInviteUserExistingUser EventType = "ACCEPT_INVITE_USER_EXISTING_USER"

	EventAcceptInviteEditorNewUser      EventType = "ACCEPT_INVITE_EDITOR_NEW_USER"
	EventAcceptInviteEditorExistingUser EventType = "ACCEPT_INVITE_EDITOR_EXISTING_USER"

	EventAcceptInviteWriterNewUser      EventType = "ACCEPT_INVITE_WRITER_NEW_USER"
	EventAcceptInviteWriterExistingUser EventType = "ACCEPT_INVITE_WRITER_EXISTING_USER"

	EventEnrolledCourse             EventType = "enrolled_course"
	EventSpecificTime               EventType = "specific_time"
	EventCompletedCourse            EventType = "completed_course"
	EventNDaysAfterCourseEnrollment EventType = "n_days_after_course_enrollment"
	EventStartedSection             EventType = "started_section"
	EventCompletedSection           EventType = "completed_section"
	EventStartedLesson              EventType = "started_lesson"
	EventCompletedLesson            EventType = "completed_lesson"
	EventClickOn                    EventType = "clicked_on"
	EventNDaysAfterSettingTrigger   EventType = "n_days_after_setting_trigger"
	EventReachNumSubmissions        EventType = "reach_num_submissions"
	EventCompletedQuiz              EventType = "completed_quiz"
	EventPaymentInsufficient        EventType = "payment_insufficient"
	EventPaymentOver                EventType = "payment_over"
	EventBecomeAffiliate            EventType = "become_affiliate"
	EventRejectRegisterCreator      EventType = "reject_register_creator"
	EventRejectRegisterWriter       EventType = "reject_register_writer"
	EventRejectRegisterOrg          EventType = "reject_register_org"
)

func (e EventType) String() string {
	return string(e)
}

func (e EventType) IsValidFormTriggerEvent() bool {
	switch e {
	case EventEnrolledCourse,
		EventSpecificTime,
		EventCompletedCourse,
		EventNDaysAfterCourseEnrollment,
		EventStartedSection,
		EventCompletedSection,
		EventStartedLesson,
		EventCompletedLesson,
		EventClickOn,
		EventNDaysAfterSettingTrigger,
		EventReachNumSubmissions,
		EventCompletedQuiz:
		return true

	default:
		return false
	}
}

// type SendEmailParams struct {
// 	Event       EventType
// 	User        *User
// 	UserToken   *UserToken
// 	Org         *Organization
// 	Course      *Course
// 	ExtendDatas map[string]interface{}
// }
