package models

import (
	"embed"
	"errors"
	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/golang-migrate/migrate/v4/source/iofs"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"os"
)

const migrateSrcName = "iofs"

//var sharedTables = []any{
//	&User{},
//	&Role{},
//	&UserRoleOrg{},
//	&Permission{},
//	&Session{},
//	&SnsAccount{},
//	&SystemConfig{},
//	&Organization{},
//	&UserRoleOrg{},
//	&File{},
//	&FileRelation{},
//	&UserToken{},
//	&Category{},
//	&CategoryRelation{},
//	&Hashtag{},
//	&HashtagRelation{},
//	&Coupon{},
//	&CouponHistory{},
//	&EmailTemplate{},
//	&Form{},
//	&FormQuestion{},
//	&FormQuestionOption{},
//	&FormSession{},
//	&FormRelation{},
//	&FormAnswer{},
//	&FormAnswerStats{},
//	&Approval{},
//	&PublishCourse{},
//	&CourseEnrollment{},
//	&PublishBlog{},
//	&UserAction{},
//	&PageConfig{},
//	&PageAccess{},
//	&Wallet{},
//	&Transaction{},
//	&PaymentMethod{},
//	&Payment{},
//	&Order{},
//	&OrderItem{},
//	&LearningProgress{},
//	&Bookmark{},
//	&Certificate{},
//	&HtmlTemplate{},
//	&UserSetting{},
//	&AffiliateCampaign{},
//	&Commission{},
//	&CourseCampaign{},
//	&Referral{},
//	&ReferralLink{},
//	&Referrer{},
//	&AIBlogRewrite{},
//	&UserSummary{},
//	&AICourse{},
//	&AIHistory{},
//	&PricingPlan{},
//	&Subscription{},
//	&ResourceUsage{},
//	&Course{},
//	&Section{},
//	&LessonContent{},
//	&CoursePartner{},
//	&CoursePrice{},
//	&Quiz{},
//	&QuizRelation{},
//	&QuizQuestion{},
//	&QuizSubmission{},
//	&QuizAnswer{},
//	&Blog{},
//	&AIModel{},
//	&OEPointHistory{},
//	&OEPointCampaign{},
//	&OEReferral{},
//	&OEReferralCode{},
//	&Reminder{},
//	&FeaturedContent{},
//	&ClpLaunchpad{},
//	&ClpCourseLaunchpad{},
//	&ClpVotingMilestone{},
//	&ClpVotingPhase{},
//	&ClpVotingLaunchpad{},
//	&ClpInvestment{},
//	&CourseRevenuePoint{},
//	&AIPrompt{},
//	&LearningStatus{},
//	&OEReferralLeaderBoard{},
//	&OECampaignAccount{},
//	&OEReferralReport{},
//	&Schedule{},
//	&EventSchedule{},
//}

//go:embed migrations/*.sql
var migrations embed.FS

func MigrateDatabase() {
	shouldMigrate := false
	if os.Getenv("MIGRATE") == "1" {
		shouldMigrate = true
	}

	if !shouldMigrate {
		log.Warnf("MIGRATE=%s Skip run DB migration!", os.Getenv("MIGRATE"))
		return
	}

	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatalf("models.MigrateDatabase get DB instance failed: %v", err)
	}

	migrationDriver, err := iofs.New(migrations, "migrations")
	if err != nil {
		log.Fatalf("models.MigrateDatabase get migration driver failed: %v", err)
	}

	dbDriver, err := postgres.WithInstance(sqlDB, &postgres.Config{})
	if err != nil {
		log.Fatalf("models.MigrateDatabase get migrate DB instance failed: %v", err)
	}

	m, err := migrate.NewWithInstance(
		migrateSrcName,
		migrationDriver,
		setting.DatabaseSetting.Name,
		dbDriver,
	)
	if err != nil {
		log.Fatalf("models.MigrateDatabase new with instance failed: %v", err)
	}

	if err = m.Up(); err != nil && !errors.Is(err, migrate.ErrNoChange) {
		log.Fatalf("models.MigrateDatabase migrate database up failed: %v", err)
	}

	//// Migrate DB tables
	//if err := DB.AutoMigrate(sharedTables...); err != nil {
	//	log.Fatalf("Failed to run DB migration: %v", err)
	//}
	//
	//// Create DB indexes
	//if err := CreateIndexes(); err != nil {
	//	log.Fatalf("Failed to create or update indexes: %v", err)
	//}
}

//
//func CreateIndexes() error {
//	prefix := setting.DatabaseSetting.TablePrefix
//	indexRawSQLs := []string{
//		// Index name convention: idx_{table_name}_{column_name(s)}
//		// Example: idx_user_role_orgs_user_id_role_id -> Index for table `user_role_orgs` on columns (`user_id`, `role_id`)
//
//		// Index for `user_id`, `role_id` columns in `user_role_orgs` table
//		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_%[1]s_user_id_role_id
//			ON %[2]s (user_id, role_id);`, prefix+string(UserRoleOrgTbl), GetTblName(UserRoleOrgTbl)),
//
//		// Index for `parent_id` column in `categories` table
//		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_%[1]s_parent_id
//			ON %[2]s (parent_id);`, prefix+string(CategoryTbl), GetTblName(CategoryTbl)),
//
//		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_learning_progresses_user_event ON %[1]s (user_id, event, complete_at, lesson_content_uid, course_cuid);`, prefix+string(LearningProgressTbl)),
//		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_lesson_contents_lesson ON %[1]s (lesson_id, uid);`, prefix+string(LessonContentTbl)),
//		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_sections_uid_course ON %[1]s (uid, course_id, delete_at);`, prefix+string(SectionTbl)),
//
//		// Index for `course_id` columns in `course_prices` table
//		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_%[1]s_course_id
//			ON %[2]s (course_id);`, prefix+string(CoursePriceTbl), GetTblName(CoursePriceTbl)),
//
//		// Index for `related_type`, `field`, `related_id`, `delete_at` columns in `category_relations` table
//		fmt.Sprintf(
//			`CREATE INDEX IF NOT EXISTS idx_%[1]s_composite
//			ON %[2]s (related_type, field, related_id, delete_at);`,
//			prefix+string(CategoryRelationTbl),
//			GetTblName(CategoryRelationTbl),
//		),
//
//		// Index for `related_type`, `field`, `related_id`, `delete_at`,  "order", `category_id` columns in `category_relations` table
//		fmt.Sprintf(
//			`CREATE INDEX IF NOT EXISTS idx_%[1]s_covering
//			ON %[2]s (related_type, field, related_id, delete_at, "order", category_id)	INCLUDE (id, create_at, update_at);`,
//			prefix+string(CategoryRelationTbl),
//			GetTblName(CategoryRelationTbl),
//		),
//
//		// Index for `category_id` column in `category_relations` table
//		fmt.Sprintf(
//			`CREATE INDEX IF NOT EXISTS idx_%[1]s_category_id ON %[2]s (category_id);`,
//			prefix+string(CategoryRelationTbl),
//			GetTblName(CategoryRelationTbl),
//		),
//
//		//	Index for course_enrollments filtering and joining
//		fmt.Sprintf(
//			`CREATE INDEX IF NOT EXISTS idx_%[1]s_composite
//			ON %[2]s (course_cuid, blocked, delete_at, user_id);`,
//			prefix+string(CourseEnrollmentTbl),
//			GetTblName(CourseEnrollmentTbl),
//		),
//
//		//	Index on users for join optimization
//		fmt.Sprintf(
//			`CREATE INDEX IF NOT EXISTS idx_%[1]s_id ON %[2]s (id);`,
//			prefix+string(UserTbl),
//			GetTblName(UserTbl),
//		),
//	}
//
//	uniqIndexRawSQLs := []string{
//		// Unique index name convention: {constraint_type}_{table_name}_{column_name(s)}
//		// Example: uniq_idx_hashtags_name_org_id -> Unique index for table `hashtags` on columns (`name`, `org_id`)
//
//		// Unique index for `user_id`, `action`, `target_id` columns in `user_actions` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS idx_%[1]s_user_id_action_target_user_id
//			ON %[2]s(user_id, action, target_user_id);`, prefix+string(UserActionTbl), GetTblName(UserActionTbl)),
//
//		// Unique index for `user_id`, `role_id`, `org_id` columns in `user_role_orgs` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS idx_%[1]s_user_id_org_id_role_id
//	 		ON %[2]s (user_id, role_id, org_id);`, prefix+string(UserRoleOrgTbl), GetTblName(UserRoleOrgTbl)),
//
//		// Unique index for `name`, `org_id` columns in `hashtags` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS idx_%[1]s_name_org_id
//	 		ON %[2]s (name, org_id);`, prefix+string(HashtagTbl), GetTblName(HashtagTbl)),
//
//		// Unique index for `category_id`, `related_id`, `field` columns in `category_relations` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS idx_%[1]s_category_id_related_id_field
//			ON %[2]s (category_id, related_id, field);`, prefix+string(CategoryRelationTbl), GetTblName(CategoryRelationTbl)),
//
//		// Unique index for `course_id` columns in `course_prices` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS idx_%[1]s_course_id
//			ON %[2]s (course_id);`, prefix+string(CoursePriceTbl), GetTblName(CoursePriceTbl)),
//
//		// Unique index for `session_id` column in `learning_progresses` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS idx_%[1]s_session_id
//			ON %[2]s (session_id);`, prefix+string(LearningProgressTbl), GetTblName(LearningProgressTbl)),
//
//		// Unique index for `user_id`, `user_agent`, `org_id` columns in `sessions` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS idx_%[1]s_user_id_user_agent_org_id
//        	ON %[2]s (user_id, user_agent, org_id);`, prefix+string(SessionTbl), GetTblName(SessionTbl)),
//
//		// Unique index for `user_id`, `course_cuid` columns in `course_enrollments` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS uniq_idx_%[1]s_user_id_course_cuid
//        	ON %[2]s (user_id, course_cuid);`, prefix+string(CourseEnrollmentTbl), GetTblName(CourseEnrollmentTbl)),
//
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS uniq_idx_%[1]s_user_id_course_cuid_lesson_uid_event
//        	ON %[2]s (user_id, course_cuid,lesson_uid, event) WHERE event = '%s'`, prefix+string(LearningProgressTbl), GetTblName(LearningProgressTbl), LatestLessonProgress),
//
//		// Unique index for `key`, `org_id`, `locale` columns in `system_configs` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS uniq_idx_%[1]s_key_org_id_locale
//        	ON %[2]s (key, org_id, locale);`, prefix+string(SystemConfigTbl), GetTblName(SystemConfigTbl)),
//
//		// Unique index for `user_id`, `course_cuid` columns in `learning_statuses` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS uniq_idx_%[1]s_user_id_course_cuid
//        	ON %[2]s (user_id, course_cuid);`, prefix+string(LearningStatusTbl), GetTblName(LearningStatusTbl)),
//
//		// Unique index for `org_id`, `campaign_key`, `user_id`, `course_cuid` columns in `oe_referral_reports` table
//		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS uniq_idx_%[1]s_user_id_course_cuid
//        	ON %[2]s (org_id, campaign_key, user_id, course_cuid);`, prefix+string(OEReferralReportTbl), GetTblName(OEReferralReportTbl)),
//	}
//
//	for _, rawSQL := range indexRawSQLs {
//		result := DB.Exec(rawSQL)
//		if err := result.Error; err != nil {
//			log.Errorf("Create index result for `%s` error: %v", rawSQL, result)
//			return err
//		}
//	}
//
//	for _, rawSQL := range uniqIndexRawSQLs {
//		result := DB.Exec(rawSQL)
//		if err := result.Error; err != nil {
//			log.Errorf("Create unique index result for `%s` error: %v", rawSQL, result)
//			return err
//		}
//	}
//
//	if err := DB.Exec(`
//		CREATE OR REPLACE FUNCTION generate_id(length INTEGER DEFAULT 16) RETURNS TEXT AS $$
//		DECLARE
//		chars TEXT := '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
//		result TEXT := '';
//		i INT;
//		BEGIN
//		FOR i IN 1..length LOOP
//		result := result || substr(chars, ceil(random() * length(chars))::int, 1);
//		END LOOP;
//		RETURN result;
//		END;
//		$$ LANGUAGE plpgsql
//	`).Error; err != nil {
//		log.Errorf("Create function generate_id() failed: %v", err)
//		return err
//	}
//
//	return nil
//}
