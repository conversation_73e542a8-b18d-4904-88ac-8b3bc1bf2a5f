package models

import (
	"gorm.io/gorm"
)

type PaymentMethod struct {
	Model
	UserID        string            `json:"user_id" gorm:"type:varchar(20)"`
	CourseID      string            `json:"course_id" gorm:"type:varchar(20)"`
	OrganizeID    string            `json:"organize_id" gorm:"type:varchar(20)"`
	Type          PaymentMethodType `json:"type"`
	Service       PaymentService    `json:"service"`
	Account       string            `json:"account"`
	AccountNumber string            `json:"account_number"`
	AccountName   string            `json:"account_name"`
	Network       BlockchainNetwork `json:"network"`
	PaymentType   Currency          `json:"payment_type"`
	Enable        bool              `json:"enable"`
}

type PaymentMethodQuery struct {
	ID             *string            `form:"id" json:"id"`
	IDIn           []string           `form:"id_in" json:"id_in"`
	UserID         *string            `form:"user_id" json:"user_id"`
	CourseID       *string            `form:"course_id" json:"course_id"`
	OrganizeID     *string            `form:"organize_id" json:"organize_id"`
	Type           *PaymentMethodType `form:"type" json:"type"`
	Service        *PaymentService    `form:"service" json:"service"`
	Account        *string            `form:"account" json:"account"`
	AccountNumber  *string            `form:"account_number" json:"account_number"`
	AccountName    *string            `form:"account_name" json:"account_name"`
	Network        *BlockchainNetwork `form:"network" json:"network"`
	PaymentType    *Currency          `form:"payment_type" json:"payment_type"`
	Enable         *bool              `form:"enable" json:"enable"`
	IncludeDeleted *bool              `form:"include_deleted" json:"include_deleted"`
}

type SimplePaymentMethod struct {
	Model
	UserID        string            `json:"user_id"`
	CourseID      string            `json:"course_id"`
	OrganizeID    string            `json:"organize_id"`
	Type          PaymentMethodType `json:"type"`
	Service       PaymentService    `json:"service"`
	Account       string            `json:"account"`
	AccountNumber string            `json:"account_number"`
	AccountName   string            `json:"account_name"`
	Network       BlockchainNetwork `json:"network"`
	Enable        bool              `json:"enable"`
	PaymentType   Currency          `json:"payment_type"`
}

func (p *PaymentMethod) Sanitize() *SimplePaymentMethod {
	return &SimplePaymentMethod{
		Model:         p.Model,
		UserID:        p.UserID,
		CourseID:      p.CourseID,
		OrganizeID:    p.OrganizeID,
		Type:          p.Type,
		Service:       p.Service,
		Account:       p.Account,
		AccountNumber: p.AccountNumber,
		AccountName:   p.AccountName,
		Network:       p.Network,
		Enable:        p.Enable,
		PaymentType:   p.PaymentType,
	}
}

func (query *PaymentMethodQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrganizeID != nil {
		qb = qb.Where("organize_id = ?", *query.OrganizeID)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.Service != nil {
		qb = qb.Where("service = ?", *query.Service)
	}

	if query.Account != nil {
		qb = qb.Where("account = ?", *query.Account)
	}

	if query.AccountNumber != nil {
		qb = qb.Where("account_number = ?", *query.AccountNumber)
	}

	if query.AccountName != nil {
		qb = qb.Where("account_name = ?", *query.AccountName)
	}

	if query.PaymentType != nil {
		qb = qb.Where("payment_type = ?", *query.PaymentType)
	}

	if query.Network != nil {
		qb = qb.Where("network = ?", *query.Network)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *PaymentMethodRepository) Create(p *PaymentMethod, trans *gorm.DB) error {
	return create(PaymentMethodTbl, p, trans)
}

func (r *PaymentMethodRepository) CreateMany(p []*PaymentMethod, trans *gorm.DB) error {
	return createMany(PaymentMethodTbl, p, trans)
}

func (r *PaymentMethodRepository) Update(p *PaymentMethod, trans *gorm.DB) error {
	return update(PaymentMethodTbl, p, trans)
}

func (r *PaymentMethodRepository) FindByID(id string, options *FindOneOptions) (*PaymentMethod, error) {
	return findByID[PaymentMethod](PaymentMethodTbl, id, options)
}

func (r *PaymentMethodRepository) FindOne(query *PaymentMethodQuery, options *FindOneOptions) (*PaymentMethod, error) {
	return findOne[PaymentMethod](PaymentMethodTbl, query, options)
}

func (r *PaymentMethodRepository) FindMany(query *PaymentMethodQuery, options *FindManyOptions) ([]*PaymentMethod, error) {
	return findMany[PaymentMethod](PaymentMethodTbl, query, options)
}

func (r *PaymentMethodRepository) FindPage(query *PaymentMethodQuery, options *FindPageOptions) ([]*PaymentMethod, *Pagination, error) {
	return findPage[PaymentMethod](PaymentMethodTbl, query, options)
}

func (r *PaymentMethodRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[PaymentMethod](PaymentMethodTbl, id, trans)
}

func (r *PaymentMethodRepository) DeleteMany(query *PaymentMethodQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[PaymentMethod](PaymentMethodTbl, query, trans)
}
