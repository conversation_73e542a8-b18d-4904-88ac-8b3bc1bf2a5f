package models

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CouponStatus string

const (
	CouponStatusNew     CouponStatus = "new"
	CouponStatusInUse   CouponStatus = "in-use"
	CouponStatusUsed    CouponStatus = "used"
	CouponStatusExpired CouponStatus = "expired"
)

type CouponHistory struct {
	Model
	UserID                string          `json:"user_id" gorm:"type:varchar(20)"`
	OrgID                 string          `json:"org_id" gorm:"type:varchar(20)"`
	CouponID              string          `json:"coupon_id" gorm:"type:varchar(20)"`
	DiscountAmount        decimal.Decimal `json:"discount_amount" gorm:"type:numeric(19,4);not null;default:0"`
	DiscountPercent       decimal.Decimal `json:"discount_percent" gorm:"type:numeric(19,4);not null;default:0"`
	CryptoDiscountAmount  decimal.Decimal `json:"crypto_discount_amount" gorm:"type:numeric(19,4);not null;default:0"`
	CryptoDiscountPercent decimal.Decimal `json:"crypto_discount_percent" gorm:"type:numeric(19,4);not null;default:0"`
	Status                CouponStatus    `json:"status"`
	OrderID               string          `json:"order_id" gorm:"type:varchar(20)"`
	Coupon                *Coupon         `json:"coupon"`
}

type SimpleCouponHistory struct {
	Model
	UserID               string          `json:"user_id"`
	DiscountAmount       decimal.Decimal `json:"discount_amount"`
	CryptoDiscountAmount decimal.Decimal `json:"crypto_discount_amount"`
	OrgID                string          `json:"org_id"`
	CouponID             string          `json:"coupon_id"`
	Status               CouponStatus    `json:"status"`
	OrderID              string          `json:"order_id"`
	Coupon               *Coupon         `json:"coupon"`
}

type CouponHistoryQuery struct {
	ID             *string        `json:"id" form:"id"`
	OrgID          *string        `json:"org_id" form:"org_id"`
	UserID         *string        `json:"user_id" form:"user_id"`
	OrgIDIn        []string       `json:"org_id_in" form:"org_id_in"`
	OrderID        *string        `json:"order_id" form:"order_id"`
	OrderIDIn      []string       `json:"order_id_in" form:"order_id_in"`
	CouponID       *string        `json:"coupon_id" form:"coupon_id"`
	Status         *CouponStatus  `json:"status" form:"status"`
	NotStatus      *CouponStatus  `json:"not_status" form:"not_status"`
	StatusIn       []CouponStatus `json:"status_in" form:"status"`
	StatusNotIn    []CouponStatus `json:"status_not_in" form:"status"`
	IncludeDeleted *bool          `json:"include_deleted" form:"include_deleted"`
	CreateAtLte    *int64         `json:"create_at_lte" form:"create_at_lte"`
}

func (ch *CouponHistory) ToSimple() *SimpleCouponHistory {
	return &SimpleCouponHistory{
		Model:                ch.Model,
		UserID:               ch.UserID,
		CouponID:             ch.CouponID,
		Status:               ch.Status,
		OrderID:              ch.OrderID,
		Coupon:               ch.Coupon,
		DiscountAmount:       ch.DiscountAmount,
		CryptoDiscountAmount: ch.CryptoDiscountAmount,
	}
}

func (query *CouponHistoryQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.OrderID != nil {
		qb = qb.Where("order_id = ?", *query.OrderID)
	}

	if query.OrderIDIn != nil {
		qb = qb.Where("order_id IN (?)", query.OrderIDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if len(query.OrgIDIn) > 0 {
		qb = qb.Where("org_id IN (?)", query.OrgIDIn)
	}

	if query.CouponID != nil {
		qb = qb.Where("coupon_id = ?", *query.CouponID)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.NotStatus != nil {
		qb = qb.Where("status != ?", *query.NotStatus)
	}

	if query.StatusIn != nil {
		qb = qb.Where("status IN (?)", *&query.StatusIn)
	}

	if query.StatusNotIn != nil {
		qb = qb.Where("status NOT IN (?)", *&query.StatusNotIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.CreateAtLte != nil {
		qb = qb.Where("create_at <= ?", *&query.CreateAtLte)
	}

	return qb
}

func (r *CouponHistoryRepository) Create(c *CouponHistory, trans *gorm.DB) error {
	return create(CouponHistoryTbl, c, trans)
}

func (r *CouponHistoryRepository) CreateMany(c []*CouponHistory, trans *gorm.DB) error {
	return createMany(CouponHistoryTbl, c, trans)
}

func (r *CouponHistoryRepository) Update(c *CouponHistory, trans *gorm.DB) error {
	return update(CouponHistoryTbl, c, trans)
}

func (r *CouponHistoryRepository) FindByID(id string, options *FindOneOptions) (*CouponHistory, error) {
	return findByID[CouponHistory](CouponHistoryTbl, id, options)
}

func (r *CouponHistoryRepository) FindOne(query *CouponHistoryQuery, options *FindOneOptions) (*CouponHistory, error) {
	return findOne[CouponHistory](CouponHistoryTbl, query, options)
}

func (r *CouponHistoryRepository) FindMany(query *CouponHistoryQuery, options *FindManyOptions) ([]*CouponHistory, error) {
	return findMany[CouponHistory](CouponHistoryTbl, query, options)
}

func (r *CouponHistoryRepository) FindPage(query *CouponHistoryQuery, options *FindPageOptions) ([]*CouponHistory, *Pagination, error) {
	return findPage[CouponHistory](CouponHistoryTbl, query, options)
}

func (r *CouponHistoryRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[CouponHistory](CouponHistoryTbl, id, trans)
}

func (r *CouponHistoryRepository) DeleteMany(query *CouponHistoryQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[CouponHistory](CouponHistoryTbl, query, trans)
}
