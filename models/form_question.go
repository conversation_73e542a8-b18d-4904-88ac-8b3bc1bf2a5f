package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type FormQuestionType string

type FormQuestionKey string

const (
	FormQuestionTypeHeading            FormQuestionType = "heading"
	FormQuestionTypeSpace              FormQuestionType = "space"
	FormQuestionTypeParagraph          FormQuestionType = "paragraph"
	FormQuestionTypeImage              FormQuestionType = "image"
	FormQuestionTypeInput              FormQuestionType = "input"
	FormQuestionTypeTextarea           FormQuestionType = "textarea"
	FormQuestionTypeNumber             FormQuestionType = "inputNumber"
	FormQuestionTypePhone              FormQuestionType = "inputPhoneNumber"
	FormQuestionTypeEmail              FormQuestionType = "email"
	FormQuestionTypeSelectBox          FormQuestionType = "selectbox"
	FormQuestionTypeAutoComplete       FormQuestionType = "autoComplete"
	FormQuestionTypeRadio              FormQuestionType = "radio"
	FormQuestionTypeMultipleChoice     FormQuestionType = "multipleChoice"
	FormQuestionTypeMultipleChoiceGrid FormQuestionType = "multipleChoiceGrid"
	FormQuestionTypeMultipleSelection  FormQuestionType = "multipleSelection"
	FormQuestionTypeCheckboxChoiceGrid FormQuestionType = "checkboxGrid"
	FormQuestionTypeCheckbox           FormQuestionType = "checkbox"
	FormQuestionTypeDateTimePicker     FormQuestionType = "datetimePicker"
	FormQuestionTypeSubmitBtn          FormQuestionType = "submitButton"

	FormQuestionTypeFile      FormQuestionType = "file"
	FormQuestionTypeSubDomain FormQuestionType = "sub_domain"

	FormQuestionKeyFullName        FormQuestionKey = "full_name"
	FormQuestionKeyFirstName       FormQuestionKey = "first_name"
	FormQuestionKeyLastName        FormQuestionKey = "last_name"
	FormQuestionKeyEmail           FormQuestionKey = "email"
	FormQuestionKeyPhone           FormQuestionKey = "phone"
	FormQuestionKeyCompanyName     FormQuestionKey = "company_name"
	FormQuestionKeyDomain          FormQuestionKey = "domain"
	FormQuestionKeyGoal            FormQuestionKey = "goal"
	FormQuestionKeyInterestDomains FormQuestionKey = "interest_domains"
	FormQuestionKeyAgeGroup        FormQuestionKey = "age_group"
	FormQuestionKeyJob             FormQuestionKey = "job"
	FormQuestionKeyProvince        FormQuestionKey = "province"
	FormQuestionKeySchool          FormQuestionKey = "school"
)

type FormQuestion struct {
	Model
	UID          string                `json:"uid" gorm:"type:varchar(20)"`
	ParentID     *string               `json:"parent_id"`
	FormID       string                `json:"form_id"`
	Title        string                `json:"title"`
	Description  string                `json:"description"`
	QuestionType FormQuestionType      `json:"question_type" gorm:"type:varchar(100)"`
	Order        int                   `json:"order"  gorm:"type:int8"`
	SubQuestions []*FormQuestion       `json:"sub_questions" gorm:"foreignKey:ParentID"`
	Options      []*FormQuestionOption `json:"options" gorm:"foreignKey:QuestionID"`
	Settings     FormQuestionSettings  `json:"settings" gorm:"type:jsonb"`
}

type FormQuestionSettings struct {
	IsDefault             bool            `json:"is_default"`
	Required              bool            `json:"required"`
	OtherOptionEnabled    bool            `json:"other_option_enabled"`
	OtherOptionLabel      string          `json:"other_option_label"`
	BaseDomain            string          `json:"base_domain"` // Display for question type `sub_domain`
	Key                   FormQuestionKey `json:"key,omitempty"`
	ValidateDomainEnabled bool            `json:"validate_domain_enabled"`
	Props                 JSONB           `json:"props" gorm:"type:jsonb"`
}

func (s FormQuestionSettings) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *FormQuestionSettings) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

func (q *FormQuestion) IsOptionBasedQuestion() bool {
	switch q.QuestionType {
	case FormQuestionTypeMultipleSelection,
		FormQuestionTypeRadio,
		FormQuestionTypeCheckboxChoiceGrid,
		FormQuestionTypeMultipleChoice,
		FormQuestionTypeMultipleChoiceGrid,
		FormQuestionTypeAutoComplete,
		FormQuestionTypeSelectBox:
		return true
	default:
		return false
	}
}

func (q *FormQuestion) IsSubQuestion() bool {
	return q.ParentID != nil && *q.ParentID != ""
}

func (q *FormQuestion) HasSubQuestions() bool {
	return len(q.SubQuestions) > 0
}

func (q *FormQuestion) IsRequired() bool {
	return q.Settings.Required
}

func (q *FormQuestion) IsTypeDropdown() bool {
	return q.QuestionType == FormQuestionTypeSelectBox
}

func (q *FormQuestion) IsTypeAutoComplete() bool {
	return q.QuestionType == FormQuestionTypeAutoComplete
}

func (q *FormQuestion) IsTypeMultipleChoice() bool {
	return q.QuestionType == FormQuestionTypeMultipleSelection
}

func (q *FormQuestion) IsTypeMultipleChoiceGrid() bool {
	return q.QuestionType == FormQuestionTypeCheckboxChoiceGrid
}

func (q *FormQuestion) IsTypeSingleChoice() bool {
	return q.QuestionType == FormQuestionTypeMultipleChoice
}

func (q *FormQuestion) IsTypeSingleChoiceGrid() bool {
	return q.QuestionType == FormQuestionTypeMultipleChoiceGrid
}

func (q *FormQuestion) IsTypeFile() bool {
	return q.QuestionType == FormQuestionTypeFile
}

func (q *FormQuestion) IsTypeSubDomain() bool {
	return q.QuestionType == FormQuestionTypeSubDomain
}

func (q *FormQuestion) CanEnableOtherOption() bool {
	switch q.QuestionType {
	case FormQuestionTypeMultipleSelection,
		FormQuestionTypeCheckboxChoiceGrid,
		FormQuestionTypeRadio,
		FormQuestionTypeMultipleChoice,
		FormQuestionTypeMultipleChoiceGrid:
		return true
	default:
		return false
	}
}

func (q *FormQuestion) IsKeyFullName() bool {
	return q.Settings.Key == FormQuestionKeyFullName
}

func (q *FormQuestion) IsKeyEmail() bool {
	return q.Settings.Key == FormQuestionKeyEmail
}

func (q *FormQuestion) IsKeyCompanyName() bool {
	return q.Settings.Key == FormQuestionKeyCompanyName
}

type SimpleFormQuestion struct {
	ID           string                      `json:"id"`
	UID          string                      `json:"uid"`
	Title        string                      `json:"title"`
	Description  string                      `json:"description"`
	QuestionType FormQuestionType            `json:"question_type"`
	SubQuestions []*FormQuestion             `json:"sub_questions"`
	Order        int                         `json:"order"`
	Options      []*SimpleFormQuestionOption `json:"options"`
	Settings     FormQuestionSettings        `json:"settings"  gorm:"type:jsonb"`
}

func (q *FormQuestion) Sanitize() *SimpleFormQuestion {
	return &SimpleFormQuestion{
		ID:           q.Model.ID,
		UID:          q.UID,
		Title:        q.Title,
		Description:  q.Description,
		QuestionType: q.QuestionType,
		SubQuestions: q.SubQuestions,
		Order:        q.Order,
		Options: lo.Map(q.Options, func(option *FormQuestionOption, _ int) *SimpleFormQuestionOption {
			return option.Sanitize()
		}),
		Settings: q.Settings,
	}
}

func (q *FormQuestion) IsOtherOptionEnabled() bool {
	return q.Settings.OtherOptionEnabled
}

type FormQuestionQuery struct {
	ID             *string  `json:"id,omitempty" form:"id"`
	UID            *string  `json:"uid,omitempty" form:"uid"`
	IDIn           []string `json:"id_in,omitempty" form:"id_in"`
	Type           *string  `json:"type,omitempty" form:"type"`
	FormID         *string  `json:"form_id,omitempty" form:"form_id"`
	IsTopLevel     *bool    `json:"is_parent,omitempty" form:"is_parent"`
	IncludeDeleted *bool    `json:"include_deleted,omitempty" form:"include_deleted"`
}

func (query *FormQuestionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UID != nil {
		qb = qb.Where("uid = ?", *query.UID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.FormID != nil {
		qb = qb.Where("form_id = ?", *query.FormID)
	}

	if query.IsTopLevel != nil {
		if *query.IsTopLevel {
			qb = qb.Where("parent_id is NULL")
		} else {
			qb = qb.Where("parent_id is NOT NULL")
		}
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *FormQuestionRepository) Create(question *FormQuestion, trans *gorm.DB) error {
	subQuestions := question.SubQuestions
	question.SubQuestions = nil
	if err := create(FormQuestionTbl, question, trans); err != nil {
		return err
	}

	for _, subQuestion := range subQuestions {
		subQuestion.ParentID = &question.ID
		subQuestion.FormID = question.FormID
		if subQuestion.UID == "" {
			subQuestion.UID = util.GenerateId()
		}
	}
	return createMany(FormQuestionTbl, subQuestions, trans)
}

func (r *FormQuestionRepository) CreateMany(questions []*FormQuestion, trans *gorm.DB) error {
	var nestedQuestionArr [][]*FormQuestion
	for _, question := range questions {
		nestedQuestionArr = append(nestedQuestionArr, question.SubQuestions)
		question.SubQuestions = nil
	}
	if err := createMany(FormQuestionTbl, questions, trans); err != nil {
		return err
	}
	var subQuestions []*FormQuestion
	for idx, question := range questions {
		for _, subQuestion := range nestedQuestionArr[idx] {
			subQuestion.ParentID = &question.ID
			subQuestion.FormID = question.FormID
			if subQuestion.UID == "" {
				subQuestion.UID = util.GenerateId()
			}
			subQuestions = append(subQuestions, subQuestion)
		}
	}
	return createMany(FormQuestionTbl, subQuestions, trans)
}

func (r *FormQuestionRepository) Update(question *FormQuestion, trans *gorm.DB) error {
	return update(FormQuestionTbl, question, trans)
}

func (r *FormQuestionRepository) FindByID(id string, options *FindOneOptions) (*FormQuestion, error) {
	return findByID[FormQuestion](FormQuestionTbl, id, options)
}

func (r *FormQuestionRepository) FindOne(query *FormQuestionQuery, options *FindOneOptions) (*FormQuestion, error) {
	return findOne[FormQuestion](FormQuestionTbl, query, options)
}

func (r *FormQuestionRepository) FindMany(query *FormQuestionQuery, options *FindManyOptions) ([]*FormQuestion, error) {
	return findMany[FormQuestion](FormQuestionTbl, query, options)
}

func (r *FormQuestionRepository) FindPage(query *FormQuestionQuery, options *FindPageOptions) ([]*FormQuestion, *Pagination, error) {
	return findPage[FormQuestion](FormQuestionTbl, query, options)
}

func (r *FormQuestionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[FormQuestion](FormQuestionTbl, id, trans)
}

func (r *FormQuestionRepository) DeleteMany(query *FormQuestionQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[FormQuestion](FormQuestionTbl, query, trans)
}
