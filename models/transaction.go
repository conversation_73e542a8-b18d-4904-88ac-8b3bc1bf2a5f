package models

import (
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
)

type TransactionType string
type TransactionStatus string

const (
	TransactionTypeDeposit            TransactionType = "deposit"
	TransactionTypeWithdraw           TransactionType = "withdraw"
	TransactionTypeClaim              TransactionType = "claim"
	TransactionTypeUsePoint           TransactionType = "use_point"
	TransactionTypeSale               TransactionType = "sale"
	TransactionTypeReferral           TransactionType = "referral"
	TransactionTypeBuy                TransactionType = "buy"
	TransactionTypeMintNFT            TransactionType = "mint_nft"
	TransactionTypeSaleEarn           TransactionType = "sale_earn"
	TransactionTypeReferralEarn       TransactionType = "referral_earn"
	TransactionTypeClaimEarning       TransactionType = "claim_earning"
	TransactionTypeRetroactive        TransactionType = "retroactive"
	TransactionTypeDepositSponsorGas  TransactionType = "deposit_sponsor_gas"
	TransactionTypeWithdrawSponsorGas TransactionType = "withdraw_sponsor_gas"
	TransactionTypeInitLaunchpadPool    TransactionType = "init_launchpad_pool"
	TransactionTypePledgeLaunchpad      TransactionType = "pledge_launchpad"
	TransactionTypeClaimLaunchpadRefund TransactionType = "claim_launchpad_refund"
	TransactionTypeLaunchpadProfitEarn  TransactionType = "launchpad_profit_earn"

	TransactionStatusPending TransactionStatus = "pending"
	TransactionStatusSuccess TransactionStatus = "success"
	TransactionStatusFailed  TransactionStatus = "failed"
)

type Transaction struct {
	UserID         string            `json:"user_id" gorm:"not null;type:varchar(20)"`
	WalletID       string            `json:"wallet_id" gorm:"not null;type:varchar(20)"`
	ToAddress      string            `json:"to_address"`
	Network        BlockchainNetwork `json:"network"`
	CurrencyType   AssetType         `json:"currency_type"`
	Currency       Currency          `json:"currency"`
	Amount         decimal.Decimal   `json:"amount" gorm:"type:numeric(19,4);not null;default:0"`
	Type           TransactionType   `json:"type"`
	Status         TransactionStatus `json:"status"`
	ErrorCode      int               `json:"error_code"`
	PaymentID      string            `json:"payment_id"`
	TxHash         string            `json:"tx_hash"`
	OrgID          string            `json:"org_id" gorm:"type:varchar(20)"`
	Data           JSONB             `json:"data" gorm:"type:jsonb"`
	EntityType     ModelName         `json:"entity_type"`
	EntityID       string            `json:"entity_id"`
	Note           string            `json:"note"`
	BlockchainTxID string            `json:"blockchain_tx_id"`
	Model

	Files []*File `json:"files" gorm:"-"`
}

func (t *Transaction) IsPending() bool {
	return t.Status == TransactionStatusPending
}

func (t *Transaction) IsSuccess() bool {
	return t.Status == TransactionStatusSuccess
}

func (t *Transaction) IsFailed() bool {
	return t.Status == TransactionStatusFailed
}

type TransactionPreload struct {
	Files bool
}

type TransactionQuery struct {
	ID           *string            `json:"id" form:"id"`
	UserID       *string            `json:"user_id" from:"user_id"`
	OrgID        *string            `json:"org_id" form:"org_id"`
	WalletID     *string            `json:"wallet_id" form:"wallet_id"`
	CurrencyType *AssetType         `json:"currency_type" form:"currency_type"`
	Currency     *Currency          `json:"currency" form:"currency"`
	Type         *TransactionType   `json:"type" form:"type"`
	Status       *TransactionStatus `json:"status" form:"status"`
	PaymentID    *string            `json:"payment_id" form:"payment_idd"`
	CreateAtLt   *int               `json:"create_at_lt" form:"create_at_lt"`
	CreateAtGt   *int               `json:"create_at_gt" form:"create_at_gt"`
	EntityID     *string            `json:"entity_id"`
	EntityType   *ModelName         `json:"entity_type"`
}

func (query *TransactionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.WalletID != nil {
		qb = qb.Where("wallet_id = ?", *query.WalletID)
	}

	if query.CurrencyType != nil {
		qb = qb.Where("currency_type = ?", *query.CurrencyType)
	}

	if query.Currency != nil {
		qb = qb.Where("currency = ?", *query.Currency)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.PaymentID != nil {
		qb = qb.Where("payment_id = ?", *query.PaymentID)
	}

	if query.CreateAtLt != nil {
		qb = qb.Where("create_at <= ?", *query.CreateAtLt)
	}

	if query.CreateAtGt != nil {
		qb = qb.Where("create_at >= ?", *query.CreateAtGt)
	}

	if query.EntityID != nil {
		qb = qb.Where("entity_id = ?", *query.EntityID)
	}

	if query.EntityType != nil {
		qb = qb.Where("entity_type = ?", *query.EntityType)
	}

	return qb
}

func (r *TransactionRepository) Create(t *Transaction, trans *gorm.DB) error {
	err := create(TransactionTbl, t, trans)
	if err != nil {
		return err
	}
	if t.Files != nil && len(t.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(TransactionModelName, t.ID, FilesField, t.Files); fErr != nil {
			return fErr
		}
	}
	return nil
}

func (r *TransactionRepository) CreateMany(txs []*Transaction, trans *gorm.DB) error {
	if err := createMany(TransactionTbl, txs, trans); err != nil {
		return err
	}
	for _, tx := range txs {
		if tx.Files != nil && len(tx.Files) > 0 {
			if fErr := Repository.FileRelation.AddFiles(TransactionModelName, tx.ID, FilesField, tx.Files); fErr != nil {
				return fErr
			}
		}
	}
	return nil
}

func (r *TransactionRepository) Update(t *Transaction, trans *gorm.DB) error {
	if err := update(TransactionTbl, t, trans); err != nil {
		return err
	}

	if t.Files != nil && len(t.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(TransactionModelName, t.ID, FilesField, t.Files); fErr != nil {
			return fErr
		}
	}

	if cErr := Cache.Transaction.DeleteByKey(t.ID); cErr != nil {
		log.Errorf("Delete transaction cache error: %v", cErr)
	}

	return nil
}

func (r *TransactionRepository) FindByID(id string, options *FindOneOptions) (*Transaction, error) {
	var txCache interface{}
	if err := Cache.Transaction.Get(id, &txCache); err == nil && txCache != nil {
		var tx Transaction
		if err = Cache.Convert(txCache, &tx); err == nil {
			return &tx, nil
		}
		log.Errorf("Convert transaction cache error: %v", err)
	}

	t, err := findByID[Transaction](TransactionTbl, id, options)
	if err != nil {
		return nil, err
	}

	if files, mErr := Repository.FileRelation.GetFiles(TransactionModelName, t.ID, FilesField); mErr != nil {
		return t, mErr
	} else {
		t.Files = files
	}

	if cErr := Cache.Transaction.Set(id, t); cErr != nil {
		log.Errorf("Set transaction cache error: %v", cErr)
	}

	return t, nil
}

func (r *TransactionRepository) FindOne(query *TransactionQuery, options *FindOneOptions) (*Transaction, error) {
	t, err := findOne[Transaction](TransactionTbl, query, options)
	if err != nil {
		return nil, err
	}

	if files, mErr := Repository.FileRelation.GetFiles(TransactionModelName, t.ID, FilesField); mErr != nil {
		return t, mErr
	} else {
		t.Files = files
	}
	return t, nil
}

func (r *TransactionRepository) getExPreload(preloads []string) (*TransactionPreload, []string) {
	shouldPreloadFiles := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, FilesField) {
		shouldPreloadFiles = true
		newPreloads = util.RemoveElement(newPreloads, FilesField)
	}

	return &TransactionPreload{
		Files: shouldPreloadFiles,
	}, newPreloads
}

func (r *TransactionRepository) preloadFiles(txs []*Transaction) error {
	txIDs := lo.Map(txs, func(tx *Transaction, _ int) string {
		return tx.ID
	})
	if filesByTxIDs, mErr := Repository.FileRelation.GetFilesByEntities(TransactionModelName, txIDs, FilesField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(txs, func(tx *Transaction, _ int) {
			tx.Files = filesByTxIDs[tx.ID]
		})
	}
	return nil
}

func (r *TransactionRepository) FindPage(query *TransactionQuery, options *FindPageOptions) ([]*Transaction, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	txs, pagination, err := findPage[Transaction](TransactionTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles(txs); fErr != nil {
			return txs, pagination, fErr
		}
	}
	return txs, pagination, nil
}

func (r *TransactionRepository) FindMany(query *TransactionQuery, options *FindManyOptions) ([]*Transaction, error) {
	if options == nil {
		options = &FindManyOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	txs, err := findMany[Transaction](TransactionTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles(txs); fErr != nil {
			return txs, fErr
		}
	}
	return txs, nil
}

func (r *TransactionRepository) Count(query *TransactionQuery) (int64, error) {
	return count[Transaction](TransactionTbl, query)
}

func (r *TransactionRepository) CreateAndUpdateBalance(transaction *Transaction, shouldUpdateAvailableBalance bool) error {
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create transaction
	if err := tx.Table(GetTblName(TransactionTbl)).Debug().Create(&transaction).Error; err != nil {
		tx.Rollback()
		return err
	}

	if transaction.Files != nil && len(transaction.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(TransactionModelName, transaction.ID, FilesField, transaction.Files); fErr != nil {
			tx.Rollback()
			return fErr
		}
	}

	if transaction.Status == TransactionStatusSuccess {
		// Crypto wallets do not save balance, so we don't need to update balance
		if transaction.CurrencyType == AssetTypeCrypto {
			return tx.Commit().Error
		}

		// Lock the wallet until balance updated
		var wall Wallet
		if err := tx.Table(GetTblName(WalletTbl)).Debug().
			Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("id = ? AND delete_at = 0", transaction.WalletID).
			First(&wall).Error; err != nil {
			tx.Rollback()
			return err
		}

		// Update wallet balance
		if !shouldUpdateAvailableBalance {
			if err := tx.Table(GetTblName(WalletTbl)).Debug().
				Where("id = ?", transaction.WalletID).
				Update("balance", gorm.Expr("balance + ?", transaction.Amount)).Error; err != nil {
				tx.Rollback()
				return err
			}
		} else {
			if err := tx.Table(GetTblName(WalletTbl)).Debug().
				Where("id = ?", transaction.WalletID).
				Updates(map[string]interface{}{
					"balance":           gorm.Expr("balance + ?", transaction.Amount),
					"available_balance": gorm.Expr("available_balance + ?", transaction.Amount),
				}).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
		Cache.Wallet.DeleteByUserID(transaction.UserID)
	}

	return tx.Commit().Error
}

func (r *TransactionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Transaction](TransactionTbl, id, trans)
}
