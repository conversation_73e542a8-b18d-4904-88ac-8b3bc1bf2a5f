package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"github.com/shopspring/decimal"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type Certificate struct {
	Model
	UserID      string            `json:"user_id" gorm:"not null;type:varchar(20)"`
	CourseCuid  string            `json:"course_cuid" gorm:"not null,type:varchar(20)"`
	CourseName  string            `json:"course_name"`
	CompletedAt int               `json:"completed_at"`
	OrgID       string            `json:"org_id" gorm:"not null"`
	OrgSchema   string            `json:"org_schema"`
	NftTokenID  string            `json:"nft_token_id"`
	NftTxHash   string            `json:"nft_tx_hash"`
	NftNetwork  BlockchainNetwork `json:"nft_network,omitempty"`
	Props       CertificateProps  `json:"props" gorm:"type:jsonb"`

	Image *File   `json:"image" gorm:"-"`
	Files []*File `json:"files" gorm:"-"`
}

type CertificateProps struct {
	NftStorageCost  decimal.Decimal `json:"nft_storage_cost"`
	NftGasCost      decimal.Decimal `json:"nft_gas_cost"`
	NftTotalGasCost decimal.Decimal `json:"nft_total_gas_cost"`
	NftMintAt       int64           `json:"nft_mint_at"`
	GasFeePayer     Participant     `json:"gas_fee_payer"`
}

func (p CertificateProps) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (p *CertificateProps) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, p)
}

func (c *Certificate) GetImageURL() string {
	return setting.ServerSetting.HostDomain + "/certificates/" + c.ID + "/image"
}

func (c *Certificate) IsMintedNFT() bool {
	return c.NftTxHash != ""
}

type CertificateQuery struct {
	ID             *string  `form:"id" json:"id,omitempty"`
	IDIn           []string `form:"id_in"`
	UserID         *string  `form:"user_id" json:"user_id"`
	CourseCuid     *string  `form:"course_cuid" json:"course_cuid"`
	OrgID          *string  `form:"org_id" json:"org_id"`
	IncludeDeleted *bool
}

type CertificatePreload struct {
	Files bool
	Image bool
}

func (query *CertificateQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *query.CourseCuid)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *CertificateRepository) Create(cert *Certificate, trans *gorm.DB) error {
	if err := create(CertificateTbl, cert, trans); err != nil {
		return err
	}

	if cert.Files != nil && len(cert.Files) > 0 {
		if err := Repository.FileRelation.AddFiles(CertificateModelName, cert.ID, FilesField, cert.Files); err != nil {
			return err
		}
	}

	if cert.Image != nil {
		if err := Repository.FileRelation.AddFiles(CertificateModelName, cert.ID, ImageField, []*File{cert.Image}); err != nil {
			return err
		}
	}
	return nil
}

func (r *CertificateRepository) Update(cert *Certificate, trans *gorm.DB) error {
	if err := update(CertificateTbl, cert, trans); err != nil {
		return err
	}

	if cert.Files != nil && len(cert.Files) > 0 {
		if err := Repository.FileRelation.AddFiles(CertificateModelName, cert.ID, FilesField, cert.Files); err != nil {
			return err
		}
	}

	if cert.Image != nil {
		if err := Repository.FileRelation.AddFiles(CertificateModelName, cert.ID, ImageField, []*File{cert.Image}); err != nil {
			return err
		}
	}
	return nil
}

func (r *CertificateRepository) getExPreload(preloads []string) (*CertificatePreload, []string) {
	shouldPreloadFiles := false
	shouldPreloadImage := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, FilesField) {
		shouldPreloadFiles = true
		newPreloads = util.RemoveElement(newPreloads, FilesField)
	}

	if lo.Contains(newPreloads, ImageField) {
		shouldPreloadImage = true
		newPreloads = util.RemoveElement(newPreloads, ImageField)
	}

	return &CertificatePreload{
		Files: shouldPreloadFiles,
		Image: shouldPreloadImage,
	}, newPreloads
}

func (r *CertificateRepository) preloadImage(certs []*Certificate) error {
	if len(certs) == 0 {
		return nil
	}
	certIDs := lo.Map(certs, func(cert *Certificate, _ int) string {
		return cert.ID
	})
	if imagesByCertIDs, mErr := Repository.FileRelation.GetFilesByEntities(CertificateModelName, certIDs, ImageField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(certs, func(cert *Certificate, _ int) {
			if images, found := imagesByCertIDs[cert.ID]; found {
				cert.Image = images[0]
			}
		})
	}
	return nil
}

func (r *CertificateRepository) preloadFiles(certs []*Certificate) error {
	if len(certs) == 0 {
		return nil
	}
	certIDs := lo.Map(certs, func(cert *Certificate, _ int) string {
		return cert.ID
	})
	if filesByCertIDs, mErr := Repository.FileRelation.GetFilesByEntities(CertificateModelName, certIDs, FilesField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(certs, func(cert *Certificate, _ int) {
			cert.Files = filesByCertIDs[cert.ID]
		})
	}
	return nil
}

// FindOne finds one Certificate with given find queries and options, transaction is optional
func (r *CertificateRepository) FindOne(query *CertificateQuery, options *FindOneOptions) (*Certificate, error) {
	if options == nil {
		options = &FindOneOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	certificate, err := findOne[Certificate](CertificateTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles([]*Certificate{certificate}); fErr != nil {
			return certificate, fErr
		}
	}

	if exPreload.Image {
		if fErr := r.preloadImage([]*Certificate{certificate}); fErr != nil {
			return certificate, fErr
		}
	}

	return certificate, nil
}

func (r *CertificateRepository) FindPage(query *CertificateQuery, options *FindPageOptions) ([]*Certificate, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	certificates, pagination, err := findPage[Certificate](CertificateTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles(certificates); fErr != nil {
			return certificates, pagination, fErr
		}
	}

	if exPreload.Image {
		if fErr := r.preloadImage(certificates); fErr != nil {
			return certificates, pagination, fErr
		}
	}
	return certificates, pagination, err
}

func (r *CertificateRepository) FindMany(query *CertificateQuery, options *FindManyOptions) ([]*Certificate, error) {
	if options == nil {
		options = &FindManyOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	certificates, err := findMany[Certificate](CertificateTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Files {
		if fErr := r.preloadFiles(certificates); fErr != nil {
			return certificates, fErr
		}
	}

	if exPreload.Image {
		if fErr := r.preloadImage(certificates); fErr != nil {
			return certificates, fErr
		}
	}
	return certificates, err
}

func (r *CertificateRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Certificate](CertificateTbl, id, trans)
}
