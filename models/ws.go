package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

type MessageType string
type Channel string

const (
	PaymentMessage MessageType = "Payment"

	UserChannel Channel = "/users/"
)

type WsMessage struct {
	DataType  MessageType     `json:"data_type,omitempty"`
	DataID    string          `json:"data_id,omitempty"`
	Data      json.RawMessage `json:"data,omitempty"`
	TimeStamp time.Time       `json:"time_stamp,omitempty"`
}

func NewWsMessage(wsType MessageType, dataID string, data json.RawMessage) *WsMessage {
	return &WsMessage{DataType: wsType, DataID: dataID, Data: data, TimeStamp: time.Now()}
}

func (m WsMessage) Value() (driver.Value, error) {
	val, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (m *WsMessage) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	err := json.Unmarshal(b, &m)
	if err != nil {
		return err
	}

	return nil
}
