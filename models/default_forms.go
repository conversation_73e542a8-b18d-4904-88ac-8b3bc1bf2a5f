package models

import (
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

func GetDefaultQuestionsForRegisterOrgForm() []*FormQuestion {
	return []*FormQuestion{
		{
			Title:        "Full name",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyFullName,
			},
		},
		{
			Title:        "Email",
			QuestionType: FormQuestionTypeEmail,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyEmail,
			},
		},
		{
			Title:        "Phone",
			QuestionType: FormQuestionTypePhone,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyPhone,
			},
		},
		{
			Title:        "Company name",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyCompanyName,
			},
		},
		{
			Title:        "Domain",
			QuestionType: FormQuestionTypeSubDomain,
			Settings: FormQuestionSettings{
				IsDefault:             true,
				Required:              true,
				OtherOptionEnabled:    false,
				BaseDomain:            setting.AppSetting.BaseDomain,
				Key:                   FormQuestionKeyDomain,
				ValidateDomainEnabled: true,
			},
		},
	}
}

func GetDefaultQuestionsForRegisterCreatorForm() []*FormQuestion {
	return []*FormQuestion{
		{
			Title:        "Full name",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyFullName,
			},
		},
		{
			Title:        "Email",
			QuestionType: FormQuestionTypeEmail,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyEmail,
			},
		},
		{
			Title:        "Phone",
			QuestionType: FormQuestionTypePhone,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyPhone,
			},
		},
	}
}

func GetDefaultQuestionsForRegisterWriterForm() []*FormQuestion {
	return []*FormQuestion{
		{
			Title:        "Full name",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyFullName,
			},
		},
		{
			Title:        "Email",
			QuestionType: FormQuestionTypeEmail,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyEmail,
			},
		},
		{
			Title:        "Phone",
			QuestionType: FormQuestionTypePhone,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyPhone,
			},
		},
	}
}

func GetDefaultQuestionsForContactOrgForm() []*FormQuestion {
	return []*FormQuestion{
		{
			Title:        "First name",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyFirstName,
			},
		},
		{
			Title:        "Last name",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyLastName,
			},
		},
		{
			Title:        "Email",
			QuestionType: FormQuestionTypeEmail,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyEmail,
			},
		},
	}
}

func GetDefaultQuestionsForRegisterCourseForm() []*FormQuestion {
	return []*FormQuestion{
		{
			Title:        "Full name",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyFullName,
			},
		},
		{
			Title:        "Email",
			QuestionType: FormQuestionTypeEmail,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyEmail,
			},
		},
		{
			Title:        "Phone",
			QuestionType: FormQuestionTypePhone,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyPhone,
			},
		},
	}
}

func GetDefaultQuestionsForNewUserForm() []*FormQuestion {
	return []*FormQuestion{
		{
			Title:        "Chào mừng bạn đến với OpenEdu",
			QuestionType: FormQuestionTypeHeading,
			Settings: FormQuestionSettings{
				IsDefault:          false,
				Required:           false,
				OtherOptionEnabled: false,
				Props: JSONB{
					"align":       "center",
					"fieldId":     "heading-1042683837649",
					"fieldType":   "heading",
					"headingType": "h3",
					"label":       "Chào mừng bạn đến với OpenEdu",
					"name":        "heading-1298085169962",
				},
			},
		},
		//{
		//	Title:        "Bạn hãy trả lời một số câu hỏi sau để chúng tôi hiểu rõ về bạn hơn nhé!",
		//	QuestionType: FormQuestionTypeParagraph,
		//	Settings: FormQuestionSettings{
		//		IsDefault:          false,
		//		Required:           false,
		//		OtherOptionEnabled: false,
		//		Props: JSONB{
		//			"align":     "center",
		//			"fieldId":   "paragraph-30158513793",
		//			"fieldType": "paragraph",
		//			"label":     "Bạn hãy trả lời một số câu hỏi sau để chúng tôi hiểu rõ về bạn hơn nhé!",
		//			"name":      "paragraph-1313984051173",
		//		},
		//	},
		//},
		{
			Title:        "Image",
			QuestionType: FormQuestionTypeImage,
			Settings: FormQuestionSettings{
				IsDefault:          false,
				Required:           false,
				OtherOptionEnabled: false,
				Props: JSONB{
					"align":           "start",
					"alt":             "image",
					"aspectRatio":     "1:1",
					"backgroundImage": true,
					"containerHeight": 150,
					"fieldId":         "image-244175712408",
					"fieldType":       "image",
					"label":           "Image",
					"name":            "image-1010738966248",
					"quality":         85,
					"src":             "https://s3.ap-southeast-1.amazonaws.com/openedu.net-production/images/8g2Ru2LBmJpb9r4e_X8k7gE4mNRw3noZb_Group-1261153056.png",
					"objectFit":       "contain",
				},
			},
		},
		{
			Title:        "Để hỗ trợ tốt nhất cho mục tiêu học & trải nghiệm của bạn, hãy lựa chọn các mong muốn phù hợp sau đây:",
			QuestionType: FormQuestionTypeMultipleSelection,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: true,
				OtherOptionLabel:   "Mục tiêu khác",
				Key:                FormQuestionKeyGoal,
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "multiple-selection-817600234546",
					"fieldType":   "multipleSelection",
					"hour12":      false,
					"infoText":    "",
					"label":       "Để hỗ trợ tốt nhất cho mục tiêu học & trải nghiệm của bạn, hãy lựa chọn các mong muốn phù hợp sau đây:",
					"locale":      "en",
					"max":         100,
					"min":         1,
					"name":        "multipleSelection-710988292557",
					"options": []map[string]any{
						{
							"id":    "596424428116",
							"label": "Học để hỗ trợ công việc hiện tại",
							"value": "value-1",
						},
						{
							"id":    18345429364,
							"label": "Học để thăng cấp & tăng lương",
							"value": "value-2",
						},
						{
							"id":    456762303668,
							"label": "Học để chuyển ngành mới",
							"value": "value-3",
						},
						{
							"id":    1400071479298,
							"label": "Sử dụng các Model AI & AI Agent của OpenEdu",
							"value": "value-4",
						},
						{
							"id":    1233616422602,
							"label": "Xây dựng các khoá học để kinh doanh",
							"value": "value-5",
						},
						{
							"id":    62702667971,
							"label": "Chia sẻ các khoá học để tăng thu nhập.",
							"value": "value-6",
						},
					},
					"otherOption": true,
					"placeholder": "Khác",
					"required":    true,
					"rowIndex":    0,
					"step":        1,
				},
			},
			Options: []*FormQuestionOption{
				{
					Text: "Học để hỗ trợ công việc hiện tại",
				},
				{
					Text: "Học để thăng cấp & tăng lương",
				},
				{
					Text: "Học để chuyển ngành mới",
				},
				{
					Text: "Sử dụng các Model AI & AI Agent của OpenEdu",
				},
				{
					Text: "Xây dựng các khoá học để kinh doanh",
				},
				{
					Text: "Chia sẻ các khoá học để tăng thu nhập.",
				},
			},
		},
		{
			Title:        "Các lĩnh vực bạn đang quan tâm để học hoặc để xây dựng khoá học là gì ?",
			QuestionType: FormQuestionTypeMultipleSelection,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				OtherOptionEnabled: true,
				OtherOptionLabel:   "Các lĩnh vực khác:",
				Key:                FormQuestionKeyInterestDomains,
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "multiple-selection-475815813191",
					"fieldType":   "multipleSelection",
					"hour12":      false,
					"infoText":    "",
					"label":       "Các lĩnh vực bạn đang quan tâm để học hoặc để xây dựng khoá học là gì ?",
					"locale":      "en",
					"max":         100,
					"min":         1,
					"name":        "multipleSelection-1175649723118",
					"options": []map[string]any{
						{
							"id":    "1498485398403",
							"label": "Tài chính",
							"value": "value-1",
						},
						{
							"id":    631240954313,
							"label": "Công nghệ thông tin",
							"value": "value-2",
						},
						{
							"id":    1668676209837,
							"label": "Marketing & bán hàng",
							"value": "value-3",
						},
						{
							"id":    1102621107115,
							"label": "Quản trị & kinh doanh",
							"value": "value-4",
						},
						{
							"id":    1706220719523,
							"label": "Thiết kế & nghệ thuật",
							"value": "value-5",
						},
						{
							"id":    873281630844,
							"label": "Tâm lý & xã hội",
							"value": "value-6",
						},
						{
							"id":    795988329884,
							"label": "Âm nhạc",
							"value": "value-7",
						},
						{
							"id":    1455150691522,
							"label": "Các kỹ năng mềm trong công việc",
							"value": "value-8",
						},
					},
					"otherOption": true,
					"placeholder": "Multiple Selection...",
					"required":    true,
					"rowIndex":    0,
					"step":        1,
				},
			},
			Options: []*FormQuestionOption{
				{
					Text: "Tài chính",
				},
				{
					Text: "Công nghệ thông tin",
				},
				{
					Text: "Marketing & bán hàng",
				},
				{
					Text: "Quản trị & kinh doanh",
				},
				{
					Text: "Thiết kế & nghệ thuật",
				},
				{
					Text: "Tâm lý & xã hội",
				},
				{
					Text: "Âm nhạc",
				},
				{
					Text: "Các kỹ năng mềm trong công việc",
				},
			},
		},
		{
			Title:        "Hãy chọn nhóm độ tuổi của bạn để nhận được các ưu đãi & quà tặng phù hợp từ OpenEdu",
			QuestionType: FormQuestionTypeMultipleChoice,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           false,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeyAgeGroup,
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "selectbox-207137915906",
					"fieldType":   "selectbox",
					"hour12":      false,
					"infoText":    "",
					"label":       "Hãy chọn nhóm độ tuổi của bạn để nhận được các ưu đãi & quà tặng phù hợp từ OpenEdu",
					"locale":      "en",
					"max":         100,
					"min":         1,
					"name":        "selectbox-865878673300",
					"options": []map[string]any{
						{
							"id":    "1159589499548",
							"label": "Dưới 18 tuổi",
							"value": "value-1",
						},
						{
							"id":    1128359666237,
							"label": "Từ 18 - 24 ",
							"value": "value-2",
						},
						{
							"id":    650401506436,
							"label": "Từ 25 - 35",
							"value": "value-3",
						},
						{
							"id":    1183009731250,
							"label": "Từ 36 - 45",
							"value": "value-4",
						},
						{
							"id":    1642847800185,
							"label": "Trên 45 tuổi.",
							"value": "value-5",
						},
					},
					"placeholder": "Độ tuổi của bạn là?",
					"required":    false,
					"rowIndex":    0,
					"step":        1,
					"value":       "",
				},
			},
			Options: []*FormQuestionOption{
				{
					Text: "Dưới 18 tuổi",
				},
				{
					Text: "Từ 18 - 24 ",
				},
				{
					Text: "Từ 25 - 35",
				},
				{
					Text: "Từ 36 - 45",
				},
				{
					Text: "Trên 45 tuổi",
				},
			},
		},
		{
			Title:        "Hoàn thành!",
			QuestionType: FormQuestionTypeSubmitBtn,
			Settings: FormQuestionSettings{
				IsDefault:          false,
				Required:           false,
				OtherOptionEnabled: false,
				Props: JSONB{
					"align":     "end",
					"fieldId":   "submit-button-932936562246",
					"fieldType": "submitButton",
					"label":     "Hoàn thành!",
					"name":      "submit-button-1221104512650",
				},
			},
		},
	}
}

func GetDefaultQuestionsForAIGovernmentForm() []*FormQuestion {
	return []*FormQuestion{
		//{
		//	Title:        "Chào mừng bạn đến với OpenEdu",
		//	QuestionType: FormQuestionTypeHeading,
		//	Settings: FormQuestionSettings{
		//		IsDefault:          false,
		//		Required:           false,
		//		OtherOptionEnabled: false,
		//		Props: JSONB{
		//			"align":       "center",
		//			"fieldId":     "heading-1042683837649",
		//			"fieldType":   "heading",
		//			"headingType": "h3",
		//			"label":       "Chào mừng bạn đến với OpenEdu",
		//			"name":        "heading-1298085169962",
		//		},
		//	},
		//},
		{
			Title:        "Vui lòng điền chính xác thông tin để được cấp chứng chỉ sau khi hoàn thành khóa học.",
			QuestionType: FormQuestionTypeParagraph,
			Settings: FormQuestionSettings{
				IsDefault:          false,
				Required:           false,
				OtherOptionEnabled: false,
				Props: JSONB{
					"align":     "center",
					"fieldId":   "paragraph-30158513793",
					"fieldType": "paragraph",
					"label":     "Vui lòng điền chính xác thông tin để được cấp chứng chỉ sau khi hoàn thành khóa học.",
					"name":      "paragraph-1313984051173",
				},
			},
		},
		{
			Title:        "Họ & Tên:",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				Key:                FormQuestionKeyFullName,
				OtherOptionEnabled: false,
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "input-195289000245",
					"fieldType":   "input",
					"hour12":      false,
					"infoText":    "",
					"label":       "Họ & Tên:",
					"locale":      "en",
					"max":         100,
					"min":         1,
					"name":        "input-928158496603",
					"placeholder": "Enter input",
					"required":    true,
					"rowIndex":    0,
					"step":        1,
					"value":       "",
				},
			},
		},
		{
			Title:        "Công việc hiện tại của bạn:",
			QuestionType: FormQuestionTypeRadio,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				Key:                FormQuestionKeyJob,
				OtherOptionEnabled: true,
				OtherOptionLabel:   "Khác:",
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "radio-35970119726",
					"fieldType":   "radio",
					"hour12":      false,
					"infoText":    "",
					"label":       "Công việc hiện tại của bạn:",
					"locale":      "en",
					"max":         100,
					"min":         1,
					"name":        "radio-793754953147",
					"options": []any{
						map[string]any{
							"id":    1257651669912,
							"label": "Học sinh",
							"value": "value-1",
						},
						map[string]any{
							"id":    1285105941754,
							"label": "Sinh viên",
							"value": "value-2",
						},
						map[string]any{
							"id":    1380002968866,
							"label": "Nhân viên văn phòng",
							"value": "value-3",
						},
						map[string]any{
							"id":    1611756516047,
							"label": "Lao động tự do",
							"value": "value-4",
						},
						map[string]any{
							"id":    1370024050936,
							"label": "Chủ doanh nghiệp",
							"value": "value-5",
						},
					},
					"otherOption": true,
					"placeholder": "Multiple Selection...",
					"required":    true,
					"rowIndex":    0,
					"step":        1,
				},
			},
			Options: []*FormQuestionOption{
				{
					Text: "Học sinh",
				},
				{
					Text: "Sinh viên",
				},
				{
					Text: "Nhân viên văn phòng",
				},
				{
					Text: "Lao động tự do",
				},
				{
					Text: "Chủ doanh nghiệp",
				},
			},
		},
		{
			Title:        "Độ tuổi của bạn:",
			QuestionType: FormQuestionTypeRadio,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				Key:                FormQuestionKeyAgeGroup,
				OtherOptionEnabled: false,
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "radio-207137915906",
					"fieldType":   "radio",
					"hour12":      false,
					"infoText":    "",
					"label":       "Độ tuổi của bạn:",
					"locale":      "en",
					"max":         100,
					"min":         1,
					"name":        "radio-865878673300",
					"options": []map[string]any{
						{
							"id":    "1159589499548",
							"label": "Dưới 18",
							"value": "value-1",
						},
						{
							"id":    1128359666237,
							"label": "18 - 22",
							"value": "value-2",
						},
						{
							"id":    650401506436,
							"label": "23 - 30",
							"value": "value-3",
						},
						{
							"id":    1183009731250,
							"label": "31 - 40",
							"value": "value-4",
						},
						{
							"id":    1642847800185,
							"label": "Trên 40",
							"value": "value-5",
						},
					},
					"placeholder": "Độ tuổi của bạn là?",
					"required":    true,
					"rowIndex":    0,
					"step":        1,
					"value":       "",
				},
			},
			Options: []*FormQuestionOption{
				{
					Text: "Dưới 18",
				},
				{
					Text: "18 - 22",
				},
				{
					Text: "23 - 30",
				},
				{
					Text: "31 - 40",
				},
				{
					Text: "Trên 40",
				},
			},
		},
		{
			Title:        "Tỉnh thành bạn đang sinh sống:",
			QuestionType: FormQuestionTypeAutoComplete,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           true,
				Key:                FormQuestionKeyProvince,
				OtherOptionEnabled: false,
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "autoComplete-province",
					"fieldType":   "autoComplete",
					"hour12":      false,
					"infoText":    "",
					"label":       "Tỉnh thành bạn đang sinh sống:",
					"locale":      "vi",
					"max":         100,
					"min":         1,
					"name":        "autoComplete-province",
					"options":     util.DefaultProvinceVN(),
					"placeholder": "Chọn tỉnh thành bạn đang sinh sống",
					"required":    true,
					"rowIndex":    0,
					"step":        1,
					"value":       "",
				},
			},
			Options: []*FormQuestionOption{
				{
					Text: "An Giang",
				},
				{
					Text: "Bà Rịa - Vũng Tàu",
				},
				{
					Text: "Bạc Liêu",
				},
				{
					Text: "Bắc Giang",
				},
				{
					Text: "Bắc Kạn",
				},
				{
					Text: "Bắc Ninh",
				},
				{
					Text: "Bến Tre",
				},
				{
					Text: "Bình Định",
				},
				{
					Text: "Bình Dương",
				},
				{
					Text: "Bình Phước",
				},
				{
					Text: "Bình Thuận",
				},
				{
					Text: "Cà Mau",
				},
				{
					Text: "Thành phố Cần Thơ",
				},
				{
					Text: "Cao Bằng",
				},
				{
					Text: "Thành phố Đà Nẵng",
				},
				{
					Text: "Đắk Lắk",
				},
				{
					Text: "Đắk Nông",
				},
				{
					Text: "Điện Biên",
				},
				{
					Text: "Đồng Nai",
				},
				{
					Text: "Đồng Tháp",
				},
				{
					Text: "Gia Lai",
				},
				{
					Text: "Hà Giang",
				},
				{
					Text: "Hà Nam",
				},
				{
					Text: "Thành phố Hà Nội",
				},
				{
					Text: "Hà Tĩnh",
				},
				{
					Text: "Hải Dương",
				},
				{
					Text: "Thành phố Hải Phòng",
				},
				{
					Text: "Hậu Giang",
				},
				{
					Text: "Thành phố Hồ Chí Minh",
				},
				{
					Text: "Hoà Bình",
				},
				{
					Text: "Hưng Yên",
				},
				{
					Text: "Khánh Hòa",
				},
				{
					Text: "Kiên Giang",
				},
				{
					Text: "Kon Tum",
				},
				{
					Text: "Lai Châu",
				},
				{
					Text: "Lâm Đồng",
				},
				{
					Text: "Lạng Sơn",
				},
				{
					Text: "Lào Cai",
				},
				{
					Text: "Long An",
				},
				{
					Text: "Nam Định",
				},
				{
					Text: "Nghệ An",
				},
				{
					Text: "Ninh Bình",
				},
				{
					Text: "Ninh Thuận",
				},
				{
					Text: "Phú Thọ",
				},
				{
					Text: "Phú Yên",
				},
				{
					Text: "Quảng Bình",
				},
				{
					Text: "Quảng Nam",
				},
				{
					Text: "Quảng Ngãi",
				},
				{
					Text: "Quảng Ninh",
				},
				{
					Text: "Quảng Trị",
				},
				{
					Text: "Sóc Trăng",
				},
				{
					Text: "Sơn La",
				},
				{
					Text: "Tây Ninh",
				},
				{
					Text: "Thái Bình",
				},
				{
					Text: "Thái Nguyên",
				},
				{
					Text: "Thanh Hóa",
				},
				{
					Text: "Thừa Thiên Huế",
				},
				{
					Text: "Tiền Giang",
				},
				{
					Text: "Trà Vinh",
				},
				{
					Text: "Tuyên Quang",
				},
				{
					Text: "Vĩnh Long",
				},
				{
					Text: "Vĩnh Phúc",
				},
				{
					Text: "Yên Bái",
				},
			},
		},
		{
			Title:        "Nếu là Học sinh - Sinh viên, vui lòng điền đầy đủ tên trường bên dưới:",
			QuestionType: FormQuestionTypeInput,
			Settings: FormQuestionSettings{
				IsDefault:          true,
				Required:           false,
				OtherOptionEnabled: false,
				Key:                FormQuestionKeySchool,
				Props: JSONB{
					"checked":     false,
					"className":   "",
					"description": "",
					"disabled":    false,
					"fieldId":     "input-125289000245",
					"fieldType":   "input",
					"hour12":      false,
					"infoText":    "",
					"label":       "Nếu là Học sinh - Sinh viên, vui lòng điền đầy đủ tên trường bên dưới:",
					"locale":      "en",
					"max":         100,
					"min":         0,
					"name":        "input-938158496603",
					"placeholder": "Enter input",
					"required":    false,
					"rowIndex":    0,
					"step":        1,
					"value":       "",
				},
			},
		},
		{
			Title:        "Hoàn thành!",
			QuestionType: FormQuestionTypeSubmitBtn,
			Settings: FormQuestionSettings{
				IsDefault:          false,
				Required:           false,
				OtherOptionEnabled: false,
				Props: JSONB{
					"align":     "end",
					"fieldId":   "submit-button-932936562246",
					"fieldType": "submitButton",
					"label":     "Hoàn thành!",
					"name":      "submit-button-1221104512650",
				},
			},
		},
	}
}

func GetDefaultRegisterOrgForm() *Form {
	return &Form{
		Title:     FormTitleRegisterOrg,
		Event:     FormEventRegisterOrg,
		Type:      FormTypePage,
		Status:    FormStatusPublishedAll,
		Questions: GetDefaultQuestionsForRegisterOrgForm(),
	}
}

func GetDefaultRegisterCreatorForm() *Form {
	return &Form{
		Title:     FormTitleRegisterCreator,
		Event:     FormEventRegisterCreator,
		Type:      FormTypePage,
		Status:    FormStatusPublishedAll,
		Questions: GetDefaultQuestionsForRegisterCreatorForm(),
	}
}

func GetDefaultRegisterWriterForm() *Form {
	return &Form{
		Title:     FormTitleRegisterWriter,
		Event:     FormEventRegisterWriter,
		Type:      FormTypePage,
		Status:    FormStatusPublishedAll,
		Questions: GetDefaultQuestionsForRegisterWriterForm(),
	}
}

func GetDefaultContactOrgForm() *Form {
	return &Form{
		Title:     FormTitleContactOrg,
		Event:     FormEventContactOrg,
		Type:      FormTypePage,
		Status:    FormStatusPublishedAll,
		Questions: GetDefaultQuestionsForContactOrgForm(),
	}
}

func GetDefaultEmptyForm() *Form {
	return &Form{
		Title:     "",
		Event:     FormEventEmpty,
		Type:      FormTypePage,
		Status:    FormStatusPublishedAll,
		Questions: []*FormQuestion{},
	}
}

func GetDefaultNewUserForm() *Form {
	return &Form{
		Title:     FormTitleNewUserForm,
		Event:     FormEventNewUser,
		Type:      FormTypeSlide,
		Status:    FormStatusPublishedAll,
		Questions: GetDefaultQuestionsForNewUserForm(),
	}
}

func GetDefaultAIGovernmentForm() *Form {
	return &Form{
		Title:     FormTitleAIGovernment,
		Event:     FormEventAIGovernment,
		Type:      FormTypePage,
		Status:    FormStatusPublishedAll,
		Questions: GetDefaultQuestionsForAIGovernmentForm(),
	}
}
