package models

import (
	"fmt"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Wallet struct {
	UserID             string            `json:"user_id" gorm:"not null;type:varchar(20)"`
	Balance            decimal.Decimal   `json:"balance" gorm:"type:numeric(19,4);not null;default:0"`
	AvailableBalance   decimal.Decimal   `json:"available_balance" gorm:"type:numeric(19,4);not null;default:0"`
	EarningBalance     decimal.Decimal   `json:"earning_balance" gorm:"type:numeric(19,4);not null;default:0"`
	Type               AssetType         `json:"type" gorm:"type:varchar(20);not null"`
	Currency           Currency          `json:"currency"`
	Default            bool              `json:"default"`
	Network            BlockchainNetwork `json:"network"`
	Address            string            `json:"address"`
	PublicKey          string            `json:"public_key"`
	BlockchainWalletID string            `json:"blockchain_wallet_id" gorm:"type:varchar(20)"`
	ParentID           string            `json:"parent_id" gorm:"type:varchar(20)"`
	Model
}

type SimpleWallet struct {
	UserID   string            `json:"user_id" gorm:"not null;type:varchar(20)"`
	Network  BlockchainNetwork `json:"network"`
	Address  string            `json:"address"`
	Currency Currency          `json:"currency"`
}

type WalletEarning struct {
	Address  string          `json:"address"`
	Currency Currency        `json:"currency"`
	Amount   decimal.Decimal `json:"balance"`
}

func (w *Wallet) GetUserID() string {
	return w.UserID
}

func (w *Wallet) GetID() string {
	return w.ID
}

func (w *Wallet) IsOwnedBy(user *User) bool {
	return user != nil && w.UserID == user.ID
}

func (w *Wallet) IsCrypto() bool {
	return w.Type == AssetTypeCrypto
}

type WalletQuery struct {
	ID             *string            `json:"id" form:"id"`
	IDIn           []string           `json:"id_in" form:"id_in"`
	UserIDIn       []string           `json:"user_id_in" form:"user_id_in"`
	ParentID       *string            `json:"parent_id" form:"parent_id"`
	UserID         *string            `json:"user_id" form:"user_id"`
	Address        *string            `json:"address" form:"address"`
	Currency       *Currency          `json:"currency" form:"currency"`
	Type           *AssetType         `json:"type" form:"type"`
	Network        *BlockchainNetwork `json:"network" form:"network"`
	Default        *bool              `json:"default" form:"default"`
	IncludeDeleted *bool              `json:"include_deleted" form:"include_deleted"`
}

func (query WalletQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if len(query.UserIDIn) > 0 {
		qb = qb.Where("user_id IN (?)", query.UserIDIn)
	}

	if query.Address != nil {
		qb = qb.Where("address = ?", *query.Address)
	}

	if query.ParentID != nil {
		qb = qb.Where("parent_id = ?", *query.ParentID)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.Currency != nil {
		qb = qb.Where("currency = ?", *query.Currency)
	}

	if query.Network != nil {
		qb = qb.Where("network = ?", *query.Network)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.Default != nil {
		qb = qb.Where(`"default" = ?`, *query.Default)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a wallet to database, transaction is optional
func (r *WalletRepository) Create(e *Wallet, trans *gorm.DB) error {
	if err := create(WalletTbl, e, trans); err != nil {
		return err
	}
	Cache.Wallet.DeleteByUserID(e.UserID)
	return nil
}

func (r *WalletRepository) CreateMany(wallets []*Wallet, trans *gorm.DB) error {
	if err := createMany(WalletTbl, wallets, trans); err != nil {
		return err
	}

	for _, w := range wallets {
		Cache.Wallet.DeleteByUserID(w.UserID)
	}

	return nil
}

// Update balance a wallet by ID in database, transaction is optional
// This function don't execute commit
//func (r *WalletRepository) UpdateBalance(walletID string, amount decimal.Decimal, tx *gorm.DB) error {
//	var wall Wallet
//	if err := tx.Table(GetTblName(WalletTbl)).Debug().
//		Clauses(clause.Locking{Strength: "UPDATE"}).
//		Where("id = ? AND delete_at = 0", walletID).
//		First(&wall).Error; err != nil {
//		return err
//	}
//
//	// Update wallet
//	if err := tx.Table(GetTblName(WalletTbl)).Debug().
//		Where("id = ?", walletID).
//		Update("balance", gorm.Expr("balance + ?", amount)).Error; err != nil {
//		tx.Rollback()
//		return err
//	}
//	return nil
//}

func (r *WalletRepository) IncreaseBalance(wallet *Wallet, amount decimal.Decimal, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	// Lock the wallet until balance updated
	var wall Wallet
	if err = tx.Table(GetTblName(WalletTbl)).Debug().
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ? AND delete_at = 0", wallet.ID).
		First(&wall).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update wallet balance
	if err = tx.Table(GetTblName(WalletTbl)).Debug().
		Where("id = ?", wallet.ID).
		Update("balance", gorm.Expr("balance + ?", amount)).Error; err != nil {
		tx.Rollback()
		return err
	}

	Cache.Wallet.DeleteByUserID(wallet.UserID)
	return
}

func (r *WalletRepository) IncreaseAvailableBalance(wallet *Wallet, amount decimal.Decimal, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	// Lock the wallet until balance updated
	var wall Wallet
	if err = tx.Table(GetTblName(WalletTbl)).Debug().
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ? AND delete_at = 0", wallet.ID).
		First(&wall).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update wallet balance
	if err = tx.Table(GetTblName(WalletTbl)).Debug().
		Where("id = ?", wallet.ID).
		Update("available_balance", gorm.Expr("available_balance + ?", amount)).Error; err != nil {
		tx.Rollback()
		return err
	}

	Cache.Wallet.DeleteByUserID(wallet.UserID)
	return
}

func (r *WalletRepository) Update(w *Wallet, trans *gorm.DB) error {
	return update(WalletTbl, w, trans)
}

// FindOne finds one wallet with given find queries and options, transaction is optional
func (r *WalletRepository) FindOne(query *WalletQuery, options *FindOneOptions) (*Wallet, error) {
	return findOne[Wallet](WalletTbl, query, options)
}

func (r *WalletRepository) FindByID(id string, options *FindOneOptions) (*Wallet, error) {
	return findByID[Wallet](WalletTbl, id, options)
}

// FindPage returns wallets and pagination by query conditions and find options, transaction is optional
func (r *WalletRepository) FindPage(query *WalletQuery, options *FindPageOptions) ([]*Wallet, *Pagination, error) {
	return findPage[Wallet](WalletTbl, query, options)
}

func (r *WalletRepository) FindMany(query *WalletQuery, options *FindManyOptions) ([]*Wallet, error) {
	return findMany[Wallet](WalletTbl, query, options)
}

// Count returns number of wallets by query conditions, transaction is optional
func (r *WalletRepository) Count(query *WalletQuery) (int64, error) {
	return count[Wallet](WalletTbl, query)
}

func (r *WalletRepository) FindManySimple(query *WalletQuery, options *FindManyOptions) ([]*SimpleWallet, error) {
	return findMany[SimpleWallet](WalletTbl, query, options)
}
