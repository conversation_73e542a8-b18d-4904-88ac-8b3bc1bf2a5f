package models

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"openedu-core/cache_clients"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"os"
	"reflect"
	"strings"

	"github.com/redis/go-redis/v9"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	_ "gorm.io/driver/postgres"

	"time"
)

var DB *gorm.DB
var Redis *redis.Client
var Repository *AppRepository
var Cache *cache_clients.Caching
var AppSchema = "vbi"
var PublicSchema = "public"
var Search *SearchBuilder

type Model struct {
	ID       string `gorm:"primaryKey;type:varchar(20);unique" json:"id" dbscan:"id" excel:"-"`
	CreateAt int    `gorm:"type:int8;not null;default:0" json:"create_at" dbscan:"create_at"`
	UpdateAt int    `gorm:"type:int8;not null;default:0" json:"update_at" dbscan:"update_at" excel:"-"`
	DeleteAt int    `gorm:"type:int8;not null;default:0" json:"delete_at" dbscan:"delete_at" excel:"-"`
}

func IsRecordNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

func IsSharedTable(table TableName) bool {
	for _, t := range SharedTables {
		if t == table {
			return true
		}
	}
	return false
}

func GetDb(tbl TableName) *gorm.DB {
	log.Printf("AppSchema: %s, IsSharedTable: %v\n", AppSchema, IsSharedTable(tbl))
	// IF shared table, the schema is 'public'
	if AppSchema == "" || IsSharedTable(tbl) {
		return DB.Table(PublicSchema + "." + setting.DatabaseSetting.TablePrefix + string(tbl))
	}
	// use org schema
	return DB.Table(AppSchema + "." + setting.DatabaseSetting.TablePrefix + string(tbl))
}

func GetTblName(tbl TableName) string {
	log.Printf("AppSchema: %s, IsSharedTable: %v\n", AppSchema, IsSharedTable(tbl))
	// IF shared table, the schema is 'public'
	if AppSchema == "" || IsSharedTable(tbl) {
		return PublicSchema + "." + setting.DatabaseSetting.TablePrefix + string(tbl)
	}
	// use org schema
	return AppSchema + "." + setting.DatabaseSetting.TablePrefix + string(tbl)
}

func GetTblNameWithSchema(schema string, tbl TableName) string {
	return schema + "." + setting.DatabaseSetting.TablePrefix + string(tbl)
}

// Setup initializes the database instance
func Setup() {
	var err error
	dsnConn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		setting.DatabaseSetting.Host,
		setting.DatabaseSetting.User,
		setting.DatabaseSetting.Password,
		setting.DatabaseSetting.Name,
		setting.DatabaseSetting.Port,
		setting.DatabaseSetting.SSLMode)
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             time.Second,            // Slow SQL threshold
			LogLevel:                  logger.Info,            // Log level
			IgnoreRecordNotFoundError: true,                   // Ignore ErrRecordNotFound error for logger
			ParameterizedQueries:      setting.IsProduction(), // Don't include params in the SQL log
			Colorful:                  true,                   // Disable color
		},
	)

	db, err := gorm.Open(postgres.New(postgres.Config{
		DSN:                  dsnConn,
		PreferSimpleProtocol: true, // disables implicit prepared statement usage
	}), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   setting.DatabaseSetting.TablePrefix, // table name prefix, table for `User` would be `t_users`
			SingularTable: false,                               // use singular table name, table for `User` would be `user` with this option enabled
			NoLowerCase:   false,                               // skip the snake_casing of names
			NameReplacer:  strings.NewReplacer("CID", "Cid"),   // use name replacer to change struct/field name before convert it to DB name
		},
		Logger: newLogger,

		//Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		log.Fatalf("models.Setup err: %v", err)
	}

	db.Callback().Create().Before("gorm:create").Register("before_create", beforeCreate)
	db.Callback().Update().Before("gorm:update").Register("before_update", beforeUpdate)
	sDB, err := db.DB()
	if err != nil {
		log.Fatalf("models.Setup get DB instance error: %v", err)
	}

	// TODO: consider changing when setup/upgrade/change DB instance
	sDB.SetMaxOpenConns(100) // calculate base on DB instance and number of server instance, (dbMaxConection - (15~20% buffer))/numberOfServer
	sDB.SetMaxIdleConns(25)  // MaxOpen/numberOfDbCPU
	sDB.SetConnMaxLifetime(time.Hour)
	sDB.SetConnMaxIdleTime(30 * time.Minute)
	DB = db
	Repository = &AppRepository{
		User:               &UserRepository{},
		Role:               &RoleRepository{},
		UserRoleOrg:        &UserRoleOrgRepository{},
		Permission:         &PermissionRepository{},
		SnsAccount:         &SnsAccountRepository{},
		System:             &SystemConfigRepository{},
		Session:            &SessionRepository{},
		File:               &FileRepository{},
		FileRelation:       &FileRelationRepository{},
		Organization:       &OrganizationRepository{},
		CoursePrice:        &CoursePriceRepository{},
		Payment:            &PaymentRepository{},
		PaymentMethod:      &PaymentMethodRepository{},
		Order:              &OrderRepository{},
		OrderItem:          &OrderItemRepository{},
		CoursePartner:      &CoursePartnerRepository{},
		UserToken:          &UserTokenRepository{},
		Section:            &SectionRepository{},
		Category:           &CategoryRepository{},
		CategoryRelation:   &CategoryRelationRepository{},
		Hashtag:            &HashtagRepository{},
		HashtagRelation:    &HashtagRelationRepository{},
		Coupon:             &CouponRepository{},
		CouponHistory:      &CouponHistoryRepository{},
		Form:               &FormRepository{},
		FormQuestion:       &FormQuestionRepository{},
		FormQuestionOption: &FormQuestionOptionRepository{},
		FormAnswer:         &FormAnswerRepository{},
		FormSession:        &FormSessionRepository{},
		FormRelation:       &FormRelationRepository{},
		FormAnswerStats:    &FormAnswerStatsRepository{},
		Blog:               &BlogRepository{},
		Approval:           &ApprovalRepository{},
		Team:               &TeamRepository{},
		PublishBlog:        &PublishBlogRepository{},
		UserAction:         &UserActionRepository{},
		PageConfig:         &PageConfigRepository{},
		PageAccess:         &PageAccessRepository{},
		Wallet:             &WalletRepository{},
		Transaction:        &TransactionRepository{},
		Quiz:               &QuizRepository{},
		QuizRelation:       &QuizRelationRepository{},
		QuizQuestion:       &QuizQuestionRepository{},
		QuizSubmission:     &QuizSubmissionRepository{},
		QuizAnswer:         &QuizAnswerRepository{},
		Bookmark:           &BookmarkRepository{},
		Certificate:        &CertificateRepository{},
		HtmlTemplate:       &HtmlTemplateRepository{},
		UserSetting:        &UserSettingRepository{},
		AffiliateCampaign:  &AffiliateCampaignRepository{},
		CourseCampaign:     &CourseCampaignRepository{},
		Commission:         &CommissionRepository{},
		Referrer:           &ReferrerRepository{},
		ReferralLink:       &ReferralLinkRepository{},
		Referral:           &ReferralRepository{},
		AIBlogRewrite:      &AIBlogRewriteRepository{},
		UserSummary:        &UserSummaryRepository{},
		Report:             &ReportRepository{},
		AICourse:           &AICourseRepository{},
		AIHistory:          &AIHistoryRepository{},
		PricingPlan:        &PricingPlanRepository{},
		Subscription:       &SubscriptionRepository{},
		ResourceUsage:      &ResourceUsageRepository{},
		AIModel:            &AIModelRepository{},
		FeaturedContent:    &FeaturedContentRepository{},
		ClpVotingMilestone: &ClpVotingMilestoneRepository{},
		ClpVotingPhase:     &ClpVotingPhaseRepository{},
		AIPrompt:           &AIPromptRepository{},
	}

	dbPrefix := setting.DatabaseSetting.TablePrefix
	// Setup Cache Model
	Cache = &cache_clients.Caching{
		User:               &cache_clients.UserCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + UserCachePrefix}},
		Permission:         &cache_clients.PermissionCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + PermissionCachePrefix}},
		System:             &cache_clients.SystemConfigCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + SystemConfigCachePrefix}},
		Organization:       &cache_clients.OrganizationCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + OrganizationCachePrefix}},
		Category:           &cache_clients.CategoryCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + CategoryCachePrefix}},
		UserRole:           &cache_clients.UserRoleCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + UserRoleCachePrefix}},
		Form:               &cache_clients.FormCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + FormCachePrefix}},
		PageAccess:         &cache_clients.PageAccessCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + PageAccessCachePrefix}},
		Wallet:             &cache_clients.WalletCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + WalletCachePrefix}},
		Quiz:               &cache_clients.QuizCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + QuizCachePrefix}},
		Blog:               &cache_clients.BlogCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + BlogCachePrefix}},
		CourseEnrollment:   &cache_clients.CourseEnrollmentCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + CourseEnrollmentCachePrefix}},
		Transaction:        &cache_clients.TransactionCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + TransactionCachePrefix}},
		WalletEarning:      &cache_clients.WalletEarningCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + WalletEarningCachePrefix}},
		PricingPlan:        &cache_clients.PricingPlanCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + PricingPlanCachePrefix}},
		FeaturedContent:    &cache_clients.FeaturedContentCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + FeaturedContentCachePrefix}},
		OEReferralCode:     &cache_clients.OEReferralCodeCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + OEReferralCodeCachePrefix}},
		OEReferralCampaign: &cache_clients.OEReferralCampaignCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + OEReferralCampaignCachePrefix}},
		PublishCourse:      &cache_clients.PublishCourseCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + PublishCourseCachePrefix}},
		LessonContent:      &cache_clients.LessonContentCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + LessonContentCachePrefix}},

		// Currency exchange rates should be used all ENVIRONMENTS to avoid rate limit
		ExchangeRate: &cache_clients.ExchangeRateCache{CacheModel: cache_clients.CacheModel{Prefix: ExchangeRateCachePrefix}},
		Course: &cache_clients.CourseCache{
			CacheModel: cache_clients.CacheModel{
				Prefix: dbPrefix + CourseCachePrefix,
			},
		},
		Subscription: &cache_clients.SubscriptionCache{CacheModel: cache_clients.CacheModel{Prefix: dbPrefix + SubscriptionCachePrefix}},
	}
	Search = NewSearchBuilder()

	// Init Redis instance
	Redis = redis.NewClient(&redis.Options{
		Addr:            setting.RedisSetting.Host,
		Password:        setting.RedisSetting.Password,
		DB:              0, // use default DB
		MaxIdleConns:    setting.RedisSetting.MaxIdle,
		MaxActiveConns:  setting.RedisSetting.MaxActive,
		ConnMaxIdleTime: setting.RedisSetting.IdleTimeout,
	})
	ctxRedis, cancelRedis := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelRedis()
	_, err = Redis.Ping(ctxRedis).Result()
	if err != nil {
		log.Fatalf("Failed to ping to Redis: %v", err)
	}
}

func beforeCreate(db *gorm.DB) {
	if db.Statement.Schema != nil {
		switch db.Statement.ReflectValue.Kind() {
		case reflect.Struct:
			rv := db.Statement.ReflectValue
			db.AddError(applyPrimaryKeyAndTimestampOnCreate(db, rv))

		case reflect.Slice, reflect.Array:
			for i := 0; i < db.Statement.ReflectValue.Len(); i++ {
				rv := db.Statement.ReflectValue.Index(i)
				if reflect.Indirect(rv).Kind() != reflect.Struct {
					break
				}

				db.AddError(applyPrimaryKeyAndTimestampOnCreate(db, rv))
			}
		}

		return
	}
}

func beforeUpdate(db *gorm.DB) {
	if db.Statement.Schema != nil {
		switch db.Statement.ReflectValue.Kind() {
		case reflect.Struct:
			rv := db.Statement.ReflectValue
			db.AddError(applyPrimaryKeyAndTimestampOnUpdate(db, rv))

		case reflect.Slice, reflect.Array:
			for i := 0; i < db.Statement.ReflectValue.Len(); i++ {
				rv := db.Statement.ReflectValue.Index(i)
				if reflect.Indirect(rv).Kind() != reflect.Struct {
					break
				}

				db.AddError(applyPrimaryKeyAndTimestampOnUpdate(db, rv))
			}
		}

		return
	}
}

func applyPrimaryKeyAndTimestampOnCreate(db *gorm.DB, rv reflect.Value) error {
	dbContext := db.Statement.Context

	if db.Statement.Schema.PrioritizedPrimaryField != nil {
		_, isZero := db.Statement.Schema.PrioritizedPrimaryField.ValueOf(dbContext, rv)
		if isZero {
			err := db.Statement.Schema.PrioritizedPrimaryField.Set(dbContext, rv, util.GenerateId())
			if err != nil {
				return err
			}
		}
	}

	createAtField := db.Statement.Schema.LookUpField("create_at")
	if createAtField != nil {
		err := createAtField.Set(dbContext, rv, time.Now().UnixMilli())
		if err != nil {
			return err
		}
	}

	updateAtField := db.Statement.Schema.LookUpField("update_at")
	if updateAtField != nil {
		err := updateAtField.Set(dbContext, rv, time.Now().UnixMilli())
		if err != nil {
			return err
		}
	}

	deleteAtField := db.Statement.Schema.LookUpField("delete_at")
	if deleteAtField != nil {
		err := deleteAtField.Set(dbContext, rv, 0)
		if err != nil {
			return err
		}
	}

	return nil
}

func applyPrimaryKeyAndTimestampOnUpdate(db *gorm.DB, rv reflect.Value) error {
	dbContext := db.Statement.Context

	updateAtField := db.Statement.Schema.LookUpField("update_at")
	if updateAtField != nil {
		err := updateAtField.Set(dbContext, rv, time.Now().UnixMilli())
		if err != nil {
			return err
		}
	}

	return nil
}

func IsSchemaExist(schema string) bool {
	query := fmt.Sprintf("SELECT schema_name FROM information_schema.schemata WHERE schema_name = '%s'", schema)
	var s string
	result := DB.Raw(query).First(&s)
	if err := result.Error; err != nil {
		log.Printf("Find schema false err: %v", result.Error)
		return false
	}
	return true
}

type JSONB map[string]interface{}

func (j JSONB) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *JSONB) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type JSONArray []interface{}

func (j JSONArray) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *JSONArray) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type StringArray []string

func (p StringArray) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	return string(val), err
}

func (p *StringArray) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &p)
	if err != nil {
		return err
	}

	return nil
}

func FindManyWithPagination[T any, Q QueryOptions](
	findPageFunc func(query Q, options *FindPageOptions) ([]T, *Pagination, error),
	query Q,
) ([]T, error) {
	const pageSize = 100

	currentPage := 1

	var result []T

	for {
		pageOptions := &FindPageOptions{
			Page:    currentPage,
			PerPage: pageSize,
			Sort:    []string{"created_at desc"},
		}

		items, pagination, err := findPageFunc(query, pageOptions)
		if err != nil {
			return nil, err
		}

		result = append(result, items...)

		if currentPage >= pagination.TotalPages {
			break
		}

		currentPage++
	}

	return result, nil
}
