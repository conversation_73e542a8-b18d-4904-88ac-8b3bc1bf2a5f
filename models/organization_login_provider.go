package models

import "openedu-core/pkg/util"

type OrgLoginProvider struct {
	Name           util.SNSProvider `json:"name"`
	Active         bool             `json:"active"`
	IconURL        string           `json:"icon_url,omitempty"`
	LoginPage      string           `json:"login_page,omitempty"`
	RedirectURLKey string           `json:"redirect_url_key,omitempty"`
}

func MakeDefaultLoginProviders() []*OrgLoginProvider {
	return []*OrgLoginProvider{
		{
			Name:   util.Kakao,
			Active: true,
		},
		{
			Name:   util.Naver,
			Active: true,
		},
		{
			Name:   util.Twitter,
			Active: true,
		},
		{
			Name:   util.Google,
			Active: true,
		},
		{
			Name:   util.Facebook,
			Active: true,
		},
	}
}
