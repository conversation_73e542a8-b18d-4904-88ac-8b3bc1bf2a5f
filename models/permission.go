package models

import (
	"fmt"
	"github.com/samber/lo"
	lop "github.com/samber/lo/parallel"
	"gorm.io/gorm"
	"openedu-core/cache_clients"
	"openedu-core/pkg/log"
)

type Permission struct {
	Role           *Role  `json:"role"`
	Method         string `json:"method"`
	Path           string `json:"path"`
	ControllerName string `json:"controller_name"`
	RoleID         string `json:"role_id" gorm:"type:varchar(50);index_role_id"`
	Model
	Allow bool `json:"allow"`
}

type SimplePermission struct {
	Method         string `json:"method"`
	Path           string `json:"path"`
	ControllerName string `json:"controller_name"`
	RoleID         string `json:"role_id" gorm:"type:varchar(50);index_role_id"`
	Allow          bool   `json:"allow"`
}

type RoleAccess struct {
	RoleID string `json:"role_id"`
	Allow  bool   `json:"allow"`
}

func (p *Permission) ToSimple() *SimplePermission {
	return &SimplePermission{
		Method:         p.Method,
		Path:           p.Path,
		ControllerName: p.ControllerName,
		RoleID:         p.RoleID,
		Allow:          p.Allow,
	}
}

type PermissionQuery struct {
	ID             *string  `form:"id" json:"id"`
	IDIn           []string `form:"id_in" json:"id_in,omitempty"`
	Method         *string  `form:"method" json:"method"`
	Path           *string  `form:"path" json:"path"`
	ControllerName *string  `form:"controller_name " json:"controller_name"`
	IncludeDeleted *bool    `form:"include_deleted" json:"include_deleted,omitempty"`
}

func (query *PermissionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}
	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.Method != nil {
		qb = qb.Where("method = ?", *query.Method)
	}

	if query.Path != nil {
		qb = qb.Where("path = ?", *query.Path)
	}

	if query.ControllerName != nil {
		qb = qb.Where("controller_name = ?", *query.ControllerName)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}
	return qb
}

func (r *PermissionRepository) makeCacheKeyForPermAccess(method, path, controllerName string) string {
	return fmt.Sprintf("%s_%s_%s", method, path, controllerName)
}

func (r *PermissionRepository) Create(permission *Permission, trans *gorm.DB) error {
	if err := create(PermissionTbl, permission, trans); err != nil {
		return err
	}

	if cErr := Cache.Permission.DeleteByKey(cache_clients.AllPermissionsCacheKey); cErr != nil {
		log.Errorf("PermissionRepository::Create Delete all permissions cache error: %v", cErr)
	}

	cacheKey := r.makeCacheKeyForPermAccess(permission.Method, permission.Path, permission.ControllerName)
	if cErr := Cache.Permission.DeleteByKey(cacheKey); cErr != nil {
		log.Errorf("PermissionRepository::Create Delete permission role access cache error: %v", cErr)
	}

	return nil
}

func (r *PermissionRepository) CreateMany(permissions []*Permission, trans *gorm.DB) error {
	if err := createMany(PermissionTbl, permissions, trans); err != nil {
		return err
	}

	if cErr := Cache.Permission.Flush(); cErr != nil {
		log.Errorf("PermissionRepository::CreateMany Flush permission role accesses cache error: %v", cErr)
	}
	return nil
}

func (r *PermissionRepository) FetchAll() ([]*Permission, error) {
	var permissions []*Permission
	if err := GetDb(PermissionTbl).Find(&permissions).Error; err != nil {
		log.Debug("Fetch from DB failed")
		return nil, err
	}
	return permissions, nil
}

func (r *PermissionRepository) FindAll() ([]*SimplePermission, error) {
	var permissions []*Permission
	var permissionsCache []interface{}
	if cErr := Cache.Permission.FindAll(&permissionsCache); cErr != nil && len(permissionsCache) > 0 {
		log.Errorf("Get permissions from cache error: %v", cErr)
		Cache.Convert(permissionsCache, &permissions)
	}
	if len(permissions) > 0 {
		log.Debugf("Permission::Repo::Get from cache, number of permissions: %d", len(permissions))
		return lo.Map(permissions, func(item *Permission, _ int) *SimplePermission {
			return item.ToSimple()
		}), nil
	}

	if err := GetDb(PermissionTbl).Find(&permissions).Error; err != nil {
		log.Errorf("Permission::Repo::Get from DB failed: %v", err)
		return nil, err
	}

	simples := lop.Map(permissions, func(item *Permission, idx int) *SimplePermission {
		return item.ToSimple()
	})
	log.Debugf("Permission::Repo::Get permissions from DB, number of permissions: %d", len(simples))

	Cache.Permission.SetAll(lop.Map(simples, func(simplePerm *SimplePermission, idx int) interface{} {
		return simplePerm
	}))
	//log.Debugf("Permission::Repo::Set permissions to cache, number of permissions: %d", len(simples))

	return simples, nil
}

func (r *PermissionRepository) FindAccesses(method, path, controllerName string) ([]*RoleAccess, error) {
	// Try to get from cache
	var roleAccessCache []interface{}
	cacheKey := r.makeCacheKeyForPermAccess(method, path, controllerName)
	err := Cache.Permission.Get(cacheKey, &roleAccessCache)
	if err == nil && roleAccessCache != nil {
		fmt.Println("FindAccesses: Cache.Permission.Get:", cacheKey)
		var roleAccesses []*RoleAccess
		if err = Cache.Convert(roleAccessCache, &roleAccesses); err == nil {
			return roleAccesses, nil
		}
		fmt.Println("FindAccesses: Cache.Convert:", cacheKey)
		log.Errorf("PermissionRepository::FindAccesses Convert permission role accesses (method=%s, path=%s, controller_name=%s) role access cache error: %v",
			method, path, controllerName, err)
	}

	fmt.Println("FindAccesses: cache key:", cacheKey)

	// If not found in cache, get role accesses from DB
	query := &PermissionQuery{
		Method:         &method,
		Path:           &path,
		ControllerName: &controllerName,
	}
	permissions, err := r.FindMany(query, &FindManyOptions{})
	if err != nil {
		return nil, err
	}

	fmt.Println("FindAccesses: FindMany:", cacheKey)

	roleAccesses := lo.Map(permissions, func(p *Permission, _ int) *RoleAccess {
		return &RoleAccess{
			RoleID: p.RoleID,
			Allow:  p.Allow,
		}
	})

	// Set role accesses to cache
	cacheItems := lo.Map(roleAccesses, func(item *RoleAccess, _ int) interface{} {
		return item
	})
	if cErr := Cache.Permission.Set(cacheKey, cacheItems); cErr != nil {
		log.Errorf("PermissionRepository::FindAccesses Set permission role accesses (method=%s, path=%s, controller_name=%s) role access to cache error: %v",
			method, path, controllerName, err)
	}

	return roleAccesses, nil
}

func (r *PermissionRepository) FindMany(query *PermissionQuery, options *FindManyOptions) ([]*Permission, error) {
	return findMany[Permission](PermissionTbl, query, options)
}

func (r *PermissionRepository) Update(permission *Permission, trans *gorm.DB) error {
	if err := update(PermissionTbl, permission, trans); err != nil {
		return err
	}

	if cErr := Cache.Permission.DeleteByKey(cache_clients.AllPermissionsCacheKey); cErr != nil {
		log.Errorf("PermissionRepository::Update Delete all permissions cache error: %v", cErr)
	}

	cacheKey := r.makeCacheKeyForPermAccess(permission.Method, permission.Path, permission.ControllerName)
	if cErr := Cache.Permission.DeleteByKey(cacheKey); cErr != nil {
		log.Errorf("PermissionRepository::Update Delete permission role access cache error: %v", cErr)
	}

	return nil
}

func (r *PermissionRepository) Delete(permission *Permission, trans *gorm.DB) error {
	if err := deleteByID[Permission](PermissionTbl, permission.ID, trans); err != nil {
		return err
	}

	if cErr := Cache.Permission.DeleteByKey(cache_clients.AllPermissionsCacheKey); cErr != nil {
		log.Errorf("PermissionRepository::Delete Delete all permissions cache error: %v", cErr)
	}

	cacheKey := r.makeCacheKeyForPermAccess(permission.Method, permission.Path, permission.ControllerName)
	if cErr := Cache.Permission.DeleteByKey(cacheKey); cErr != nil {
		log.Errorf("PermissionRepository::Delete Delete permission role access cache error: %v", cErr)
	}

	return nil
}

func (r *PermissionRepository) DeleteMany(query *PermissionQuery, trans *gorm.DB) (int64, error) {
	deletedCount, err := hardDeleteMany[Permission](PermissionTbl, query, trans)
	if err != nil {
		return deletedCount, err
	}

	if cErr := Cache.Permission.Flush(); cErr != nil {
		log.Errorf("PermissionRepository::DeleteMany Flush permission role accesses cache error: %v", cErr)
	}
	return 0, nil
}

func (r *PermissionRepository) Count(query *PermissionQuery) (int64, error) {
	return count[Permission](PermissionTbl, query)
}
