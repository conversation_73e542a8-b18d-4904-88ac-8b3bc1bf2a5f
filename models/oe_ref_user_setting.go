package models

type OEReferralTrigger string

const (
	CompleteRegisterTrigger    OEReferralTrigger = "register_account"
	FirstCompleteCourseTrigger OEReferralTrigger = "first_complete_course"
	FirstDepositFiatTrigger    OEReferralTrigger = "first_deposit_fiat"
	FirstDepositCryptoTrigger  OEReferralTrigger = "first_deposit_crypto"
)

// issues: https://github.com/Openedu-teams/openedu-management/issues/399
type ReferralInviteUserProgram struct {
	ReferrerReward PointReward       `json:"referrer_reward"`
	RefereeReward  PointReward       `json:"referee_reward"`
	Trigger        OEReferralTrigger `json:"trigger"`
	MilestoneBonus bool              `json:"milestone_bonus" default:"false"`
	RefCountBonus  []RefCountBonus   `json:"ref_count_bonus"`

	FeaturedDiscover    bool        `json:"featured_discover" default:"false"`
	CompleteCourseBonus PointReward `json:"complete_course_bonus"`
	DepositFiatBonus    PointReward `json:"deposit_fiat_bonus"`
	DepositTokenBonus   PointReward `json:"deposit_crypto_bonus"`

	TimeBased      bool           `json:"time_based" default:"false"`
	TimeBasedBonus TimeBasedBonus `json:"time_base_rewards"`

	StreakReward       bool           `json:"streak_bonus" default:"false"`
	WeeklyStreakBonus  RefStreakBonus `json:"weekly_streak_bonus"`
	MonthlyStreakBonus RefStreakBonus `json:"monthly_streak_bonus"`
}

type RefCountBonus struct {
	ReachCount int         `json:"reach_count" default:"0"`
	Reward     PointReward `json:"reward"`
	Enable     bool        `json:"enable" default:"false"`
	Order      int         `json:"order" default:"0"`
}

type TimeBasedBonus struct {
	StartDate int64       `json:"start_date" default:"0"`
	EndDate   int64       `json:"end_date" default:"0"`
	Reward    PointReward `json:"reward"`
}

type RefStreakBonus struct {
	Threshold int         `json:"threshold" default:"0"`
	Reward    PointReward `json:"reward"`
	Enable    bool        `json:"enable" default:"true"`
}
