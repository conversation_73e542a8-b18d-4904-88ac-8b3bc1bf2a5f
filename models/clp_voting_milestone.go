package models

import (
	"database/sql/driver"
	"encoding/json"

	"gorm.io/gorm"
)

type VotingMilestoneStatus string

const (
	VotingMilestoneCompleted VotingMilestoneStatus = "completed"
	VotingMilestoneFailed    VotingMilestoneStatus = "failed"
	VotingMilestoneRunning   VotingMilestoneStatus = "running"
	VotingMilestonePending   VotingMilestoneStatus = "pending"
)

type ClpVotingMilestone struct {
	Model
	ClpLaunchpadID        string                `json:"clp_launchpad_id"`
	Title                 string                `json:"title"`
	EstimatedOpenVoteDate int64                 `json:"estimated_open_vote_date"`
	EstimatedEndVoteDate  int64                 `json:"estimated_end_vote_date"`
	OpenVoteDate          int64                 `json:"open_vote_date"`
	EndVoteDate           int64                 `json:"end_vote_date"`
	Order                 int                   `json:"order"`
	TargetSection         int                   `json:"target_section"`
	Status                VotingMilestoneStatus `json:"status"`
	Props                 VotingMilestoneProps  `json:"props" gorm:"type:jsonb"`
	Voting                *VotingProcess        `json:"voting_process" gorm:"-"`
}

type VotingProcess struct {
	IsVoted           bool    `json:"is_voted"`
	TotalVote         int     `json:"total_vote"`
	ApprovePercentage float64 `json:"approve_percentage"`
	RejectPercentage  float64 `json:"reject_percentage"`
}

func (m *ClpVotingMilestone) IsStatusPending() bool {
	return m.Status == VotingMilestonePending
}

func (m *ClpVotingMilestone) IsStatusRunning() bool {
	return m.Status == VotingMilestoneRunning
}

func (m *ClpVotingMilestone) IsStatusCompleted() bool {
	return m.Status == VotingMilestoneCompleted
}

func (m *ClpVotingMilestone) IsStatusFailed() bool {
	return m.Status == VotingMilestoneFailed
}

type VotingMilestoneProps struct {
	CourseIDs []string `json:"course_ids"`
}

func (p VotingMilestoneProps) Value() (driver.Value, error) {
	valueString, err := json.Marshal(p)
	return string(valueString), err
}

func (p *VotingMilestoneProps) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &p); err != nil {
		return err
	}
	return nil
}

type ClpVotingMilestoneQuery struct {
	ID               *string  `json:"id" form:"id"`
	IDIn             []string `json:"id_in" form:"id_in"`
	ClpLaunchpadID   *string  `json:"clp_launchpad_id" form:"clp_launchpad_id"`
	ClpLaunchpadIDIn []string `json:"clp_launchpad_id_in" form:"clp_launchpad_id_in"`
	Status           *bool    `json:"status" form:"status"`
	IncludeDeleted   *bool    `json:"include_deleted" form:"include_deleted"`
}

func (query *ClpVotingMilestoneQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.ClpLaunchpadID != nil {
		qb = qb.Where("clp_launchpad_id = ?", *query.ClpLaunchpadID)
	}

	if len(query.ClpLaunchpadIDIn) > 0 {
		qb = qb.Where("clp_launchpad_id IN (?)", query.ClpLaunchpadIDIn)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *ClpVotingMilestoneRepository) Create(e *ClpVotingMilestone, trans *gorm.DB) error {
	return create(ClpVotingMilestoneTbl, e, trans)
}

func (r *ClpVotingMilestoneRepository) CreateMany(ts []*ClpVotingMilestone, trans *gorm.DB) error {
	return createMany(ClpVotingMilestoneTbl, ts, trans)
}

func (r *ClpVotingMilestoneRepository) Update(f *ClpVotingMilestone, trans *gorm.DB) error {
	return update(ClpVotingMilestoneTbl, f, trans)
}

func (r *ClpVotingMilestoneRepository) FindOne(query *ClpVotingMilestoneQuery, options *FindOneOptions) (*ClpVotingMilestone, error) {
	return findOne[ClpVotingMilestone](ClpVotingMilestoneTbl, query, options)
}

func (r *ClpVotingMilestoneRepository) FindPage(query *ClpVotingMilestoneQuery, options *FindPageOptions) ([]*ClpVotingMilestone, *Pagination, error) {
	return findPage[ClpVotingMilestone](ClpVotingMilestoneTbl, query, options)
}

func (r *ClpVotingMilestoneRepository) FindMany(query *ClpVotingMilestoneQuery, options *FindManyOptions) ([]*ClpVotingMilestone, error) {
	return findMany[ClpVotingMilestone](ClpVotingMilestoneTbl, query, options)
}

func (r *ClpVotingMilestoneRepository) Count(query *ClpVotingMilestoneQuery) (int64, error) {
	return count[ClpVotingMilestone](ClpVotingMilestoneTbl, query)
}

func (r *ClpVotingMilestoneRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ClpVotingMilestone](ClpVotingMilestoneTbl, id, trans)
}
