package models

import (
	"database/sql/driver"
	"encoding/json"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ReferralType string
type ReferralScope string

const (
	RefUserProgram      ReferralType = "ref-user"
	ShareContentProgram ReferralType = "share-content"
	OtherProgram        ReferralType = "other"

	ReferralScopeGlobal   ReferralScope = "global"
	ReferralScopeSpecific ReferralScope = "specific"
)

type OEPointCampaign struct {
	Model
	Program     ReferralType           `json:"program"`
	Name        string                 `json:"name"`
	Scope       ReferralScope          `json:"scope"`
	Enabled     bool                   `json:"enabled" default:"false"`
	Entities    ReferralCampaignEntity `json:"entities" gorm:"type:jsonb"`
	StartDate   int                    `json:"start_date" gorm:"type:int8;not null;default:0"`
	EndDate     int                    `json:"end_date" gorm:"type:int8;not null;default:0"`
	TotalReward int                    `json:"total_reward" gorm:"default:0"`

	Setting JSONB `json:"setting" gorm:"type:jsonb"`
}

type PointReward struct {
	Type   NumberValueType `json:"type"`
	Amount decimal.Decimal `json:"amount"`
}

func (j PointReward) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *PointReward) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type ReferralCampaignEntity []CampaignEntity
type CampaignEntity struct {
	EntityID   string    `json:"entity_id"`
	EntityType ModelName `json:"entity_type"`
}

func (j ReferralCampaignEntity) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *ReferralCampaignEntity) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type OEPointCampaignQuery struct {
	ID          *string        `json:"id" form:"id"`
	Program     *ReferralType  `json:"program" form:"program"`
	Name        *string        `json:"name" form:"name"`
	Scope       *ReferralScope `json:"scope" form:"scope"`
	Enable      *bool          `json:"enable" form:"enable"`
	StartDateLt *int           `json:"start_date" form:"start_date"`
	EndDateGt   *int           `json:"end_date" form:"end_date"`
	Deleted     *bool          `json:"deleted,omitempty" form:"deleted"`
}

func (query *OEPointCampaignQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.Program != nil {
		qb = qb.Where("program = ?", *query.Program)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Scope != nil {
		qb = qb.Where("scope = ?", *query.Scope)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.Deleted == nil || !*query.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.StartDateLt != nil {
		qb = qb.Where("start_date = 0 OR start_date < ?", *query.StartDateLt)
	}

	if query.EndDateGt != nil {
		qb = qb.Where("end_date = 0 OR end_date > ?", *query.EndDateGt)
	}

	return qb
}

// Create inserts a OEPointCampaign to database, transaction is optional
func (r *OEPointCampaignRepository) Create(q *OEPointCampaign, trans *gorm.DB) error {
	return create(OEPointCampaignTbl, q, trans)
}

func (r *OEPointCampaignRepository) CreateMany(qs []*OEPointCampaign, trans *gorm.DB) error {
	return createMany(OEPointCampaignTbl, qs, trans)
}

// Update updates a OEPointCampaign by ID in database, transaction is optional
func (r *OEPointCampaignRepository) Update(q *OEPointCampaign, trans *gorm.DB) error {
	return update(OEPointCampaignTbl, q, trans)
}

// FindByID finds a OEPointCampaign by ID with given find options, transaction is optional
func (r *OEPointCampaignRepository) FindByID(id string, options *FindOneOptions) (*OEPointCampaign, error) {
	return findByID[OEPointCampaign](OEPointCampaignTbl, id, options)
}

// FindOne finds one OEPointCampaign with given find queries and options, transaction is optional
func (r *OEPointCampaignRepository) FindOne(query *OEPointCampaignQuery, options *FindOneOptions) (*OEPointCampaign, error) {
	return findOne[OEPointCampaign](OEPointCampaignTbl, query, options)
}

// FindMany finds OEPointCampaigns by query conditions with give find options
func (r *OEPointCampaignRepository) FindMany(query *OEPointCampaignQuery, options *FindManyOptions) ([]*OEPointCampaign, error) {
	return findMany[OEPointCampaign](OEPointCampaignTbl, query, options)
}

// FindPage returns OEPointCampaigns and pagination by query conditions and find options, transaction is optional
func (r *OEPointCampaignRepository) FindPage(query *OEPointCampaignQuery, options *FindPageOptions) ([]*OEPointCampaign, *Pagination, error) {
	return findPage[OEPointCampaign](OEPointCampaignTbl, query, options)
}

// Delete perform soft deletion to a OEPointCampaign by ID, transaction is optional
func (r *OEPointCampaignRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OEPointCampaign](OEPointCampaignTbl, id, trans)
}

// Count returns number of users by query conditions, transaction is optional
func (r *OEPointCampaignRepository) Count(query *OEPointCampaignQuery) (int64, error) {
	return count[OEPointCampaign](OEPointCampaignTbl, query)
}
