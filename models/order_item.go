package models

import (
	"fmt"

	"github.com/shopspring/decimal"

	"gorm.io/gorm"
)

type OrderItem struct {
	Model
	OrderID string `json:"order_id" gorm:"type:varchar(20);not null"`

	EntityID   string    `json:"entity_id" gorm:"type:varchar(20)"`
	EntityCuid string    `json:"entity_cuid" gorm:"type:varchar(20)"`
	EntityType ModelName `json:"entity_type" gorm:"type:varchar(255)"`

	PayFromOrgID string `json:"pay_from_org_id" gorm:"type:varchar(20);not null"`
	OrgID        string `json:"org_id" gorm:"type:varchar(20);not null"`
	OrgSchema    string `json:"org_schema" gorm:"not null"`

	UserID         string          `json:"user_id" gorm:"type:varchar(20);not null"`
	Order          *Order          `json:"order"`
	Amount         decimal.Decimal `json:"amount" gorm:"type:numeric(19,4);not null;default:0"`
	DiscountAmount decimal.Decimal `json:"discount_amount" gorm:"type:numeric(19,4);not null;default:0"`
	ActualAmount   decimal.Decimal `json:"actual_amount" gorm:"type:numeric(19,4);not null;default:0"`
	Status         OrderStatus     `json:"status"`
	Currency       Currency        `json:"currency" gorm:"default:VND"`
}

type SimpleOrderItem struct {
	Model
	OrderID        string          `json:"order_id"`
	EntityID       string          `json:"entity_id"`
	EntityCuid     string          `json:"entity_cuid"`
	EntityType     ModelName       `json:"entity_type"`
	UserID         string          `json:"user_id"`
	Amount         decimal.Decimal `json:"amount"`
	DiscountAmount decimal.Decimal `json:"discount_amount"`
	ActualAmount   decimal.Decimal `json:"actual_amount"`
	Status         OrderStatus     `json:"status"`
	Currency       Currency        `json:"currency"`
}

type OrderItemQuery struct {
	OrderID        *string       `json:"order_id" form:"order_id"`
	OrderIDIn      []string      `json:"order_id_in" form:"order_id_in"`
	ID             *string       `json:"id" form:"id"`
	IDIn           []string      `json:"id_in" form:"id_in"`
	EntityCuid     *string       `json:"entity_cuid" form:"entity_cuid"`
	EntityCuidIn   []string      `json:"entity_cuid_in" form:"entity_cuid_in"`
	EntityID       *string       `json:"entity_id" form:"entity_id"`
	EntityIDIn     []string      `json:"entity_id_in" form:"entity_id_in"`
	EntityType     *ModelName    `json:"entity_type" form:"entity_type"`
	UserID         *string       `json:"user_id" form:"user_id"`
	NotStatus      *OrderStatus  `json:"not_status" form:"not_status"`
	Status         *OrderStatus  `json:"status" form:"status"`
	StatusIn       []OrderStatus `json:"status_in" form:"status_in"`
	StatusNotIn    []OrderStatus `json:"status_not_in" form:"status_not_in"`
	IncludeDeleted *bool         `json:"include_deleted" form:"include_deleted"`
}

func (o *OrderItem) Sanitize() *SimpleOrderItem {
	return &SimpleOrderItem{
		Model:          o.Model,
		OrderID:        o.OrderID,
		EntityID:       o.EntityID,
		EntityCuid:     o.EntityCuid,
		EntityType:     o.EntityType,
		Amount:         o.Amount,
		DiscountAmount: o.DiscountAmount,
		ActualAmount:   o.ActualAmount,
		UserID:         o.UserID,
		Status:         o.Status,
		Currency:       o.Currency,
	}
}

func (query *OrderItemQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where(fmt.Sprintf("%s.id = ?", GetTblName(OrderItemTbl)), *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where(fmt.Sprintf("%s.id IN (?)", GetTblName(OrderItemTbl)), *&query.IDIn)
	}

	if query.OrderID != nil {
		qb = qb.Where(fmt.Sprintf("%s.order_id = ?", GetTblName(OrderItemTbl)), *&query.OrderID)
	}

	if len(query.OrderIDIn) > 0 {
		qb = qb.Where(fmt.Sprintf("%s.order_id IN (?)", GetTblName(OrderItemTbl)), *&query.OrderIDIn)
	}

	if query.EntityID != nil {
		qb = qb.Where(fmt.Sprintf("%s.entity_id = ?", GetTblName(OrderItemTbl)), query.EntityID)
	}

	if query.EntityIDIn != nil && len(query.EntityIDIn) > 0 {
		qb = qb.Where(fmt.Sprintf("%s.entity_id IN (?)", GetTblName(OrderItemTbl)), *&query.EntityIDIn)
	}

	if query.EntityCuid != nil {
		qb = qb.Where(fmt.Sprintf("%s.entity_cuid = ?", GetTblName(OrderItemTbl)), query.EntityCuid)
	}

	if query.EntityCuidIn != nil && len(query.EntityCuidIn) > 0 {
		qb = qb.Where(fmt.Sprintf("%s.entity_cuid IN (?)", GetTblName(OrderItemTbl)), *&query.EntityCuidIn)
	}

	if query.EntityType != nil {
		qb = qb.Where(fmt.Sprintf("%s.entity_type = ?", GetTblName(OrderItemTbl)), query.EntityType)
	}

	if query.NotStatus != nil {
		qb = qb.Where(fmt.Sprintf("%s.status != ?", GetTblName(OrderItemTbl)), query.NotStatus)
	}

	if query.Status != nil {
		qb = qb.Where(fmt.Sprintf("%s.status = ?", GetTblName(OrderItemTbl)), query.Status)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", *&query.StatusIn)
	}

	if len(query.StatusNotIn) > 0 {
		qb = qb.Where(fmt.Sprintf("%s.status NOT IN (?)", GetTblName(OrderItemTbl)), *&query.StatusNotIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where(fmt.Sprintf("%s.delete_at = 0", GetTblName(OrderItemTbl)))
	}

	if query.UserID != nil {
		joinStatement := fmt.Sprintf("JOIN %s ON %s.order_id = %s.id", GetTblName(OrderTbl), GetTblName(OrderItemTbl), GetTblName(OrderTbl))
		whereStatement := fmt.Sprintf("%s.user_id = ?", GetTblName(OrderTbl))
		qb = qb.Joins(joinStatement).
			Where(whereStatement, *&query.UserID)
	}

	return qb
}

func (oi *OrderItemRepository) Create(o *OrderItem, trans *gorm.DB) error {
	return create(OrderItemTbl, o, trans)
}

func (oi *OrderItemRepository) CreateMany(o []*OrderItem, trans *gorm.DB) error {
	return createMany(OrderItemTbl, o, trans)
}

func (oi *OrderItemRepository) Update(o *OrderItem, trans *gorm.DB) error {
	return update(OrderItemTbl, o, trans)
}

func (oi *OrderItemRepository) FindByID(id string, options *FindOneOptions) (*OrderItem, error) {
	return findByID[OrderItem](OrderItemTbl, id, options)
}

func (oi *OrderItemRepository) FindOne(query *OrderItemQuery, options *FindOneOptions) (*OrderItem, error) {
	return findOne[OrderItem](OrderItemTbl, query, options)
}

func (oi *OrderItemRepository) FindMany(query *OrderItemQuery, options *FindManyOptions) ([]*OrderItem, error) {
	return findMany[OrderItem](OrderItemTbl, query, options)
}

func (oi *OrderItemRepository) FindManyOnSchemas(schemas []string, query *OrderItemQuery, options *FindManyOptions) ([]*OrderItem, error) {
	return findManyWithSchemas[OrderItem](schemas, OrderItemTbl, query, options)
}

func (oi *OrderItemRepository) FindPage(query *OrderItemQuery, options *FindPageOptions) ([]*OrderItem, *Pagination, error) {
	return findPage[OrderItem](OrderItemTbl, query, options)
}

func (oi *OrderItemRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OrderItem](OrderItemTbl, id, trans)
}

func (oi *OrderItemRepository) DeleteMany(query *OrderItemQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[OrderItem](OrderItemTbl, query, trans)
}
