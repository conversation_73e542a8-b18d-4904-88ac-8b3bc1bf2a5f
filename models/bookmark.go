package models

import "gorm.io/gorm"

type Bookmark struct {
	Model      `dbscan:"model"`
	Name       string    `json:"name" dbscan:"name" gorm:"type:varchar(255)"`
	EntityID   string    `json:"entity_id" dbscan:"entity_id" gorm:"type:varchar(20)"`
	EntityType ModelName `json:"entity_type" dbscan:"entity_type" gorm:"type:varchar(255)"`
	UserID     string    `json:"user_id" dbscan:"user_id" gorm:"type:varchar(20);not null"`
	ParentID   *string   `json:"parent_id" dbscan:"parent_id" gorm:"type:varchar(20)"`
	Link       string    `json:"link" dbscan:"link" gorm:"type:varchar(255)"`
}

type SimpleBookmark struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	EntityID   string    `json:"entity_id"`
	EntityType ModelName `json:"entity_type"`
	ParentID   *string   `json:"parent_id"`
	Link       string    `json:"link"`
}

func (b *Bookmark) ToSimple() *SimpleBookmark {
	return &SimpleBookmark{
		ID:         b.ID,
		Name:       b.Name,
		EntityID:   b.EntityID,
		EntityType: b.EntityType,
		ParentID:   b.ParentID,
		Link:       b.Link,
	}
}

type BookmarkQuery struct {
	ID             *string    `json:"id" form:"id"`
	EntityID       *string    `json:"entity_id" form:"entity_id"`
	EntityIDIn     []string   `json:"entity_id_in" form:"entity_id_in"`
	EntityType     *ModelName `json:"entity_type" form:"entity_type"`
	UserID         *string    `json:"user_id" form:"user_id"`
	ParentID       *string    `json:"parent_id" form:"parent_id"`
	IncludeDeleted *bool      `form:"include_deleted" json:"include_deleted,omitempty"`
}

func (query *BookmarkQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.EntityID != nil {
		qb = qb.Where("entity_id = ?", *query.EntityID)
	}

	if len(query.EntityIDIn) > 0 {
		qb = qb.Where("entity_id IN (?)", query.EntityIDIn)
	}

	if query.EntityType != nil {
		qb = qb.Where("entity_type = ?", *query.EntityType)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.ParentID != nil {
		qb = qb.Where("parent_id = ?", *query.ParentID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *BookmarkRepository) Create(c *Bookmark, trans *gorm.DB) error {
	return create(BookmarkTbl, c, trans)
}

func (r *BookmarkRepository) FindOne(query *BookmarkQuery, options *FindOneOptions) (*Bookmark, error) {
	return findOne[Bookmark](BookmarkTbl, query, options)
}

func (r *BookmarkRepository) FindByID(id string, options *FindOneOptions) (*Bookmark, error) {
	return findByID[Bookmark](BookmarkTbl, id, options)
}

func (r *BookmarkRepository) FindMany(query *BookmarkQuery, options *FindManyOptions) ([]*Bookmark, error) {
	return findMany[Bookmark](BookmarkTbl, query, options)
}

func (r *BookmarkRepository) FindPage(query *BookmarkQuery, options *FindPageOptions) ([]*Bookmark, *Pagination, error) {
	return findPage[Bookmark](BookmarkTbl, query, options)
}

func (r *BookmarkRepository) Update(c *Bookmark, trans *gorm.DB) error {
	return update(BookmarkTbl, c, trans)
}

func (r *BookmarkRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Bookmark](BookmarkTbl, id, trans)
}
