package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// QuizRelationType is enum to present relation of entity to the quiz.
// Length of QuizRelationType should be equal or less than 100 characters.
type QuizRelationType string

const (
	QuizRelationTypeIs          = "is"
	QuizRelationTypeTriggeredBy = "triggered_by"
)

var QuizRelationTypes = map[QuizRelationType]struct{}{
	QuizRelationTypeIs:          {},
	QuizRelationTypeTriggeredBy: {},
}

// QuizRelationEntity is enum to present entities has relation to the quiz.
// Length of QuizRelationEntity should be equal or less than 100 characters.
type QuizRelationEntity string

const (
	QuizRelationEntityLessonContent QuizRelationEntity = "lesson_content"
)

var QuizRelationEntities = map[QuizRelationEntity]struct{}{
	QuizRelationEntityLessonContent: {},
}

type QuizRelation struct {
	QuizID            string                `json:"quiz_id" gorm:"primaryKey,type:varchar(20)"`
	RelatedEntityType QuizRelationEntity    `json:"related_entity_type" gorm:"primaryKey,type:varchar(100)"`
	RelatedEntityID   string                `json:"related_entity_id" gorm:"primaryKey,type:varchar(20)"`
	RelationType      QuizRelationType      `json:"relation_type" gorm:"primaryKey,type:varchar(100)"`
	TriggerConditions QuizTriggerConditions `json:"trigger_conditions" gorm:"type:jsonb"`
}

func (qr *QuizRelation) IsRelationIs() bool {
	return qr.RelationType == QuizRelationTypeIs
}

func (qr *QuizRelation) IsRelationTriggerBy() bool {
	return qr.RelationType == QuizRelationTypeTriggeredBy
}

func (qr *QuizRelation) IsRelationToLessonContent() bool {
	return qr.RelatedEntityType == QuizRelationEntityLessonContent
}

func (qr *QuizRelation) IsTriggeredByTimestamp() bool {
	return qr.RelationType == QuizRelationTypeTriggeredBy && qr.TriggerConditions.IsTriggeredByTimestamp
}

func (qr *QuizRelation) IsTriggeredByReachPageNumber() bool {
	return qr.RelationType == QuizRelationTypeTriggeredBy && qr.TriggerConditions.IsTriggerByReachPageNumber
}

func (qr *QuizRelation) CheckValid() error {
	if qr.QuizID == "" {
		return errors.New("quiz_id is required")
	}

	if qr.RelatedEntityType == "" {
		return errors.New("entity_type is required")
	}

	if qr.RelatedEntityID == "" {
		return errors.New("entity_id is required")
	}

	if qr.RelationType == "" {
		return errors.New("relation_type is required")
	}

	if _, ok := QuizRelationEntities[qr.RelatedEntityType]; !ok {
		return errors.New("entity_type is invalid")
	}

	if _, ok := QuizRelationTypes[qr.RelationType]; !ok {
		return errors.New("relation_type is invalid")
	}

	return nil
}

type QuizTriggerConditions struct {
	IsTriggeredByTimestamp     bool    `json:"is_triggered_by_timestamp"`
	Timestamp                  string  `json:"timestamp"` // Timestamp has the following format hh:mm:ss, example: 01:59:59
	IsTriggerByReachPageNumber bool    `json:"is_trigger_by_reach_page_number"`
	PageNumber                 int     `json:"page_number"`
	ShowAtPercentage           float64 `json:"show_at_percentage"`
}

func (c QuizTriggerConditions) Value() (driver.Value, error) {
	val, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (c *QuizTriggerConditions) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, c)
}

type QuizRelationQuery struct {
	QuizID            *string             `form:"quiz_id" json:"quiz_id"`
	RelatedEntityType *QuizRelationEntity `form:"related_entity_type" json:"related_entity_type"`
	RelatedEntityID   *string             `form:"related_entity_id" json:"related_entity_id"`
	RelatedEntityIDIn []string            `form:"related_entity_ids" json:"related_entity_ids"`
	RelationType      *QuizRelationType   `form:"relation_type" json:"relation_type"`
}

func (query *QuizRelationQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.QuizID != nil {
		qb = qb.Where("quiz_id = ?", *query.QuizID)
	}

	if query.RelatedEntityType != nil {
		qb = qb.Where("related_entity_type = ?", *query.RelatedEntityType)
	}

	if query.RelatedEntityID != nil {
		qb = qb.Where("related_entity_id = ?", *query.RelatedEntityID)
	}

	if len(query.RelatedEntityIDIn) > 0 {
		qb = qb.Where("related_entity_id IN (?)", query.RelatedEntityIDIn)
	}

	if query.RelationType != nil {
		qb = qb.Where("relation_type = ?", *query.RelationType)
	}

	return qb
}

// Create inserts a Quiz Relation to database, transaction is optional
func (r *QuizRelationRepository) Create(q *QuizRelation, trans *gorm.DB) error {
	return create(QuizRelationTbl, q, trans)
}

func (r *QuizRelationRepository) CreateMany(qs []*QuizRelation, trans *gorm.DB) error {
	return createMany(QuizRelationTbl, qs, trans)
}

// Update updates a Quiz Relation by ID in database, transaction is optional
func (r *QuizRelationRepository) Update(q *QuizRelation, trans *gorm.DB) error {
	return update(QuizRelationTbl, q, trans)
}

// FindByID finds a Quiz Relation by ID with given find options, transaction is optional
func (r *QuizRelationRepository) FindByID(id string, options *FindOneOptions) (*QuizRelation, error) {
	return findByID[QuizRelation](QuizRelationTbl, id, options)
}

// FindOne finds one Quiz Relation with given find queries and options, transaction is optional
func (r *QuizRelationRepository) FindOne(query *QuizRelationQuery, options *FindOneOptions) (*QuizRelation, error) {
	return findOne[QuizRelation](QuizRelationTbl, query, options)
}

// FindMany finds Quiz Relations by query conditions with give find options
func (r *QuizRelationRepository) FindMany(query *QuizRelationQuery, options *FindManyOptions) ([]*QuizRelation, error) {
	return findMany[QuizRelation](QuizRelationTbl, query, options)
}

// FindPage returns Quiz Relations and pagination by query conditions and find options, transaction is optional
func (r *QuizRelationRepository) FindPage(query *QuizRelationQuery, options *FindPageOptions) ([]*QuizRelation, *Pagination, error) {
	return findPage[QuizRelation](QuizRelationTbl, query, options)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *QuizRelationRepository) DeleteMany(query *QuizRelationQuery, trans *gorm.DB) (count int64, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	result := qb.Table(GetTblName(QuizRelationTbl)).Debug().Delete(&QuizRelation{})
	err = result.Error
	count = result.RowsAffected
	return
}

// Count returns number of Quiz Relations by query conditions, transaction is optional
func (r *QuizRelationRepository) Count(query *QuizRelationQuery) (int64, error) {
	return count[QuizRelation](QuizRelationTbl, query)
}
