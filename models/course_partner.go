package models

import (
	"openedu-core/pkg/util"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type CourseRole string
type CoursePermission string

const (
	CourseRoleOwner      CourseRole = "owner"
	CourseRoleCoCreator  CourseRole = "co-creator"
	CourseRoleMentor     CourseRole = "mentor"
	CourseRoleSupervisor CourseRole = "supervisor"
	CourseRoleSponsor    CourseRole = "sponsor"

	CoursePerPublish        CoursePermission = "publish"
	CoursePerUnPublish      CoursePermission = "unpublish"
	CoursePerViewReport     CoursePermission = "view_report"
	CoursePerUpdate         CoursePermission = "update"
	CoursePerEnable         CoursePermission = "enable"
	CoursePerAll            CoursePermission = "all"
	CoursePerCancel         CoursePermission = "cancel"
	CoursePerModifyCampaign CoursePermission = "modify_campaign"
)

type CoursePartner struct {
	OrgID string `json:"org_id"`
	//Cuid
	CourseID    string         `json:"course_id" gorm:"not null"`
	PartnerID   string         `json:"partner_id" gorm:"not null"`
	Partner     *SimpleProfile `json:"partner" gorm:"-"`
	Roles       StringArray    `json:"roles" gorm:"type:jsonb"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	Permissions StringArray    `json:"permissions" gorm:"type:jsonb"`
	Model
}

type SimplePartner struct {
	ID       string      `json:"id"`
	OrgID    string      `json:"org_id"`
	Username string      `json:"username"`
	Email    string      `json:"email"`
	Avatar   string      `json:"avatar"`
	Active   bool        `json:"active"`
	Blocked  bool        `json:"blocked"`
	Roles    StringArray `json:"roles"`
	Enable   bool        `json:"enable"`
	CreateAt int         `json:"create_at"`
	Headline string      `json:"headline"`
}

func (c *CoursePartner) ToSimplePartner() *SimplePartner {
	if c.Partner != nil {
		return &SimplePartner{
			ID:       c.Partner.ID,
			OrgID:    c.OrgID,
			Username: c.Partner.Username,
			Email:    c.Partner.Email,
			Avatar:   c.Partner.Avatar,
			Active:   c.Partner.Active,
			Blocked:  c.Partner.Blocked,
			Roles:    c.Roles,
			Enable:   c.IsActive,
			CreateAt: c.CreateAt,
			Headline: c.Partner.Headline,
		}
	}
	return &SimplePartner{}
}

type CoursePartnerQuery struct {
	OrgID          *string
	PartnerID      *string
	PartnerIDIn    []string
	CourseID       *string
	IsActive       *bool
	IncludeDeleted *bool
	IncludeRoles   *[]string
	CourseIDIn     []string
}

func (query *CoursePartnerQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.PartnerID != nil {
		qb = qb.Where("partner_id = ?", *query.PartnerID)
	}

	if len(query.PartnerIDIn) > 0 {
		qb = qb.Where("partner_id IN (?)", query.PartnerIDIn)
	}

	if query.CourseID != nil {
		qb = qb.Where("course_id = ?", *query.CourseID)
	}

	if len(query.CourseIDIn) > 0 {
		qb = qb.Where("course_id IN (?)", query.CourseIDIn)
	}

	if query.IsActive != nil {
		qb = qb.Where("is_active = ?", *query.IsActive)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.IncludeRoles != nil {
		qb = qb.Where("roles ?| ARRAY['" + strings.Join(*query.IncludeRoles, "','") + "']")
	}

	return qb
}

// Create inserts a org to database, transaction is optional
func (r *CoursePartnerRepository) Create(entity *CoursePartner, trans *gorm.DB) error {
	return create(CoursePartnerTbl, entity, trans)
}

func (r *CoursePartnerRepository) CreateMany(entities []*CoursePartner, trans *gorm.DB) error {
	return createMany(CoursePartnerTbl, entities, trans)
}

// Update updates a course instructor by ID in database, transaction is optional
func (r *CoursePartnerRepository) Update(entity *CoursePartner, trans *gorm.DB) error {
	return update(CoursePartnerTbl, entity, trans)
}

// FindOne finds one course instructor with given find queries and options, transaction is optional
func (r *CoursePartnerRepository) FindOne(query *CoursePartnerQuery, options *FindOneOptions) (*CoursePartner, error) {
	shouldPreloadPartner := false
	if options.Preloads != nil && lo.Contains(options.Preloads, PartnerField) {
		shouldPreloadPartner = true
		options.Preloads = util.RemoveElement(options.Preloads, PartnerField)
	}

	partner, err := findOne[CoursePartner](CoursePartnerTbl, query, options)
	if err != nil {
		return nil, err
	}

	if shouldPreloadPartner {
		user, uErr := Repository.User.FindByID(partner.PartnerID)
		if uErr != nil {
			return nil, uErr
		}
		partner.Partner = user.ToSimpleProfile()
	}

	return partner, err
}

// FindMany finds partners by query conditions with give find options
func (r *CoursePartnerRepository) FindMany(query *CoursePartnerQuery, options *FindManyOptions) ([]*CoursePartner, error) {
	shouldPreloadPartner := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, PartnerField) {
		shouldPreloadPartner = true
		options.Preloads = util.RemoveElement(options.Preloads, PartnerField)
	}

	partners, err := findMany[CoursePartner](CoursePartnerTbl, query, options)
	if err != nil {
		return nil, err
	}

	if shouldPreloadPartner {
		partnerIDs := lo.Map(partners, func(item *CoursePartner, _ int) string { return item.PartnerID })
		users, uErr := Repository.User.FindMany(&UserQuery{
			IDIn:           &partnerIDs,
			IncludeDeleted: util.NewBool(false),
		}, nil)

		if uErr != nil {
			return nil, err
		}

		lo.ForEach(partners, func(p *CoursePartner, _ int) {
			if p.PartnerID != "" {
				u, ok := lo.Find(users, func(item *User) bool { return item.ID == p.PartnerID })
				if ok {
					p.Partner = u.ToSimpleProfile()
				}
			}
		})
	}

	return partners, nil
}

// FindPage returns partners and pagination by query conditions and find options, transaction is optional
func (r *CoursePartnerRepository) FindPage(query *CoursePartnerQuery, options *FindPageOptions) ([]*CoursePartner, *Pagination, error) {
	shouldPreloadPartner := false
	if options.Preloads != nil && lo.Contains(options.Preloads, PartnerField) {
		shouldPreloadPartner = true
		options.Preloads = util.RemoveElement(options.Preloads, PartnerField)
	}

	partners, pagination, err := findPage[CoursePartner](CoursePartnerTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if shouldPreloadPartner {
		parnerIDs := lo.Map(partners, func(item *CoursePartner, _ int) string { return item.PartnerID })
		users, uErr := Repository.User.FindMany(&UserQuery{
			IDIn:           &parnerIDs,
			IncludeDeleted: util.NewBool(false),
		}, nil)

		if uErr != nil {
			return nil, nil, err
		}

		lo.ForEach(partners, func(p *CoursePartner, _ int) {
			if p.PartnerID != "" {
				u, ok := lo.Find(users, func(item *User) bool { return item.ID == p.PartnerID })
				if ok {
					p.Partner = u.ToSimpleProfile()
				}
			}
		})
	}

	return partners, pagination, nil
}

// Delete perform soft deletion to a org by ID, transaction is optional
func (r *CoursePartnerRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[CoursePartner](CoursePartnerTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *CoursePartnerRepository) DeleteMany(query *CoursePartnerQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[CoursePartner](CoursePartnerTbl, query, trans)
}

// Count returns number of partners by query conditions, transaction is optional
func (r *CoursePartnerRepository) Count(query *CoursePartnerQuery) (int64, error) {
	return count[CoursePartner](CoursePartnerTbl, query)
}

func (r *CoursePartnerRepository) FirstOrCreate(partner *CoursePartner) (*CoursePartner, error) {
	err := GetDb(CoursePartnerTbl).Where(CoursePartner{
		CourseID:  partner.CourseID,
		PartnerID: partner.PartnerID,
	}).Assign(map[string]interface{}{
		"roles":       partner.Roles,
		"is_active":   partner.IsActive,
		"permissions": partner.Permissions,
		"delete_at":   0,
	}).FirstOrCreate(&partner).Error
	return partner, err
}
