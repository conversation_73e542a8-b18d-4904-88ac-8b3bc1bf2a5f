package models

import (
	"fmt"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"

	"github.com/samber/lo"

	"gorm.io/gorm"
)

type Organization struct {
	User        *User       `json:"user,omitempty"`
	UserID      string      `json:"user_id"`
	ThumbnailID *string     `json:"thumbnail_id" gorm:"default:null"`
	Thumbnail   *File       `json:"thumbnail"`
	Settings    *OrgSetting `json:"settings" gorm:"type:jsonb"`
	Schema      string      `json:"schema" validate:"required" gorm:"unique,not null"`
	Name        string      `json:"name" validate:"required" gorm:"unique,not null"`
	Domain      string      `json:"domain" validate:"required" gorm:"unique,not null"`
	AltDomain   string      `json:"alt_domain" validate:"required" gorm:"unique,not null"`
	Active      bool        `json:"active" gorm:"default:true"`
	CreateByID  string      `json:"create_by_id"`
	CreateBy    *User       `json:"create_by"`

	BannerID *string `json:"banner_id"`
	Banner   *File   `json:"banner"`
	Model
}

func (o *Organization) Sanitize() *Organization {
	return &Organization{
		Model:       o.Model,
		UserID:      o.UserID,
		User:        o.User,
		ThumbnailID: o.ThumbnailID,
		Thumbnail:   o.Thumbnail,
		Name:        o.Name,
		Domain:      o.Domain,
		Schema:      o.Schema,
		Active:      o.Active,
		AltDomain:   o.AltDomain,
		CreateBy:    o.CreateBy,
		Banner:      o.Banner,
		BannerID:    o.BannerID,
	}
}

func (o *Organization) IsAvail() bool {
	return o.Schema == SchemaAvail
}

func (o *Organization) IsVBI() bool {
	return o.Schema == SchemaVBI
}

func (o *Organization) ToOrgProfile() *OrgProfile {
	var thumb *SimpleFile
	if o.Thumbnail != nil {
		thumb = o.Thumbnail.Sanitize()
	}
	return &OrgProfile{
		ID:        o.ID,
		Thumbnail: thumb,
		Name:      o.Name,
		Domain:    o.Domain,
		AltDomain: o.AltDomain,
	}
}

type SimpleOrganization struct {
	Model
	User      *SimpleUser `json:"user" dbscan:"user"`
	Thumbnail *SimpleFile `json:"thumbnail" dbscan:"thumbnail"`
	Schema    string      `json:"schema" dbscan:"schema"`
	Name      string      `json:"name" dbscan:"name"`
	Domain    string      `json:"domain" dbscan:"domain"`
	AltDomain string      `json:"alt_domain" dbscan:"alt_domain"`
	Active    bool        `json:"active" dbscan:"active"`
	CreateBy  *SimpleUser `json:"create_by" dbscan:"create_by"`
	Banner    *SimpleFile `json:"banner" dbscan:"create_by"`
}

type OrgProfile struct {
	ID        string      `json:"id" dbscan:"id"`
	Thumbnail *SimpleFile `json:"thumbnail" dbscan:"thumbnail"`
	Name      string      `json:"name" dbscan:"name"`
	Domain    string      `json:"domain" dbscan:"domain"`
	AltDomain string      `json:"alt_domain" dbscan:"alt_domain"`
}

type OrganizationForTopList struct {
	Rank    int    `json:"rank"`
	OrgID   string `json:"org_id"`
	Name    string `json:"name"`
	SiteURL string `json:"site_url"`
	Active  bool   `json:"active"`
}

func (o *Organization) GetID() string {
	return o.ID
}

func (o *Organization) GetDomain() string {
	return o.Domain
}

func (o *Organization) IsRoot() bool {
	return o.Domain == setting.AppSetting.BaseDomain
}

func (o *Organization) ToSimple() *SimpleOrganization {
	if o == nil {
		return nil
	}
	var thumb *SimpleFile
	if o.Thumbnail != nil {
		thumb = NewSimpleFile(o.Thumbnail)
	}
	var banner *SimpleFile
	if o.Banner != nil {
		thumb = NewSimpleFile(o.Banner)
	}
	var createBy *SimpleUser
	if o.CreateBy != nil {
		createBy = o.CreateBy.ToSimpleUser()
	}
	var user *SimpleUser
	if o.User != nil {
		user = o.User.ToSimpleUser()
	}
	return &SimpleOrganization{
		Model:     o.Model,
		Name:      o.Name,
		Domain:    o.Domain,
		AltDomain: o.AltDomain,
		Schema:    o.Schema,
		Thumbnail: thumb,
		User:      user,
		CreateBy:  createBy,
		Active:    o.Active,
		Banner:    banner,
	}
}

func (o *Organization) LandingPageURL() string {
	return "https://" + o.Domain
}

func (o *Organization) AdminURL() string {
	return "https://" + o.Domain + setting.AppSetting.OrgAdminPath
}

func (o *Organization) CreatorURL() string {
	return "https://" + o.Domain + setting.AppSetting.PartnerPath
}

func (o *Organization) BlogURL() string {
	return "https://" + o.Domain + setting.AppSetting.BlogPath
}

type OrganizationQuery struct {
	ID                *string  `json:"id" form:"id"`
	IDNe              *string  `json:"id_ne" form:"id_ne"`
	IDIn              []string `json:"id_in"`
	Schema            *string  `json:"schema" form:"schema"`
	Domain            *string  `json:"domain" form:"domain"`
	DomainOrAltDomain *string  `json:"domain_or_alt_domain" form:"domain_or_alt_domain"`
	Name              *string  `json:"name" form:"name"`
	UserID            *string  `json:"user_id" form:"user_id"`
	IncludeDeleted    *bool    `json:"include_deleted"`
	Active            *bool    `json:"active" form:"active"`
	SearchTerm        *string  `json:"search_term" form:"search_term"`
	SearchCategories  *string  `json:"search_categories" form:"search_categories"`
}

// Apply builds a GORM query from OrgQuery
func (query *OrganizationQuery) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	if query.ID != nil {
		qb = qb.Where(fmt.Sprintf("%[1]s.id = ?", GetTblName(OrganizationTbl)), *query.ID)
	}

	if query.IDNe != nil {
		qb = qb.Where(fmt.Sprintf("%[1]s.id <> ?", GetTblName(OrganizationTbl)), *query.IDNe)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where(fmt.Sprintf("%[1]s.id IN (?)", GetTblName(OrganizationTbl)), query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.Schema != nil {
		qb = qb.Where("schema = ?", *query.Schema)
	}

	if query.Domain != nil {
		qb = qb.Where("domain = ?", *query.Domain)
	}

	if query.DomainOrAltDomain != nil {
		qb = qb.Where(fmt.Sprintf("(%[1]s.domain = ? OR %[1]s.alt_domain = ?)", GetTblName(OrganizationTbl)), *query.DomainOrAltDomain, *query.DomainOrAltDomain)

	}

	if query.Name != nil {
		qb = qb.Where(fmt.Sprintf("%[1]s.name = ?", GetTblName(OrganizationTbl)), *query.Name)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where(fmt.Sprintf("%[1]s.delete_at = 0", GetTblName(OrganizationTbl)))
	}

	if query.Active != nil {
		qb = qb.Where(fmt.Sprintf("%[1]s.active = ?", GetTblName(OrganizationTbl)), *query.Active)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		for _, category := range categories {
			switch category {
			case "name":
				qb = qb.Where("name ~* ?", *query.SearchTerm)

			case "domain":
				qb = qb.Where("domain ~* ?", *query.SearchTerm)

			case "status":
				qb = qb.Where(fmt.Sprintf("(CASE WHEN %[1]s.active = true THEN 'active' ELSE 'deactive' END) ~* ?", GetTblName(OrganizationTbl)), *query.SearchTerm)

			case "owner_name":
				qb = qb.Joins(fmt.Sprintf("INNER JOIN %[1]s ON %[1]s.id = %[2]s.user_id", GetTblName(UserTbl), GetTblName(OrganizationTbl))).
					Where(fmt.Sprintf("%[1]s.username ~* ?", GetTblName(UserTbl)), *query.SearchTerm)
			}
		}
	}

	return qb
}

// Create inserts an org to database, transaction is optional
func (r *OrganizationRepository) Create(c *Organization, trans *gorm.DB) error {
	return create(OrganizationTbl, c, trans)
}

func (r *OrganizationRepository) CreateMany(orgs []*Organization, trans *gorm.DB) error {
	return createMany(OrganizationTbl, orgs, trans)
}

// Update updates an organization by ID in database, transaction is optional
func (r *OrganizationRepository) Update(c *Organization, trans *gorm.DB) error {
	return update(OrganizationTbl, c, trans)
}

// FindByID finds an Organization by ID with given find options, transaction is optional
func (r *OrganizationRepository) FindByID(id string, options *FindOneOptions) (*Organization, error) {
	if options == nil {
		options = &FindOneOptions{}
	}
	cacheKey := id + "_preloads_" + strings.Join(options.Preloads, "_")
	var orgCache Organization
	Cache.Organization.Get(cacheKey, &orgCache)
	if orgCache.ID != "" {
		return &orgCache, nil
	}
	org, err := findByID[Organization](OrganizationTbl, id, options)
	if err != nil {
		return nil, err
	}
	Cache.Organization.Set(cacheKey, org)
	return org, err
}

// FindByDomain finds an Organization by domain with given find options, transaction is optional
func (r *OrganizationRepository) FindByDomain(domain string) (*Organization, error) {
	options := &FindOneOptions{Preloads: []string{UserField, ThumbnailField, BannerField}}
	cacheKey := domain + "_preloads_" + strings.Join(options.Preloads, "_")
	var orgCache Organization
	Cache.Organization.Get(cacheKey, &orgCache)
	if orgCache.ID != "" {
		return &orgCache, nil
	}
	org, err := r.FindOne(&OrganizationQuery{DomainOrAltDomain: util.NewString(domain)}, options)
	if err != nil {
		return nil, err
	}
	Cache.Organization.Set(cacheKey, org)
	return org, err
}

func (r *OrganizationRepository) FindByIDs(ids []string) ([]*Organization, error) {
	if len(ids) == 0 {
		return []*Organization{}, nil
	}

	var orgs []*Organization
	var queryIDs []string
	for _, id := range ids {
		var orgCache Organization
		Cache.Organization.Get(id, &orgCache)
		if orgCache.ID != "" {
			orgs = append(orgs, &orgCache)
		} else {
			queryIDs = append(queryIDs, id)
		}
	}

	if len(queryIDs) > 0 {
		dbOrgs, err := r.FindMany(&OrganizationQuery{IDIn: queryIDs}, &FindManyOptions{Preloads: []string{"User", "Thumbnail"}})
		if err != nil {
			return nil, err
		}
		orgs = append(orgs, dbOrgs...)

		for _, org := range dbOrgs {
			Cache.Organization.Set(org.ID, org)
		}
	}
	return orgs, nil
}

// FindOne finds one Organization with given find queries and options, transaction is optional
func (r *OrganizationRepository) FindOne(query *OrganizationQuery, options *FindOneOptions) (*Organization, error) {
	return findOne[Organization](OrganizationTbl, query, options)
}

// FindMany finds Organizations by query conditions with give find options
func (r *OrganizationRepository) FindMany(query *OrganizationQuery, options *FindManyOptions) ([]*Organization, error) {
	return findMany[Organization](OrganizationTbl, query, options)
}

// FindPage returns Organization and pagination by query conditions and find options, transaction is optional
func (r *OrganizationRepository) FindPage(query *OrganizationQuery, options *FindPageOptions) ([]*Organization, *Pagination, error) {
	return findPage[Organization](OrganizationTbl, query, options)
}

// Delete perform soft deletion to an org by ID, transaction is optional
func (r *OrganizationRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Organization](OrganizationTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *OrganizationRepository) DeleteMany(query *OrganizationQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Organization](OrganizationTbl, query, trans)
}

// Count returns number of orgs by query conditions, transaction is optional
func (r *OrganizationRepository) Count(query *OrganizationQuery) (int64, error) {
	return count[Organization](OrganizationTbl, query)
}

type OrgHolder interface {
	GetOrgID() string
	SetOrg(org *Organization)
}

func PreloadOrgs[T OrgHolder](items []T) error {
	if len(items) == 0 {
		return nil
	}

	orgIDs := lo.Map(items, func(item T, _ int) string {
		return item.GetOrgID()
	})

	orgs, err := Repository.Organization.FindByIDs(orgIDs)
	if err != nil {
		return err
	}

	orgsByIDs := make(map[string]*Organization)
	for _, org := range orgs {
		orgsByIDs[org.ID] = org
	}

	for _, item := range items {
		org, found := orgsByIDs[item.GetOrgID()]
		if !found {
			continue
		}

		item.SetOrg(org)
	}
	return nil
}
