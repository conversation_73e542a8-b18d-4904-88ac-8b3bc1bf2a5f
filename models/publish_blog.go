package models

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UnPublishBlogTarget string

const (
	UnPublishBlogTargetOrg = "org"
	UnPublishBlogPerson    = "personal"
	UnPublishBlogTargetAll = "all"
)

type PublishBlog struct {
	Model
	OrgID         string `json:"org_id" gorm:"not null;type:varchar(20)"`
	OrgSchema     string `json:"org_schema"`
	BlogID        string `json:"blog_id" gorm:"not null;type:varchar(20)"`
	AuthorId      string `json:"user_id"`
	BlogCuid      string `json:"blog_cuid" gorm:"not null;unique;type:varchar(20)"`
	Author        *User
	Title         string `json:"name"`
	ID            string `gorm:"primaryKey;type:varchar(20);unique" json:"id"`
	Slug          string `json:"slug,omitempty"`
	PubDate       int    `gorm:"type:int8;not null" json:"publish_date"`
	DeleteAt      int    `gorm:"type:int8;not null" json:"delete_at"`
	Description   string `json:"description,omitempty" gorm:"type:text"`
	Props         JSONB  `json:"props" gorm:"type:jsonb"`
	IsPin         bool   `json:"is_pin" gorm:"type:boolean"`
	IsAIGenerated bool   `json:"is_ai_generated" gorm:"type:boolean"`
	Locale        string `json:"locale" gorm:"default:'en'"`
	AIBlogID      string `json:"ai_blog_id" gorm:"type:varchar(20)"`
}

type PublishBlogQuery struct {
	OrgID              *string  `json:"org_id" form:"org_id"`
	NotOrgID           *string  `json:"not_org_id" form:"not_org_id"`
	BlogID             *string  `json:"blog_id" form:"blog_id"`
	BlogCuid           *string  `json:"blog_cuid" form:"blog_cuid"`
	BlogCuidIn         []string `json:"blog_cuid_in" form:"blog_cuid_in"`
	AuthorID           *string  `json:"author_id"`
	UserID             *string  `json:"user_id" form:"user_id"`
	Name               *string  `json:"name" form:"name"`
	IsPin              *bool    `json:"is_pin" form:"is_pin"`
	IncludeUnPublished *bool
	IncludeDeleted     *bool
	IDOrSlug           *string `json:"id_or_slug,omitempty" form:"slug"`
	Locale             *string `json:"locale" form:"locale"`
	BaseSearchQuery
}

func (query *PublishBlogQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.NotOrgID != nil {
		qb = qb.Where("org_id != ?", *query.NotOrgID)
	}

	if query.IDOrSlug != nil {
		qb = qb.Where("(blog_id = ? or slug = ?)", *query.IDOrSlug, *query.IDOrSlug)
	}

	if query.BlogID != nil {
		qb = qb.Where("blog_id = ?", *query.BlogID)
	}
	if query.BlogCuid != nil {
		qb = qb.Where("blog_cuid = ?", *query.BlogCuid)
	}

	if len(query.BlogCuidIn) > 0 {
		qb = qb.Where("blog_cuid IN (?)", query.BlogCuidIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}
	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}
	if query.AuthorID != nil {
		qb = qb.Where("author_id = ?", *query.AuthorID)
	}

	if query.IncludeUnPublished == nil || !*query.IncludeUnPublished {
		qb = qb.Where("pub_date != 0")
	}

	if query.IsPin != nil {
		qb = qb.Where("is_pin = ?", *query.IsPin)
	}

	if query.Locale != nil {
		qb = qb.Where("locale = ?", *query.Locale)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, &PublishBlog{}, nil)

	}
	return qb
}

func (r *PublishBlogRepository) Create(pubBlog *PublishBlog, trans *gorm.DB) error {
	return create(PublishBlogTbl, pubBlog, trans)
}

func (r *PublishBlogRepository) Update(pubBlog *PublishBlog, trans *gorm.DB) error {
	return update(PublishBlogTbl, pubBlog, trans)
}

func (r *PublishBlogRepository) FindOne(query *PublishBlogQuery, options *FindOneOptions) (*PublishBlog, error) {
	return findOne[PublishBlog](PublishBlogTbl, query, options)
}

func (r *PublishBlogRepository) FindMany(query *PublishBlogQuery, options *FindManyOptions) ([]*PublishBlog, error) {
	return findMany[PublishBlog](PublishBlogTbl, query, options)
}

func (r *PublishBlogRepository) FindPage(query *PublishBlogQuery, options *FindPageOptions) ([]*PublishBlog, *Pagination, error) {
	return findPage[PublishBlog](PublishBlogTbl, query, options)
}

func (r *PublishBlogRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[PublishBlog](PublishBlogTbl, id, trans)
}

func (r *PublishBlogRepository) Upsert(b *PublishBlog, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	// Begin trans
	if trans != nil {
		tx = trans
	} else {
		tx = GetDb(PublishBlogTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	if err := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "blog_cuid"}},
		UpdateAll: true,
	}).Create(&b).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit tx
	return tx.Commit().Error

}

// Count returns number of public blogs by query conditions, transaction is optional
func (r *PublishBlogRepository) Count(query *PublishBlogQuery) (int64, error) {
	return count[PublishBlog](PublishBlogTbl, query)
}
