package models

import (
	"errors"
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"

	"gorm.io/gorm"
)

type FileRelationModel string

var timeUnit = time.Hour

type FileRelation struct {
	FileID      string    `json:"file_id" gorm:"primaryKey"`
	RelatedID   string    `json:"related_id" gorm:"primaryKey"`
	RelatedType ModelName `json:"related_type" gorm:"primaryKey"`
	Field       string    `json:"field" gorm:"primaryKey"`
	File        File      `json:"file"`
	Order       int       `json:"order" gorm:"primaryKey,type:int4"`
}

type FileRelationQuery struct {
	FileID      *string  `form:"file_id"`
	RelatedID   *string  `form:"related_id"`
	RelatedIDIn []string `form:"related_id_in"`
	RelatedType *string  `form:"related_type"`
	Field       *string  `form:"field"`
	FieldIn     []string `form:"field_in"`
}

func (query *FileRelationQuery) Apply(tx *gorm.DB) *gorm.DB {
	qb := DB

	if query.FileID != nil {
		qb = qb.Where("file_id = ?", *query.FileID)
	}

	if query.RelatedID != nil {
		qb = qb.Where("related_id = ?", *query.RelatedID)
	}

	if len(query.RelatedIDIn) > 0 {
		qb = qb.Where("related_id IN (?)", query.RelatedIDIn)
	}

	if query.RelatedType != nil {
		qb = qb.Where("related_type = ?", *query.RelatedType)
	}

	if query.Field != nil {
		qb = qb.Where("field = ?", *query.Field)
	}

	if len(query.FieldIn) > 0 {
		qb = qb.Where("field IN (?)", query.FieldIn)
	}
	return qb
}

func (r *FileRelationRepository) CreateMany(entities []*FileRelation, trans *gorm.DB) error {
	return createMany(FileRelationTbl, entities, trans)
}

// FindMany finds file relations by query conditions with give find options
func (r *FileRelationRepository) FindMany(query *FileRelationQuery, options *FindManyOptions) ([]*FileRelation, error) {
	return findMany[FileRelation](FileRelationTbl, query, options)
}

// DeleteMany performs hard deletion to file relations by query conditions
func (r *FileRelationRepository) DeleteMany(query *FileRelationQuery, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	result := qb.Table(GetTblName(FileRelationTbl)).Debug().Delete(&FileRelation{})
	if err := result.Error; err != nil {
		return err
	}

	return nil
}

func (r *FileRelationRepository) AddFiles(model ModelName, entityID string, field string, files []*File) error {
	if files == nil {
		files = []*File{}
	}
	query := FileRelationQuery{
		RelatedID:   util.NewString(entityID),
		RelatedType: util.NewString(string(model)),
		Field:       util.NewString(field),
	}
	var exists []*FileRelation
	if relations, err := Repository.FileRelation.FindMany(&query, nil); err != nil {
		return err
	} else {
		exists = relations
	}

	log.Debug("File exists: ", len(exists))

	var relations []*FileRelation
	for idx, f := range files {
		relations = append(relations, &FileRelation{
			FileID:      f.ID,
			RelatedID:   entityID,
			RelatedType: model,
			Field:       field,
			Order:       idx + 1,
		})
	}

	toCreate := lo.Filter(relations, func(fl *FileRelation, _ int) bool {
		_, ok := lo.Find(exists, func(e *FileRelation) bool {
			return e.FileID == fl.FileID
		})
		return !ok
	})

	log.Debug("toCreate: ", len(toCreate))
	if len(toCreate) > 0 {
		if rErr := Repository.FileRelation.CreateMany(toCreate, nil); rErr != nil {
			log.Debug(rErr)
			return errors.New(fmt.Sprintf("Update `%s` for `%s` failed", field, model))
		}
	}

	toDelete := lo.Filter(exists, func(e *FileRelation, index int) bool {
		_, ok := lo.Find(relations, func(r *FileRelation) bool {
			return r.FileID == e.FileID
		})
		return !ok
	})

	// Delete
	lo.ForEach(toDelete, func(item *FileRelation, _ int) {
		delQuery := &FileRelationQuery{
			FileID:    util.NewString(item.FileID),
			RelatedID: util.NewString(item.RelatedID),
			Field:     util.NewString(field),
		}
		Repository.FileRelation.DeleteMany(delQuery, nil)
	})
	return nil

	//if rErr := Repository.FileRelation.CreateMany(relations, nil); rErr != nil {
	//	return errors.New(fmt.Sprintf("Add `%s` for `%s` failed", field, model))
	//}
	//return nil
}

func (r *FileRelationRepository) GetFiles(model ModelName, entityID string, field string) ([]*File, error) {
	query := FileRelationQuery{
		RelatedID:   util.NewString(entityID),
		RelatedType: util.NewString(string(model)),
		Field:       util.NewString(field),
	}
	option := FindManyOptions{
		Preloads: []string{FileField},
		Sort:     []string{OrderASC},
	}
	if relations, rErr := Repository.FileRelation.FindMany(&query, &option); rErr != nil {
		return nil, errors.New("get course medias failed")
	} else {
		return lo.Map(relations, func(item *FileRelation, _ int) *File {
			return &item.File
		}), nil
	}
}

func (r *FileRelationRepository) GetFilesByEntities(model ModelName, entitiesID []string, field string) (map[string][]*File, error) {
	if len(entitiesID) == 0 {
		return make(map[string][]*File), nil
	}
	fileRelations, err := Repository.FileRelation.FindMany(&FileRelationQuery{
		RelatedIDIn: lo.Uniq(entitiesID),
		Field:       util.NewString(field),
		RelatedType: util.NewString(string(model)),
	}, &FindManyOptions{
		Preloads: []string{FileField},
		Sort:     []string{OrderASC},
	})
	if err != nil {
		return nil, err
	}

	relationsByEntitiesIDs := map[string][]*File{}
	lo.ForEach(fileRelations, func(fr *FileRelation, _ int) {
		if _, found := relationsByEntitiesIDs[fr.RelatedID]; found {
			relationsByEntitiesIDs[fr.RelatedID] = append(relationsByEntitiesIDs[fr.RelatedID], &fr.File)
		} else {
			relationsByEntitiesIDs[fr.RelatedID] = []*File{&fr.File}
		}
	})

	return relationsByEntitiesIDs, nil
}
func (r *FileRelationRepository) FindOne(query *FileRelationQuery, options *FindOneOptions) (*FileRelation, error) {
	return findOne[FileRelation](FileRelationTbl, query, options)
}

func (r *FileRelationRepository) FindPage(query *FileRelationQuery, options *FindPageOptions) ([]*FileRelation, *Pagination, error) {
	frs, pagination, findErr := findPage[FileRelation](FileRelationTbl, query, options)
	if findErr != nil {
		return nil, nil, findErr
	}

	if preloadErr := preloadsFile(frs); preloadErr != nil {
		return nil, nil, preloadErr
	}

	return frs, pagination, nil
}

func preloadsFile(frs []*FileRelation) error {
	fileIDs := lo.Map(frs, func(fr *FileRelation, _ int) string {
		return fr.FileID
	})
	files, err := Repository.File.FindMany(&FileQuery{
		IDIn: fileIDs,
	}, nil)
	if err != nil {
		return err
	}

	mapFileIDFile := make(map[string]*File)
	for _, file := range files {
		mapFileIDFile[file.ID] = file
	}

	lo.ForEach(frs, func(fr *FileRelation, _ int) {
		if file, ok := mapFileIDFile[fr.FileID]; ok {
			fr.File = *file
		}
	})
	return nil
}
