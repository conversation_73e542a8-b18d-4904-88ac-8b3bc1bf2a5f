package models

import (
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type FormEvent string

type FormType string

type FormStatus string

const (
	FormSlugEncodePrefix = "form_slug_"

	FormEventRegisterOrg     FormEvent = "register_organization"
	FormEventRegisterCreator FormEvent = "register_creator"
	FormEventRegisterWriter  FormEvent = "register_writer"
	FormEventRegisterCourse  FormEvent = "register_course"
	FormEventSurveyCourse    FormEvent = "survey_course"
	FormEventContactOrg      FormEvent = "contact_organization"
	FormEventNewUser         FormEvent = "new_user"
	FormEventAIGovernment    FormEvent = "ai-government-campaign"
	FormEventEmpty           FormEvent = "empty"
	FormEventOthers          FormEvent = "others"

	FormTypeSlide = "slide"
	FormTypePage  = "page"

	FormStatusDraft        FormStatus = "draft"
	FormStatusUnPublished  FormStatus = "unpublished"
	FormStatusPublishedOrg FormStatus = "published_org"
	FormStatusPublishedAll FormStatus = "published_all" // ca org va openedu deu nhan duoc request, org nao approve thi su dung tren

	FormTitleRegisterOrg     = "Organization Registration Form"
	FormTitleRegisterCreator = "Creator Registration Form"
	FormTitleRegisterWriter  = "Writer Registration Form"
	FormTitleContactOrg      = "New user survey form"
	FormTitleNewUserForm     = "Form new user in first entry"
	FormTitleAIGovernment    = "AI Government Form"
)

var FindOneFormOptsFullPreloads = &FindOneOptions{
	CustomPreloads: map[string]func(*gorm.DB) *gorm.DB{
		util.QuestionsField: func(db *gorm.DB) *gorm.DB {
			return db.Where("parent_id IS NULL AND delete_at = 0").Order(`"order" ASC`)
		},
		util.QuestionsOptionsField: func(db *gorm.DB) *gorm.DB {
			return db.Where("delete_at = 0")
		},
		util.QuestionsSubQuestionsField: func(db *gorm.DB) *gorm.DB {
			return db.Where("delete_at = 0")
		},
	},
}

var FindManyFormsOptsFullPreloads = &FindManyOptions{
	CustomPreloads: map[string]func(*gorm.DB) *gorm.DB{
		util.QuestionsField: func(db *gorm.DB) *gorm.DB {
			return db.Where("parent_id IS NULL AND delete_at = 0").Order(`"order" ASC`)
		},
		util.QuestionsOptionsField: func(db *gorm.DB) *gorm.DB {
			return db.Where("delete_at = 0")
		},
		util.QuestionsSubQuestionsField: func(db *gorm.DB) *gorm.DB {
			return db.Where("delete_at = 0")
		},
	},
}

type Form struct {
	Model
	UID          string          `json:"uid"`
	Latest       bool            `json:"latest" gorm:"default:true"`
	Title        string          `json:"title"`
	Description  string          `json:"description"`
	Slug         string          `json:"slug"`
	Event        FormEvent       `json:"event" gorm:"type:varchar(100)"`
	Type         FormType        `json:"type" gorm:"type:varchar(100)"`
	Status       FormStatus      `json:"status" gorm:"type:varchar(100)"`
	StartDate    int             `gorm:"type:int8" json:"start_date"`
	EndDate      int             `gorm:"type:int8" json:"end_date"`
	OrgID        string          `json:"org_id" gorm:"type:varchar(20)"`
	CreatorID    string          `json:"creator_id" gorm:"type:varchar(20)"`
	Questions    []*FormQuestion `json:"questions" gorm:"foreignKey:FormID;references:ID"`
	AuthRequired bool            `json:"auth_required"`
	IsTemplate   bool            `json:"is_template" gorm:"default:false"`
}

type SimpleForm struct {
	Model
	UID          string                `json:"uid"`
	Title        string                `json:"title"`
	Description  string                `json:"description"`
	Slug         string                `json:"slug"`
	Event        FormEvent             `json:"event"`
	Type         FormType              `json:"type"`
	Status       FormStatus            `json:"status"`
	StartDate    int                   `gorm:"type:int8" json:"start_date"`
	EndDate      int                   `gorm:"type:int8" json:"end_date"`
	OrgID        string                `json:"org_id"`
	CreatorID    string                `json:"creator_id"`
	Questions    []*SimpleFormQuestion `json:"questions"`
	AuthRequired bool                  `json:"auth_required"`
	IsTemplate   bool                  `json:"is_template"`
}

func (f *Form) Sanitize() *SimpleForm {
	return &SimpleForm{
		Model:       f.Model,
		UID:         f.UID,
		Title:       f.Title,
		Description: f.Description,
		Slug:        f.Slug,
		Event:       f.Event,
		Type:        f.Type,
		Status:      f.Status,
		StartDate:   f.StartDate,
		EndDate:     f.EndDate,
		OrgID:       f.OrgID,
		CreatorID:   f.CreatorID,
		Questions: lo.Map(f.Questions, func(question *FormQuestion, _ int) *SimpleFormQuestion {
			return question.Sanitize()
		}),
		AuthRequired: f.AuthRequired,
		IsTemplate:   f.IsTemplate,
	}
}

func IsFormSlug(s string) bool {
	decodedStr := util.Base64Decode(s)
	return strings.HasPrefix(decodedStr, FormSlugEncodePrefix)
}

func (f *Form) GetID() string {
	return f.ID
}

func (f *Form) GetSlug() string {
	return f.Slug
}

func (f *Form) GenerateSlug() {
	f.Slug = util.Base64Encode(FormSlugEncodePrefix + util.GenerateId())
}

func (f *Form) IsDraft() bool {
	return f.Status == FormStatusDraft
}

func (f *Form) IsPublishedAll() bool {
	return f.Status == FormStatusPublishedAll
}

func (f *Form) IsPublishedOrg() bool {
	return f.Status == FormStatusPublishedOrg
}

func (f *Form) IsUnPublished() bool {
	return f.Status == FormStatusUnPublished
}

func (f *Form) IsExpired() bool {
	now := time.Now()
	return (f.StartDate != 0 && int64(f.StartDate) > now.UnixMilli()) &&
		(f.EndDate != 0 && now.UnixMilli() < int64(f.EndDate))
}

func (f *Form) IsFormRegisterOrg() bool {
	return f.Event == FormEventRegisterOrg
}

func (f *Form) IsFormRegisterCreator() bool {
	return f.Event == FormEventRegisterCreator
}

func (f *Form) IsFormRegisterWriter() bool {
	return f.Event == FormEventRegisterWriter
}

func (f *Form) IsFormRegisterCourse() bool {
	return f.Event == FormEventRegisterCourse
}

func (f *Form) BelongsToOrg(org *Organization) bool {
	return org != nil && org.ID == f.OrgID
}

func (f *Form) IsRequiredAuth() bool {
	return f.AuthRequired
}

func (f *Form) CanViewForm(user *User, org *Organization) bool {
	if f.IsPublishedAll() {
		return true
	}

	if user == nil || org == nil {
		return false
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Form:CanViewForm: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	switch f.Status {
	case FormStatusPublishedOrg:
		return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
			return f.OrgID == role.OrgID
		})

	case FormStatusDraft,
		FormStatusUnPublished:
		return IsSysAdminRoles(roles) || ExactlyOrgAdminRoles(roles, f.OrgID) || IsPartnerRoles(roles, f.OrgID)

	default:
		return false
	}
}

func (f *Form) CheckValid() error {
	return nil
}

func (f *Form) CanUpdateForm(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Form:CanUpdateForm: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	switch f.Event {
	case FormEventRegisterOrg,
		FormEventRegisterCreator:
		return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
			return IsSysAdminRole(role.RoleID) || (IsOrgAdminRole(role.RoleID) && f.OrgID == role.OrgID)
		})
	default:
		return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
			return IsSysAdminRole(role.RoleID) || ((IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType) && f.OrgID == role.OrgID)
		})
	}
}

func (f *Form) CanDuplicateForm(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Form:CanDuplicateForm: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	switch f.Event {
	case FormEventRegisterOrg,
		FormEventRegisterCreator:
		return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
			return IsSysAdminRole(role.RoleID) || (IsOrgAdminRole(role.RoleID) && f.OrgID == role.OrgID)
		})
	default:
		return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
			return IsSysAdminRole(role.RoleID) || ((IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType) && f.OrgID == role.OrgID)
		})
	}
}

func (f *Form) CanDeleteForm(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Form:CanDeleteForm: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	switch f.Event {
	case FormEventRegisterOrg,
		FormEventRegisterCreator:
		return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
			return IsSysAdminRole(role.RoleID) || (IsOrgAdminRole(role.RoleID) && f.OrgID == role.OrgID)
		})
	default:
		return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
			return IsSysAdminRole(role.RoleID) || ((IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType) && f.OrgID == role.OrgID)
		})
	}
}

func (f *Form) CanViewSummary(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	if f.Event == FormEventNewUser {
		return true
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Form:CanViewSummary: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
		return IsSysAdminRole(role.RoleID) || ((IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType) && f.OrgID == role.OrgID)
	})
}

func (f *Form) CanViewSessions(user *User, org *Organization) bool {
	if f.Event == FormEventEmpty {
		return true
	}

	if f.Event == FormEventNewUser {
		return true
	}

	if user == nil || org == nil {
		return false
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Form:CanViewSessions: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
		return IsSysAdminRole(role.RoleID) || ((IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType) && f.OrgID == role.OrgID)
	})
}

func (e FormEvent) IsValid() bool {
	switch e {
	case FormEventRegisterOrg,
		FormEventRegisterCreator,
		FormEventRegisterCourse,
		FormEventSurveyCourse,
		FormEventOthers:
		return true

	default:
		return false
	}
}

func (t FormType) IsValid() bool {
	switch t {
	case FormTypeSlide,
		FormTypePage:
		return true

	default:
		return false
	}
}

func (s FormStatus) IsValid() bool {
	switch s {
	case FormStatusDraft,
		FormStatusUnPublished,
		FormStatusPublishedOrg,
		FormStatusPublishedAll:
		return true

	default:
		return false
	}
}

type FormQuery struct {
	ID             *string      `json:"id,omitempty" form:"id"`
	UID            *string      `json:"uid,omitempty" form:"uid"`
	IDIn           []string     `json:"id_in,omitempty" form:"id_in"`
	OrgID          *string      `json:"org_id,omitempty" form:"org_id"`
	CreatorID      *string      `json:"creator_id,omitempty" form:"creator_id"`
	Slug           *string      `json:"slug,omitempty" form:"slug"`
	Event          *FormEvent   `json:"event,omitempty" form:"event"`
	StartDateGte   *int         `json:"start_date_gte,omitempty" form:"start_date_gte"`
	EndDateLte     *int         `json:"end_date_lte,omitempty" form:"end_date_lte"`
	IsUnExpired    *bool        `json:"is_unexpired,omitempty" form:"is_unexpired"`
	IsTemplate     *bool        `json:"is_template,omitempty" form:"is_template"`
	Latest         *bool        `json:"latest,omitempty" form:"latest"`
	Status         *FormStatus  `json:"status,omitempty" form:"status"`
	StatusIn       []FormStatus `json:"status_in,omitempty" form:"status_in"`
	StatusNotIn    []FormStatus `json:"status_not_in,omitempty" form:"status_not_in"`
	IncludeDeleted *bool        `json:"include_deleted,omitempty" form:"include_deleted"`
}

func (query *FormQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UID != nil {
		qb = qb.Where("uid = ?", *query.UID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.CreatorID != nil {
		qb = qb.Where("creator_id = ?", *query.CreatorID)
	}

	if query.Slug != nil {
		qb = qb.Where("slug = ?", *query.Slug)
	}

	if query.IsTemplate != nil {
		qb = qb.Where("is_template = ?", *query.IsTemplate)
	}

	if query.Event != nil {
		qb = qb.Where("event = ?", *query.Event)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.StartDateGte != nil {
		qb = qb.Where("start_date >= ?", *query.StartDateGte)
	}

	if query.EndDateLte != nil {
		qb = qb.Where("end_date <= ?", *query.EndDateLte)
	}

	if query.IsUnExpired != nil {
		nowMilli := time.Now().UnixMilli()
		if *query.IsUnExpired {
			qb = qb.Where("(start_date = 0 OR start_date >= ?) AND (end_date = 0 OR end_date <= ?)", nowMilli, nowMilli)
		} else {
			qb = qb.Where("(start_date <> 0 AND start_date < ?) OR (end_date <> 0 AND end_date > ?)", nowMilli, nowMilli)
		}
	}

	if query.Latest != nil {
		qb = qb.Where("latest = ?", *query.Latest)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", *&query.StatusIn)
	}

	if len(query.StatusNotIn) > 0 {
		qb = qb.Where("status NOT IN (?)", *&query.StatusNotIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *FormRepository) Create(f *Form, trans *gorm.DB) (err error) {
	if f.Slug == "" {
		f.GenerateSlug()
	}

	if f.UID == "" {
		f.UID = util.GenerateId()
	}

	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Omit("Questions").Table(GetTblName(FormTbl)).Debug().Create(&f).Error
	return
}

func (r *FormRepository) CreateMany(fs []*Form, trans *gorm.DB) (err error) {
	if len(fs) == 0 {
		return nil
	}

	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Omit("Questions").Table(GetTblName(FormTbl)).Debug().Create(&fs).Error
	return
}

func (r *FormRepository) Update(f *Form, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Table(GetTblName(FormTbl)).Debug().Select("*").Omit("id", "create_at", "Questions").Updates(&f).Error; err != nil {
		return
	}
	if cErr := Cache.Form.Flush(); cErr != nil {
		log.Errorf("FormRepository.Delete::Clear cache forms error: %v", cErr)
	}
	return
}

func (r *FormRepository) FindByIDWithOptions(id string, options *FindOneOptions) (*Form, error) {
	return findByID[Form](FormTbl, id, options)
}

func (r *FormRepository) FindByID(id string) (*Form, error) {
	var formCache Form
	Cache.Form.GetByFormID(id, &formCache)
	if formCache.ID != "" {
		return &formCache, nil
	}

	options := FindOneFormOptsFullPreloads
	form, err := findByID[Form](FormTbl, id, options)
	if err != nil {
		return nil, err
	}

	if cErr := Cache.Form.SetFormByID(form.ID, form); cErr != nil {
		log.Errorf("FormRepository.FindByID::Set form to cache error: %v", cErr)
	}

	if cErr := Cache.Form.SetFormBySlug(form.GetSlug(), form); cErr != nil {
		log.Errorf("FormRepository.FindByID::Set form to cache error: %v", cErr)
	}
	return form, nil
}

func (r *FormRepository) FindBySlug(slug string) (*Form, error) {
	var formCache Form
	Cache.Form.GetByFormSlug(slug, &formCache)
	if formCache.ID != "" {
		return &formCache, nil
	}

	query := &FormQuery{Slug: &slug}
	options := FindOneFormOptsFullPreloads
	form, err := findOne[Form](FormTbl, query, options)
	if err != nil {
		return nil, err
	}

	if cErr := Cache.Form.SetFormByID(form.ID, form); cErr != nil {
		log.Errorf("FormRepository.FindBySlug::Set form to cache error: %v", cErr)
	}

	if cErr := Cache.Form.SetFormBySlug(form.GetSlug(), form); cErr != nil {
		log.Errorf("FormRepository.FindBySlug::Set form to cache error: %v", cErr)
	}
	return form, nil
}

func (r *FormRepository) getCacheKey(query *FormQuery, options *FindOneOptions) string {
	var cacheKey string
	if query != nil && query.Event != nil &&
		options != nil && options == FindOneFormOptsFullPreloads {
		switch *query.Event {
		case FormEventRegisterOrg:
			cacheKey = string(FormEventRegisterOrg)

		case FormEventRegisterCreator:
			if query.OrgID != nil {
				cacheKey = *query.OrgID + "__" + string(FormEventRegisterCreator)
			}

		case FormEventRegisterWriter:
			if query.OrgID != nil {
				cacheKey = *query.OrgID + "__" + string(FormEventRegisterWriter)
			}

		case FormEventContactOrg:
			if query.OrgID != nil {
				cacheKey = *query.OrgID + "__" + string(FormEventContactOrg)
			}
		}
	}
	return cacheKey
}

func (r *FormRepository) FindOne(query *FormQuery, options *FindOneOptions) (*Form, error) {
	var formCache Form
	cacheKey := r.getCacheKey(query, options)
	if cacheKey != "" {
		Cache.Form.GetByKey(cacheKey, &formCache)
	}

	if formCache.ID != "" {
		return &formCache, nil
	}

	form, err := findOne[Form](FormTbl, query, options)
	if err != nil {
		return nil, err
	}

	if cacheKey != "" {
		if cErr := Cache.Form.SetFormByKey(cacheKey, form); cErr != nil {
			log.Errorf("FormRepository.FindOne::Set form with key %s to cache error: %v", cacheKey, cErr)
		}
	}
	return form, nil
}

func (r *FormRepository) FindMany(query *FormQuery, options *FindManyOptions) ([]*Form, error) {
	return findMany[Form](FormTbl, query, options)
}

func (r *FormRepository) FindPage(query *FormQuery, options *FindPageOptions) ([]*Form, *Pagination, error) {
	return findPage[Form](FormTbl, query, options)
}

func (r *FormRepository) Delete(id string, trans *gorm.DB) error {
	if err := deleteByID[Form](FormTbl, id, trans); err != nil {
		return err
	}

	if cErr := Cache.Form.Flush(); cErr != nil {
		log.Errorf("FormRepository.Delete::Clear cache forms error: %v", cErr)
	}
	return nil
}

func (r *FormRepository) DeleteMany(query *FormQuery, trans *gorm.DB) (int64, error) {
	deletedCount, err := deleteMany[Form](FormTbl, query, trans)
	if err != nil {
		return 0, err
	}

	if cErr := Cache.Form.Flush(); cErr != nil {
		log.Errorf("FormRepository.DeleteMany::Clear cache forms error: %v", cErr)
	}
	return deletedCount, nil
}
