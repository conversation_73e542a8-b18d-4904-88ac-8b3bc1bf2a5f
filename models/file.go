package models

import (
	"context"
	"fmt"
	"net/http"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"

	"gorm.io/gorm"
)

type File struct {
	UserID       string `json:"user_id" gorm:"default:''"`
	Name         string `json:"name"`
	Mime         string `json:"mime"`
	Ext          string `json:"ext"`
	URL          string `json:"url"`
	ThumbnailURL string `json:"thumbnail_url"`
	Model
	Width          int64     `json:"width"`
	Height         int64     `json:"height"`
	Size           int64     `json:"size"`
	BunnyVideoID   string    `json:"bunny_video_id"`
	BunnyLibraryID string    `json:"bunny_library_id"`
	Duration       int64     `json:"duration"`
	Props          FileProps `json:"props" gorm:"-"`
}

type FileProps struct {
	PresignedRequest *PresignedHTTPRequest `json:"presigned_request,omitempty"`
}

type PresignedHTTPRequest struct {
	URL          string      `json:"url,omitempty"`
	Method       string      `json:"method,omitempty"`
	SignedHeader http.Header `json:"signed_header,omitempty"`
}

type SimpleFile struct {
	UserID       string `json:"user_id" dbscan:"user_id"`
	ID           string `json:"id" dbscan:"id"`
	Name         string `json:"name" dbscan:"name"`
	Mime         string `json:"mime" dbscan:"mime"`
	Ext          string `json:"ext" dbscan:"ext"`
	URL          string `json:"url" dbscan:"url"`
	ThumbnailURL string `json:"thumbnail_url" dbscan:"thumbnail_url"`
	BunnyVideoID string `json:"bunny_video_id" dbscan:"bunny_video_id"`
	Duration     int64  `json:"duration" dbscan:"duration"`
}

type FileQuery struct {
	ID             *string
	Ext            *string
	IDIn           []string
	IncludeDeleted *bool
	BunnyVideoID   *string
	UserID         *string
}

const (
	VideoMimeTypePrefix      = "video/"
	DefaultVideoDuration     = 0
	MIMETypeAppleMPEGURL     = "application/vnd.apple.mpegurl"
	MIMETypeImagePng         = "image/png"
	MIMETypeTextPlain       = "text/plain"
	MIMETypeApplicationJson = "application/json"
	MIMETypeApplicationExcel = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
)

func NewSimpleFile(f *File) *SimpleFile {
	return &SimpleFile{
		UserID:       f.UserID,
		ID:           f.ID,
		Name:         f.Name,
		Mime:         f.Mime,
		Ext:          f.Ext,
		URL:          f.URL,
		ThumbnailURL: f.ThumbnailURL,
		BunnyVideoID: f.BunnyVideoID,
		Duration:     f.Duration,
	}
}

func (f *File) DeepCopy() *File {
	return &File{
		Model:        f.Model,
		Name:         f.Name,
		Mime:         f.Mime,
		Ext:          f.Ext,
		URL:          f.URL,
		ThumbnailURL: f.ThumbnailURL,
		BunnyVideoID: f.BunnyVideoID,
		Duration:     f.Duration,
		UserID:       f.UserID,
	}
}

func (f *File) Sanitize() *SimpleFile {
	return &SimpleFile{
		ID:           f.ID,
		Name:         f.Name,
		Mime:         f.Mime,
		Ext:          f.Ext,
		URL:          f.URL,
		ThumbnailURL: f.ThumbnailURL,
		BunnyVideoID: f.BunnyVideoID,
		Duration:     f.Duration,
		UserID:       f.UserID,
	}
}

func (f *File) IsVideo() bool {
	return strings.HasPrefix(f.Mime, VideoMimeTypePrefix) || f.Mime == MIMETypeAppleMPEGURL
}

func (f *File) IsFromBunny() bool {
	// get only resource name from https://<<resource-name>>/path
	bunnyResourceName := strings.Split(setting.UploadSetting.UploadBunnyIFrameURL, "/")[2]
	return strings.Contains(f.URL, bunnyResourceName)
}

func (f *File) IsFromS3() bool {
	// s3 object url has format: https://{{bucket name}}.s3.{{region}}.amazonaws.com/{{object key}}
	return strings.Contains(f.URL, ".s3.")
}

func (f *File) IsBunnyVideo() bool {
	return f.IsFromBunny() && f.IsVideo()
}

func (f *File) IsS3Video() bool {
	return f.IsFromS3() && f.IsVideo()
}

// Apply builds a GORM query from FileQuery
func (query *FileQuery) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	if query.ID != nil {
		qb = qb.Where("id = ?", query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", query.UserID)
	}

	if query.Ext != nil {
		qb = qb.Where("ext = ?", query.Ext)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.BunnyVideoID != nil {
		qb = qb.Where("bunny_video_id = ?", query.BunnyVideoID)
	}

	return qb
}

// Create inserts a file to database, transaction is optional
func (r *FileRepository) Create(f *File, trans *gorm.DB) error {
	return create(FileTbl, f, trans)
}

// CreateMany inserts files to database, transaction is optional
func (r *FileRepository) CreateMany(files []*File, trans *gorm.DB) error {
	return createMany(FileTbl, files, trans)
}

// Update updates a file by ID in database, transaction is optional
func (r *FileRepository) Update(f *File, trans *gorm.DB) error {
	return update(FileTbl, f, trans)
}

// FindByID finds a file by ID with give find options
func (r *FileRepository) FindByID(id string, options *FindOneOptions) (*File, error) {
	return findByID[File](FileTbl, id, options)
}

// FindOne finds one file by query conditions with give find options
func (r *FileRepository) FindOne(query *FileQuery, options *FindOneOptions) (*File, error) {
	return findOne[File](FileTbl, query, options)
}

// FindMany finds files by query conditions with give find options
func (r *FileRepository) FindMany(query *FileQuery, options *FindManyOptions) ([]*File, error) {
	return findMany[File](FileTbl, query, options)
}

// FindPage returns files and pagination by query conditions and find options, transaction is optional
func (r *FileRepository) FindPage(query *FileQuery, options *FindPageOptions) ([]*File, *Pagination, error) {
	return findPage[File](FileTbl, query, options)
}

// Delete performs soft deletion to a file by ID
func (r *FileRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[File](FileTbl, id, trans)
}

// DeleteMany performs soft deletion to files by query conditions
func (r *FileRepository) DeleteMany(query *FileQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[File](FileTbl, query, trans)
}

// Count returns number of files by query conditions, transaction is optional
func (r *FileRepository) Count(query *FileQuery) (int64, error) {
	return count[File](FileTbl, query)
}

func (f *File) AfterFind(tx *gorm.DB) (err error) {
	if f.IsBunnyVideo() && f.BunnyLibraryID == setting.UploadSetting.UploadPrivateBunnyLibraryID {
		nowTime := time.Now()
		expireTime := nowTime.Add(time.Duration(setting.UploadSetting.UploadBunnyIFrameTokenExpireIn) * timeUnit).UnixMilli()
		expireTimeStr := fmt.Sprintf("%d", expireTime)

		iFrameAuthKey := setting.UploadSetting.UploadPrivateBunnyIFrameAuthKey
		signInput := iFrameAuthKey + f.BunnyVideoID + expireTimeStr
		sha256Hash := util.HashSHA256(signInput)

		signBunnyIFrame := fmt.Sprintf("%s?token=%s&expires=%s", f.URL, sha256Hash, expireTimeStr)

		f.URL = signBunnyIFrame
	} else if f.IsS3Video() {
		objKey, err := util.GetS3ObjectKeyFromURL(f.URL)
		if err != nil {
			return err
		}

		bucketName, err := util.GetS3BucketFromURL(f.URL)
		if err != nil {
			return err
		}

		s3Cfg, err := config.LoadDefaultConfig(
			context.Background(),
			config.WithRegion(setting.AwsSetting.Region),
			config.WithCredentialsProvider(
				credentials.NewStaticCredentialsProvider(setting.AwsSetting.AccessKey, setting.AwsSetting.SecretKey, ""),
			),
		)
		if err != nil {
			return err
		}

		s3Client := s3.NewFromConfig(s3Cfg)
		presignedReq, err := util.GetS3PresignedURL(context.Background(), s3Client, bucketName, objKey, time.Duration(setting.UploadSetting.UploadBunnyIFrameTokenExpireIn)*timeUnit)
		if err != nil {
			return err
		}

		f.URL = presignedReq.URL
		f.Props.PresignedRequest = &PresignedHTTPRequest{
			URL:          presignedReq.URL,
			Method:       presignedReq.Method,
			SignedHeader: presignedReq.SignedHeader,
		}
	}
	return
}
