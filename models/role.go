package models

import (
	"github.com/samber/lo"
	"gorm.io/gorm"
)

const (
	SystemAdminRoleType   = "system_admin"
	AdminRoleType         = "admin"
	ModeratorRoleType     = "moderator"
	OrgAdminRoleType      = "org_admin"
	OrgModeratorRoleType  = "org_moderator"
	OrgModerator2RoleType = "org_moderator_2"
	PartnerRoleType       = "partner"
	LearnerRoleType       = "learner"
	GuestRoleType         = "guest"
	OrgEditorRoleType     = "org_editor"
	OrgWriterRoleType     = "org_writer"
)

var AllRole = []string{
	SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType, PartnerRoleType, LearnerRoleType, GuestRoleType, OrgEditorRoleType, OrgWriterRoleType,
}

var CanCreateFormRegisterOrgRoles = []string{
	SystemAdminRoleType, AdminRoleType, ModeratorRoleType,
}

var CanCreateFormRegisterInstructorRoles = []string{
	SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType,
}

var CanCreateFormRegisterCourseRoles = []string{
	SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType, PartnerRoleType,
}

var CanUpdateRoleAdminRoles = []string{
	SystemAdminRoleType, AdminRoleType,
}

var Permission2CreateUserWithRole = map[string][]string{
	SystemAdminRoleType:   {SystemAdminRoleType},
	AdminRoleType:         {SystemAdminRoleType},
	ModeratorRoleType:     {SystemAdminRoleType, AdminRoleType},
	OrgAdminRoleType:      {SystemAdminRoleType, AdminRoleType, ModeratorRoleType},
	OrgModeratorRoleType:  {SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType},
	OrgModerator2RoleType: {SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType},
	PartnerRoleType:       {SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType},
	OrgEditorRoleType:     {SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType},
	OrgWriterRoleType:     {SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType, OrgEditorRoleType},
	LearnerRoleType:       {SystemAdminRoleType, AdminRoleType, ModeratorRoleType, OrgAdminRoleType, OrgModeratorRoleType},
}

func CanCreateUserWithRole(creatorRoles []*UserRoleOrg, orgID string, targetRole string) bool {
	allowRoles := Permission2CreateUserWithRole[targetRole]
	if len(allowRoles) == 0 {
		return false
	}

	_, ok := lo.Find(creatorRoles, func(item *UserRoleOrg) bool {
		return lo.Contains(allowRoles, item.RoleID) && item.OrgID == orgID
	})

	return ok
}

var CanUpdateRoleModeratorRoles = CanUpdateRoleAdminRoles

var CanUpdateRoleOrgAdminRoles = append(CanUpdateRoleModeratorRoles, OrgAdminRoleType)

var CanUpdateRoleOrgModeratorRoles = CanUpdateRoleOrgAdminRoles

var CanUpdateRolePartnerRoles = CanUpdateRoleOrgModeratorRoles

var CanUpdaterRoleEditorRoles = CanUpdateRoleOrgModeratorRoles

var CanUpdaterRoleWriterRoles = append(CanUpdaterRoleEditorRoles, OrgEditorRoleType)

type Role struct {
	ID          string `json:"id" gorm:"primaryKey;type:varchar(50);unique"`
	CreateAt    int    `gorm:"type:int8;not null" json:"create_at"`
	UpdateAt    int    `gorm:"type:int8;not null" json:"update_at"`
	DeleteAt    int    `gorm:"type:int8;not null" json:"delete_at"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Level       int    `gorm:"int8" json:"level"`
	OrgId       string `json:"org_id" gorm:"default:''"`
	Default     bool   `json:"default" gorm:"not null; default:false"`
}

type RoleQuery struct {
	ID             *string  `form:"id" json:"id"`
	IDIn           []string `form:"id_in" json:"id_in,omitempty"`
	Name           *string  `form:"name" json:"name"`
	Description    *string  `form:"description" json:"description"`
	OrgId          *string  `form:"org_id" json:"org_id"`
	Default        *bool    `form:"default" json:"default"`
	IncludeDeleted *bool    `form:"include_deleted" json:"include_deleted,omitempty"`
	AllAndOrgID    *string  `json:"all_and_org_id" form:"all_and_org_id"`
}

func (query *RoleQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}
	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Description != nil {
		qb = qb.Where("description = ?", *query.Description)
	}

	if query.OrgId != nil {
		qb = qb.Where("org_id = ?", *query.OrgId)
	}

	if query.Default != nil {
		qb = qb.Where("default = ?", *query.Default)
	}

	if query.AllAndOrgID != nil {
		qb = qb.Where("org_id = '' OR org_id = ?", *query.AllAndOrgID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}
	return qb
}

func IsSysAdminRole(roleId string) bool {
	return roleId == SystemAdminRoleType || roleId == AdminRoleType || roleId == ModeratorRoleType
}

func IsOrgAdminRole(roleId string) bool {
	return roleId == OrgAdminRoleType || roleId == OrgModeratorRoleType
}

func IsCreatorRole(roleId string) bool {
	return roleId == PartnerRoleType
}

func IsSysAdminRoles(roles []*UserRoleOrg) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return IsSysAdminRole(item.RoleID)
	})
}

func IsPartnerRoles(roles []*UserRoleOrg, orgID string) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return item.RoleID == PartnerRoleType && item.OrgID == orgID
	})
}

func IsOrgAdminRoles(roles []*UserRoleOrg) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return IsOrgAdminRole(item.RoleID)
	})
}

func IsOrgEditor(roles []*UserRoleOrg, orgID string) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return item.RoleID == OrgEditorRoleType && item.OrgID == orgID
	})
}

func ExactlyOrgAdminRoles(roles []*UserRoleOrg, orgID string) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return (IsOrgAdminRole(item.RoleID) && item.OrgID == orgID)
	})
}

func IsOrgMod(roles []*UserRoleOrg, orgID string) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return item.RoleID == OrgModeratorRoleType && item.OrgID == orgID
	})
}

func IsOrgWriter(roles []*UserRoleOrg, orgID string) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return item.RoleID == OrgWriterRoleType && item.OrgID == orgID
	})
}

func ExactlyOrgCreatorRoles(roles []*UserRoleOrg, orgID string) bool {
	return lo.ContainsBy(roles, func(item *UserRoleOrg) bool {
		return (IsCreatorRole(item.RoleID) && item.OrgID == orgID)
	})

}

func (r *RoleRepository) CreateOrUpdate(role *Role) (*Role, error) {
	result := GetDb(RoleTbl).Where(&Role{ID: role.ID}).Attrs(&role).FirstOrCreate(&role)
	if result.Error != nil {
		return nil, result.Error
	}

	return role, nil
}

func (r *RoleRepository) CreateRole(role *Role) (*Role, error) {
	result := GetDb(RoleTbl).Create(&role)
	if result.Error != nil {
		return nil, result.Error
	}

	return role, nil
}

func (r *RoleRepository) CreateRoles(roles []*Role) ([]*Role, error) {
	result := GetDb(RoleTbl).Create(&roles)
	if result.Error != nil {
		return nil, result.Error
	}

	return roles, nil
}

func (r *RoleRepository) FindRoles(pageNum int, PerPage int, maps any) ([]*Role, error) {
	// TODO: implement find roles
	return nil, nil
}

func (r *RoleRepository) FindAll() ([]*Role, error) {
	var roles []*Role
	if err := GetDb(RoleTbl).Find(&roles).Where("delete_at = 0").Error; err != nil {
		return nil, nil
	}
	return roles, nil
}

func (r *RoleRepository) FindById(id string) (*Role, error) {
	var role Role
	err := GetDb(RoleTbl).Where("id = ?", id).First(&role).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

func (r *RoleRepository) FindMapAllRoleToLevel() (map[string]int, error) {
	roles, err := r.FindAll()
	if err != nil {
		return nil, err
	}
	roleMap := make(map[string]int)
	for _, role := range roles {
		roleMap[role.ID] = role.Level
	}
	return roleMap, nil
}

func (r *RoleRepository) CountTotalRoles() (int64, error) {
	var total int64
	if err := GetDb(RoleTbl).Model(&Role{}).Count(&total).Error; err != nil {
		return -1, err
	}
	return total, nil
}

func (r *RoleRepository) Update(p *Role, trans *gorm.DB) error {
	return update(RoleTbl, p, trans)
}

func (r *RoleRepository) Delete(id string, org *string, trans *gorm.DB) error {
	var role *Role
	var err error

	if org != nil {
		err = GetDb(RoleTbl).Where("id = ?", id).Where(`"default" = ?`, false).Where("org_id = ?", *org).First(&role).Error
	} else {
		err = GetDb(RoleTbl).Where("id = ?", id).Where(`"default" = ?`, false).First(&role).Error
	}

	if err != nil {
		return err
	}

	if role != nil {
		return deleteByID[Role](RoleTbl, id, trans)
	}
	return nil
}

func (r *RoleRepository) FindPage(query *RoleQuery, options *FindPageOptions) ([]*Role, *Pagination, error) {
	return findPage[Role](RoleTbl, query, options)
}
