package models

import (
	"gorm.io/gorm"
	"strings"
)

type ReferrerType string

const (
	ReferrerTypeUser          ReferrerType = "user"
	ReferrerTypePurchasedUser ReferrerType = "purchased_user"
	ReferrerTypeKOL           ReferrerType = "kol"
	ReferrerTypeAgency        ReferrerType = "agency"
)

type Referrer struct {
	Model
	UserID       string        `json:"user_id" gorm:"type:varchar(20)"`
	OrgID        string        `json:"org_id" gorm:"type:varchar(20);not null"`
	CampaignID   string        `json:"campaign_id" gorm:"type:varchar(20);not null"`
	Type         ReferrerType  `json:"type" gorm:"default:user"`
	Enable       bool          `json:"enable" gorm:"default:true"`
	Email        string        `json:"email"`
	InviteStatus DefaultStatus `json:"invite_status"`

	PurchasedLinkID       *string       `json:"purchased_link_id" type:"varchar(20)"` //referral_link that user purchased
	PurchasedLink         *ReferralLink `json:"purchased_link"`
	PurchasedCommissionID *string       `json:"purchased_commission_id" type:"varchar(20)"` // commission from link user purchased, user can create link from link comm
	PurchasedCommission   *Commission   `json:"purchased_commission"`
}

type ReferrerQuery struct {
	ID             *string        `json:"id" form:"id"`
	IDIn           []*string      `json:"id_in" form:"id_in"`
	UserID         *string        `json:"user_id" form:"user_id"`
	CampaignID     *string        `json:"campaign_id" form:"campaign_id"`
	CampaignIDIn   []string       `json:"campaign_id_in" form:"campaign_id_in"`
	OrgID          *string        `json:"org_id" form:"org_id"`
	IncludeDeleted *bool          `json:"include_deleted"`
	Enable         *bool          `json:"enable"`
	Email          *string        `json:"email" form:"email"`
	InviteStatus   *DefaultStatus `json:"invite_status" form:"invite_status"`
	Type           *ReferrerType  `json:"type" form:"type"`
	TypeIn         []ReferrerType `json:"type_in" form:"type_in"`

	SearchTerm       *string `json:"search_term" form:"search_term"`
	SearchCategories *string `json:"search_categories" form:"search_categories"`
}

func (query *ReferrerQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *query.CampaignID)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.Email != nil {
		qb = qb.Where("email = ?", *query.Email)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if len(query.TypeIn) > 0 {
		qb = qb.Where("type IN (?)", query.TypeIn)
	}

	if query.InviteStatus != nil {
		qb = qb.Where("invite_status = ?", *query.InviteStatus)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		for _, category := range categories {
			switch category {
			case "email":
				qb = qb.Where("email ~* ?", *query.SearchTerm)
			case "id":
				qb = qb.Where("id ~* ?", *query.SearchTerm)
			default:
				break
			}
		}
	}

	return qb
}

func (r *ReferrerRepository) Create(e *Referrer, trans *gorm.DB) error {
	return create(ReferrerTbl, e, trans)
}

func (r *ReferrerRepository) CreateMany(ts []*Referrer, trans *gorm.DB) error {
	return createMany(ReferrerTbl, ts, trans)
}

func (r *ReferrerRepository) Update(f *Referrer, trans *gorm.DB) error {
	return update(ReferrerTbl, f, trans)
}

func (r *ReferrerRepository) FindOne(query *ReferrerQuery, options *FindOneOptions) (*Referrer, error) {
	return findOne[Referrer](ReferrerTbl, query, options)
}

func (r *ReferrerRepository) FindPage(query *ReferrerQuery, options *FindPageOptions) ([]*Referrer, *Pagination, error) {
	return findPage[Referrer](ReferrerTbl, query, options)
}

func (r *ReferrerRepository) FindMany(query *ReferrerQuery, options *FindManyOptions) ([]*Referrer, error) {
	return findMany[Referrer](ReferrerTbl, query, options)
}

func (r *ReferrerRepository) Count(query *ReferrerQuery) (int64, error) {
	return count[Referrer](ReferrerTbl, query)
}

func (r *ReferrerRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Referrer](ReferrerTbl, id, trans)
}
