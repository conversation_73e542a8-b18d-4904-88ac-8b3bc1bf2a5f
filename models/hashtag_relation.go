package models

import (
	"fmt"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type HashtagRelation struct {
	Model
	HashtagID   string    `json:"hashtag_id" validate:"required" gorm:"unique,not null"`
	RelatedID   string    `json:"related_id"  validate:"required" gorm:"not null"`
	RelatedType ModelName `json:"related_type"  validate:"required" gorm:"default:null"`
	Hashtag     Hashtag   `json:"hashtag"`
}

type HashtagRelationQuery struct {
	ID             *string    `json:"id" form:"id"`
	IDIn           []string   `json:"id_in,omitempty" form:"id_in"`
	HashtagID      *string    `json:"hashtag_id" form:"hashtag_id"`
	HashtagIDIn    []string   `json:"hashtag_id_in" form:"hashtag_id_in"`
	RelatedID      *string    `json:"related_id" form:"related_id"`
	RelatedIDIn    []string   `json:"related_id_in" form:"related_id_in"`
	RelatedType    *ModelName `json:"related_type" form:"related_type"`
	IncludeDeleted *bool
	BlogCuid       *string  `form:"blog_cuid"`
	BlogCuidIn     []string `form:"blog_cuid_in"`
	Locale         *string  `json:"locale" form:"locale"`
}

type SimpleHashtagRelation struct {
	ID          string    `json:"id"`
	HashtagID   string    `json:"hashtag_id"`
	RelatedID   string    `json:"related_id"`
	RelatedType ModelName `json:"related_type"`
	Hashtag     Hashtag   `json:"hashtag"`
}

func (h *HashtagRelation) Sanitize() *SimpleHashtagRelation {
	return &SimpleHashtagRelation{
		ID:          h.ID,
		HashtagID:   h.HashtagID,
		RelatedID:   h.RelatedID,
		RelatedType: h.RelatedType,
		Hashtag:     h.Hashtag,
	}
}

func (query *HashtagRelationQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.HashtagID != nil {
		qb = qb.Where("hashtag_id = ?", *query.HashtagID)
	}

	if len(query.HashtagIDIn) > 0 {
		qb = qb.Where("hashtag_id IN (?)", query.HashtagIDIn)
	}

	if query.RelatedID != nil {
		qb = qb.Where("related_id = ?", *query.RelatedID)
	}

	if len(query.RelatedIDIn) > 0 {
		qb = qb.Where("related_id IN (?)", query.RelatedIDIn)
	}

	if query.RelatedType != nil {
		qb = qb.Where("related_type = ?", *query.RelatedType)
	}
	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (query *HashtagRelationQuery) ApplyJoinBlog(tx *gorm.DB) *gorm.DB {
	qb := tx

	if query.ID != nil {
		qb = qb.Where("hr.id = ?", *query.ID)
	}

	if query.HashtagID != nil {
		qb = qb.Where("hr.hashtag_id = ?", *query.HashtagID)
	}

	if len(query.HashtagIDIn) > 0 {
		qb = qb.Where("hr.hashtag_id IN (?)", query.HashtagIDIn)
	}

	if query.RelatedID != nil {
		qb = qb.Where("hr.related_id = ?", *query.RelatedID)
	}

	if len(query.RelatedIDIn) > 0 {
		qb = qb.Where("hr.related_id IN (?)", query.RelatedIDIn)
	}

	if query.RelatedType != nil {
		qb = qb.Where("hr.related_type = ?", *query.RelatedType)
	}
	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("hr.delete_at = 0")
	}

	if len(query.HashtagIDIn) > 0 {
		qb = qb.Where("hr.hashtag_id IN (?)", query.HashtagIDIn)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("hr.id IN (?)", query.IDIn)
	}

	if query.BlogCuid != nil {
		qb = qb.Where("b.blog_cuid = ?", *query.BlogCuid)
	}

	if len(query.BlogCuidIn) > 0 {
		qb = qb.Where("b.blog_cuid IN (?)", query.BlogCuidIn)
	}

	if query.Locale != nil {
		qb = qb.Where("b.locale = ?", *query.Locale)
	}

	return qb

}

// Create inserts a HashtagRelation to database, transaction is optional
func (r *HashtagRelationRepository) Create(he *HashtagRelation, trans *gorm.DB) error {
	return create(HashtagRelationTbl, he, trans)
}

func (r *HashtagRelationRepository) CreateMany(hes []*HashtagRelation, trans *gorm.DB) error {
	return createMany(HashtagRelationTbl, hes, trans)
}

// Update updates a HashtagRelation by ID in database, transaction is optional
func (r *HashtagRelationRepository) Update(he *HashtagRelation, trans *gorm.DB) error {
	return update(HashtagRelationTbl, he, trans)
}

// FindByID finds a HashtagRelation by ID with given find options, transaction is optional
func (r *HashtagRelationRepository) FindByID(id string, options *FindOneOptions) (*HashtagRelation, error) {
	return findByID[HashtagRelation](HashtagRelationTbl, id, options)
}

// FindOne finds one HashtagRelation with given find queries and options, transaction is optional
func (r *HashtagRelationRepository) FindOne(query *HashtagRelationQuery, options *FindOneOptions) (*HashtagRelation, error) {
	return findOne[HashtagRelation](HashtagRelationTbl, query, options)
}

// FindMany finds HashtagRelations by query conditions with give find options
func (r *HashtagRelationRepository) FindMany(query *HashtagRelationQuery, options *FindManyOptions) ([]*HashtagRelation, error) {
	return findMany[HashtagRelation](HashtagRelationTbl, query, options)
}

// FindPage returns HashtagRelations and pagination by query conditions and find options, transaction is optional
func (r *HashtagRelationRepository) FindPage(query *HashtagRelationQuery, options *FindPageOptions) ([]*HashtagRelation, *Pagination, error) {
	return findPage[HashtagRelation](HashtagRelationTbl, query, options)
}

// Delete perform soft deletion to a HashtagRelations by ID, transaction is optional
func (r *HashtagRelationRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[HashtagRelation](HashtagRelationTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *HashtagRelationRepository) DeleteMany(query *HashtagRelationQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[HashtagRelation](HashtagRelationTbl, query, trans)
}

// Count returns number of HashtagRelations by query conditions, transaction is optional
func (r *HashtagRelationRepository) Count(query *HashtagRelationQuery) (int64, error) {
	return count[HashtagRelation](HashtagRelationTbl, query)
}

func (r *HashtagRelationRepository) GetSimpleHashtagByEntities(model ModelName, entitiesID []string) (map[string][]*SimpleHashtag, error) {
	relations, err := Repository.HashtagRelation.FindMany(&HashtagRelationQuery{
		RelatedIDIn: lo.Uniq(entitiesID),
		RelatedType: util.NewT(model),
	}, &FindManyOptions{
		Preloads: []string{HashTagField},
	})
	if err != nil {
		return nil, err
	}

	relationsByEntitiesIDs := map[string][]*SimpleHashtag{}
	lo.ForEach(relations, func(hr *HashtagRelation, _ int) {
		relationsByEntitiesIDs[hr.RelatedID] = append(relationsByEntitiesIDs[hr.RelatedID], hr.Hashtag.Sanitize())
	})

	return relationsByEntitiesIDs, nil
}
func (r *HashtagRelationRepository) FindPageJoinBlog(query *HashtagRelationQuery, options *FindPageOptions) ([]*HashtagRelation, *Pagination, error) {
	entitiesChan := make(chan []*HashtagRelation)
	countChan := make(chan int64)
	errorChan := make(chan error)
	var err error
	defer func() {
		if re := recover(); re != nil {
			err = fmt.Errorf("panic: %v", re)
		}
	}()

	go func() {
		defer func() {
			if re := recover(); re != nil {
				errorChan <- fmt.Errorf("panic: %v", re)
			}
		}()

		var entities []*HashtagRelation
		qb := query.ApplyJoinBlog(DB)
		lo.ForEach(options.Sort, func(clause string, _ int) {
			qb = qb.Order("hr." + clause)
		})
		qb = qb.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)

		result := qb.Table(GetTblName(HashtagRelationTbl) + " as hr").Debug().
			Select("hr.*").
			Joins("join " + GetTblName(PublishBlogTbl) + " as b on b.blog_cuid = hr.related_id").
			Find(&entities)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if re := recover(); re != nil {
				errorChan <- fmt.Errorf("panic: %v", re)
			}
		}()

		var c int64
		var entity HashtagRelation
		qb := query.ApplyJoinBlog(DB)
		result := qb.Table(GetTblName(HashtagRelationTbl) + " as hr").Debug().
			Joins("join " + GetTblName(PublishBlogTbl) + " as b on b.blog_cuid = hr.related_id").
			Model(&entity).Count(&c)
		if err := result.Error; err != nil {
			errorChan <- err
			return
		}
		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*HashtagRelation
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

func (r *HashtagRelationRepository) FindManyJoinBlog(query *HashtagRelationQuery, options *FindManyOptions) ([]*HashtagRelation, error) {
	var entities []*HashtagRelation

	qb := query.ApplyJoinBlog(DB)
	lo.ForEach(options.Sort, func(clause string, _ int) {
		qb = qb.Order("hr." + clause)
	})

	err := qb.Table(GetTblName(HashtagRelationTbl) + " as hr").Debug().
		Select("hr.*").
		Joins("join " + GetTblName(PublishBlogTbl) + " as b on b.blog_cuid = hr.related_id").
		Find(&entities).Error

	if err != nil {
		return nil, err
	}

	return entities, nil
}
