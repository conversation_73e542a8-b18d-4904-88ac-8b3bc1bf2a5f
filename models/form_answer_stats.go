package models

import (
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"openedu-core/pkg/util"
	"strings"
	"time"
)

type FormAnswerStats struct {
	FormID         string  `json:"form_id" gorm:"primaryKey;type:varchar(20)"`
	FormUID        string  `json:"form_uid" gorm:"primaryKey;type:varchar(20)"`
	QuestionUID    string  `json:"question_uid" gorm:"primaryKey;type:varchar(20)"`
	SubQuestionUID string  `json:"sub_question_uid" gorm:"primaryKey;type:varchar(20)"`
	OptionUID      string  `json:"option_uid" gorm:"primaryKey;type:varchar(20)"`
	FileIDs        string  `json:"file_ids" gorm:"primaryKey"`
	Text           string  `json:"text" gorm:"primaryKey"`
	Count          int64   `json:"count"`
	Files          []*File `json:"files,omitempty" gorm:"-"`
	CreateAt       int     `gorm:"type:int8;not null;default:0" json:"create_at"`
	UpdateAt       int     `gorm:"type:int8;not null;default:0" json:"update_at"`
}

type SimpleFormAnswerStats struct {
	QuestionUID    string  `json:"question_uid" gorm:"primaryKey;type:varchar(20)"`
	SubQuestionUID string  `json:"sub_question_uid" gorm:"primaryKey;type:varchar(20)"`
	OptionUID      string  `json:"option_uid" gorm:"primaryKey;type:varchar(20)"`
	FileIDs        string  `json:"file_ids" gorm:"primaryKey"`
	Text           string  `json:"text" gorm:"primaryKey"`
	Count          int64   `json:"count"`
	Files          []*File `json:"files,omitempty" gorm:"-"`
	CreateAt       int     `gorm:"type:int8;not null;default:0" json:"create_at"`
	UpdateAt       int     `gorm:"type:int8;not null;default:0" json:"update_at"`
}

type FormQuestionTotalCount struct {
	QuestionUID string `json:"question_uid"`
	TotalCount  int64  `json:"total_count"`
}

func (s *FormAnswerStats) ToSimple() *SimpleFormAnswerStats {
	return &SimpleFormAnswerStats{
		QuestionUID:    s.QuestionUID,
		SubQuestionUID: s.SubQuestionUID,
		OptionUID:      s.OptionUID,
		FileIDs:        s.FileIDs,
		Text:           s.Text,
		Count:          s.Count,
		Files:          s.Files,
		CreateAt:       s.CreateAt,
		UpdateAt:       s.UpdateAt,
	}
}

type FormAnswerStatsQuery struct {
	FormID         *string  `json:"form_id" form:"form_id"`
	FormUID        *string  `json:"form_uid" form:"form_uid"`
	QuestionUID    *string  `json:"question_uid" form:"question_uid"`
	QuestionUIDIn  []string `json:"question_uid_in" form:"question_uid_in"`
	SubQuestionUID *string  `json:"sub_question_uid" form:"sub_question_uid"`
	OptionUID      *string  `json:"option_uid" form:"option_uid"`
}

func (query *FormAnswerStatsQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.FormID != nil {
		qb = qb.Where("form_id = ?", *query.FormID)
	}

	if query.FormUID != nil {
		qb = qb.Where("form_uid = ?", *query.FormUID)
	}

	if query.QuestionUID != nil {
		qb = qb.Where("question_uid = ?", *query.QuestionUID)
	}

	if len(query.QuestionUIDIn) > 0 {
		qb = qb.Where("question_uid IN (?)", query.QuestionUIDIn)
	}

	if query.SubQuestionUID != nil {
		qb = qb.Where("sub_question_uid = ?", *query.SubQuestionUID)
	}

	if query.OptionUID != nil {
		qb = qb.Where("option_uid = ?", *query.OptionUID)
	}
	return qb
}

// Create inserts a FormAnswerStats to database, transaction is optional
func (r *FormAnswerStatsRepository) Create(answerStats *FormAnswerStats, trans *gorm.DB) error {
	return create(FormAnswerStatsTbl, answerStats, trans)
}

func (r *FormAnswerStatsRepository) Upsert(answerStats *FormAnswerStats, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(FormAnswerStatsTbl)).Clauses(

		clause.OnConflict{
			Columns: []clause.Column{
				{Name: "form_id"},
				{Name: "form_uid"},
				{Name: "question_uid"},
				{Name: "sub_question_uid"},
				{Name: "option_uid"},
				{Name: "file_ids"},
				{Name: "text"},
			},
			DoUpdates: clause.Assignments(map[string]interface{}{
				"count":     gorm.Expr(GetTblName(FormAnswerStatsTbl)+".count + ?", 1),
				"update_at": time.Now().UnixMilli(),
			}),
		},
	).Create(&answerStats).Error
	return
}
func (r *FormAnswerStatsRepository) UpsertMany(entities []*FormAnswerStats, trans *gorm.DB) (err error) {
	// Create a map to deduplicate entities with the same conflict keys
	deduplicatedMap := make(map[string]*FormAnswerStats)

	for _, entity := range entities {
		// Create a unique key based on all conflict columns
		key := fmt.Sprintf("%s-%s-%s-%s-%s-%s-%s",
			entity.FormID,
			entity.FormUID,
			entity.QuestionUID,
			entity.SubQuestionUID,
			entity.OptionUID,
			entity.FileIDs,
			entity.Text)

		if existing, found := deduplicatedMap[key]; found {
			// Increment count in the existing entity
			existing.Count += entity.Count
		} else {
			// Add new entity to map
			deduplicatedMap[key] = entity
		}
	}

	deduplicateEntities := make([]*FormAnswerStats, 0, len(deduplicatedMap))
	for _, entity := range deduplicatedMap {
		deduplicateEntities = append(deduplicateEntities, entity)
	}

	// Continue with your existing transaction logic
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(FormAnswerStatsTbl)).Clauses(
		clause.OnConflict{
			Columns: []clause.Column{
				{Name: "form_id"},
				{Name: "form_uid"},
				{Name: "question_uid"},
				{Name: "sub_question_uid"},
				{Name: "option_uid"},
				{Name: "file_ids"},
				{Name: "text"},
			},
			DoUpdates: clause.Assignments(map[string]interface{}{
				"count":     gorm.Expr(GetTblName(FormAnswerStatsTbl) + ".count + excluded.count"),
				"update_at": time.Now().UnixMilli(),
			}),
		},
	).Create(&deduplicateEntities).Error

	return
}

func (r *FormAnswerStatsRepository) CreateMany(entities []*FormAnswerStats, trans *gorm.DB) error {
	return createMany(FormAnswerStatsTbl, entities, trans)
}

// Update updates a FormAnswerStats by ID in database, transaction is optional
func (r *FormAnswerStatsRepository) Update(answerStats *FormAnswerStats, trans *gorm.DB) error {
	return update(FormAnswerStatsTbl, answerStats, trans)
}

func (r *FormAnswerStatsRepository) FindByID(id string, options *FindOneOptions) (*FormAnswerStats, error) {
	return findByID[FormAnswerStats](FormAnswerStatsTbl, id, options)
}

func (r *FormAnswerStatsRepository) FindOne(query *FormAnswerStatsQuery, options *FindOneOptions) (*FormAnswerStats, error) {
	return findOne[FormAnswerStats](FormAnswerStatsTbl, query, options)
}

func (r *FormAnswerStatsRepository) FindMany(query *FormAnswerStatsQuery, options *FindManyOptions) ([]*FormAnswerStats, error) {
	return findMany[FormAnswerStats](FormAnswerStatsTbl, query, options)
}

func (r *FormAnswerStatsRepository) preloadFiles(listAnswerStats []*FormAnswerStats) error {
	var fileIDs []string
	for _, answerStats := range listAnswerStats {
		if answerStats.FileIDs != "" {
			fileIDs = append(fileIDs, strings.Split(answerStats.FileIDs, "__")...)
		}
	}
	fileIDs = lo.Uniq(fileIDs)

	files, err := Repository.File.FindMany(&FileQuery{IDIn: fileIDs}, nil)
	if err != nil {
		return err
	}

	filesByIDs := make(map[string]*File)
	for _, file := range files {
		filesByIDs[file.ID] = file
	}

	for _, answerStats := range listAnswerStats {
		if answerStats.FileIDs != "" {
			for _, fileID := range strings.Split(answerStats.FileIDs, "__") {
				if file, found := filesByIDs[fileID]; found {
					answerStats.Files = append(answerStats.Files, file)
				}
			}
		}
	}
	return nil
}

func (r *FormAnswerStatsRepository) FindPage(query *FormAnswerStatsQuery, options *FindPageOptions) ([]*FormAnswerStats, *Pagination, error) {
	shouldPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		shouldPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	listAnswerStats, pagination, err := findPage[FormAnswerStats](FormAnswerStatsTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if len(listAnswerStats) == 0 {
		return listAnswerStats, pagination, nil
	}

	if shouldPreloadFiles {
		if err = r.preloadFiles(listAnswerStats); err != nil {
			return nil, nil, err
		}
	}
	return listAnswerStats, pagination, nil
}

func (r *FormAnswerStatsRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[FormAnswerStats](FormAnswerStatsTbl, id, trans)
}

func (r *FormAnswerStatsRepository) DeleteMany(query *FormAnswerStatsQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[FormAnswerStats](FormAnswerStatsTbl, query, trans)
}

func (r *FormAnswerStatsRepository) Count(query *FormAnswerStatsQuery) (int64, error) {
	return count[FormAnswerStats](FormAnswerStatsTbl, query)
}

func (r *FormAnswerStatsRepository) CountTotalByQuestionUIDs(questionUIDs []string) ([]*FormQuestionTotalCount, error) {
	var answerCounts []*FormQuestionTotalCount
	query := fmt.Sprintf(
		`
		SELECT a.question_uid, SUM(a.count) AS total_count
		FROM %[1]s a
		WHERE a.question_uid IN (?)
		GROUP BY a.question_uid;
    `,
		GetTblName(FormAnswerStatsTbl),
	)

	result := DB.Raw(query, questionUIDs).Scan(&answerCounts)
	if err := result.Error; err != nil {
		return nil, err
	}

	return answerCounts, nil
}
