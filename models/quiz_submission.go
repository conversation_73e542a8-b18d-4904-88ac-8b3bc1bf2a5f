package models

import (
	"fmt"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type QuizSubmissionStatus string

const (
	QuizSubmissionStatusInProgress QuizSubmissionStatus = "in-progress"
	QuizSubmissionStatusDone       QuizSubmissionStatus = "done"
)

type QuizSubmission struct {
	Model
	QuizUID                       string               `json:"quiz_uid"`
	QuizID                        string               `json:"quiz_id" gorm:"type:varchar(20);not null"`
	UserID                        string               `json:"user_id" gorm:"type:varchar(20)"`
	CourseCuid                    string               `json:"course_cuid" gorm:"type:varchar(20)"`
	Status                        QuizSubmissionStatus `json:"status"`
	Passed                        bool                 `json:"passed"`
	StartAt                       int64                `json:"start_at"`
	EndAt                         int64                `json:"end_at"`
	DeadlineAt                    int64                `json:"deadline_at"`
	TimeToCompleteInMilliSeconds  int64                `json:"time_to_complete_in_milli_seconds"`
	ArchivedPoints                int                  `json:"archived_points"`
	HighestPointsOnSingleQuestion int                  `json:"highest_points_on_single_question"`
	HighestStreak                 int                  `json:"highest_streak"`
	Answers                       []*QuizAnswer        `json:"answers" gorm:"foreignKey:SubmissionID"`
	NumCorrectAnswers             int                  `json:"num_correct_answers" gorm:"-"`
	NumQuestions                  int                  `json:"num_questions" gorm:"-"`
}

type SimpleQuizSubmission struct {
	Model
	QuizID                        string               `json:"quiz_id"`
	QuizUID                       string               `json:"quiz_uid"`
	UserID                        string               `json:"user_id"`
	CourseCuid                    string               `json:"course_cuid"`
	Status                        QuizSubmissionStatus `json:"status"`
	Passed                        bool                 `json:"passed"`
	StartAt                       int64                `json:"start_at"`
	EndAt                         int64                `json:"end_at"`
	DeadlineAt                    int64                `json:"deadline_at"`
	TimeToCompleteInMilliSeconds  int64                `json:"time_to_complete_in_milli_seconds"`
	ArchivedPoints                int                  `json:"archived_points"`
	HighestPointsOnSingleQuestion int                  `json:"highest_points_on_single_question"`
	HighestStreak                 int                  `json:"highest_streak"`
	NumCorrectAnswers             int                  `json:"num_correct_answers"`
	NumQuestions                  int                  `json:"num_questions"`
}

func (s *QuizSubmission) Sanitize(quiz *Quiz) *QuizSubmission {
	return &QuizSubmission{
		Model:                         s.Model,
		QuizUID:                       s.QuizUID,
		QuizID:                        s.QuizID,
		UserID:                        s.UserID,
		CourseCuid:                    s.CourseCuid,
		Status:                        s.Status,
		Passed:                        s.Passed,
		StartAt:                       s.StartAt,
		EndAt:                         s.EndAt,
		DeadlineAt:                    s.DeadlineAt,
		TimeToCompleteInMilliSeconds:  s.TimeToCompleteInMilliSeconds,
		ArchivedPoints:                s.ArchivedPoints,
		HighestPointsOnSingleQuestion: s.HighestPointsOnSingleQuestion,
		HighestStreak:                 s.HighestStreak,
		Answers: lo.Filter(s.Answers, func(answer *QuizAnswer, _ int) bool {
			if answer.Question != nil && !quiz.Settings.ShowCorrectAnswersEnabled {
				answer.Question = answer.Question.Sanitize()
			}
			return answer.EndAt != 0
		}),
		NumCorrectAnswers: s.NumCorrectAnswers,
		NumQuestions:      s.NumQuestions,
	}
}

func (s *QuizSubmission) ToSimple() *SimpleQuizSubmission {
	return &SimpleQuizSubmission{
		Model:                         s.Model,
		QuizID:                        s.QuizID,
		QuizUID:                       s.QuizUID,
		UserID:                        s.UserID,
		CourseCuid:                    s.CourseCuid,
		Status:                        s.Status,
		Passed:                        s.Passed,
		StartAt:                       s.StartAt,
		EndAt:                         s.EndAt,
		DeadlineAt:                    s.DeadlineAt,
		TimeToCompleteInMilliSeconds:  s.TimeToCompleteInMilliSeconds,
		ArchivedPoints:                s.ArchivedPoints,
		HighestPointsOnSingleQuestion: s.HighestPointsOnSingleQuestion,
		HighestStreak:                 s.HighestStreak,
		NumCorrectAnswers:             s.NumCorrectAnswers,
		NumQuestions:                  s.NumQuestions,
	}
}

func (s *QuizSubmission) IsInDeadline() bool {
	if s.DeadlineAt == 0 {
		return true
	}
	now := time.Now()
	deadline := time.UnixMilli(s.DeadlineAt)
	return !now.After(deadline)
}

func (s *QuizSubmission) IsDone() bool {
	return s.Status == QuizSubmissionStatusDone
}

func (s *QuizSubmission) IsInProgress() bool {
	return s.Status == QuizSubmissionStatusInProgress
}

func (s *QuizSubmission) CalcTimeToCompleteInMilliSeconds() int64 {
	return time.UnixMilli(int64(s.EndAt)).Sub(time.UnixMilli(int64(s.StartAt))).Milliseconds()
}

type QuizSubmissionQuery struct {
	QuizID         *string               `json:"quiz_id"`
	QuizIDIn       []string              `json:"quiz_id_in"`
	QuizUID        *string               `json:"quiz_uid"`
	QuizUIDIn      []string              `json:"quiz_uid_in"`
	UserID         *string               `form:"user_id" json:"user_id"`
	CourseCuid     *string               `form:"course_cuid" json:"course_cuid"`
	Status         *QuizSubmissionStatus `form:"status" json:"status"`
	Passed         *bool                 `form:"passed" json:"passed"`
	IncludeDeleted *bool
	UIDs           []string `form:"uids" json:"uids"`
}

func (query *QuizSubmissionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.QuizID != nil {
		qb = qb.Where("quiz_id = ?", *query.QuizID)
	}

	if query.QuizUID != nil {
		qb = qb.Where("quiz_uid = ?", *query.QuizUID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *query.CourseCuid)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if len(query.UIDs) > 0 {
		qb = qb.Where("uids IN (?)", query.UIDs)
	}

	if len(query.QuizUIDIn) > 0 {
		qb = qb.Where("quiz_uid IN (?)", query.QuizUIDIn)
	}

	if len(query.QuizIDIn) > 0 {
		qb = qb.Where("quiz_id IN (?)", query.QuizIDIn)
	}

	if query.Passed != nil {
		qb = qb.Where("passed = ?", *query.Passed)
	}

	return qb
}

func (r *QuizSubmissionRepository) preloadAnswers(submissions []*QuizSubmission, hasPreloadQuestions bool) error {
	submissionIDs := lo.Map(submissions, func(submission *QuizSubmission, _ int) string {
		return submission.ID
	})
	answers, err := Repository.QuizAnswer.FindMany(
		&QuizAnswerQuery{
			SubmissionIDIn: submissionIDs,
			IncludeDeleted: util.NewBool(false),
		},
		&FindManyOptions{
			Sort: []string{`"order" ASC`},
		},
	)
	if err != nil {
		return err
	}

	if hasPreloadQuestions {
		if err = r.preloadQuestions(answers); err != nil {
			return err
		}
	}

	answersBySubmissionIDs := make(map[string][]*QuizAnswer)
	for _, answer := range answers {
		if _, found := answersBySubmissionIDs[answer.SubmissionID]; !found {
			answersBySubmissionIDs[answer.SubmissionID] = []*QuizAnswer{answer}
			continue
		}
		answersBySubmissionIDs[answer.SubmissionID] = append(answersBySubmissionIDs[answer.SubmissionID], answer)
	}
	for _, submission := range submissions {
		submission.Answers = answersBySubmissionIDs[submission.ID] // No need sort because already sort by `order ASC` when FindMany questions
		submission.NumQuestions = len(submission.Answers)
		submission.NumCorrectAnswers = lo.Reduce(submission.Answers, func(agg int, item *QuizAnswer, _ int) int {
			if item.Correct {
				return agg + 1
			}
			return agg
		}, 0)
	}
	return nil
}

func (r *QuizSubmissionRepository) preloadQuestions(answers []*QuizAnswer) error {
	questionIDs := lo.Map(answers, func(answer *QuizAnswer, _ int) string {
		return answer.QuestionID
	})
	questions, err := Repository.QuizQuestion.FindMany(
		&QuizQuestionQuery{
			IDIn:           questionIDs,
			IncludeDeleted: util.NewBool(false),
		},
		&FindManyOptions{
			Sort:     []string{`"order" ASC`},
			Preloads: []string{util.FilesField, util.ItemsFilesField},
		},
	)
	if err != nil {
		return err
	}

	questionsByIDs := make(map[string]*QuizQuestion)
	for _, question := range questions {
		questionsByIDs[question.ID] = question
	}
	for _, answer := range answers {
		answer.Question = questionsByIDs[answer.QuestionID]
	}
	return nil
}

// Create inserts a Quiz Submission to database, transaction is optional
func (r *QuizSubmissionRepository) Create(q *QuizSubmission, trans *gorm.DB) error {
	return create(QuizSubmissionTbl, q, trans)
}

func (r *QuizSubmissionRepository) CreateMany(qs []*QuizSubmission, trans *gorm.DB) error {
	return createMany(QuizSubmissionTbl, qs, trans)
}

// Update updates a Quiz Submissions by ID in database, transaction is optional
func (r *QuizSubmissionRepository) Update(submission *QuizSubmission, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(QuizSubmissionTbl)).Debug().Select("*").Omit("id", "create_at", "Answers").Updates(&submission).Error
	return
}

// FindByID finds a Quiz Submission by ID with given find options, transaction is optional
func (r *QuizSubmissionRepository) FindByID(id string, options *FindOneOptions) (*QuizSubmission, error) {
	hasPreloadAnswers := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersField) {
		hasPreloadAnswers = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersField)
	}

	hasPreloadQuestions := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersQuestionField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersQuestionField)
	}

	submission, err := findByID[QuizSubmission](QuizSubmissionTbl, id, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadAnswers {
		if err = r.preloadAnswers([]*QuizSubmission{submission}, hasPreloadQuestions); err != nil {
			return nil, err
		}
	}
	return submission, nil
}

// FindOne finds one Quiz Submission with given find queries and options, transaction is optional
func (r *QuizSubmissionRepository) FindOne(query *QuizSubmissionQuery, options *FindOneOptions) (*QuizSubmission, error) {
	hasPreloadAnswers := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersField) {
		hasPreloadAnswers = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersField)
	}

	hasPreloadQuestions := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersQuestionField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersQuestionField)
	}

	submission, err := findOne[QuizSubmission](QuizSubmissionTbl, query, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadAnswers {
		if err = r.preloadAnswers([]*QuizSubmission{submission}, hasPreloadQuestions); err != nil {
			return nil, err
		}
	}
	return submission, nil
}

// FindMany finds Quiz Submissions by query conditions with give find options
func (r *QuizSubmissionRepository) FindMany(query *QuizSubmissionQuery, options *FindManyOptions) ([]*QuizSubmission, error) {
	hasPreloadAnswers := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersField) {
		hasPreloadAnswers = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersField)
	}

	hasPreloadQuestions := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersQuestionField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersQuestionField)
	}

	submissions, err := findMany[QuizSubmission](QuizSubmissionTbl, query, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadAnswers {
		if err = r.preloadAnswers(submissions, hasPreloadQuestions); err != nil {
			return nil, err
		}
	}
	return submissions, err
}

// FindPage returns Quiz Submissions and pagination by query conditions and find options, transaction is optional
func (r *QuizSubmissionRepository) FindPage(query *QuizSubmissionQuery, options *FindPageOptions) ([]*QuizSubmission, *Pagination, error) {
	hasPreloadAnswers := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersField) {
		hasPreloadAnswers = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersField)
	}

	hasPreloadQuestions := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersQuestionField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersQuestionField)
	}

	submissions, pagination, err := findPage[QuizSubmission](QuizSubmissionTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if hasPreloadAnswers {
		if err = r.preloadAnswers(submissions, hasPreloadQuestions); err != nil {
			return nil, nil, err
		}
	}
	return submissions, pagination, err
}

// Delete perform soft deletion to a Quiz Submissions by ID, transaction is optional
func (r *QuizSubmissionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[QuizSubmission](QuizSubmissionTbl, id, trans)
}

// DeleteMany performs soft deletion to Quiz Submissions by query conditions
func (r *QuizSubmissionRepository) DeleteMany(query *QuizSubmissionQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[QuizSubmission](QuizSubmissionTbl, query, trans)
}

// Count returns number of Quiz Submissions by query conditions, transaction is optional
func (r *QuizSubmissionRepository) Count(query *QuizSubmissionQuery) (int64, error) {
	return count[QuizSubmission](QuizSubmissionTbl, query)
}

func (r *QuizSubmissionRepository) FindRanksByQuizUID(quizUID, userID string, top int) ([]*QuizSubmissionRank, error) {
	var ranks []*QuizSubmissionRank
	query := fmt.Sprintf(`
    WITH ranks AS (
        SELECT
            id AS submission_id,
            user_id,
            archived_points,
            highest_points_on_single_question,
			time_to_complete_in_milli_seconds,
			highest_streak,
            ROW_NUMBER() OVER (ORDER BY archived_points DESC, highest_points_on_single_question DESC, time_to_complete_in_milli_seconds ASC) AS rank
        FROM %[1]s quiz_submissions
		WHERE quiz_uid = ?
    )
    SELECT
        submission_id,
		user_id,
		archived_points,
		highest_points_on_single_question,
		time_to_complete_in_milli_seconds,
		highest_streak,
        rank
    FROM ranks
    WHERE rank <= ? OR user_id = ?
    LIMIT ?
    `, GetTblName(QuizSubmissionTbl))

	err := DB.Raw(query, quizUID, top, userID, top+1).Scan(&ranks).Error
	if err != nil {
		return nil, err
	}

	userIDs := lo.Map(ranks, func(rank *QuizSubmissionRank, _ int) string {
		return rank.UserID
	})
	if len(userIDs) > 0 {
		userIDs = lo.Uniq(userIDs)
		users, uErr := Repository.User.FindMany(&UserQuery{
			IDIn: &userIDs,
		}, nil)
		if uErr != nil {
			return nil, uErr
		}

		usersByIDs := make(map[string]*User)
		for _, user := range users {
			usersByIDs[user.ID] = user
		}

		for _, rank := range ranks {
			if user, ok := usersByIDs[rank.UserID]; ok {
				rank.User = user
			}
		}
	}

	return ranks, nil
}

func (r *QuizSubmissionRepository) FindTotalPointForUserByCourse(courseCuid, userID string) (int, error) {
	var totalPoint int
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(archived_points), 0) AS total_point
		FROM %[1]s quiz_submissions
		WHERE course_cuid = ? AND user_id = ? AND delete_at = 0 AND status = ?
	`, GetTblName(QuizSubmissionTbl))

	err := DB.Raw(query, courseCuid, userID, QuizSubmissionStatusDone).Scan(&totalPoint).Error
	if err != nil {
		return 0, err
	}

	return totalPoint, nil
}

func (r *QuizSubmissionRepository) CountQuizSubmisison(user *User, quizIDs []string) (map[string]int, error) {
	tx := GetDb(QuizSubmissionTbl).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	type Res struct {
		QuizID string `json:"quiz_id" gorm:"column:quiz_id"`
		Count  int    `json:"count" gorm:"column:count"`
	}

	var results []Res

	query := tx.Model(&QuizSubmission{}).
		Select("quiz_id, COUNT(*) as count").
		Where("user_id = ?", user.ID).
		Where("quiz_id IN (?)", quizIDs).
		Debug()

	if err := query.Where("delete_at = 0").Group("quiz_id").
		Scan(&results).Error; err != nil {
		query.Rollback()
		return nil, err
	}

	if err := query.Commit().Error; err != nil {
		return nil, err
	}

	mapCount := make(map[string]int)
	for _, id := range quizIDs {
		if count, ok := lo.Find(results, func(item Res) bool {
			return item.QuizID == id
		}); ok {
			mapCount[id] = count.Count
		} else {
			mapCount[id] = 0
		}
	}

	return mapCount, nil
}
