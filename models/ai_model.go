package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"strconv"

	"gorm.io/gorm"
)

func GetDefaultAIModels() []*AIModel {
	return []*AIModel{
		{
			Name:        string(ModelMetaLlama3),
			Description: "",
			Enabled:     true,
			Configs: AIModelConfigs{
				ImageAnalysisEnabled:       true,
				StreamResponseEnabled:      true,
				ImageGeneratorEnabled:      false,
				PresentationCreatorEnabled: false,
				CodeExecutorEnabled:        false,
				SearcherEnabled:            false,
				ExtendedThinkingEnabled:    false,
			},
			DisplayName:  "Llama 3.2",
			ThumbnailURL: "https://s3.ap-southeast-1.amazonaws.com/openedu.net-staging/images/y0VZ4GCaKmapdmZR_meta_logo.png",
			Order:        6,
		},
		{
			Name:        string(ModelGPT4oMini),
			Description: "",
			Enabled:     true,
			Configs: AIModelConfigs{
				ImageAnalysisEnabled:       true,
				StreamResponseEnabled:      true,
				ImageGeneratorEnabled:      true,
				PresentationCreatorEnabled: true,
				CodeExecutorEnabled:        true,
				SearcherEnabled:            true,
				ExtendedThinkingEnabled:    false,
			},
			DisplayName:  "ChatGPT - 4o mini",
			ThumbnailURL: "https://s3.ap-southeast-1.amazonaws.com/openedu.net-staging/images/iBPUdGWqLa69E7WK_openai_logo.png",
			Order:        1,
		},
		{
			Name:        string(ModelO3Mini),
			Description: "",
			Enabled:     true,
			Configs: AIModelConfigs{
				ImageAnalysisEnabled:       true,
				StreamResponseEnabled:      true,
				ImageGeneratorEnabled:      true,
				PresentationCreatorEnabled: true,
				CodeExecutorEnabled:        true,
				SearcherEnabled:            true,
				ExtendedThinkingEnabled:    false,
			},
			DisplayName:  "o3 - mini",
			ThumbnailURL: "https://s3.ap-southeast-1.amazonaws.com/openedu.net-staging/images/02bcdrzAtu38O4qr_o1_logo.png",
			Order:        3,
		},
		{
			Name:        string(ModelGPT4o),
			Description: "",
			Enabled:     true,
			Configs: AIModelConfigs{
				ImageAnalysisEnabled:       true,
				StreamResponseEnabled:      true,
				ImageGeneratorEnabled:      true,
				PresentationCreatorEnabled: true,
				CodeExecutorEnabled:        true,
				SearcherEnabled:            true,
				ExtendedThinkingEnabled:    false,
			},
			DisplayName:  "ChatGPT - 4o",
			ThumbnailURL: "https://s3.ap-southeast-1.amazonaws.com/openedu.net-staging/images/iBPUdGWqLa69E7WK_openai_logo.png",
			Order:        2,
		},
		{
			Name:        string(ModelClaude3Sonnet),
			Description: "",
			Enabled:     true,
			Configs: AIModelConfigs{
				ImageAnalysisEnabled:       true,
				StreamResponseEnabled:      true,
				ImageGeneratorEnabled:      true,
				PresentationCreatorEnabled: true,
				CodeExecutorEnabled:        true,
				SearcherEnabled:            true,
				ExtendedThinkingEnabled:    true,
			},
			DisplayName:  "Claude 3.7 Sonnet",
			ThumbnailURL: "https://s3.ap-southeast-1.amazonaws.com/openedu.net-staging/images/hKz3nGdwgsTSJGJ9_claude_logo.png",
			Order:        4,
		},
		{
			Name:        string(ModelGemini2),
			Description: "",
			Enabled:     true,
			Configs: AIModelConfigs{
				ImageAnalysisEnabled:       true,
				StreamResponseEnabled:      true,
				ImageGeneratorEnabled:      true,
				PresentationCreatorEnabled: true,
				CodeExecutorEnabled:        true,
				SearcherEnabled:            true,
				ExtendedThinkingEnabled:    false,
			},
			DisplayName:  "Gemini 2.0",
			ThumbnailURL: "https://s3.ap-southeast-1.amazonaws.com/openedu.net-staging/images/Ws0H5nNysOm0waSZ_gemini_logo.png",
			Order:        5,
		},
	}
}

type AIModel struct {
	Model
	Name         string         `json:"name" gorm:"name"`
	Description  string         `json:"description" gorm:"description"`
	Enabled      bool           `json:"enabled" gorm:"enabled"`
	Configs      AIModelConfigs `json:"configs" gorm:"configs;type:jsonb"`
	DisplayName  string         `json:"display_name" gorm:"display_name"`
	ThumbnailURL string         `json:"thumbnail_url" gorm:"thumbnail_url"`
	Order        int16          `json:"order" gorm:"order"`
}

func (s AIModelConfigs) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	return string(val), err
}

func (s *AIModelConfigs) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &s)
	if err != nil {
		return err
	}

	return nil
}

type AIModelName string

const (
	ModelMetaLlama3    AIModelName = "us.meta.llama3-2-90b-instruct-v1:0"
	ModelGPT4oMini     AIModelName = "gpt-4o-mini"
	ModelGPT4o         AIModelName = "gpt-4o"
	ModelClaude3Sonnet AIModelName = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
	ModelO3Mini        AIModelName = "o3-mini"
	ModelGemini2       AIModelName = "gemini-2.0-flash"
)

type AIModelConfigs struct {
	ImageAnalysisEnabled       bool `json:"image_analysis_enabled"  gorm:"image_analysis_enabled"`
	StreamResponseEnabled      bool `json:"stream_response_enabled"  gorm:"stream_response_enabled"`
	ImageGeneratorEnabled      bool `json:"image_generator_enabled"  gorm:"image_generator_enabled"`
	PresentationCreatorEnabled bool `json:"present_creator_enabled"  gorm:"present_creator_enabled"`
	CodeExecutorEnabled        bool `json:"code_executor_enabled"  gorm:"code_executor_enabled"`
	SearcherEnabled            bool `json:"searcher_enabled"  gorm:"searcher_enabled"`
	ExtendedThinkingEnabled    bool `json:"extended_thinking_enabled" gorm:"extended_thinking_enabled"`
}

type AIModelQuery struct {
	ID                      *string       `form:"id,omitempty" json:"id,omitempty"`
	Name                    *string       `form:"name,omitempty" json:"name,omitempty"`
	Enabled                 *bool         `form:"enabled,omitempty" json:"enabled,omitempty"`
	IDIn                    []string      `form:"id_in,omitempty" json:"id_in,omitempty"`
	IncludeDeleted          *bool         `form:"include_deleted,omitempty" json:"include_deleted,omitempty"`
	NameIn                  []AIModelName `form:"name_in,omitempty" json:"name_in,omitempty"`
	Order                   *int16        `form:"order,omitempty" json:"order,omitempty"`
	ExtendedThinkingEnabled *bool         `json:"extended_thinking_enabled" form:"extended_thinking_enabled"`
}

func (query *AIModelQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Enabled != nil {
		qb = qb.Where("enabled = ?", *query.Enabled)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN ?", query.IDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if len(query.NameIn) > 0 {
		qb = qb.Where("name IN ?", query.NameIn)
	}

	if query.ExtendedThinkingEnabled != nil {
		qb = qb.Where("configs->>'extended_thinking_enabled' = ?", strconv.FormatBool(*query.ExtendedThinkingEnabled))
	}

	return qb
}

// Create inserts a aiModel to database, transaction is optional
func (r *AIModelRepository) Create(am *AIModel, trans *gorm.DB) error {
	return create(AIModelTbl, am, trans)
}

func (r *AIModelRepository) CreateMany(ams []*AIModel, trans *gorm.DB) error {
	return createMany(AIModelTbl, ams, trans)
}

// Update updates a aiModel by ID in database, transaction is optional
func (r *AIModelRepository) Update(am *AIModel, trans *gorm.DB) error {
	return update(AIModelTbl, am, trans)
}

// FindByID finds a aiModel by ID with given find options, transaction is optional
func (r *AIModelRepository) FindByID(id string, options *FindOneOptions) (*AIModel, error) {
	return findByID[AIModel](AIModelTbl, id, options)
}

// FindOne finds one aiModel with given find queries and options, transaction is optional
func (r *AIModelRepository) FindOne(query *AIModelQuery, options *FindOneOptions) (*AIModel, error) {
	return findOne[AIModel](AIModelTbl, query, options)
}

// FindMany finds aiModels by query conditions with give find options
func (r *AIModelRepository) FindMany(query *AIModelQuery, options *FindManyOptions) ([]*AIModel, error) {
	return findMany[AIModel](AIModelTbl, query, options)
}

// FindPage returns aiModels and pagination by query conditions and find options, transaction is optional
func (r *AIModelRepository) FindPage(query *AIModelQuery, options *FindPageOptions) ([]*AIModel, *Pagination, error) {
	return findPage[AIModel](AIModelTbl, query, options)
}

// Delete perform soft deletion to a aiModels by ID, transaction is optional
func (r *AIModelRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[AIModel](AIModelTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *AIModelRepository) DeleteMany(query *AIModelQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[AIModel](AIModelTbl, query, trans)
}
