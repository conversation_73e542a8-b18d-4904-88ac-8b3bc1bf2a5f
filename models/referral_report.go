package models

import (
	"fmt"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"openedu-core/pkg/util"
	"strconv"
	"strings"
)

type ReferralReportType string

const (
	ReferralReportTypeOverall ReferralReportType = "overall"
	ReferralReportTypeUser    ReferralReportType = "user"
)

type ReferralSummaryReport struct {
	Type       ReferralReportType `json:"type" form:"type"`
	CampaignID *string            `json:"campaign_id" form:"campaign_id"`
	UserID     *string            `json:"user_id" form:"user_id"`
	FromDate   *int64             `json:"from_date" form:"from_date"`
	ToDate     *int64             `json:"to_date" form:"to_date"`
	Currency   *Currency          `json:"currency" form:"currency"`
}

type ReferralReportByUser struct {
	CampaignID    string          `json:"campaign_id"`
	Name          string          `json:"name"`
	Currency      Currency        `json:"currency"`
	CreateAt      int             `json:"create_at"`
	Price         decimal.Decimal `json:"price"`
	CourseSlug    string          `json:"course_slug"`
	CourseCuid    string          `json:"course_cuid"`
	CourseName    string          `json:"course_name"`
	OrgDomain     string          `json:"org_domain"`
	CourseOwnerID string          `json:"course_owner_id"`
	ThumbnailID   string          `json:"thumbnail_id"`

	Ref1Amount   decimal.Decimal `json:"ref1_amount"`
	Ref2Amount   decimal.Decimal `json:"ref2_amount"`
	Ref3Amount   decimal.Decimal `json:"ref3_amount"`
	ShareAmount  decimal.Decimal `json:"share_amount"`
	MaxShareRate float32         `json:"max_share_rate"`
	TotalAmount  decimal.Decimal `json:"total_amount"`
}

type ReferralReportByUserQuery struct {
	UserID     *string   `json:"user_id" form:"user_id"`
	CampaignID *string   `json:"campaign_id" form:"campaign_id"`
	CourseCuid *string   `json:"course_cuid" form:"course_cuid"`
	CourseSlug *string   `json:"course_slug" form:"course_slug"`
	Currency   *Currency `json:"currency" form:"currency"`
	CourseName *string   `json:"course_name" form:"course_name"`

	SearchTerm       *string `json:"search_term" form:"search_term"`
	SearchCategories *string `json:"search_categories" form:"search_categories"`
}

type BaseReferralReport struct {
	UserID      string          `json:"user_id"`
	TotalAmount decimal.Decimal `json:"total_amount"`
}

type TotalReferralAmountReport struct {
	Ref1Amount decimal.Decimal `json:"ref1_amount"`
	Ref2Amount decimal.Decimal `json:"ref2_amount"`
	Ref3Amount decimal.Decimal `json:"ref3_amount"`
}

func (r *ReferralRepository) GetTotalReferralAmountByUser(userID string, query *ReferralSummaryReport) (*TotalReferralAmountReport, error) {
	var result *TotalReferralAmountReport
	refTbl := GetTblName(ReferralTbl)

	queryStr := fmt.Sprintf(`SELECT
        COALESCE(SUM(
				CASE
					WHEN ref1_user_id = '%[1]s' THEN ref1_amount
					ELSE 0
				END
			), 0) as ref1_amount,
		COALESCE(SUM(
				CASE
					WHEN ref2_user_id = '%[1]s' THEN ref2_amount
					ELSE 0
				END
			), 0) as ref2_amount,
		COALESCE(SUM(
				CASE
					WHEN ref3_user_id = '%[1]s' THEN ref3_amount
					ELSE 0
				END
			), 0) as ref3_amount
    FROM
        %[2]s
    WHERE
        delete_at = 0 AND
        (ref1_user_id = '%[1]s' OR ref2_user_id = '%[1]s' OR ref3_user_id = '%[1]s')`,
		userID, refTbl,
	)

	if query.CampaignID != nil {
		queryStr += " AND campaign_id = " + *query.CampaignID
	}

	if query.FromDate != nil {
		queryStr += " AND create_at >= " + strconv.Itoa(int(*query.FromDate))
	}

	if query.ToDate != nil {
		queryStr += " AND create_at <= " + strconv.Itoa(int(*query.ToDate))
	}

	if query.Currency == nil {
		query.Currency = util.NewT(FiatCurrencyVND)
	}
	queryStr += fmt.Sprintf(" AND currency = '%s'", *query.Currency)

	err := DB.Debug().Raw(queryStr).Scan(&result).Error

	return result, err
}

func (r *ReferralRepository) SumReferralAmounts(query *ReferralSummaryReport) (*BaseReferralReport, error) {
	if query == nil {
		query = &ReferralSummaryReport{}
	}
	result := &BaseReferralReport{}
	qb := DB.Table(GetTblName(ReferralTbl)).
		Select("COALESCE(SUM(ref1_amount + ref2_amount + ref3_amount), 0) AS total_amount").
		Where("delete_at = 0")

	if query.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *query.CampaignID)
	}

	if query.FromDate != nil {
		qb = qb.Where("create_at >= ?", *query.FromDate)
	}

	if query.ToDate != nil {
		qb = qb.Where("create_at <= ?", *query.ToDate)
	}

	if query.Currency == nil {
		query.Currency = util.NewT(FiatCurrencyVND)
	}
	qb = qb.Where("currency = ?", *query.Currency)

	err := qb.Scan(&result).Error
	if err != nil {
		return &BaseReferralReport{}, err
	}

	return result, nil
}

func (r *ReferralRepository) GetReferralReportByUser(userID string, query *ReferralReportByUserQuery, options *FindPageOptions) ([]*ReferralReportByUser, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{
			Page:    1,
			PerPage: 10,
			Sort:    []string{"ref.create_at desc"},
		}
	}
	limit := options.PerPage
	offset := (options.Page - 1) * options.PerPage
	var reports []*ReferralReportByUser
	var totalCount int64
	var err error
	refTbl := GetTblName(ReferralTbl)
	actbl := GetTblName(AffiliateCampaignTbl)
	pctbl := GetTblName(PublishCourseTbl)

	entitiesChan := make(chan []*ReferralReportByUser)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	optionWhereStatement := ""
	if query.CampaignID != nil {
		optionWhereStatement += fmt.Sprintf(" AND ac.id = '%s' ", *query.CampaignID)
	}

	if query.CourseCuid != nil {
		optionWhereStatement += fmt.Sprintf(" AND pc.course_cuid = '%s' ", *query.CourseCuid)
	}

	if query.CourseSlug != nil {
		optionWhereStatement += fmt.Sprintf(" AND pc.course_slug = '%s' ", *query.CourseSlug)
	}

	if query.CourseName != nil {
		optionWhereStatement += fmt.Sprintf(" AND pc.name = '%s' ", *query.CourseName)
	}

	if query.Currency != nil {
		optionWhereStatement += fmt.Sprintf(" AND ref.currency = '%s' ", *query.Currency)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		for _, category := range categories {
			switch category {
			case "campaign_name":
				optionWhereStatement += fmt.Sprintf(" AND ac.name ~* '%s' ", *query.SearchTerm)
			case "course_name":
				optionWhereStatement += fmt.Sprintf(" AND pc.name ~* '%s' ", *query.SearchTerm)
			default:
				break
			}
		}
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		// add sort
		sortStr := ""
		if options.Sort != nil && len(options.Sort) > 0 {
			sortStr = "ORDER BY"
			lo.ForEach(options.Sort, func(item string, _ int) {
				sortStr += " " + item
			})
		}

		mainQuery := fmt.Sprintf(`
SELECT rp.* FROM
	(SELECT 
		ac.id as campaign_id,
		ac."name" as "name",
		ANY_VALUE(ref.currency) as currency,
		ANY_VALUE(ref.create_at) as create_at,
		ANY_VALUE(pc.price) as price,
		ANY_VALUE(pc.course_cuid) as course_cuid,
		ANY_VALUE(pc.course_slug) as course_slug,
		ANY_VALUE(pc."name") as course_name,
		ANY_VALUE(pc.thumbnail_id) as thumbnail_id,
		ANY_VALUE(pc.org_domain) as org_domain,
		ANY_VALUE(pc.user_id) as course_owner_id,
		COALESCE(SUM(
				CASE 
					WHEN ref.ref1_user_id = '%[1]s' THEN ref.ref1_amount
					ELSE 0
				END
			), 0) as ref1_amount,
		COALESCE(SUM(
				CASE 
					WHEN ref.ref2_user_id = '%[1]s' THEN ref.ref2_amount
					ELSE 0
				END
			), 0) as ref2_amount,
		COALESCE(SUM(
				CASE 
					WHEN ref.ref3_user_id = '%[1]s' THEN ref.ref3_amount
					ELSE 0
				END
			), 0) as ref3_amount,
		COALESCE(SUM(
				CASE 
					WHEN ref.ref1_user_id = '%[1]s' THEN ref.share_amount
					ELSE 0
				END
			), 0) as share_amount,
	    COALESCE(MAX(
				CASE 
					WHEN ref.ref1_user_id = '%[1]s' THEN ref.share_rate
					ELSE 0
				END
			), 0) as max_share_rate
	FROM 
		%[2]s as ref
		INNER JOIN %[3]s as ac on ac.id = ref.campaign_id
		INNER JOIN %[4]s pc on pc.course_cuid = ref.course_cuid
	WHERE 
		(
			ref.ref1_user_id = '%[1]s'
		OR ref.ref2_user_id = '%[1]s'
		OR ref.ref3_user_id = '%[1]s'
		) 
		%[5]s
	GROUP BY ac.id, pc.course_cuid, currency) as rp
	%[6]s 
	LIMIT %[7]d OFFSET %[8]d
;`,
			userID, refTbl, actbl, pctbl, optionWhereStatement,
			sortStr, limit, offset,
		)

		result := DB.Debug().Raw(mainQuery).Scan(&reports)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- reports
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		countQuery := fmt.Sprintf(`
SELECT count(*) FROM 
(
	SELECT 
	ac.id
FROM 
	%[2]s as ref
	INNER JOIN %[3]s as ac on ac.id = ref.campaign_id
	INNER JOIN %[4]s pc on pc.course_cuid = ref.course_cuid
WHERE 
	(
		ref.ref1_user_id = '%[1]s'
	OR ref.ref2_user_id = '%[1]s'
	OR ref.ref3_user_id = '%[1]s'
	) 
	%[5]s
GROUP BY ac.id, pc.course_cuid, currency
);`,
			userID, refTbl, actbl, pctbl, optionWhereStatement,
		)

		qb := DB.Debug()
		totalErr := qb.Raw(countQuery).Count(&totalCount).Error
		if totalErr != nil {
			errorChan <- totalErr
			return
		}
		countChan <- totalCount
	}()

	// Wait for all goroutines to finish
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case reports = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return reports, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}
