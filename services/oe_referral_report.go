package services

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm/clause"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"sync"
	"time"
)

func (s *OEReferralReportService) GenerateReports(req *dto.GenerateOEReferralReportsRequest) *e.AppError {
	aiCampaign := models.GetConfig[*models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	courseCuids := lo.Map(aiCampaign.Courses, func(item *models.AiCampaignCourseItem, _ int) string {
		return item.CourseCuid
	})
	err := models.Repository.OEReferralReport(context.TODO()).GenReportsFromRegisterEvent(
		aiCampaign.CampaignKey,
		aiCampaign.OrgID,
		req.From,
		req.To,
		courseCuids,
		req.BatchSize,
	)
	if err != nil {
		return e.NewError500(e.ERROR, "Generate reports from register event failed: "+err.Error())
	}

	err = models.Repository.OEReferralReport(context.TODO()).GenReportsFromEnrollEvent(
		aiCampaign.CampaignKey,
		aiCampaign.OrgID,
		req.From,
		req.To,
		courseCuids,
		req.BatchSize,
	)
	if err != nil {
		return e.NewError500(e.ERROR, "Generate reports from enroll event failed: "+err.Error())
	}

	err = models.Repository.OEReferralReport(context.TODO()).GenReportsFromLearningStatus(
		aiCampaign.CampaignKey,
		aiCampaign.OrgID,
		req.From,
		req.To,
		courseCuids,
		req.BatchSize,
	)
	if err != nil {
		return e.NewError500(e.ERROR, "Generate reports from learning status failed: "+err.Error())
	}

	err = models.Repository.OEReferralReport(context.TODO()).GenReportsFromCertificates(
		aiCampaign.CampaignKey,
		aiCampaign.OrgID,
		req.From,
		req.To,
		courseCuids,
		req.BatchSize,
	)
	if err != nil {
		return e.NewError500(e.ERROR, "Generate reports from certificate failed: "+err.Error())
	}

	if appErr := s.generateReportsFormSession(aiCampaign, req); appErr != nil {
		return e.NewError500(e.ERROR, "Generate reports from form session failed: "+appErr.Error())
	}

	courseOutlinesByCuids := make(map[string]*models.Course)
	pubCourses, _, appErr := PublishCourse.FindPagePublishCourse(&models.PublishCourseQuery{
		CourseCuidIn: courseCuids,
	}, &models.FindPageOptions{
		Page:    1,
		PerPage: len(courseCuids),
	})
	if appErr != nil {
		return appErr
	}

	var checkCertCourseCUIDs []string
	for _, pubCourse := range pubCourses {
		outline, aErr := Course.FindByIdForOutline(pubCourse.CourseID, false, &models.FindOneOptions{})
		if aErr != nil {
			return aErr
		}

		if outline.MarkAsCompleted && outline.HasCertificate {
			checkCertCourseCUIDs = append(checkCertCourseCUIDs, outline.Cuid)
		}

		courseOutlinesByCuids[pubCourse.CourseCuid] = outline
	}

	if len(checkCertCourseCUIDs) > 0 {
		page := 1
		for {
			reports, pagination, nErr := models.Repository.OEReferralReport(s.ctx).FindPage(&models.OEReferralReportQuery{
				EnrollNe:     util.NewInt64(0),
				CanClaimCert: util.NewBool(false),
			}, &models.FindPageOptions{
				Page:    page,
				PerPage: setting.AppSetting.DefaultPerPage,
				Sort:    []string{models.CreateAtASC, models.UserIDASC},
			})
			if nErr != nil {
				return e.NewError500(e.ERROR, "Find OE referral reports failed: "+nErr.Error())
			}

			var wg sync.WaitGroup
			for _, report := range reports {
				wg.Add(1)
				go func(rp *models.OEReferralReport) {
					defer wg.Done()

					course, found := courseOutlinesByCuids[rp.CourseCuid]
					if !found {
						return
					}

					canClaim, aErr := Certificate.CheckingCertificateCondition(&dto.CheckingCertificateConditionRequest{
						UserID: rp.UserID,
						Course: course,
						OrgID:  rp.OrgID,
					})
					if aErr != nil {
						log.ErrorWithAlertf("OEReferralReportService::GenerateReports Check certificate condition failed: %v", aErr)
						return
					}

					if rp.CanClaimCert != canClaim {
						rp.CanClaimCert = canClaim
						if uErr := models.Repository.OEReferralReport(s.ctx).Update(rp, nil); uErr != nil {
							log.ErrorWithAlertf("OEReferralReportService::GenerateReports Update claim certificate for OE referral report failed: %v", aErr)
							return
						}
					}
				}(report)
			}

			wg.Wait()

			if !pagination.HasNextPage() {
				break
			}
			page++
		}
	}
	return nil
}

func (s *OEReferralReportService) generateReportsFormSession(aiCampaign *models.AiGovVn2025Campaign, req *dto.GenerateOEReferralReportsRequest) *e.AppError {
	courseCuids := lo.Map(aiCampaign.Courses, func(item *models.AiCampaignCourseItem, _ int) string {
		return item.CourseCuid
	})

	// Find form submissions of courses
	publishCourses, err := models.Repository.PublishCourse(context.TODO()).FindMany(&models.PublishCourseQuery{
		OrgID:        &aiCampaign.OrgID,
		CourseCuidIn: courseCuids,
	}, &models.FindManyOptions{})
	if err != nil {
		return e.NewError500(e.ERROR, "Find publish courses by CUIDs failed: "+err.Error())
	}
	if len(publishCourses) == 0 {
		return nil
	}

	formRelations, err := models.Repository.FormRelation.FindMany(&models.FormRelationQuery{
		RelatedEntityIDIn: lo.Map(publishCourses, func(pubCourse *models.PublishCourse, _ int) string {
			return pubCourse.CourseID
		}),
		RelatedEntityType: util.NewT(models.CourseModelName),
	}, &models.FindManyOptions{})
	if err != nil {
		return e.NewError500(e.ERROR, "Find form relations failed: "+err.Error())
	}
	if len(formRelations) == 0 {
		return nil
	}

	var formIDs []string
	formRelationsByIDs := make(map[string]*models.FormRelation)
	for _, formRelation := range formRelations {
		formIDs = append(formIDs, formRelation.FormID)
		formRelationsByIDs[formRelation.ID] = formRelation
	}
	if len(formRelations) == 0 {
		return nil
	}

	forms, err := models.Repository.Form.FindMany(&models.FormQuery{
		IDIn:  formIDs,
		Event: util.NewT(aiCampaign.FormEvent),
	}, &models.FindManyOptions{})
	if err != nil {
		return e.NewError500(e.ERROR, "Find AI government forms failed: "+err.Error())
	}
	if len(forms) == 0 {
		return nil
	}

	formSessions, err := models.Repository.FormSession.FindMany(&models.FormSessionQuery{
		FormUIDIn: lo.Map(forms, func(form *models.Form, _ int) string {
			return form.UID
		}),
		CreateAtGte: &req.From,
		CreateAtLte: &req.To,
	}, &models.FindManyOptions{
		Preloads: []string{models.AnswersField, models.AnswersOptionField},
	})
	if err != nil {
		return e.NewError500(e.ERROR, "Find form sessions failed: "+err.Error())
	}
	if len(formRelations) == 0 {
		return nil
	}

	refReportsByUniqueKeys := make(map[string]*models.OEReferralReport)
	for _, session := range formSessions {
		province, userProps := s.extractAIGovernFormSession(session)
		userID := ""
		if session.UserID != nil {
			userID = *session.UserID
		}

		courseCuid := ""
		if session.FormRelationID != nil {
			formRelation, found := formRelationsByIDs[*session.FormRelationID]
			if found {
				courseCuid = formRelation.RelatedEntityUID
			}
		}

		refReport := &models.OEReferralReport{
			OrgID:                    aiCampaign.OrgID,
			CampaignKey:              aiCampaign.CampaignKey,
			LocalLevel:               models.LocalLevelProvince,
			Province:                 province,
			LocalUnit:                "",
			UserID:                   userID,
			UserProps:                userProps,
			CourseCuid:               courseCuid,
			FillFormDate:             int64(session.CreateAt),
			RegisterDate:             0,
			EnrollDate:               0,
			CompleteDate:             0,
			ClaimCertDate:            0,
			NumberOfCompletedSection: 0,
		}

		uniqueKey := aiCampaign.CampaignKey + "__" + userID + "__" + courseCuid
		existingRefReport, found := refReportsByUniqueKeys[uniqueKey]
		if !found {
			refReportsByUniqueKeys[uniqueKey] = refReport
			continue
		}

		if existingRefReport.FillFormDate < refReport.FillFormDate {
			refReportsByUniqueKeys[uniqueKey] = refReport
		}
	}

	var refReports []*models.OEReferralReport
	for _, refReport := range refReportsByUniqueKeys {
		refReports = append(refReports, refReport)
	}

	if len(refReports) == 0 {
		return nil
	}

	var userIDs []string
	seenUserIDs := make(map[string]struct{})
	for _, rp := range refReports {
		if _, found := seenUserIDs[rp.UserID]; !found && rp.UserID != "" {
			seenUserIDs[rp.UserID] = struct{}{}
			userIDs = append(userIDs, rp.UserID)
		}
	}

	userRoles, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		UserIDIn: userIDs,
		RoleID:   util.NewT(models.LearnerRoleType),
	}, &models.FindManyOptions{})
	if err != nil {
		return e.NewError500(e.ERROR, "Find user role org failed: "+err.Error())
	}

	userRolesByUserIDs := make(map[string]*models.UserRoleOrg)
	for _, userRole := range userRoles {
		userRolesByUserIDs[userRole.UserID] = userRole
	}

	for _, rp := range refReports {
		if userRole, found := userRolesByUserIDs[rp.UserID]; found {
			rp.RegisterDate = int64(userRole.CreateAt)
		}
	}

	for _, chunk := range lo.Chunk(refReports, lo.If(req.BatchSize <= 0, models.DefaultBatchSize).Else(req.BatchSize)) {
		result := models.DB.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "org_id"},
				{Name: "campaign_key"},
				{Name: "user_id"},
				{Name: "course_cuid"},
			},
			DoUpdates: clause.AssignmentColumns([]string{"local_level", "province", "local_unit", "user_props", "fill_form_date", "register_date"}),
		}).Create(&chunk)
		if err = result.Error; err != nil {
			return e.NewError500(e.ERROR, "Create OE referral reports failed: "+err.Error())
		}
	}

	return nil
}

func (s *OEReferralReportService) extractAIGovernFormSession(session *models.FormSession) (string, models.JSONB) {
	var province string
	data := make(models.JSONB)
	for _, answer := range session.Answers {
		switch answer.Key {
		case models.FormQuestionKeyFullName:
			data[string(models.FormQuestionKeyFullName)] = answer.Text

		case models.FormQuestionKeyAgeGroup:
			if answer.Option != nil {
				data[string(models.FormQuestionKeyAgeGroup)] = answer.Option.Text
			}

		case models.FormQuestionKeyProvince:
			if answer.Option != nil {
				province = answer.Option.Text
				data[string(models.FormQuestionKeyProvince)] = answer.Option.Text
			}

		case models.FormQuestionKeySchool:
			data[string(models.FormQuestionKeySchool)] = answer.Text

		case models.FormQuestionKeyJob:
			data[string(models.FormQuestionKeyJob)] = answer.Text // Other option
			if answer.Option != nil {
				data[string(models.FormQuestionKeyJob)] = answer.Option.Text
			}
		}
	}
	return province, data
}

func (s *OEReferralService) SummaryReferralReport(configKey string) {
	aiCampaign := models.GetConfig[*models.AiGovVn2025Campaign](configKey)
	leaders, err := OEReferralLeaderBoard(s.ctx).FindMany(&models.OEReferralLeaderBoardQuery{
		CampaignKey: util.NewString(aiCampaign.CampaignKey),
		OrgID:       util.NewT(aiCampaign.OrgID),
	}, nil)
	if err != nil {
		log.ErrorWithAlertf("Failed to find referral leaderboard", "error", err.Error())
		return
	}

	pRegCount := int64(0)
	pRefCount := int64(0)
	pCertCount := int64(0)
	pCcCount := int64(0)

	for _, leader := range leaders {
		baseQuery := &models.OEReferralReportQuery{
			CampaignKey: util.NewString(aiCampaign.CampaignKey),
			Province:    util.NewString(leader.DisplayName),
			LocalLevel:  util.NewT(leader.LocalLevel),
			OrgID:       util.NewT(aiCampaign.OrgID),
		}
		if leader.CourseCuid != "" {
			baseQuery.CourseCUID = util.NewString(leader.CourseCuid)
		}

		regQuery := util.DeepCopy(baseQuery)
		regQuery.EnrollLT = util.NewInt64(0)
		regCount, regErr := models.Repository.OEReferralReport(s.ctx).Count(regQuery)
		if regErr != nil {
			log.ErrorWithAlertf("Failed to count regCount report ", regErr.Error())
			return
		}
		// Count ref Theo tỉnh
		refQuery := util.DeepCopy(baseQuery)
		refQuery.IsRef = util.NewBool(true)
		refCount, refErr := models.Repository.OEReferralReport(s.ctx).Count(refQuery)
		if refErr != nil {
			log.ErrorWithAlertf("Failed to count refCount report ", refErr.Error())
			return
		}
		//- Count cert
		certQuery := util.DeepCopy(baseQuery)
		certQuery.ClaimCertLT = util.NewInt64(0)
		certCount, certErr := models.Repository.OEReferralReport(s.ctx).Count(certQuery)
		if certErr != nil {
			log.ErrorWithAlertf("Failed to count certCount report ", certErr.Error())
			return
		}
		ccQuery := util.DeepCopy(baseQuery)
		ccQuery.CompleteLT = util.NewInt64(0)
		ccCount, ccErr := models.Repository.OEReferralReport(s.ctx).Count(ccQuery)
		if ccErr != nil {
			log.ErrorWithAlertf("Failed to count ccCount report ", ccErr.Error())
		}

		perCertOnRef, _ := util.RoundDivision(float64(certCount), float64(refCount), 2)
		perCertOnReg, _ := util.RoundDivision(float64(certCount), float64(regCount), 2)

		leader.RegisterCount = regCount
		leader.RefCount = refCount
		leader.CertCount = certCount
		leader.CompletedCourseCount = ccCount
		leader.PercentCertOnRef = perCertOnRef
		leader.PercentCertOnReg = perCertOnReg

		pRegCount += regCount
		pRefCount += refCount
		pCertCount += certCount
		pCcCount += ccCount

		if _, uErr := OEReferralLeaderBoard(s.ctx).Update(leader); uErr != nil {
			log.ErrorWithAlertf("Failed to update referral leaderboard", "error", uErr.Error())
		}
	}

	reg, _ := models.Repository.OEReferralReport(s.ctx).Count(&models.OEReferralReportQuery{
		CampaignKey: util.NewString(aiCampaign.CampaignKey),
		LocalLevel:  util.NewT(models.LocalLevelProvince),
		OrgID:       util.NewT(aiCampaign.OrgID),
		EnrollLT:    util.NewInt64(0),
	})

	ref, _ := models.Repository.OEReferralReport(s.ctx).Count(&models.OEReferralReportQuery{
		CampaignKey: util.NewString(aiCampaign.CampaignKey),
		LocalLevel:  util.NewT(models.LocalLevelProvince),
		OrgID:       util.NewT(aiCampaign.OrgID),
		IsRef:       util.NewBool(true),
	})

	cert, _ := models.Repository.OEReferralReport(s.ctx).Count(&models.OEReferralReportQuery{
		CampaignKey: util.NewString(aiCampaign.CampaignKey),
		LocalLevel:  util.NewT(models.LocalLevelProvince),
		OrgID:       util.NewT(aiCampaign.OrgID),
		ClaimCertLT: util.NewInt64(0),
	})

	cc, _ := models.Repository.OEReferralReport(s.ctx).Count(&models.OEReferralReportQuery{
		CampaignKey: util.NewString(aiCampaign.CampaignKey),
		LocalLevel:  util.NewT(models.LocalLevelProvince),
		OrgID:       util.NewT(aiCampaign.OrgID),
		CompleteLT:  util.NewInt64(0),
	})

	other, _ := OEReferralLeaderBoard(s.ctx).FindOne(&models.OEReferralLeaderBoardQuery{
		CampaignKey: util.NewString(aiCampaign.CampaignKey),
		OrgID:       util.NewT(aiCampaign.OrgID),
		DisplayName: util.NewString(util.ProvinceOther),
	}, nil)
	if other != nil {
		other.RefCount = ref - pRefCount
		other.RegisterCount = reg - pRegCount
		other.CertCount = cert - pCertCount
		other.CompletedCourseCount = cc - pCcCount
		perCertOnRef, _ := util.RoundDivision(float64(other.CertCount), float64(other.RefCount), 2)
		perCertOnReg, _ := util.RoundDivision(float64(other.CertCount), float64(other.RegisterCount), 2)
		other.PercentCertOnRef = perCertOnRef
		other.PercentCertOnReg = perCertOnReg
		if _, uErr := OEReferralLeaderBoard(s.ctx).Update(other); uErr != nil {
			log.ErrorWithAlertf("Failed to update referral leaderboard", "error", uErr.Error())
		}
	}
}

func (s *OEReferralReportService) FindWidgetStatistic(
	campaignKey string,
	req *dto.OERefWidgetStatisticRequest,
) (*dto.OERefWidgetStatisticResponse, *e.AppError) {

	generalStats, err := models.Repository.OEReferralReport(s.ctx).FindGeneralStats(
		req.FromDate,
		req.ToDate,
		campaignKey,
		req.CourseCUIDs,
	)
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Find OE referral widget statistic failed: "+err.Error())
	}

	resp := &dto.OERefWidgetStatisticResponse{
		TotalRegisteredUsers: generalStats.TotalRegisteredUsers,
		TotalEnrolledUsers:   generalStats.TotalEnrolledUsers,
		TotalCompletedUsers:  generalStats.TotalCompletedUsers,
		CompletionRate:       0,
	}
	// Calculate completion rate
	if resp.TotalCompletedUsers > 0 {
		resp.CompletionRate = float64(resp.TotalCompletedUsers) / float64(resp.TotalEnrolledUsers) * 100
	}

	return resp, nil
}

func (s *OEReferralReportService) FindLearnerGrowthStatistic(
	campaignKey string,
	req *dto.OERefLearnerGrowthStatisticRequest,
) (*dto.OERefLearnerGrowthStatisticResponse, *e.AppError) {
	// Validate request and set default values if needed
	if req.GroupBy == "" {
		req.GroupBy = "day" // Default groupBy if not provided
	}

	// Validate groupBy parameter
	if req.GroupBy != "hour" && req.GroupBy != "day" && req.GroupBy != "month" && req.GroupBy != "year" {
		return nil, e.NewError400(e.ERROR, "Invalid group_by parameter. Must be one of: hour, day, month, year")
	}

	/* TODO validate req.GroupBy value
	Tùy chỉnh (chọn khoảng thời gian cụ thể):
	 - ≤ 31 ngày: hiển thị theo ngày
	 - 32 ngày - 12 tháng: hiển thị theo tháng
	 - > 1 năm: hiển thị theo năm
	*/

	// Define time format patterns based on group_by
	var timeFormat string
	switch req.GroupBy {
	case "hour":
		timeFormat = "2006-01-02 15:04"
	case "day":
		timeFormat = "2006-01-02"
	case "month":
		timeFormat = "2006-01"
	case "year":
		timeFormat = "2006"
	}

	// Parse timezone from request or use default
	timezone := "UTC"
	if req.Timezone != "" {
		timezone = req.Timezone
	}

	// Load timezone location
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		// Fallback to UTC if timezone is invalid
		loc = time.UTC
	}

	// Prepare response object
	response := &dto.OERefLearnerGrowthStatisticResponse{
		GroupBy: req.GroupBy,
		Points:  []*dto.OERefStudentGrowthDataPoint{},
	}

	// Get table name for the OEReferralReport model
	referralReportTbl := models.GetTblName(models.OEReferralReportTbl)

	// Build course filter condition for SQL
	courseCuidsFilter := ""
	if len(req.CourseCUIDs) > 0 {
		courseCuidsFilter = "AND course_cuid IN @courseCuids"
	}

	// SQL query to get enrollment counts by time period
	// Note: We cast the period_epoch to BIGINT to avoid the string conversion issue
	query := fmt.Sprintf(`
		SELECT 
			date_trunc('%[1]s', to_timestamp(enroll_date/1000.0) AT TIME ZONE @timezone) AS period,
			to_char(date_trunc('%[1]s', to_timestamp(enroll_date/1000.0) AT TIME ZONE @timezone), 
				CASE 
					WHEN '%[1]s' = 'hour' THEN 'YYYY-MM-DD HH24:MI:SS'
					WHEN '%[1]s' = 'day' THEN 'YYYY-MM-DD HH24:MI:SS'
					WHEN '%[1]s' = 'month' THEN 'YYYY-MM-DD HH24:MI:SS'
					WHEN '%[1]s' = 'year' THEN 'YYYY-MM-DD HH24:MI:SS'
				END
			) AS period_text,
			CAST((extract(epoch from date_trunc('%[1]s', to_timestamp(enroll_date/1000.0) AT TIME ZONE @timezone)) * 1000) AS BIGINT) AS period_epoch,
			COUNT(*) AS count
		FROM %[2]s
		WHERE campaign_key = @campaignKey
			AND enroll_date BETWEEN @fromDate AND @toDate
			%[3]s
		GROUP BY period
		ORDER BY period ASC
	`,
		req.GroupBy,
		referralReportTbl,
		courseCuidsFilter,
	)

	// Execute the query with parameters
	type EnrollmentCount struct {
		Period      time.Time `json:"period"`       // Go will parse this as UTC
		PeriodText  string    `json:"period_text"`  // Plain text format of the period with timezone
		PeriodEpoch int64     `json:"period_epoch"` // Epoch milliseconds of the period with timezone
		Count       int64     `json:"count"`
	}

	var enrollmentCounts []EnrollmentCount

	if err = models.DB.Debug().Raw(query, map[string]interface{}{
		"campaignKey": campaignKey,
		"fromDate":    req.FromDate,
		"toDate":      req.ToDate,
		"courseCuids": req.CourseCUIDs,
		"timezone":    timezone,
	}).Scan(&enrollmentCounts).Error; err != nil {
		return nil, e.NewError500(e.ERROR, "Find OE referral growth statistic failed: "+err.Error())
	}

	// Convert from/to dates from milliseconds to Go time.Time in the specified timezone
	fromTime := time.Unix(req.FromDate/1000, 0).In(loc)
	toTime := time.Unix(req.ToDate/1000, 0).In(loc)

	// Truncate times to match the groupBy unit
	fromTime = truncateTime(fromTime, req.GroupBy)
	toTime = truncateTime(toTime, req.GroupBy)

	// Create time series in the specified timezone
	timePoints := generateTimeSeries(fromTime, toTime, req.GroupBy)

	// Create map for quick lookups using period_text as key
	countMap := make(map[string]int64)
	for _, ec := range enrollmentCounts {
		// Using period_text as key since PeriodEpoch may have conversion issues
		countMap[ec.PeriodText] = ec.Count
	}

	// Process results and calculate growth rates
	var prevCount int64 = 0
	for i, tp := range timePoints {
		// Format time point to match period_text format from SQL
		tpFormatted := tp.Format("2006-01-02 15:04:05")

		// Get count for this time period, defaulting to 0 if not found
		count := countMap[tpFormatted]

		// Create data point with timestamp in milliseconds
		point := &dto.OERefStudentGrowthDataPoint{
			Timestamp: fmt.Sprintf("%d", tp.UnixNano()/int64(time.Millisecond)),
			TimeLabel: tp.Format(timeFormat),
			Value:     count,
		}

		// Calculate growth rate
		if i > 0 && prevCount > 0 && prevCount != count {
			point.GrowthRate = float64(count-prevCount) / float64(prevCount) * 100
		} else {
			point.GrowthRate = 0
		}

		response.Points = append(response.Points, point)
		prevCount = count
	}

	return response, nil
}

// truncateTime truncates a time.Time to the specified group_by unit (hour, day, month, year)
func truncateTime(t time.Time, groupBy string) time.Time {
	switch groupBy {
	case "hour":
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), 0, 0, 0, t.Location())
	case "day":
		return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	case "month":
		return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
	case "year":
		return time.Date(t.Year(), 1, 1, 0, 0, 0, 0, t.Location())
	default:
		return t
	}
}

// generateTimeSeries creates a series of time points from start to end with intervals based on groupBy
// Both start and end times should already be in the desired timezone
func generateTimeSeries(fromTime, toTime time.Time, groupBy string) []time.Time {
	var result []time.Time

	// Generate time series
	current := fromTime
	for current.Before(toTime) || current.Equal(toTime) {
		result = append(result, current)

		// Increment current by one unit based on groupBy
		switch groupBy {
		case "hour":
			current = current.Add(time.Hour)
		case "day":
			current = current.AddDate(0, 0, 1)
		case "month":
			current = current.AddDate(0, 1, 0)
		case "year":
			current = current.AddDate(1, 0, 0)
		}
	}

	return result
}

func (s *OEReferralReportService) FindSectionCompletionStatistic(
	campaignKey string,
	req *dto.OERefSectionCompletionRequest,
) (dto.OERefSectionCompletionResponse, *e.AppError) {
	courseCuids := util.DeepCopy(req.CourseCUIDs)
	if len(courseCuids) == 0 {
		// TODO find solution to remove this hardcode
		aiCampaign := models.GetConfig[*models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
		courseCuids = lo.Map(aiCampaign.Courses, func(item *models.AiCampaignCourseItem, _ int) string {
			return item.CourseCuid
		})
	}

	resp := dto.OERefSectionCompletionResponse{}
	for _, courseCUID := range courseCuids {
		courseStats, err := s.getSectionStatsForCourse(campaignKey, courseCUID, req)
		if err != nil {
			return nil, err
		}
		resp = append(resp, courseStats)
	}

	return resp, nil
}

// getSectionStatsForCourse gets section completion statistics for a single course
func (s *OEReferralReportService) getSectionStatsForCourse(
	campaignKey string,
	courseCUID string,
	req *dto.OERefSectionCompletionRequest,
) (*dto.OERefSectionCompletionCourse, *e.AppError) {

	// Get publish course information
	pubCourse, err := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
		CourseCuid: &courseCUID,
	}, &models.FindOneOptions{})
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Find publish course from course CUID failed: "+err.Error())
	}

	// Get course outline
	courseOutline, appErr := Course.FindByIdForOutline(pubCourse.CourseID, false, &models.FindOneOptions{})
	if appErr != nil {
		return nil, e.NewError500(e.ERROR, "Find course outline from course ID failed: "+appErr.Error())
	}

	listSectionStats, err := models.Repository.OEReferralReport(s.ctx).FindSectionStatsByCourse(
		req.FromDate,
		req.ToDate,
		campaignKey,
		courseOutline,
	)
	if err != nil {
		return nil, e.NewError500(e.ERROR, fmt.Sprintf("Failed to get section stats for course %s: %s", courseCUID, err.Error()))
	}

	sectionStatsBySectionUIDs := make(map[string]*models.OERefSectionStats)
	for _, ss := range listSectionStats {
		sectionStatsBySectionUIDs[ss.SectionUID] = ss
	}

	// Create items for each section in the outline
	courseStats := &dto.OERefSectionCompletionCourse{
		CourseCUID:  courseCUID,
		CourseName:  courseOutline.Name,
		ModuleItems: []dto.OERefSectionCompletionItem{},
	}
	for _, section := range courseOutline.Outline {
		completedCount := int64(0)
		if sectionCount, exists := sectionStatsBySectionUIDs[section.UID]; exists {
			completedCount = sectionCount.CompletedCount
		}

		courseStats.ModuleItems = append(courseStats.ModuleItems, dto.OERefSectionCompletionItem{
			SectionUID:     section.UID,
			SectionName:    section.Title,
			Order:          section.Order,
			CompletedCount: completedCount,
		})
	}

	return courseStats, nil
}

//// getSectionStatsForCourse gets section completion statistics for a single course
//func (s *OEReferralReportService) getSectionStatsForCourse(
//	campaignKey string,
//	courseCUID string,
//	req *dto.OERefSectionCompletionRequest,
//) (*dto.OERefSectionCompletionCourse, *e.AppError) {
//
//	referralReportTbl := models.GetTblName(models.OEReferralReportTbl)
//
//	// Get publish course information
//	pubCourse, err := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
//		CourseCuid: &courseCUID,
//	}, &models.FindOneOptions{})
//	if err != nil {
//		return nil, e.NewError500(e.ERROR, "Find publish course from course CUID failed: "+err.Error())
//	}
//
//	// Get course outline
//	courseOutline, appErr := Course.FindByIdForOutline(pubCourse.CourseID, false, &models.FindOneOptions{})
//	if appErr != nil {
//		return nil, e.NewError500(e.ERROR, "Find course outline from course ID failed: "+appErr.Error())
//	}
//
//	// Get section completion data
//	// AND enroll_date BETWEEN @fromDate AND @toDate
//	query := `
//		WITH section_counts AS (
//			SELECT
//				number_of_completed_section,
//				COUNT(DISTINCT user_id) as user_count
//			FROM ` + referralReportTbl + `
//			WHERE campaign_key = @campaignKey
//				AND course_cuid = @courseCUID
//				AND number_of_completed_section > 0
//			GROUP BY number_of_completed_section
//		)
//		SELECT
//			section_number,
//			SUM(user_count) AS completed_count
//		FROM generate_series(1, @totalSections) as s(section_number)
//			LEFT JOIN section_counts sc ON s.section_number <= sc.number_of_completed_section
//		GROUP BY s.section_number
//		ORDER BY s.section_number
//	`
//
//	// Temporary struct to hold query results
//	type SectionCount struct {
//		SectionNumber  int   `json:"section_number"`
//		CompletedCount int64 `json:"completed_count"`
//	}
//
//	var sectionCounts []SectionCount
//	if err = models.DB.Debug().Raw(query, map[string]interface{}{
//		"campaignKey":   campaignKey,
//		"courseCUID":    courseCUID,
//		"fromDate":      req.FromDate,
//		"toDate":        req.ToDate,
//		"totalSections": courseOutline.ActiveSection,
//	}).Scan(&sectionCounts).Error; err != nil {
//		return nil, e.NewError500(e.ERROR, fmt.Sprintf("Failed to get section statistics for course %s: %s", courseCUID, err.Error()))
//	}
//
//	sectionCompletionMap := make(map[int]int64)
//	for _, sc := range sectionCounts {
//		sectionCompletionMap[sc.SectionNumber] = sc.CompletedCount
//	}
//
//	// Create items for each section in the outline
//	courseStats := &dto.OERefSectionCompletionCourse{
//		CourseCUID:  courseCUID,
//		CourseName:  courseOutline.Name,
//		ModuleItems: []dto.OERefSectionCompletionItem{},
//	}
//	for i, section := range courseOutline.Outline {
//		sectionNumber := i + 1
//		completedCount := int64(0)
//
//		if count, exists := sectionCompletionMap[sectionNumber]; exists {
//			completedCount = count
//		}
//
//		courseStats.ModuleItems = append(courseStats.ModuleItems, dto.OERefSectionCompletionItem{
//			SectionUID:     section.UID,
//			SectionName:    section.Title,
//			Order:          section.Order,
//			CompletedCount: completedCount,
//		})
//	}
//
//	return courseStats, nil
//}

func (s *OEReferralReportService) FindLearnerCountByProvinces(
	campaignKey string,
	req *dto.OERefLearnerCountByProvinceRequest,
) (dto.OERefLearnerCountByProvinceResponse, *e.AppError) {

	provinces := req.Provinces
	if len(provinces) == 0 {
		for _, item := range util.DefaultProvinceVN() {
			v, found := item["label"]
			if !found {
				continue
			}

			province, ok := v.(string)
			if !ok {
				continue
			}

			provinces = append(provinces, province)
		}
	}

	provinceStats, err := models.Repository.OEReferralReport(s.ctx).FindProvinceStats(
		req.FromDate,
		req.ToDate,
		campaignKey,
		provinces,
		req.CourseCUIDs,
	)
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Failed to find OE referral stats by provinces: "+err.Error())
	}

	//var unknownProvinceCount *models.OERefProvinceStats
	learnerCountsByProvinces := make(map[string]*models.OERefProvinceStats)
	totalCount := int64(0)
	for _, provinceStat := range provinceStats {
		totalCount += provinceStat.LearnerCount
		//if provinceStat.Province == util.ProvinceOther {
		//	unknownProvinceCount = provinceStat
		//	continue
		//}
		learnerCountsByProvinces[provinceStat.Province] = provinceStat
	}

	var provinceCounts dto.OERefLearnerCountByProvinceResponse
	var totalPercent float64

	for idx, province := range provinces {
		percent := float64(0)

		provinceCount := &dto.OERefProvinceLearnerCount{
			Province:         province,
			LearnerCount:     0,
			CompletionCount:  0,
			EnrollCount:      0,
			CertificateCount: 0,
		}

		if refCount, found := learnerCountsByProvinces[province]; found {
			completionOnCertPercent := float64(0)
			if refCount.CertCount > 0 {
				completionOnCertPercent = float64(refCount.CertCount) * 100 / float64(refCount.EnrollCount)
			}

			provinceCount = &dto.OERefProvinceLearnerCount{
				Province:            province,
				LearnerCount:        refCount.LearnerCount,
				CompletionCount:     refCount.CompletionCount,
				EnrollCount:         refCount.EnrollCount,
				CertificateCount:    refCount.CertCount,
				CertOnEnrollPercent: completionOnCertPercent,
			}

			percent = float64(0)
			if totalCount > 0 {
				percent = float64(refCount.LearnerCount) * 100 / float64(totalCount)
			}
		}

		// If the last
		if idx == len(provinces)-1 {
			if totalCount > 0 {
				provinceCount.LearnerPercent = lo.If(provinceCount.LearnerCount == 0, float64(0)).Else(100 - totalPercent)
			}

			provinceCounts = append(provinceCounts, provinceCount)
		} else {
			totalPercent += percent
			provinceCount.LearnerPercent = percent
			provinceCounts = append(provinceCounts, provinceCount)
		}
	}
	return provinceCounts, nil
}

func (s *OEReferralReportService) FindDetailStatsByProvinces(
	campaignKey string,
	req *dto.OERefDetailsByProvinceStatsRequest,
) ([]*dto.OERefByProvinceStatItem, *e.AppError) {
	if req.FromDate > req.ToDate {
		return nil, e.NewError400(e.ERROR, "from date must be less than or equal to date")
	}

	provinces := req.Provinces
	if len(provinces) == 0 {
		for _, item := range util.DefaultProvinceVN() {
			v, found := item["label"]
			if !found {
				continue
			}

			province, ok := v.(string)
			if !ok {
				continue
			}

			provinces = append(provinces, province)
		}
	}

	courseCUIDs := util.DeepCopy(req.CourseCUIDs)
	courseOutlinesByCuids := make(map[string]*models.Course)
	if len(courseCUIDs) == 0 {
		aiCampaign := models.GetConfig[*models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
		for _, item := range aiCampaign.Courses {
			courseCUIDs = append(courseCUIDs, item.CourseCuid)
		}

		pubCourses, _, appErr := PublishCourse.FindPagePublishCourse(&models.PublishCourseQuery{
			CourseCuidIn: courseCUIDs,
		}, &models.FindPageOptions{
			Page:    1,
			PerPage: len(courseCUIDs),
		})
		if appErr != nil {
			return nil, appErr
		}

		for _, pubCourse := range pubCourses {
			outline, aErr := Course.FindByIdForOutline(pubCourse.CourseID, false, &models.FindOneOptions{})
			if aErr != nil {
				return nil, aErr
			}

			courseOutlinesByCuids[pubCourse.CourseCuid] = outline
		}
	}

	referralReportTbl := models.GetTblName(models.OEReferralReportTbl)
	query := fmt.Sprintf(
		`
       SELECT
          CASE 
             WHEN province <> '' THEN province
             ELSE @unknownProvince
          END AS province,
          COUNT(CASE WHEN register_date BETWEEN @fromDate AND @toDate THEN 1 END) AS register_count,
          COUNT(CASE WHEN enroll_date BETWEEN @fromDate AND @toDate THEN 1 END) AS enroll_count,
          COUNT(CASE WHEN complete_date BETWEEN @fromDate AND @toDate THEN 1 END) AS complete_count
       FROM %[1]s
       WHERE campaign_key = @campaignKey
          AND (
			register_date BETWEEN @fromDate AND @toDate 
			OR enroll_date BETWEEN @fromDate AND @toDate 
			OR complete_date BETWEEN @fromDate AND @toDate
          ) %[2]s %[3]s
       GROUP BY province
    `,
		referralReportTbl,
		lo.If(len(courseCUIDs) == 0, "").Else("AND course_cuid IN @courseCUIDs"),
		lo.If(len(provinces) == 0, "").Else("AND province IN @provinces"),
	)
	var byProvinceStatItems []*dto.OERefByProvinceStatItem
	if err := models.DB.Debug().Raw(query, map[string]interface{}{
		"campaignKey": campaignKey,
		"fromDate":    req.FromDate,
		"toDate":      req.ToDate,
		"courseCUIDs": courseCUIDs,
		"provinces": lo.Map(provinces, func(province string, _ int) string {
			return lo.If(province == util.ProvinceOther, "").Else(province)
		}),
		"unknownProvince": util.ProvinceOther,
	}).Scan(&byProvinceStatItems).Error; err != nil {
		return nil, e.NewError500(e.ERROR, "Find by province stats items error: "+err.Error())
	}

	statsByProvinces := make(map[string]*dto.OERefByProvinceStatItem)
	for _, stat := range byProvinceStatItems {
		statsByProvinces[stat.Province] = stat
	}

	listSectionStatsByProvince, err := models.Repository.OEReferralReport(s.ctx).FindSectionStatsByProvinces(
		req.FromDate,
		req.ToDate,
		campaignKey,
		provinces,
		courseCUIDs,
	)
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Failed to get section stats for provinces: "+err.Error())
	}

	sectionStatsMap := make(map[string]*models.OERefSectionByProvinceStats)
	for _, sectionStats := range listSectionStatsByProvince {
		sectionStatsMap[sectionStats.Province+sectionStats.SectionUID] = sectionStats
	}

	var resp []*dto.OERefByProvinceStatItem
	for _, province := range provinces {
		if stat, found := statsByProvinces[province]; found {
			completionRate := float64(0)
			if stat.EnrollCount > 0 {
				completionRate = float64(stat.CompleteCount) * 100 / float64(stat.EnrollCount)
			}
			resp = append(resp, &dto.OERefByProvinceStatItem{
				Province:       province,
				RegisterCount:  stat.RegisterCount,
				EnrollCount:    stat.EnrollCount,
				CompleteCount:  stat.CompleteCount,
				CompletionRate: completionRate,
				Courses: lo.Map(courseCUIDs, func(cuid string, _ int) *dto.OERefByProvinceCourseItem {
					courseItem := &dto.OERefByProvinceCourseItem{
						Name:          "",
						ActiveSection: 0,
						Sections:      nil,
					}
					course, oFound := courseOutlinesByCuids[cuid]
					if !oFound {
						return courseItem
					}

					courseItem.Name = course.Name
					courseItem.ActiveSection = course.ActiveSection

					for _, section := range course.Outline {
						completedCount := int64(0)
						if sectionCount, exists := sectionStatsMap[province+section.UID]; exists {
							completedCount = sectionCount.CompletedCount
						}

						courseItem.Sections = append(courseItem.Sections, &dto.OERefByProvinceSectionItem{
							SectionUID:     section.UID,
							SectionName:    section.Title,
							Order:          section.Order,
							CompletedCount: completedCount,
						})
					}
					return courseItem
				}),
			})
		} else {
			resp = append(resp, &dto.OERefByProvinceStatItem{
				Province:       province,
				RegisterCount:  0,
				EnrollCount:    0,
				CompleteCount:  0,
				CompletionRate: 0,
				Courses: lo.Map(courseCUIDs, func(cuid string, _ int) *dto.OERefByProvinceCourseItem {
					courseItem := &dto.OERefByProvinceCourseItem{
						Name:          "",
						ActiveSection: 0,
						Sections:      nil,
					}
					course, oFound := courseOutlinesByCuids[cuid]
					if !oFound {
						return courseItem
					}

					courseItem.Name = course.Name
					courseItem.ActiveSection = course.ActiveSection

					for _, section := range course.Outline {
						completedCount := int64(0)
						if sectionCount, exists := sectionStatsMap[province+section.UID]; exists {
							completedCount = sectionCount.CompletedCount
						}

						courseItem.Sections = append(courseItem.Sections, &dto.OERefByProvinceSectionItem{
							SectionUID:     section.UID,
							SectionName:    section.Title,
							Order:          section.Order,
							CompletedCount: completedCount,
						})
					}
					return courseItem
				}),
			})
		}
	}

	return resp, nil
}

func (s *OEReferralReportService) FindPageLearners(
	campaignKey string,
	req *dto.OERefLearnersByCampaignRequest,
) (*dto.OERefLearnersByCampaignResponse, *e.AppError) {

	page := 1
	if req.Page > 0 {
		page = req.Page
	}

	perPage := 10
	if req.PerPage > 0 {
		perPage = req.PerPage
	}

	courseCUIDs := util.DeepCopy(req.CourseCUIDs)
	courseOutlinesByCuids := make(map[string]*models.Course)
	if len(courseCUIDs) == 0 {
		aiCampaign := models.GetConfig[*models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
		for _, item := range aiCampaign.Courses {
			courseCUIDs = append(courseCUIDs, item.CourseCuid)
		}

		pubCourses, _, appErr := PublishCourse.FindPagePublishCourse(&models.PublishCourseQuery{
			CourseCuidIn: courseCUIDs,
		}, &models.FindPageOptions{
			Page:    1,
			PerPage: len(courseCUIDs),
		})
		if appErr != nil {
			return nil, appErr
		}

		for _, pubCourse := range pubCourses {
			outline, aErr := Course.FindByIdForOutline(pubCourse.CourseID, false, &models.FindOneOptions{})
			if aErr != nil {
				return nil, aErr
			}

			courseOutlinesByCuids[pubCourse.CourseCuid] = outline
		}
	}

	provinces := req.Provinces
	if len(provinces) == 0 {
		for _, item := range util.DefaultProvinceVN() {
			v, found := item["label"]
			if !found {
				continue
			}

			province, ok := v.(string)
			if !ok {
				continue
			}

			provinces = append(provinces, province)
		}
	}

	referralReportTbl := models.GetTblName(models.OEReferralReportTbl)
	countQuery := fmt.Sprintf(
		`
		WITH learners AS (
			SELECT rp.user_id
			FROM %[1]s rp
       		WHERE rp.campaign_key = @campaignKey
          		AND rp.enroll_date BETWEEN @fromDate AND @toDate %[2]s %[3]s
			GROUP BY rp.user_id
		)
       SELECT COUNT(*) FROM learners
    `,
		referralReportTbl,
		lo.If(len(courseCUIDs) == 0, "").Else("AND rp.course_cuid IN @courseCUIDs"),
		lo.If(len(provinces) == 0, "").Else("AND rp.province IN @provinces"),
	)

	var numOfLearners int
	if err := models.DB.Debug().Raw(countQuery, map[string]interface{}{
		"campaignKey": campaignKey,
		"fromDate":    req.FromDate,
		"toDate":      req.ToDate,
		"courseCUIDs": courseCUIDs,
		"provinces": lo.Map(provinces, func(province string, _ int) string {
			return lo.If(province == util.ProvinceOther, "").Else(province)
		}),
	}).Scan(&numOfLearners).Error; err != nil {
		return nil, e.NewError500(e.ERROR, "Count number of learners by campaign error: "+err.Error())
	}

	if numOfLearners == 0 {
		return &dto.OERefLearnersByCampaignResponse{
			Results:    []*dto.OERefLearner{},
			Pagination: models.NewPagination(page, perPage, numOfLearners),
		}, nil
	}

	userTbl := models.GetTblName(models.UserTbl)
	findQuery := fmt.Sprintf(
		`
		WITH userids AS (
			SELECT user_id
		   FROM %[1]s rp
		   WHERE rp.campaign_key = @campaignKey
			  AND rp.enroll_date BETWEEN @fromDate AND @toDate %[3]s %[4]s
		   GROUP BY rp.user_id
		   ORDER BY MAX(rp.enroll_date) DESC
		   LIMIT @limit
		   OFFSET @offset
		)
        SELECT userids.user_id AS id, u.display_name, u.email, u.phone
		FROM userids
		LEFT JOIN %[2]s u ON userids.user_id = u.id
    `,
		referralReportTbl,
		userTbl,
		lo.If(len(courseCUIDs) == 0, "").Else("AND rp.course_cuid IN @courseCUIDs"),
		lo.If(len(provinces) == 0, "").Else("AND rp.province IN @provinces"),
	)

	var userInfos []*models.UserInfo
	if err := models.DB.Debug().Raw(findQuery, map[string]interface{}{
		"campaignKey": campaignKey,
		"fromDate":    req.FromDate,
		"toDate":      req.ToDate,
		"courseCUIDs": courseCUIDs,
		"provinces": lo.Map(provinces, func(province string, _ int) string {
			return lo.If(province == util.ProvinceOther, "").Else(province)
		}),
		"limit":  perPage,
		"offset": (page - 1) * perPage,
	}).Scan(&userInfos).Error; err != nil {
		return nil, e.NewError500(e.ERROR, "Find learner IDs by campaign error: "+err.Error())
	}

	refReports, err := models.Repository.OEReferralReport(s.ctx).FindMany(&models.OEReferralReportQuery{
		CampaignKey: &campaignKey,
		UserIDIn: lo.Map(userInfos, func(userInfo *models.UserInfo, _ int) string {
			return userInfo.ID
		}),
	}, &models.FindManyOptions{})
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Find learners by campaign error: "+err.Error())
	}

	refReportsByUserIDs := make(map[string][]*models.OEReferralReport)
	for _, rp := range refReports {
		refReportsByUserIDs[rp.UserID] = append(refReportsByUserIDs[rp.UserID], rp)
	}

	var result []*dto.OERefLearner
	for _, userInfo := range userInfos {
		learner := &dto.OERefLearner{
			ID:     userInfo.ID,
			Email:  userInfo.Email,
			Source: models.OERefSourceNone,
		}
		if rps, found := refReportsByUserIDs[userInfo.ID]; found {
			latestFillFormDate := int64(0)
			for _, rp := range rps {
				if rp.FillFormDate > latestFillFormDate {
					if fullName, ok := rp.UserProps["full_name"].(string); ok {
						learner.FullName = fullName
					}

					if job, ok := rp.UserProps["job"].(string); ok {
						learner.Job = job
					}

					if ageGroup, ok := rp.UserProps["age_group"].(string); ok {
						learner.AgeGroup = ageGroup
					}

					if school, ok := rp.UserProps["school"].(string); ok {
						learner.School = school
					}

					learner.Province = rp.Province
					learner.Source = models.OERefSourceForm
					latestFillFormDate = rp.FillFormDate
				}

				course, oFound := courseOutlinesByCuids[rp.CourseCuid]
				if !oFound {
					continue
				}

				learner.Courses = append(learner.Courses, &dto.OERefLearnerCourseItem{
					Name:                     course.Name,
					ActiveSection:            course.ActiveSection,
					EnrollDate:               rp.EnrollDate,
					NumberOfCompletedSection: rp.NumberOfCompletedSection,
					CanClaimCert:             rp.CanClaimCert,
					ClaimCertDate:            rp.ClaimCertDate,
				})
			}
		}
		result = append(result, learner)
	}

	return &dto.OERefLearnersByCampaignResponse{
		Results:    result,
		Pagination: models.NewPagination(page, perPage, numOfLearners),
	}, nil
}
