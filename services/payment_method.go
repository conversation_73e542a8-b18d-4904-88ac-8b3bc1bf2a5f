package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *PaymentMethodService) Create(user *models.User, data *dto.PaymentMethodRequest) (*models.PaymentMethod, *e.AppError) {
	paymentMethod := models.PaymentMethod{
		UserID:        user.ID,
		CourseID:      data.CourseID,
		Service:       data.Service,
		Account:       data.Account,
		AccountNumber: data.AccountNumber,
		AccountName:   data.AccountName,
		Network:       data.Network,
		Enable:        data.Enable,
	}

	if err := models.Repository.PaymentMethod.Create(&paymentMethod, nil); err != nil {
		return nil, e.NewError400(e.Create_payment_method_failed, err.Error())
	}

	return &paymentMethod, nil
}

func (s *PaymentMethodService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.PaymentMethod, *e.AppError) {
	query := &models.PaymentMethodQuery{ID: util.NewString(id), IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil)}

	if paymentMethod, err := models.Repository.PaymentMethod.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Payment_method_not_found, err.Error())
		}
		return nil, e.NewError500(e.Payment_method_find_one_failed, err.Error())
	} else {
		return paymentMethod, nil
	}
}

func (s *PaymentMethodService) FindOne(query *models.PaymentMethodQuery, options *models.FindOneOptions) (*models.PaymentMethod, *e.AppError) {
	if paymentMethod, err := models.Repository.PaymentMethod.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Payment_method_not_found, err.Error())
		}
		return nil, e.NewError500(e.Payment_method_find_one_failed, err.Error())
	} else {
		return paymentMethod, nil
	}
}

func (s *PaymentMethodService) FindPage(query *models.PaymentMethodQuery, options *models.FindPageOptions) ([]*models.PaymentMethod, *models.Pagination, *e.AppError) {
	if paymentMethods, pagination, err := models.Repository.PaymentMethod.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_payment_method_failed, err.Error())
	} else {
		return paymentMethods, pagination, nil
	}

}

func (s *PaymentMethodService) Update(paymentMethod *models.PaymentMethod) *e.AppError {
	if err := models.Repository.PaymentMethod.Update(paymentMethod, nil); err != nil {
		return e.NewError500(e.Update_payment_method_failed, err.Error())
	}
	return nil

}

func (s *PaymentMethodService) Delete(paymentMethod *models.PaymentMethod) *e.AppError {
	if err := models.Repository.PaymentMethod.Delete(paymentMethod.ID, nil); err != nil {
		return e.NewError500(e.Delete_payment_method_failed, err.Error())
	}
	return nil

}

func (r *PaymentMethodService) CanUpdatePaymentMethod(p *models.PaymentMethod, user *models.User) bool {
	// org owner
	if p.UserID == user.ID {
		return true
	}

	// admin, sysadmin, mod
	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Find UserRoleOrg failed: ", err.Error())
		return false
	}

	if len(userRoles) > 0 {
		allow := false
		for _, ur := range userRoles {
			if lo.Contains[string](editRoles, ur.RoleID) {
				allow = true
				break
			}
		}
		return allow
	}
	return false

}
