package services

import (
	"context"
	"errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"
)

var CanUpdateCampaignRoles = []string{
	models.SystemAdminRoleType, models.AdminRoleType, models.ModeratorRoleType, models.OrgAdminRoleType,
}

func (s *AffiliateCampaignService) Create(request *dto.AffiliateCampaignRequest) (*models.AffiliateCampaign, *e.AppError) {
	campaign := &models.AffiliateCampaign{
		UserID:    request.User.ID,
		OrgID:     request.Org.ID,
		Name:      request.Name,
		StartDate: request.StartDate,
		EndDate:   request.EndDate,
		Enable:    request.Enable,
	}
	err := models.Repository.AffiliateCampaign.Create(campaign, nil)
	if err != nil {
		return nil, e.NewError500(e.Affiliate_campaign_create_failed, "create: "+err.Error())
	}

	return campaign, nil
}

func (s *AffiliateCampaignService) Update(campaign *models.AffiliateCampaign, request *dto.AffiliateCampaignRequest) (*models.AffiliateCampaign, *e.AppError) {
	campaign.Name = request.Name
	campaign.StartDate = request.StartDate
	campaign.EndDate = request.EndDate
	campaign.Enable = request.Enable
	if err := models.Repository.AffiliateCampaign.Update(campaign, nil); err != nil {
		return nil, e.NewError500(e.Affiliate_campaign_update_failed, "update: "+err.Error())
	}
	return campaign, nil
}

func (s *AffiliateCampaignService) FindOne(query *models.AffiliateCampaignQuery, options *models.FindOneOptions) (*models.AffiliateCampaign, *e.AppError) {
	if campaign, err := models.Repository.AffiliateCampaign.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Affiliate_campaign_find_not_found, err.Error())
		}
		return nil, e.NewError500(e.Affiliate_campaign_find_failed, err.Error())
	} else {
		org, _ := models.Repository.Organization.FindByID(campaign.OrgID, nil)
		campaign.Org = org
		return campaign, nil
	}
}

func (s *AffiliateCampaignService) FindPage(query *models.AffiliateCampaignQuery, options *models.FindPageOptions) ([]*models.AffiliateCampaign, *models.Pagination, *e.AppError) {
	if campaigns, pagination, err := models.Repository.AffiliateCampaign.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Affiliate_campaign_find_failed, err.Error())
	} else {
		return campaigns, pagination, nil
	}
}

func (s *AffiliateCampaignService) Delete(campaign *models.AffiliateCampaign) *e.AppError {
	// delete course campaign first
	if _, err := models.Repository.CourseCampaign.DeleteMany(&models.CourseCampaignQuery{
		CampaignID: util.NewString(campaign.ID),
	}, nil); err != nil {
		return e.NewError500(e.Affiliate_campaign_delete_failed, "delete course campaign"+err.Error())
	}
	if err := models.Repository.AffiliateCampaign.Delete(campaign.ID, nil); err != nil {
		return e.NewError500(e.Affiliate_campaign_delete_failed, err.Error())
	}
	return nil
}

func (s *AffiliateCampaignService) CanUpdateAffiliateCampaign(user *models.User, campaign *models.AffiliateCampaign) *e.AppError {
	if campaign.UserID == user.ID {
		return nil
	}

	urs, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:User:CanUpdateCourse: Find UserRoleOrg by user_id failed ", err)
		return e.NewError400(e.Course_update_need_permission_or_owner, "find user role failed")
	}

	for _, r := range urs {
		if lo.Contains(CanUpdateCampaignRoles, r.RoleID) {
			return nil
		}
	}

	return e.NewError400(e.Course_update_need_permission_or_owner, "Need admin or course owner for update permission")
}

func (s *AffiliateCampaignService) AddCourses(user *models.User, campaign *models.AffiliateCampaign, request *dto.AffiliateCampaignCourse) *e.AppError {
	if request.CourseCuids == nil || len(request.CourseCuids) == 0 {
		return e.NewError400(e.Affiliate_campaign_add_course_failed, "course_cuids are required")
	}
	for _, cuid := range request.CourseCuids {
		owner, partnerErr := models.Repository.CoursePartner.FindOne(
			&models.CoursePartnerQuery{
				CourseID:     util.NewString(cuid),
				PartnerID:    util.NewString(user.ID),
				IncludeRoles: util.NewT([]string{string(models.CourseRoleOwner)}),
			}, &models.FindOneOptions{})
		if partnerErr != nil && errors.Is(partnerErr, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Affiliate_campaign_add_course_failed, "get course owner: "+partnerErr.Error())
		}
		if owner == nil {
			return e.NewError400(e.Affiliate_campaign_add_course_failed, "course owner required: "+cuid)
		}
		course, cErr := Course.FindOne(&models.CourseQuery{Cuid: util.NewString(cuid), Latest: util.NewBool(true)}, nil)
		if cErr != nil {
			return cErr
		}
		addCourse2Campaign(request.Org, course, campaign)
	}

	return nil
}

func addCourse2Campaign(org *models.Organization, course *models.Course, campaign *models.AffiliateCampaign) *e.AppError {
	existed, ccErr := models.Repository.CourseCampaign.FindOne(
		&models.CourseCampaignQuery{
			CourseCuid: util.NewString(course.Cuid),
			CampaignID: util.NewString(campaign.ID),
		}, nil)
	if ccErr != nil && !errors.Is(ccErr, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Affiliate_campaign_add_course_failed, "add course campaign failed"+ccErr.Error())
	}
	if existed == nil {
		courseCampaign := models.CourseCampaign{
			CampaignID: campaign.ID,
			CourseCuid: course.Cuid,
			UserID:     campaign.UserID,
			OrgID:      campaign.OrgID,
			OrgSchema:  org.Schema,
			Enable:     campaign.Enable,
			StartDate:  campaign.StartDate,
			EndDate:    campaign.EndDate,
			CourseName: course.Name,
			CourseSlug: course.Slug,
		}

		err := models.Repository.CourseCampaign.Create(&courseCampaign, nil)
		if err != nil {
			return e.NewError500(e.Affiliate_campaign_add_course_failed, "add course campaign failed"+err.Error())
		}
	} else {
		existed.Enable = campaign.Enable
		existed.StartDate = campaign.StartDate
		existed.EndDate = campaign.EndDate
		existed.CourseName = course.Name
		existed.CourseSlug = course.Slug
		err := models.Repository.CourseCampaign.Update(existed, nil)
		if err != nil {
			return e.NewError500(e.Affiliate_campaign_add_course_failed, "update course campaign failed"+err.Error())
		}
	}

	return nil
}

func (s *AffiliateCampaignService) RemoveCourses(request *dto.DefaultDeleteManyRequest) *e.AppError {
	if request.IDs == nil || len(request.IDs) == 0 {
		return e.NewError400(e.Affiliate_campaign_remove_courses_failed, "ids are required")
	}

	for _, id := range request.IDs {
		if err := models.Repository.CourseCampaign.Delete(id, nil); err != nil {
			return e.NewError500(e.Affiliate_campaign_remove_courses_failed, "delete failed"+err.Error())
		}
	}
	return nil
}

func (s *AffiliateCampaignService) FindCourseByCampaign(
	request *dto.FindCourseCampaignRequest,
	query *models.CourseCampaignQuery,
	options *models.FindPageOptions,
) ([]*models.CourseCampaign, *models.Pagination, *e.AppError) {
	ccs, pagination, err := models.Repository.CourseCampaign.FindPage(&models.CourseCampaignQuery{
		CampaignID: util.NewString(request.Campaign.ID),
	}, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Affiliate_campaign_find_course_failed, "FindCourseByCampaign: find ccs "+err.Error())
	}

	if len(ccs) > 0 {
		//isRoot := request.Org.Domain == setting.AppSetting.BaseDomain
		cuids := lo.Map(ccs, func(item *models.CourseCampaign, _ int) string {
			return item.CourseCuid
		})
		courses, _, cErr := models.Repository.Course(context.TODO()).FindPage(&models.CourseQuery{
			CuidIn: cuids,
			Latest: util.NewBool(true),
		}, &models.FindPageOptions{PerPage: util.PerPageMax})
		if cErr != nil {
			return nil, nil, e.NewError500(e.Affiliate_campaign_find_course_failed, "FindCourseByCampaign: find courses "+cErr.Error())
		}
		for _, cs := range ccs {
			c, ok := lo.Find(courses, func(item *models.Course) bool {
				return item.Cuid == cs.CourseCuid
			})
			if ok {
				cs.Course = c
			}
		}
	}

	return ccs, pagination, nil
}

func (s *AffiliateCampaignService) FindPublishCourseByCampaign(
	campaign *models.AffiliateCampaign,
	pubCourseQuery *models.PublishCourseQuery,
	options *models.FindPageOptions,
) ([]*models.CourseCampaign, *models.Pagination, *e.AppError) {
	ccs, pagination, err := models.Repository.CourseCampaign.FindPage(&models.CourseCampaignQuery{
		CampaignID: util.NewString(campaign.ID),
	}, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Affiliate_campaign_find_course_failed, "FindCourseByCampaign: find ccs "+err.Error())
	}
	cuids := lo.Map(ccs, func(item *models.CourseCampaign, _ int) string {
		return item.CourseCuid
	})
	if pubCourseQuery == nil {
		pubCourseQuery = &models.PublishCourseQuery{}
	}
	pubCourseQuery.CourseCuidIn = cuids
	//pubCourseQuery := models.PublishCourseQuery{
	//	CourseCuidIn: cuids,
	//}
	pubOptions := models.FindPageOptions{
		PerPage:  util.PerPageMax,
		Preloads: []string{models.CategoriesField, models.UserField},
	}
	pubCourses, _, err := models.Repository.PublishCourse(context.TODO()).FindPage(pubCourseQuery, &pubOptions)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
	}

	lo.ForEach(ccs, func(item *models.CourseCampaign, index int) {
		c, ok := lo.Find(pubCourses, func(pub *models.PublishCourse) bool {
			return pub.CourseCuid == item.CourseCuid
		})
		if ok {
			item.PublishCourse = c.ToSimplePublishCourse()
		}
	})

	return ccs, pagination, nil
}

func (s *AffiliateCampaignService) FindCampaignByUser(
	userID string,
	query *models.UserCamapaignQuery,
	options *models.FindPageOptions,
) ([]*models.UserCampaign, *models.Pagination, *e.AppError) {
	if campaigns, pagination, err := models.Repository.AffiliateCampaign.GetCampaignByUser(userID, query, options); err != nil {
		return nil, nil, e.NewError500(e.Affiliate_campaign_find_failed, "FindCampaignByUser"+err.Error())
	} else {
		if len(campaigns) > 0 {
			ids := lo.Map(campaigns, func(item *models.UserCampaign, _ int) string {
				return item.ID
			})
			ids = lo.Uniq(ids)
			commissions, comErr := models.Repository.Commission.FindMany(&models.CommissionQuery{
				CampaignIDIn: ids,
				Enable:       util.NewBool(true),
			}, nil)
			if comErr != nil {
				return nil, nil, e.NewError500(e.Affiliate_campaign_find_failed, "FindCommissionByCampaigns failed: "+comErr.Error())
			}

			if len(commissions) > 0 {
				// group commission by campaign
				commissionByCampaign := lo.Reduce(commissions, func(agg map[string][]*models.Commission, curr *models.Commission, _ int) map[string][]*models.Commission {
					coms := agg[curr.CampaignID]
					if coms == nil {
						coms = []*models.Commission{}
					}
					coms = append(coms, curr)
					agg[curr.CampaignID] = coms
					return agg
				}, map[string][]*models.Commission{})

				lo.ForEach(campaigns, func(campaign *models.UserCampaign, _ int) {
					comms := commissionByCampaign[campaign.ID]
					baseRate, _ := lo.Find(comms, func(comm *models.Commission) bool {
						return comm.IsBaseRate == true && comm.ParentID == ""
					})
					// base rate value
					if baseRate != nil {
						campaign.BaseRateMin = baseRate.Ref1Rate
						campaign.BaseRateMax = baseRate.Ref1Rate
						campaign.BaseRateRef2 = baseRate.Ref2Rate
						// base rate highest bonus
						baseRateChilds := lo.Filter(comms, func(comm *models.Commission, _ int) bool {
							return comm.ParentID == baseRate.ID
						})
						if len(baseRateChilds) > 0 {
							childMax := lo.MaxBy(baseRateChilds, func(a *models.Commission, b *models.Commission) bool {
								return a.Ref1Rate > b.Ref1Rate
							})
							campaign.BaseRateMax = campaign.BaseRateMin + childMax.Ref1Rate
						}
					}

					// learners
					learner, _ := lo.Find(comms, func(comm *models.Commission) bool {
						return lo.Contains(comm.ReferrerTypes, string(models.ReferrerTypePurchasedUser)) && comm.ParentID == ""
					})
					if learner != nil {
						campaign.LearnerRateMin = learner.Ref1Rate
						campaign.LearnerRateMax = learner.Ref1Rate
						learnerChilds := lo.Filter(comms, func(comm *models.Commission, _ int) bool {
							return comm.ParentID == learner.ID
						})
						if len(learnerChilds) > 0 {
							childMax := lo.MaxBy(learnerChilds, func(a *models.Commission, b *models.Commission) bool {
								return a.Ref1Rate > b.Ref1Rate
							})
							campaign.LearnerRateMax = campaign.LearnerRateMax + childMax.Ref1Rate
						}
					}

					// premium
					premiums := lo.Filter(comms, func(comm *models.Commission, _ int) bool {
						return comm.ParentID == "" && !comm.IsBaseRate && !lo.Contains(comm.ReferrerTypes, string(models.ReferrerTypePurchasedUser))
					})
					if len(premiums) > 0 {
						//PremiumRateMin: min ref1 rate
						//PremiumRateMax: max ref1 rate + max bonus rate
						//PremiumShareRateMax: max share rate
						max := lo.MaxBy(premiums, func(a *models.Commission, b *models.Commission) bool {
							return a.Ref1Rate > b.Ref1Rate
						})
						min := lo.MaxBy(premiums, func(a *models.Commission, b *models.Commission) bool {
							return a.Ref1Rate < b.Ref1Rate
						})
						maxShare, _ := models.Repository.ReferralLink.MaxShareRateByCampaign(campaign.ID)
						parentIds := lo.Map(premiums, func(item *models.Commission, _ int) string {
							return item.ID
						})
						campaign.PremiumRateMin = min.Ref1Rate
						campaign.PremiumRateMax = max.Ref1Rate
						campaign.PremiumShareRateMax = maxShare
						premiumChild := lo.Filter(comms, func(comm *models.Commission, _ int) bool {
							return comm.ParentID != "" && lo.Contains(parentIds, comm.ParentID)
						})
						if len(premiumChild) > 0 {
							maxPremiumChild := lo.MaxBy(premiumChild, func(a *models.Commission, b *models.Commission) bool {
								return a.Ref1Rate > b.Ref1Rate
							})
							campaign.PremiumRateMax = campaign.PremiumRateMax + maxPremiumChild.Ref1Rate
						}
					}
				})
			}

		}
		return campaigns, pagination, nil
	}
}

func (s *AffiliateCampaignService) GetAvailableCampaign(id string) (*models.AffiliateCampaign, *e.AppError) {
	campaign, camErr := AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if camErr != nil {
		return nil, camErr
	}
	if !campaign.Enable {
		return nil, e.NewError400(e.Affiliate_campaign_disabled, "campaign disabled")
	}
	now := time.Now().UnixMilli()
	if campaign.StartDate > 0 && campaign.StartDate > int(now) {
		return nil, e.NewError400(e.Affiliate_campaign_not_active_time, "StartDate <= now")
	}
	if campaign.EndDate > 0 && int(now) > campaign.EndDate {
		return nil, e.NewError400(e.Affiliate_campaign_not_active_time, "now >= EndDate")
	}
	return campaign, nil
}
