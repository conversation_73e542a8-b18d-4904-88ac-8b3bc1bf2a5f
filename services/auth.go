package services

import (
	"context"
	"errors"
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/email_normalizer"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"sync"
	"time"

	"github.com/samber/lo"

	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"
)

type Claims struct {
	Sub         string `json:"sub"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	Url         string `json:"url"`
	OTPVerified bool   `json:"otp_verified"`
	jwt.RegisteredClaims
}

var timeUnit = time.Hour

func generateToken(user *models.User, url string, otpVerified bool) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(timeUnit * util.JwtAccessExpireIn)

	claims := Claims{
		user.ID,
		user.Username,
		user.Email,
		url,
		otpVerified,
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			IssuedAt:  jwt.NewNumericDate(nowTime),
			Issuer:    setting.AppSetting.BaseDomain,
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(util.JwtAccessSecret)

	return token, err
}

// ParseAccessToken parsing token
func (s *AuthService) ParseAccessToken(token string) (*Claims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return util.JwtAccessSecret, nil
	})

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}

func (s *AuthService) Authentication(params *dto.AuthParams) (*dto.AuthResponse, *e.AppError) {
	userRoles, rErr := models.Repository.UserRoleOrg.FindByUserId(params.User.ID)
	if setting.IsSysAdminSite(params.OriginUrl) {
		var isAdmin = false
		if rErr != nil {
			return nil, e.NewError500(e.Find_user_role_failed, "Find user role failed")
		}

		for _, us := range userRoles {
			if models.IsSysAdminRole(us.RoleID) {
				isAdmin = true
			}
		}
		if !isAdmin {
			return nil, e.NewError403(e.FORBIDDEN, "User don't have permission to login this site")
		}
	}

	if lo.ContainsBy(userRoles, func(item *models.UserRoleOrg) bool {
		return item.RoleID == models.OrgAdminRoleType || item.RoleID == models.OrgModeratorRoleType || item.RoleID == models.AdminRoleType || item.RoleID == models.ModeratorRoleType

	}) {
		if err := models.Repository.User.UpdateRoles(&models.UserRoleOrg{
			UserID: params.User.ID,
			RoleID: models.OrgEditorRoleType,
			OrgID:  params.Org.ID,
		}); err != nil {
			return nil, e.NewError500(e.Error_auth_add_user_role_failed, "Adding editor role failed: "+err.Error())
		}
	}

	orgID := ""
	if params.Org != nil {
		orgID = params.Org.ID
		// If admin/mod/instructor from other orgs --> will be a user of current org
		if lo.ContainsBy(userRoles, func(item *models.UserRoleOrg) bool {
			return item.RoleID != models.LearnerRoleType
		}) {
			if err := models.Repository.User.UpdateRoles(&models.UserRoleOrg{
				UserID: params.User.ID,
				RoleID: models.LearnerRoleType,
				OrgID:  orgID,
			}); err != nil {
				return nil, e.NewError500(e.Error_auth_add_user_role_failed, "Adding learner role failed: "+err.Error())
			}
		}
	}

	url := setting.AppSetting.BaseDomain
	if setting.IsSysAdminSite(params.OriginUrl) {
		url = setting.AppSetting.AdminHost[0]
	}
	// issue access_token + refresh_token
	accessToken, err := generateToken(params.User, url, params.OTPVerified)
	if err != nil {
		return nil, e.NewError500(e.Error_auth_token, err.Error())
	}

	nowTime := time.Now()
	expireTime := nowTime.Add(timeUnit * util.JwtRefreshExpireIn)

	session := models.Session{
		OrgId:    orgID,
		Url:      params.OriginUrl,
		UserId:   params.User.ID,
		Token:    util.GenerateToken(),
		ExpireAt: expireTime.UnixMilli(),
		Props: models.JSONB{
			"accessToken": accessToken,
		},
		OTPVerified: params.OTPVerified,
	}

	if params.UserAgent != nil {
		session.UserAgent = *params.UserAgent
	}

	saveErr := Session.Upsert(&session)
	if saveErr != nil {
		return nil, saveErr
	}

	return &dto.AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: session.Token,
		OTPVerified:  params.OTPVerified,
	}, nil
}

func (s *AuthService) RefreshToken(params *dto.RefreshTokenParams, session *models.Session) (*dto.AuthResponse, int) {
	if !util.IsSameDomain(params.OriginUrl, session.Url) {
		return nil, e.Error_auth_invalid_session_domain_miss_match
	}

	nowTime := time.Now()
	if nowTime.UnixMilli() >= session.ExpireAt {
		return nil, e.Error_auth_session_expire
	}

	user, _ := models.Repository.User.FindByID(session.UserId)
	if user == nil {
		return nil, e.Error_auth_session_user_not_found
	}

	// issue access_token + refresh_token
	url := setting.AppSetting.BaseDomain
	if setting.IsSysAdminSite(params.OriginUrl) {
		url = setting.AppSetting.AdminHost[0]
	}
	accessToken, err := generateToken(user, url, session.OTPVerified)
	if err != nil {
		return nil, e.Error_auth_session_make_token_failed
	}

	expireTime := nowTime.Add(timeUnit * util.JwtRefreshExpireIn)
	session.Token = util.GenerateToken()
	session.ExpireAt = expireTime.UnixMilli()
	session.Props = models.JSONB{
		"accessToken": accessToken,
	}

	saved, saveErr := models.Repository.Session.Update(session)
	if saveErr != nil {
		return nil, e.ERROR
	}

	return &dto.AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: saved.Token,
	}, e.SUCCESS
}

func SocialSignupWhenSocialBelongToUser(role *models.Role, org *models.Organization, snsAccount *models.SnsAccount) (*models.User, error) {
	var user *models.User
	sUser, suErr := models.Repository.User.FindByID(snsAccount.UserID)
	if suErr != nil {
		return nil, suErr
	}
	user = sUser

	userRoleOrg := models.UserRoleOrg{
		UserID: user.ID,
		RoleID: role.ID,
		OrgID:  org.ID,
	}
	if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
		return nil, rErr
	}

	if user.Active == false {
		user.Active = true
		if updatedUser, uaErr := models.Repository.User.Update(user); uaErr != nil {
			log.Error("Update active User failed: ", uaErr)
		} else {
			user = updatedUser
		}
	}

	return user, nil
}

func (s *AuthService) SocialSignUp(snsAccount *models.SnsAccount, org *models.Organization) (*models.User, error) {
	role, rErr := getDefaultRoleByDomain()
	if rErr != nil {
		return nil, rErr
	}

	if snsAccount.UserID != "" {
		return SocialSignupWhenSocialBelongToUser(role, org, snsAccount)
	} else {
		var user *models.User
		userByEmail, err := models.Repository.User.FindByEmailWithOpts(snsAccount.Email, nil)
		if err != nil {
			log.Error("SocialSignUp.FindByEmail", err)
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}

		user = userByEmail

		if user == nil {
			userData := &models.User{
				Email:       snsAccount.Email,
				Username:    util.GenerateId(),
				DisplayName: snsAccount.DisplayName,
				Avatar:      snsAccount.Avatar,
				Active:      true,
			}

			if _, uErr := User.Create(userData); uErr != nil {
				return nil, errors.New(uErr.Msg)
			}

			userRoleOrg := models.UserRoleOrg{
				UserID: userData.ID,
				RoleID: models.LearnerRoleType,
				OrgID:  org.ID,
			}
			if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
				return nil, rErr
			}

			// if create new user success set sns account is default
			snsAccount.IsDefault = true
			snsAccount.UserID = userData.ID
			_, usErr := models.Repository.SnsAccount.Update(snsAccount, nil)
			if usErr != nil {
				log.Error("Create User failed: ", usErr)
				return nil, usErr
			}

			user = userData
		}

		if snsAccount.UserID == "" {
			snsAccount.UserID = user.ID
			_, uSerr := models.Repository.SnsAccount.Update(snsAccount, nil)
			if uSerr != nil {
				return nil, uSerr
			}
		}

		if user.Active == false {
			user.Active = true
			if updatedUser, uaErr := models.Repository.User.Update(user); uaErr != nil {
				log.Error("Update active User failed: ", uaErr)
			} else {
				user = updatedUser
			}
		}

		return user, nil
	}
}

func (s *AuthService) normalizeAndValidateEmail(email string) (string, *e.AppError) {
	// Check if email is disposable domain
	if models.GetConfig[bool](models.BlockDisposableEmailsEnabled) {
		disposableDomainMap := models.GetConfig[map[string]struct{}](models.DisposableEmailDomains)
		if util.IsDisposableEmail(email, disposableDomainMap) {
			return "", e.NewError400(e.Email_disposable_domain_not_allowed, "Disposable email is not allowed")
		}
	}

	// Normalize email according to email domain rules
	return email_normalizer.NormalizeEmail(email), nil
}

func (s *AuthService) Register(params *dto.RegisterParams) (*dto.RegisterResponse, *e.AppError) {
	var errCode = e.SUCCESS
	var userRole *models.Role
	usernameRandom := util.GenerateId()
	var wg sync.WaitGroup
	var statusCode int
	var normalizedEmail string
	var conflictedEmail string
	wg.Add(3)
	// Check username and email are available to register
	go func() {
		defer wg.Done()

		if ok, cErr := models.Repository.User.CanRegister(usernameRandom, params.Email); cErr != nil {
			errCode = e.Error_auth_verify_username_email_failed
			statusCode = http.StatusInternalServerError
			return
		} else if !ok {
			errCode = e.Error_auth_email_already_used
			statusCode = http.StatusBadRequest
			return
		}

		if models.GetConfig[bool](models.NormalizeEmailEnabled) {
			var appErr *e.AppError
			normalizedEmail, appErr = s.normalizeAndValidateEmail(params.Email)
			if appErr != nil {
				errCode = appErr.ErrCode
				statusCode = appErr.StatusCode
				return
			}

			existingUser, err := models.Repository.User.FindOne(&models.UserQuery{
				NormalizedEmail: &normalizedEmail,
			}, &models.FindOneOptions{
				Sort: []string{models.CreateAtDESC},
			})
			if err != nil && !models.IsRecordNotFound(err) {
				errCode = e.Error_auth_verify_normalized_email_failed
				statusCode = http.StatusInternalServerError
				return
			}

			if existingUser != nil {
				errCode = e.Error_auth_existing_normalized_email
				statusCode = http.StatusBadRequest
				conflictedEmail = existingUser.Email
				return
			}
		}
	}()

	go func() {
		var err error
		defer wg.Done()
		userRole, err = models.Repository.Role.FindById(models.LearnerRoleType)
		if err != nil {
			errCode = e.Error_auth_get_default_role_failed
			statusCode = http.StatusInternalServerError

		}
	}()

	go func() {
		defer wg.Done()
		if ok, cErr := models.Repository.UserSetting.CanUseUsername(usernameRandom, nil); cErr != nil {
			errCode = e.Check_old_username_failed
			statusCode = http.StatusInternalServerError
			return
		} else if !ok {
			errCode = e.Error_username_is_existed
			statusCode = http.StatusBadRequest
			return
		}
	}()

	wg.Wait()
	log.Debug(errCode)
	if errCode != e.SUCCESS {
		return &dto.RegisterResponse{
			NormalizedEmail: normalizedEmail,
			ConflictedEmail: conflictedEmail,
		}, e.NewError(errCode, statusCode, e.MsgFlags[errCode])
	}

	// Create user with active = false
	user := &models.User{
		Username:        usernameRandom,
		DisplayName:     params.DisplayName,
		Email:           params.Email,
		NormalizedEmail: normalizedEmail,
		Password:        params.Password,
		Active:          false,
		Props: models.UserSettingsOption{
			RefCode: params.RefCode,
		},
	}

	if _, uErr := User.Create(user); uErr != nil {
		return &dto.RegisterResponse{
			NormalizedEmail: normalizedEmail,
			ConflictedEmail: conflictedEmail,
		}, uErr
	}

	orgID := ""
	if params.Org != nil {
		orgID = params.Org.ID
	}

	userRoleOrg := models.UserRoleOrg{
		UserID: user.ID,
		RoleID: userRole.ID,
		OrgID:  orgID,
	}

	if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
		return &dto.RegisterResponse{
			NormalizedEmail: normalizedEmail,
			ConflictedEmail: conflictedEmail,
		}, e.NewError500(e.Error_auth_add_user_role_failed, rErr.Error())
	}

	// issue access_token + refresh_token
	accessToken, gErr := generateToken(user, params.OriginUrl, true)
	if gErr != nil {
		return &dto.RegisterResponse{
			NormalizedEmail: normalizedEmail,
			ConflictedEmail: conflictedEmail,
		}, e.NewError500(e.Error_auth_token, gErr.Error())
	}

	nowTime := time.Now()
	expireTime := nowTime.Add(timeUnit * util.JwtRefreshExpireIn)

	session := models.Session{
		OrgId:    orgID,
		Url:      params.OriginUrl,
		UserId:   user.ID,
		Token:    util.GenerateToken(),
		ExpireAt: expireTime.UnixMilli(),
		Props: models.JSONB{
			"accessToken": accessToken,
		},
	}

	saveErr := Session.Upsert(&session)
	if saveErr != nil {
		return &dto.RegisterResponse{
			NormalizedEmail: normalizedEmail,
			ConflictedEmail: conflictedEmail,
		}, saveErr
	}

	// Create userToken record
	token := util.GenerateToken()
	userToken := &models.UserToken{
		UserID:     &user.ID,
		Email:      user.Email,
		User:       user,
		Token:      token,
		ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		Event:      models.EventRegister,
		SendEmail:  util.GetCurrentTime(),
	}

	if err := models.Repository.UserToken.Create(userToken, nil); err != nil {
		return &dto.RegisterResponse{
			NormalizedEmail: normalizedEmail,
			ConflictedEmail: conflictedEmail,
		}, e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	// send mail
	mailParams := commdto.MapEmailParams{
		commdto.EmailParamUserToken: userToken,
	}

	if v, ok := params.AllowFieldsData[util.NextPathField]; ok {
		mailParams[commdto.EmailParamNextPath] = v
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.ErrorWithAlertf("Recovered in Service::Register", r)
			}
		}()

		if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         params.Org.IntoComm(),
			Event:       commdto.EventRegister,
			ExtendDatas: mailParams,
			IsQueue:     true,
			From:        params.Org.Settings.SenderEmail,
		}); eErr != nil {
			log.ErrorWithAlertf("AuthService.Register::Send email failed: %v", eErr)
		}
		//TODO: push notification when user register
		req := &commdto.PushNotificationRequest{
			Code: commdto.CodeUserRegister,
			RuleTree: &commdto.TreeNodeRequest{
				Rule: &commdto.Rule{
					Subject:   commdto.NotiUser,
					Verb:      commdto.IsIn,
					ObjectIDs: []string{user.ID},
				},
			},
			Org: params.Org.IntoComm(),
		}
		if err := communication.Notification.PushNotification(req); err != nil {

			log.Errorf("Push notification after register error: %v", err)
		}

	}()

	return &dto.RegisterResponse{
		AccessToken:  accessToken,
		RefreshToken: session.Token,
	}, nil
}

func (s *AuthService) ResendMail(org *models.Organization, params *dto.ResendMailPrams) *e.AppError {

	// Get latest user token
	latestToken, qErr := models.Repository.UserToken.FindOne(&models.UserTokenQuery{
		Event: util.NewT(params.Event),
		Email: &params.Email,
	}, &models.FindOneOptions{Preloads: []string{"User"}, Sort: []string{"update_at DESC"}})

	if qErr != nil {
		if !errors.Is(qErr, gorm.ErrRecordNotFound) {
			// return e.NewError400(e.Error_user_token_not_found, qErr.Error())
			return e.NewError500(e.Error_user_token_find_failed, qErr.Error())
		}
	}

	var user *models.User

	// if Latest token = nil -> find user with email
	if latestToken == nil || latestToken.User == nil {
		foundedUser, err := models.Repository.User.FindByEmailWithOpts(params.Email, nil)
		if err != nil {
			return e.NewError400(e.Error_user_not_found, err.Error())
		}
		user = foundedUser
	} else {
		user = latestToken.User
	}

	if latestToken != nil {
		// check only can send after 1 minutes
		if !util.CheckTimeIsOver(int(latestToken.UpdateAt), setting.EmailSetting.ResendAfter) {
			return e.NewError(e.Error_token_not_ready, http.StatusUnprocessableEntity, e.MsgFlags[e.Error_token_not_ready])
		}
	}

	// Create userToken record
	token := util.GenerateToken()
	userToken := &models.UserToken{
		UserID:     &user.ID,
		Email:      user.Email,
		User:       user,
		Token:      token,
		ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		Event:      params.Event,
		SendEmail:  util.GetCurrentTime(),
	}

	if err := models.Repository.UserToken.Create(userToken, nil); err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	// build verify link and send mail
	mailParams := commdto.MapEmailParams{
		commdto.EmailParamUserToken: userToken,
	}

	if v, ok := params.AllowFieldsData[util.NextPathField]; ok {
		mailParams[commdto.EmailParamNextPath] = v
	}

	go func() {
		if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       userToken.Event.IntoComm(),
			ExtendDatas: mailParams,
			IsQueue:     true,
			From:        org.Settings.SenderEmail,
		}); eErr != nil {
			log.ErrorWithAlertf("AuthService.ResendMail::Send email failed: %v", eErr)
		}
	}()
	return nil
}

func (s *AuthService) Verify(params *dto.VerifyUserParams) (*dto.VerifyUserResponse, *models.User, *e.AppError) {

	// Find UserTokens and Check if token valid
	userToken, qErr := models.Repository.UserToken.FindOne(&models.UserTokenQuery{
		Token: &params.Token,
		Event: (*models.EventType)(util.NewString(string(models.EventRegister))),
	}, nil)

	if qErr != nil {
		if errors.Is(qErr, gorm.ErrRecordNotFound) {
			return nil, nil, e.NewError400(e.Error_user_token_not_found, qErr.Error())
		}
		return nil, nil, e.NewError500(e.Error_user_token_find_failed, qErr.Error())

	}

	// Check token valid
	if userToken.VerifyDate > 0 {
		return nil, nil, e.NewError400(e.Error_token_already_used, e.MsgFlags[e.Error_token_already_used])
	}

	now := util.GetCurrentTime()
	if now > int(userToken.ExpiryTime) {
		return nil, nil, e.NewError400(e.Error_user_token_expired, e.MsgFlags[e.Error_user_token_expired])
	}

	// Update user -> active = true
	user, err := models.Repository.User.FindByID(*userToken.UserID)
	if err != nil {
		return nil, nil, e.NewError400(e.Error_user_not_found, err.Error())
	}

	user.Active = true
	if _, err := models.Repository.User.Update(user); err != nil {
		return nil, nil, e.NewError500(e.Error_user_update_failed, err.Error())
	}

	// Update User tokens record
	userToken.VerifyDate = now
	if err := models.Repository.UserToken.Update(userToken, nil); err != nil {
		return nil, nil, e.NewError500(e.Error_update_user_token_failed, err.Error())
	}

	// Find organization Create new access token
	requestAuth := &dto.AuthParams{
		User:        user,
		Org:         params.Org,
		OriginUrl:   params.OriginUrl,
		OTPVerified: true,
	}

	authToken, aErr := Auth.Authentication(requestAuth)
	if aErr != nil {
		return nil, nil, e.NewError500(e.Error_auth_token, aErr.Msg)
	}

	if user.Props.RefCode != "" {
		go func() {
			if refErr := OEReferral(context.Background()).RefNewUserProgram(user.Props.RefCode, user); refErr != nil {
				log.ErrorWithAlertf("AuthService.Verify::RefNewUserProgram failed: %v", refErr)
			}
		}()
	}

	return &dto.VerifyUserResponse{
		AccessToken:  authToken.AccessToken,
		RefreshToken: authToken.RefreshToken,
	}, user, nil
}

func (s *AuthService) ForgotPassword(org *models.Organization, email string, allowFieldsData map[string]interface{}) *e.AppError {

	// Get latest user token	var preloadsArgs []*map[string]interface{}
	latestToken, qErr := models.Repository.UserToken.FindOne(&models.UserTokenQuery{
		Event: (*models.EventType)(util.NewString(string(models.EventResetPassword))),
		Email: &email,
	}, &models.FindOneOptions{Preloads: []string{"User"}, Sort: []string{"update_at DESC"}})

	if qErr != nil {
		if !errors.Is(qErr, gorm.ErrRecordNotFound) {
			return e.NewError500(e.Error_user_token_find_failed, qErr.Error())
		}
	}

	var user *models.User

	// if Latest token = nil -> find user with email
	if latestToken == nil || latestToken.User == nil {
		foundedUser, err := models.Repository.User.FindByEmailWithOpts(email, nil)
		if err != nil {
			return e.NewError400(e.Error_user_not_found, err.Error())

		}
		user = foundedUser
	} else {
		user = latestToken.User

	}

	if latestToken != nil {
		// check only can send after 1 minutes
		if !util.CheckTimeIsOver(latestToken.UpdateAt, setting.EmailSetting.ResendAfter) {
			return e.NewError(e.Error_token_not_ready, http.StatusUnprocessableEntity, e.MsgFlags[e.Error_token_not_ready])
		}
	}

	// Create userToken record
	token := util.GenerateToken()
	userToken := &models.UserToken{
		UserID:     &user.ID,
		Email:      user.Email,
		User:       user,
		Token:      token,
		ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		Event:      models.EventResetPassword,
		SendEmail:  util.GetCurrentTime(),
	}

	if err := models.Repository.UserToken.Create(userToken, nil); err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	// build verify link and send mail
	mailParams := commdto.MapEmailParams{
		commdto.EmailParamUserToken: userToken,
	}

	if v, ok := allowFieldsData[util.NextPathField]; ok {
		mailParams[commdto.EmailParamNextPath] = v
	}

	go func() {
		if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       commdto.EventResetPassword,
			ExtendDatas: mailParams,
			IsQueue:     true,
			From:        org.Settings.SenderEmail,
		}); eErr != nil {
			log.ErrorWithAlertf("Auth.ForgotPassword::Send email failed: %v", eErr)
		}
	}()
	return nil
}

func (s *AuthService) ConfirmResetPassword(params *dto.ConfirmResetParams) (*dto.ConfirmResetResponse, *e.AppError) {

	// Find UserTokens and Check if token valid
	userToken, qErr := models.Repository.UserToken.FindOne(&models.UserTokenQuery{
		Token: &params.Token,
		Email: &params.Email,
		Event: (*models.EventType)(util.NewString(string(models.EventResetPassword))),
	}, &models.FindOneOptions{Preloads: []string{"User"}})

	if qErr != nil {
		if errors.Is(qErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Error_user_token_not_found, qErr.Error())
		}
		return nil, e.NewError500(e.Error_user_token_find_failed, qErr.Error())

	}

	// Check token valid
	if userToken.VerifyDate > 0 {
		return nil, e.NewError400(e.Error_token_already_used, e.MsgFlags[e.Error_token_already_used])
	}

	now := util.GetCurrentTime()
	if now > int(userToken.ExpiryTime) {
		return nil, e.NewError400(e.Error_user_token_expired, e.MsgFlags[e.Error_user_token_expired])
	}

	// Update Users -> password
	user, err := models.Repository.User.FindByID(*userToken.UserID)
	if err != nil {
		return nil, e.NewError400(e.Error_user_not_found, err.Error())
	}

	user.Password = params.Password
	if _, err := models.Repository.User.UpdatePassword(user); err != nil {
		return nil, e.NewError500(e.Error_user_update_failed, err.Error())
	}

	user.Active = true
	if _, err := models.Repository.User.Update(user); err != nil {
		return nil, e.NewError500(e.Error_user_update_failed, err.Error())
	}

	// Update UserTokens -> verify_date
	userToken.VerifyDate = now
	if err := models.Repository.UserToken.Update(userToken, nil); err != nil {
		return nil, e.NewError500(e.Error_update_user_token_failed, err.Error())
	}

	// Find organization Create new access token
	requestAuth := &dto.AuthParams{
		User:        user,
		Org:         params.Org,
		OriginUrl:   params.OriginUrl,
		OTPVerified: true,
	}

	authToken, aErr := Auth.Authentication(requestAuth)
	if aErr != nil {
		return nil, e.NewError500(e.Error_auth_token, aErr.Msg)
	}

	return &dto.ConfirmResetResponse{
		AccessToken:  authToken.AccessToken,
		RefreshToken: authToken.RefreshToken,
	}, nil
}

func (s *AuthService) ExternalRegister(org *models.Organization, params *dto.ExternalRegisterParams) *e.AppError {

	// Check email can register
	existedUser, err := models.Repository.User.FindByEmailWithOpts(params.Email, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Error_user_find_failed, err.Error())
	}

	if existedUser != nil {
		return e.NewError400(e.Error_auth_email_existed_in_another_user, e.MsgFlags[e.Error_auth_email_existed_in_another_user])
	}

	// Create new User with active = false
	user := &models.User{
		Email:    params.Email,
		Username: util.UsernameWithSuffix("external", 5),
		Active:   false,
	}

	if _, uErr := User.Create(user); uErr != nil {
		return uErr
	}

	// Create userToken record
	token := util.GenerateToken()
	userToken := &models.UserToken{
		UserID:     &user.ID,
		Email:      user.Email,
		User:       user,
		Token:      token,
		ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		Event:      models.EventExternalRegister,
		SendEmail:  util.GetCurrentTime(),
	}

	if err = models.Repository.UserToken.Create(userToken, nil); err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	// build verify link and send mail
	mailParams := commdto.MapEmailParams{
		commdto.EmailParamUserToken: userToken,
	}

	if v, ok := params.AllowFieldsData[util.NextPathField]; ok {
		mailParams[commdto.EmailParamNextPath] = v
	}

	go func() {
		if _, err = communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       commdto.EventExternalRegister,
			ExtendDatas: mailParams,
			IsQueue:     true,
		}); err != nil {
			log.ErrorWithAlertf("Auth.ExternalRegister::Send email failed: %v", err)
		}
	}()
	return nil
}

func (s *AuthService) SetPassword(params *dto.SetPasswordParams) (*dto.SetPasswordResponse, *e.AppError) {

	// Find UserTokens and Check if token valid
	userToken, qErr := models.Repository.UserToken.FindOne(&models.UserTokenQuery{
		Token: &params.Token,
		Email: &params.Email,
		Event: (*models.EventType)(util.NewString(string(models.EventExternalRegister))),
	}, &models.FindOneOptions{Preloads: []string{"User"}})

	if qErr != nil {
		if errors.Is(qErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Error_user_token_not_found, qErr.Error())
		}
		return nil, e.NewError500(e.Error_user_token_find_failed, qErr.Error())
	}

	// Check token valid
	if userToken.VerifyDate > 0 {
		return nil, e.NewError400(e.Error_token_already_used, e.MsgFlags[e.Error_token_already_used])
	}

	now := util.GetCurrentTime()
	if now > int(userToken.ExpiryTime) {
		return nil, e.NewError400(e.Error_user_token_expired, e.MsgFlags[e.Error_user_token_expired])
	}

	// Update Users -> password
	user, err := models.Repository.User.FindByID(*userToken.UserID)
	if err != nil {
		return nil, e.NewError400(e.Error_user_not_found, err.Error())
	}

	user.Password = params.Password
	if _, err := models.Repository.User.UpdatePassword(user); err != nil {
		return nil, e.NewError500(e.Error_user_update_failed, err.Error())
	}

	user.Active = true
	if _, err := models.Repository.User.Update(user); err != nil {
		return nil, e.NewError500(e.Error_user_update_failed, err.Error())
	}

	// Update UserTokens -> verify_date
	userToken.VerifyDate = now
	if err := models.Repository.UserToken.Update(userToken, nil); err != nil {
		return nil, e.NewError500(e.Error_update_user_token_failed, err.Error())
	}

	// Find organization Create new access token
	requestAuth := &dto.AuthParams{
		User:        user,
		Org:         params.Org,
		OriginUrl:   params.OriginUrl,
		OTPVerified: true,
	}

	authToken, aErr := Auth.Authentication(requestAuth)
	if aErr != nil {
		return nil, e.NewError500(e.Error_auth_token, aErr.Msg)
	}

	return &dto.SetPasswordResponse{
		AccessToken:  authToken.AccessToken,
		RefreshToken: authToken.RefreshToken,
	}, nil
}

func (s *AuthService) AddDefaultRole(userId string, orgId string) (*models.UserRoleOrg, *e.AppError) {
	userRoleOrg := models.UserRoleOrg{
		UserID: userId,
		RoleID: models.LearnerRoleType,
		OrgID:  orgId,
	}

	if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
		return nil, e.NewError500(e.Error_auth_add_user_role_failed, rErr.Error())
	}

	return &userRoleOrg, nil
}

func (s *AuthService) RequestOTP(user *models.User, org *models.Organization) *e.AppError {
	// Create userToken record
	userToken := &models.UserToken{
		UserID:      &user.ID,
		Email:       user.Email,
		User:        user,
		ExpiryTime:  util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		Event:       models.EventSendOTP,
		SendEmail:   util.GetCurrentTime(),
		OTP:         util.GenerateOTP(),
		OTPExpireAt: util.AddCurrentTimeWithMinute(15),
	}

	if err := UserToken.Upsert(userToken, nil); err != nil {
		return e.NewError500(e.Error_upsert_user_token_failed, err.Error())
	}
	mailParams := commdto.MapEmailParams{
		commdto.EmailParamUserToken: userToken,
	}

	go func() {
		if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       commdto.EventSendOTP,
			ExtendDatas: mailParams,
			IsQueue:     true,
		}); eErr != nil {
			log.ErrorWithAlertf("AuthService.RequestOTP::Send email failed: %v", eErr)
		}
	}()

	return nil
}

func (s *AuthService) ValidateUserByEmail(email string) (*models.User, *e.AppError) {
	user, err := models.Repository.User.FindByEmailWithOpts(email, nil)
	if err != nil {
		switch {
		case errors.Is(err, gorm.ErrRecordNotFound):
			return nil, e.NewError401(e.Error_auth_invalid_credential, "Invalid credential")
		default:
			return nil, e.NewError500(e.Error_auth_verify_credential_fail, "Verify credential failed: "+err.Error())
		}
	}

	// Validate user active
	if !user.Active {
		log.Debugf("Middleware::BeforeInterceptor:: user inactive %s", user.Email)
		return nil, e.NewError403(e.Auth_user_inactive, "User inactive")
	}

	// Validate user bocked
	if user.Blocked {
		log.Debugf("Middleware::BeforeInterceptor:: user blocked %s", user.Email)
		return nil, e.NewError403(e.Auth_user_blocked, "User blocked")
	}

	return user, nil
}
