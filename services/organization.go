package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/excel"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/upload"
	"openedu-core/pkg/util"
	"regexp"
	"strings"
	"time"

	"github.com/shopspring/decimal"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

var editRoles = []string{
	models.SystemAdminRoleType,
	models.AdminRoleType,
	models.ModeratorRoleType,
}

func existSchema(schema string) bool {
	orgBySchema, _ := models.Repository.Organization.FindOne(&models.OrganizationQuery{Schema: util.NewString(schema)}, nil)
	return orgBySchema != nil || models.IsSchemaExist(schema)
}

func makeSchema(domain string) string {
	schema := strings.TrimSuffix(domain, "."+setting.AppSetting.BaseDomain)
	schema = strings.ToLower(schema)
	if !existSchema(schema) {
		return schema
	}
	for i := 0; i < 100; i++ {
		schema += util.GenerateCode(5)
		schema = strings.ToLower(schema)
		if !existSchema(schema) {
			return schema
		}
	}
	return schema
}

func getOrgOwner(data *dto.CreateOrgRequest) (*models.User, bool, *e.AppError) {
	var user *models.User
	createNewUser := false
	password := util.GenerateId()
	if userByEmail, uErr := models.Repository.User.FindByEmailWithOpts(*data.Email, nil); uErr != nil {
		if !errors.Is(uErr, gorm.ErrRecordNotFound) {
			return nil, false, e.NewError500(e.Error_find_user_by_email_failed, uErr.Error())
		}
		if userOrg, cErr := User.CreateUserForOrg(&dto.CreateUser{
			Email:       *data.Email,
			Username:    util.GenerateId(),
			Password:    password,
			DisplayName: *data.FullName,
			OrgID:       data.DefaultOrgID,
			Phone:       *data.Phone,
		}); cErr != nil {
			return nil, false, cErr
		} else {
			user = userOrg
			createNewUser = true
		}
	} else {
		user = userByEmail
	}
	return user, createNewUser, nil
}

func addDefaultOwnerRoles(user *models.User, org *models.Organization) *e.AppError {
	// Add User org admin1
	if rErr := models.Repository.User.UpdateRoles(&models.UserRoleOrg{
		UserID: user.ID,
		RoleID: models.OrgAdminRoleType,
		OrgID:  org.ID,
	}); rErr != nil {
		return e.NewError500(e.Organization_update_allow_origin_failed, "failed to add org admin: "+rErr.Error())
	}

	if rErr := models.Repository.User.UpdateRoles(&models.UserRoleOrg{
		UserID: user.ID,
		RoleID: models.PartnerRoleType,
		OrgID:  org.ID,
	}); rErr != nil {
		return e.NewError500(e.Organization_update_allow_origin_failed, "failed to add org admin: "+rErr.Error())
	}
	return nil
}

func sendEmail(user *models.User, org *models.Organization, createNewUser bool) *e.AppError {
	// Send email for indicating organization registration is approved
	userToken := &models.UserToken{
		UserID:     &user.ID,
		Email:      user.Email,
		User:       user,
		Token:      util.GenerateToken(),
		ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		Event:      lo.If(createNewUser, models.EventExternalRegister).Else(models.EventResetPassword),
		SendEmail:  util.GetCurrentTime(),
	}
	if err := models.Repository.UserToken.Create(userToken, nil); err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	event := models.EventApproveRegisterOrgExistingUser
	if createNewUser {
		event = models.EventApproveRegisterOrgNewUser
	}

	go func() {
		mailParams := commdto.MapEmailParams{
			commdto.EmailParamUserToken: userToken,
		}
		if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       event.IntoComm(),
			ExtendDatas: mailParams,
			IsQueue:     true,
		}); eErr != nil {
			log.ErrorWithAlertf("OrganizationSerivce.sendEmail::Send email failed: %v", eErr)
		}
	}()
	return nil
}

func (s *OrganizationService) Create(data *dto.CreateOrgRequest, shouldSendEmail bool) (*models.Organization, *e.AppError) {
	if orgByDomain, err := models.Repository.Organization.FindOne(&models.OrganizationQuery{Domain: util.NewString(data.Domain)}, nil); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError500(e.Organization_find_one_failed, err.Error())
		}
	} else if orgByDomain != nil {
		return nil, e.NewError400(e.Organization_domain_required, "Found an org by Domain")
	}

	if !strings.HasSuffix(data.Domain, setting.AppSetting.BaseDomain) {
		return nil, e.NewError400(e.Organization_subdomain_must_include_root, "Must be include: "+setting.AppSetting.BaseDomain)
	}

	schema := makeSchema(data.Domain)
	if data.Schema != "" {
		schema = data.Schema
	}

	var user *models.User
	var org *models.Organization

	altDomain := data.Domain
	if data.AltDomain != nil {
		altDomain = *data.AltDomain
	}

	user, createNewUser, uErr := getOrgOwner(data)
	if uErr != nil {
		return nil, uErr
	}

	org = &models.Organization{
		UserID:      user.ID,
		Name:        data.Name,
		Domain:      data.Domain,
		Schema:      schema,
		ThumbnailID: data.ThumbnailID,
		BannerID:    data.BannerID,
		AltDomain:   altDomain,
		Active:      true,
		CreateByID:  *data.CreateByID,
	}

	if data.Settings != nil {
		org.Settings = data.Settings
	}

	if err := models.Repository.Organization.Create(org, nil); err != nil {
		return nil, e.NewError400(e.Organization_set_owner_failed, err.Error())
	}

	rErr := addDefaultOwnerRoles(user, org)
	if rErr != nil {
		return nil, rErr
	}

	// Add while list domain
	if cErr := models.UpdateAllowOrigin(lo.Uniq(append(util.Domain2Link2(org.Domain), util.Domain2Link2(org.AltDomain)...))); cErr != nil {
		return org, e.NewError500(e.Organization_update_allow_origin_failed, cErr.Error())
	}

	if shouldSendEmail {
		if emailErr := sendEmail(user, org, createNewUser); emailErr != nil {
			return org, emailErr
		}
	}

	// Init default form creator
	if _, cErr := s.createDefaultCreatorForm(org); cErr != nil {
		return org, e.NewError500(e.Organization_init_register_creator_form_failed, cErr.Error())
	}

	if appErr := communication.Email.InitDefaultEmailTemplateForOrg(org.IntoComm()); appErr != nil {
		return org, e.NewError500(e.Organization_init_default_email_templates, "Init default email templates for org error: "+appErr.Error())

	}

	if appErr := s.InitOrgConfigs(user, org); appErr != nil {
		return org, appErr
	}

	return org, nil
}

func (s *OrganizationService) InitOrgConfigs(user *models.User, org *models.Organization) *e.AppError {
	for k, params := range models.GetOrganizationConfigs(org) {
		//if err := models.InitConfig[string](k, params.Value, models.JsonB, org, params.Locale); err != nil {
		//	return e.NewError500(e.ERROR, fmt.Sprintf("Init org config key %s failed: %params", k, err))
		//}

		query := &models.SystemConfigQuery{
			Key:    &k,
			OrgID:  &org.ID,
			Locale: params.Locale,
		}

		_, err := models.Repository.System.FindOne(query, &models.FindOneOptions{})
		if err != nil {
			if models.IsRecordNotFound(err) {
				req := &dto.SystemConfigRequest{
					Key:             k,
					Value:           params.Value,
					DataType:        params.DataType,
					OrgID:           org.ID,
					Domain:          org.Domain,
					IsStorageInFile: params.IsStorageInFile,
				}
				if params.Locale != nil {
					req.Locale = *params.Locale
				}

				if _, appErr := SystemConfig.Create(user, req); appErr != nil {
					return e.NewError500(e.ERROR, fmt.Sprintf("Init org config key %s failed: %v", k, appErr))
				}
			} else {
				return e.NewError500(e.ERROR, fmt.Sprintf("Check org config key %s failed: %v", k, err))
			}
		}
	}

	// TODO: Maybe remove this because we will bootstrap email template in communication
	// if appErr := services.Organization.InitDefaultEmailTemplates(org); appErr != nil {
	// 	log.Fatalf("Init default email templates failed: %params", appErr)
	// }

	return nil
}

func (s *OrganizationService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Organization, *e.AppError) {
	query := &models.OrganizationQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}
	if org, err := models.Repository.Organization.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Organization_not_found, err.Error())
		}
		return nil, e.NewError500(e.Organization_find_one_failed, err.Error())
	} else {
		return org, nil
	}
}

func (s *OrganizationService) FindOne(query *models.OrganizationQuery, options *models.FindOneOptions) (*models.Organization, *e.AppError) {
	if org, err := models.Repository.Organization.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Organization_not_found, "Org not found")
		}
		return nil, e.NewError500(e.Organization_find_one_failed, err.Error())
	} else {
		return org, nil
	}
}

func (s *OrganizationService) FindPage(query *models.OrganizationQuery, options *models.FindPageOptions) ([]*models.Organization, *models.Pagination, *e.AppError) {
	if orgs, pagination, err := models.Repository.Organization.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Organization_find_page_failed, err.Error())
	} else {
		return orgs, pagination, nil
	}
}

func (s *OrganizationService) Update(org *models.Organization) *e.AppError {
	if err := models.Repository.Organization.Update(org, nil); err != nil {
		return e.NewError500(e.Organization_update_failed, err.Error())
	}
	return nil
}

func (s *OrganizationService) Delete(org *models.Organization) *e.AppError {
	if err := models.Repository.Organization.Delete(org.ID, nil); err != nil {
		return e.NewError500(e.Organization_delete_failed, err.Error())
	}
	return nil
}

func (s *OrganizationService) UpdateOrg(org *models.Organization, data *dto.UpdateOrgRequest, user *models.User) *e.AppError {
	// admin, sysadmin, mod
	isSysAdmin := false
	if userRoles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID); err != nil {
		return e.NewError400(e.Find_user_role_failed, "UpdateOrg: "+err.Error())
	} else if !models.IsSysAdminRoles(userRoles) && org.UserID != user.ID {
		return e.NewError400(e.Organization_owner_required, "Org owner or system admin required")
	} else {
		isSysAdmin = models.IsSysAdminRoles(userRoles)
	}

	org.Name = data.Name
	if data.UserID != "" {
		org.UserID = data.UserID
	}

	if data.ThumbnailID == nil || *data.ThumbnailID == "" {
		org.ThumbnailID = nil
	} else {
		org.ThumbnailID = data.ThumbnailID
	}

	if data.BannerID == nil || *data.BannerID == "" {
		org.BannerID = nil
	} else {
		org.BannerID = data.BannerID
	}

	oldActive := org.Active
	if isSysAdmin {
		org.Active = data.Active
	}

	if aErr := Organization.Update(org); aErr != nil {
		return aErr
	}
	//Build param send mail

	if oldActive && !data.Active { // Only send email notify organization is deactivated if ACTIVE is change from TRUE to FALSE
		orgOwner, err := User.FindByID(org.UserID, &models.FindOneOptions{})
		if err != nil {
			return err
		}

		go func() {
			if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
				User:        orgOwner.IntoComm(),
				Org:         org.IntoComm(),
				Event:       models.EventDeactivateOrg.IntoComm(),
				ExtendDatas: nil,
				IsQueue:     true,
			}); eErr != nil {
				log.ErrorWithAlertf("OrganizationService.UpdateOrg::Send email failed: %v", err)
			}
		}()
	}
	return nil
}

func (s *OrganizationService) createDefaultCreatorForm(org *models.Organization) (*models.Form, error) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	form := models.GetDefaultRegisterCreatorForm()
	form.OrgID = org.ID
	if err := models.Repository.Form.Create(form, tx); err != nil {
		tx.Rollback()
		return nil, err
	}
	for idx, question := range form.Questions {
		question.FormID = form.ID
		question.Order = idx
		for subQuesIdx, subQuestion := range question.SubQuestions {
			subQuestion.FormID = form.ID
			subQuestion.Order = subQuesIdx
		}
	}
	if err := models.Repository.FormQuestion.CreateMany(form.Questions, tx); err != nil {
		tx.Rollback()
		return nil, err
	}

	return nil, tx.Commit().Error
}

func (s *OrganizationService) GetRoot() *models.Organization {
	root, _ := models.Repository.Organization.FindOne(&models.OrganizationQuery{Domain: util.NewString(setting.AppSetting.BaseDomain)}, nil)
	return root
}

func (s *OrganizationService) IsValidDomain(domain string) (bool, error) {
	// Subdomain of organization is used as Postgres schema
	// So valid subdomain must be followed Postgres schema's convention
	// Ref: https://www.postgresql.org/docs/9.2/sql-syntax-lexical.html#SQL-SYNTAX-IDENTIFIERS
	// Beside subdomain must not be included uppercase and special characters.
	subDomain := strings.Split(domain, "."+setting.AppSetting.BaseDomain)[0]
	re, err := regexp.Compile(`^[a-z][a-z0-9]{0,62}$`)
	if err != nil {
		return false, err
	}
	return re.MatchString(subDomain), nil
}

func (s *OrganizationService) CheckValidation(data *dto.CheckOrgValidationRequest) *e.AppError {
	// Check domain valid
	if !strings.HasSuffix(data.Domain, setting.AppSetting.BaseDomain) {
		return e.NewError400(e.Organization_subdomain_must_include_root, "Subdomain must be include root")
	}

	valid, err := s.IsValidDomain(data.Domain)
	if err != nil {
		return e.NewError500(e.Organization_check_validation_failed, "Check domain valid error: "+err.Error())
	}

	if !valid {
		return e.NewError400(e.Organization_domain_invalid, "Sub domain is invalid. Sub domain must be started with a lowercase letter, followed by a lowercase letter or number.")
	}

	// Check domain exists
	query := &models.OrganizationQuery{
		IDNe:   data.OrgID,
		Domain: &data.Domain,
	}
	org, err := models.Repository.Organization.FindOne(query, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Organization_check_validation_failed, "Check domain exists error: "+err.Error())
	}

	if org != nil {
		return e.NewError400(e.Organization_domain_already_exists, fmt.Sprintf("Domain %s already exists", data.Domain))
	}

	return nil
}

func (s *OrganizationService) CalcRetroactiveForAvail(airdropAmount decimal.Decimal) (*dto.CalcRetroactiveForAvailResponse, *e.AppError) {
	httpClient := httpclient.NewClient(setting.ExternalServiceSetting.AvailGameLeaderboardURL, nil)
	resp, body, err := httpClient.Get("", map[string]string{}, map[string]interface{}{})
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Make request to get leaderboard response error: "+err.Error())
	}

	if resp.StatusCode != http.StatusOK {
		var respData dto.AvailLeaderboardResponse[any]
		if uErr := json.Unmarshal(body, &respData); uErr != nil {
			return nil, e.NewError500(e.ERROR, "Unmarshal leaderboard response error: "+uErr.Error())
		}
		return nil, e.NewError500(e.ERROR, fmt.Sprintf("Get leaderboard failed with status code %d:%+v", resp.StatusCode, respData))
	}

	var leaderboardResp dto.AvailLeaderboardResponse[[]*dto.AvailLeaderboardRecord]
	if uErr := json.Unmarshal(body, &leaderboardResp); uErr != nil {
		return nil, e.NewError500(e.ERROR, "Unmarshal leaderboard response error: "+uErr.Error())
	}

	var emails []string
	totalScoreAllUsers := decimal.NewFromFloat(0)
	for _, record := range leaderboardResp.Data {
		emails = append(emails, record.User.Email)
		totalScoreAllUsers = totalScoreAllUsers.Add(record.TotalScore)
	}

	if len(emails) == 0 {
		return &dto.CalcRetroactiveForAvailResponse{
			TotalScoreAllUsers: totalScoreAllUsers,
			AirdropAmount:      airdropAmount,
			Distributions:      []*dto.RetroactiveDistribution{},
		}, nil
	}

	users, err := models.Repository.User.FindMany(&models.UserQuery{
		EmailIn: lo.Uniq(emails),
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Get list users by emails error: "+err.Error())
	}

	var userIDs []string
	usersByEmails := map[string]*models.User{}
	for _, user := range users {
		usersByEmails[user.Email] = user
	}

	walletsByUserIDs := map[string]*models.Wallet{}
	if len(users) > 0 {
		wallets, err := models.Repository.Wallet.FindMany(&models.WalletQuery{
			UserIDIn: userIDs,
			Network:  util.NewT(models.BlockchainNetworkAVAIL),
		}, nil)
		if err != nil {
			return nil, e.NewError500(e.ERROR, "Get list wallets by user ids error: "+err.Error())
		}

		for _, wallet := range wallets {
			walletsByUserIDs[wallet.UserID] = wallet
		}
	}

	calcAirdropResp := dto.CalcRetroactiveForAvailResponse{
		TotalScoreAllUsers: totalScoreAllUsers,
		AirdropAmount:      airdropAmount,
	}

	totalAmount := decimal.NewFromFloat(0)
	for idx, record := range leaderboardResp.Data {
		distribution := &dto.RetroactiveDistribution{
			UserID:               "N/A",
			UsernameInOpenEdu:    "N/A",
			UsernameInGame:       record.User.Username,
			Email:                record.User.Email,
			Address:              "N/A",
			Network:              models.BlockchainNetworkAVAIL,
			Rank:                 record.Rank,
			TotalScore:           record.TotalScore,
			TotalRefScore:        record.TotalRefScore,
			TotalSocialTaskScore: record.TotalSocialTaskScore,
			TotalGameScore:       record.TotalGameScore,
		}
		if user, uFound := usersByEmails[record.User.Email]; uFound {
			distribution.UserID = user.ID
			distribution.UsernameInOpenEdu = user.Username
			if wallet, wFound := walletsByUserIDs[user.ID]; wFound {
				distribution.Address = wallet.Address
				distribution.Network = wallet.Network
			}
		}

		if idx != len(leaderboardResp.Data)-1 {
			distribution.Amount = record.TotalScore.Div(totalScoreAllUsers).Mul(airdropAmount)
			totalAmount = totalAmount.Add(distribution.Amount)
		} else {
			distribution.Amount = airdropAmount.Sub(totalAmount)
			totalAmount = totalAmount.Add(distribution.Amount)
		}

		calcAirdropResp.Distributions = append(calcAirdropResp.Distributions, distribution)
	}

	fileContent, appErr := s.exportExcelForAirdropDistribution(calcAirdropResp.Distributions)
	if appErr != nil {
		return nil, appErr
	}

	file := &upload.File{
		Name:    fmt.Sprintf("AVAIL-Retroactive-Report-%s.xlsx", time.Now().Format("2006-01-02")),
		Mime:    models.MIMETypeApplicationExcel,
		Content: fileContent,
		Public:  true,
	}

	uploadedFiles, uErr := upload.DefaultProvider.UploadFiles([]*upload.File{file}, "reports")
	if uErr != nil {
		return nil, e.NewError500(e.ERROR, "Upload report file error: "+err.Error())
	}

	calcAirdropResp.FileURL = uploadedFiles[0].URL
	return &calcAirdropResp, nil
}

func (s *OrganizationService) exportExcelForAirdropDistribution(distributions []*dto.RetroactiveDistribution) ([]byte, *e.AppError) {
	ex := excel.New()
	if err := ex.NewExcelWithSheet([]string{models.AvailAirDropDistributionSheetName}); err != nil {
		return nil, e.NewError500(e.ERROR, "Create excel with sheets error: "+err.Error()) // TODO define error code and msg
	}

	if err := ex.WriteData(models.AvailAirDropDistributionSheetName, distributions); err != nil {
		return nil, e.NewError500(e.ERROR, "Write data to excel sheet error: "+err.Error()) // TODO define error code and msg
	}

	bytes, err := ex.ToByte()
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Parse excel to bytes error: "+err.Error()) // TODO define error code and msg
	}

	return bytes, nil
}

func (s *OrganizationService) FindOrgAdminIDs(org *models.Organization) ([]string, error) {
	userRoleOrgs, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		RoleIDIn: []string{models.OrgAdminRoleType, models.OrgModeratorRoleType},
		OrgID:    &org.ID,
	}, nil)
	if err != nil {
		log.Errorf("Get list org admins and moderators of org ID %s error: %v", org.ID, err)
		return nil, err
	}

	userIDs := lo.Map(userRoleOrgs, func(item *models.UserRoleOrg, _ int) string {
		return item.UserID
	})
	return lo.Uniq(userIDs), nil
}
