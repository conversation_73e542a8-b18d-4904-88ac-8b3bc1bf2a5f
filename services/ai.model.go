package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func (s *AIModelService) FindOne(query *models.AIModelQuery, options *models.FindOneOptions) (*models.AIModel, *e.AppError) {
	if aiModel, err := models.Repository.AIModel.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.AIModel_not_found, err.Error())
		}
		return nil, e.NewError500(e.AIModel_find_one_failed, err.Error())
	} else {
		return aiModel, nil
	}
}

func (s *AIModelService) FindPage(query *models.AIModelQuery, options *models.FindPageOptions) ([]*models.AIModel, *models.Pagination, *e.AppError) {
	if aiModels, pagination, err := models.Repository.AIModel.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.AIModel_find_page_failed, err.Error())
	} else {
		return aiModels, pagination, nil
	}
}

func (s *AIModelService) Create(data *dto.AIModelRequest) (*models.AIModel, *e.AppError) {
	aiModel := &models.AIModel{
		Name:         data.Name,
		Description:  data.Description,
		Enabled:      data.Enabled,
		Configs:      data.Configs,
		DisplayName:  data.DisplayName,
		ThumbnailURL: data.ThumbnailURL,
		Order:        data.Order,
	}
	if err := models.Repository.AIModel.Create(aiModel, nil); err != nil {
		return nil, e.NewError500(e.AIModel_create_failed, err.Error())
	}
	return aiModel, nil
}

func (s *AIModelService) CreateMany(data []*models.AIModel) ([]*models.AIModel, *e.AppError) {
	if err := models.Repository.AIModel.CreateMany(data, nil); err != nil {
		return nil, e.NewError500(e.AIModel_create_many_failed, err.Error())
	}
	return data, nil
}

func (s *AIModelService) Update(data *models.AIModel) (*models.AIModel, *e.AppError) {
	if err := models.Repository.AIModel.Update(data, nil); err != nil {
		return nil, e.NewError500(e.AIModel_update_failed, err.Error())
	}
	return data, nil
}

func (s *AIModelService) FindMany(query *models.AIModelQuery, options *models.FindManyOptions) ([]*models.AIModel, *e.AppError) {
	if aiModels, err := models.Repository.AIModel.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.AIModel_find_many_failed, err.Error())
	} else {
		return aiModels, nil
	}
}

func (s *AIModelService) GetAvailableAIModel(user *models.User, org *models.Organization) ([]*dto.GetAvailableAIModel, *e.AppError) {
	aiModels, err := models.Repository.AIModel.FindMany(&models.AIModelQuery{
		Enabled:        util.NewBool(true),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindManyOptions{Sort: []string{models.OrderASC}})
	if err != nil {
		return nil, e.NewError500(e.AIModel_find_many_failed, "GetAIModel: "+err.Error())
	}

	if len(aiModels) == 0 {
		return nil, e.NewError500(e.AIModel_not_found, "GetAIModel: "+e.GetMsg(e.AIModel_not_found))
	}

	response := []*dto.GetAvailableAIModel{}
	if user != nil {
		plan, aErr := Subscription.GetActivePlanByUser(user)
		if aErr != nil {
			return nil, aErr
		}

		usage, aErr := ResourceUsage.GetAiUsage(user, plan, org.ID)
		if aErr != nil {
			return nil, aErr
		}

		aiPlanLimitModelMapByModelID := map[string]*models.AILimitModel{}
		for _, model := range plan.AiLimitation.Models {
			aiPlanLimitModelMapByModelID[model.AiModelID] = model
		}

		aiResourceUsagesMapByAIModelID := map[string]*models.AiResourceUsage{}
		for _, u := range usage.AiUsages {
			aiResourceUsagesMapByAIModelID[u.AIModelID] = u
		}

		for _, model := range aiModels {
			isAvailable := false
			currentAIPlan := aiPlanLimitModelMapByModelID[model.ID]
			currentAIUsage := aiResourceUsagesMapByAIModelID[model.ID]
			if currentAIPlan != nil && currentAIUsage != nil {
				remainingBalance := currentAIPlan.BalanceAmount.Add(currentAIUsage.Bonus).Sub(currentAIUsage.BalanceUsed)
				if !remainingBalance.LessThanOrEqual(decimal.NewFromInt(0)) && currentAIUsage.Enable {
					isAvailable = true
				}
			} else {
				log.Infof("Model ID %s not found in plan or usage", model.ID)
			}

			response = append(response, &dto.GetAvailableAIModel{
				ID:           model.ID,
				Name:         model.Name,
				Description:  model.Description,
				Configs:      model.Configs,
				DisplayName:  model.DisplayName,
				ThumbnailURL: model.ThumbnailURL,
				IsAvailable:  isAvailable,
			})
		}
	} else {
		for _, model := range aiModels {
			response = append(response, &dto.GetAvailableAIModel{
				ID:           model.ID,
				Name:         model.Name,
				Description:  model.Description,
				Configs:      model.Configs,
				DisplayName:  model.DisplayName,
				ThumbnailURL: model.ThumbnailURL,
				IsAvailable:  true,
			})
		}
	}

	return response, nil
}

func (s *AIModelService) MigrateAIModel() *e.AppError {
	existingModels, err := s.FindMany(&models.AIModelQuery{
		IncludeDeleted: util.NewBool(false)}, nil)
	if err != nil {
		return e.NewError500(e.AIModel_find_many_failed, err.Error())
	}

	if len(existingModels) <= 0 {
		defaultModels := models.GetConfig[[]*models.AIModel](models.AIModelsConfig)
		if len(defaultModels) > 0 {
			if err := models.Repository.AIModel.CreateMany(defaultModels, nil); err != nil {
				return e.NewError500(e.AIModel_create_many_failed, err.Error())
			}

			log.Debugf("Initialized %d AI models from default configuration", len(defaultModels))
		}
		return nil
	}

	log.Debugf("AI models already exist, skipping migration. Found %d models", len(existingModels))
	return nil
}

func (s *AIModelService) FindOneAvailableAIModel(user *models.User, orgID string, extendedThinking bool) (*dto.GetAvailableAIModel, *e.AppError) {
	query := &models.AIModelQuery{
		Enabled:        util.NewBool(true),
		IncludeDeleted: util.NewBool(false),
	}

	if extendedThinking {
		query.ExtendedThinkingEnabled = util.NewBool(extendedThinking)
	}

	aiModels, err := models.Repository.AIModel.FindMany(query, &models.FindManyOptions{Sort: []string{models.OrderASC}})
	if err != nil {
		return nil, e.NewError500(e.AIModel_find_many_failed, "GetAIModel: "+err.Error())
	}

	if len(aiModels) == 0 {
		return nil, e.NewError500(e.AIModel_not_found, "GetAIModel: "+e.GetMsg(e.AIModel_not_found))
	}

	plan, aErr := Subscription.GetActivePlanByUser(user)
	if aErr != nil {
		return nil, aErr
	}

	usage, aErr := ResourceUsage.GetAiUsage(user, plan, orgID)
	if aErr != nil {
		return nil, aErr
	}

	aiPlanLimitModelMapByModelID := map[string]*models.AILimitModel{}
	for _, a := range plan.AiLimitation.Models {
		aiPlanLimitModelMapByModelID[a.AiModelID] = a
	}

	aiResourceUsagesMapByAIModelID := map[string]*models.AiResourceUsage{}
	for _, u := range usage.AiUsages {
		aiResourceUsagesMapByAIModelID[u.AIModelID] = u
	}

	// Return the first available model
	for _, model := range aiModels {
		currentAIPlan := aiPlanLimitModelMapByModelID[model.ID]
		currentAIUsage := aiResourceUsagesMapByAIModelID[model.ID]

		if currentAIPlan != nil && currentAIUsage != nil {
			remainingBalance := currentAIPlan.BalanceAmount.Add(currentAIUsage.Bonus).Sub(currentAIUsage.BalanceUsed)
			if !remainingBalance.LessThanOrEqual(decimal.NewFromInt(0)) && currentAIUsage.Enable {
				// Found an available model, return it immediately
				return &dto.GetAvailableAIModel{
					ID:           model.ID,
					Name:         model.Name,
					Description:  model.Description,
					Configs:      model.Configs,
					DisplayName:  model.DisplayName,
					ThumbnailURL: model.ThumbnailURL,
					IsAvailable:  true,
				}, nil
			}
		}
	}

	// If no available model found, return an error
	return nil, e.NewError400(e.AIModel_not_available, "FindOneAvailableAIModel: No available AI model")
}
