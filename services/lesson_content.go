package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"github.com/samber/lo"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

func copyContentData(content *models.LessonContent, req *dto.LessonContentRequest) *models.LessonContent {
	content.OrgID = req.OrgID
	content.CourseID = req.CourseID
	content.SectionID = req.SectionID
	content.LessonID = req.LessonID
	content.Title = req.Title
	content.Content = req.Content
	content.Duration = req.Duration
	content.JsonContent = req.JsonContent
	content.Type = req.Type
	content.Order = req.Order
	content.Files = req.Files
	return content
}

func (s *LessonContentService) UpsertMany(course *models.Course, user *models.User, data []*dto.LessonContentRequest) ([]*models.LessonContent, *e.AppError) {
	var toCreate []*models.LessonContent
	var toUpdate []*models.LessonContent
	var quizRequestsToCreate []*dto.QuizRequest
	var quizRequestsToUpdate []*dto.QuizRequest

	seenQuizIDs := make(map[string]struct{})
	for _, d := range data {
		if d.ID != nil {
			l, err := models.Repository.LessonContent(s.ctx).FindOne(&models.LessonContentQuery{ID: d.ID, IncludeDeleted: util.NewBool(false)}, nil)
			if l != nil {
				toUpdate = append(toUpdate, copyContentData(l, d))
				for _, quizRequest := range d.Quizzes {
					quizRequest.OrgID = course.OrgID
					quizRequest.RelatedEntityID = *d.ID
					quizRequest.RelatedEntityType = models.QuizRelationEntityLessonContent
					quizRequest.RelationType = lo.
						If[models.QuizRelationType](d.Type == models.LessonTypeQuiz, models.QuizRelationTypeIs).
						Else(models.QuizRelationTypeTriggeredBy)
					if quizRequest.ID == "" {
						quizRequestsToCreate = append(quizRequestsToCreate, quizRequest)
					} else {
						seenQuizIDs[quizRequest.ID] = struct{}{}
						quizRequestsToUpdate = append(quizRequestsToUpdate, quizRequest)
					}
				}
			} else {
				if !models.IsRecordNotFound(err) {
					return nil, e.NewError500(e.Lesson_find_one_failed, err.Error())
				}
				content := copyContentData(&models.LessonContent{}, d)
				content.ID = util.GenerateId()
				if d.UID != nil {
					content.UID = *d.UID
				} else {
					content.UID = util.GenerateId()
				}
				content.CourseID = course.ID
				content.UserID = user.ID
				toCreate = append(toCreate, content)
				// Create or update quizzes
				for _, quizRequest := range d.Quizzes {
					quizRequest.OrgID = course.OrgID
					quizRequest.RelatedEntityID = content.ID
					quizRequest.RelatedEntityType = models.QuizRelationEntityLessonContent
					quizRequest.RelationType = lo.
						If[models.QuizRelationType](d.Type == models.LessonTypeQuiz, models.QuizRelationTypeIs).
						Else(models.QuizRelationTypeTriggeredBy)
					if quizRequest.ID == "" {
						quizRequestsToCreate = append(quizRequestsToCreate, quizRequest)
					} else {
						seenQuizIDs[quizRequest.ID] = struct{}{}
						quizRequestsToUpdate = append(quizRequestsToUpdate, quizRequest)
					}
				}
			}
		} else {
			content := copyContentData(&models.LessonContent{}, d)
			content.ID = util.GenerateId()
			if d.UID != nil {
				content.UID = *d.UID
			} else {
				content.UID = util.GenerateId()
			}
			content.CourseID = course.ID
			content.UserID = user.ID
			toCreate = append(toCreate, content)
			// Create or update quizzes
			for _, quizRequest := range d.Quizzes {
				quizRequest.OrgID = course.OrgID
				quizRequest.RelatedEntityID = content.ID
				quizRequest.RelatedEntityType = models.QuizRelationEntityLessonContent
				quizRequest.RelationType = lo.
					If[models.QuizRelationType](d.Type == models.LessonTypeQuiz, models.QuizRelationTypeIs).
					Else(models.QuizRelationTypeTriggeredBy)
				if quizRequest.ID == "" {
					quizRequestsToCreate = append(quizRequestsToCreate, quizRequest)
				} else {
					seenQuizIDs[quizRequest.ID] = struct{}{}
					quizRequestsToUpdate = append(quizRequestsToUpdate, quizRequest)
				}
			}
		}
	}

	if len(toCreate) > 0 {
		if err := models.Repository.LessonContent(s.ctx).CreateMany(toCreate, nil); err != nil {
			return nil, e.NewError500(e.Lesson_create_failed, "Create lessons failed: "+err.Error())
		}
	}
	if len(toUpdate) > 0 {
		for _, item := range toUpdate {
			if err := models.Repository.LessonContent(s.ctx).Update(item, nil); err != nil {
				return nil, e.NewError500(e.Lesson_update_failed, "Update lessons failed: "+err.Error())
			}
		}
	}

	lessonContents := append(toCreate, toUpdate...)
	lessonContentsByIDs := make(map[string]*models.LessonContent)
	for _, content := range lessonContents {
		lessonContentsByIDs[content.ID] = content
	}

	// Delete no need quiz relations
	if _, err := models.Repository.QuizRelation.DeleteMany(&models.QuizRelationQuery{
		RelatedEntityType: util.NewT(models.QuizRelationEntityLessonContent),
		RelatedEntityIDIn: lo.Map(lessonContents, func(content *models.LessonContent, _ int) string {
			return content.ID
		}),
	}, nil); err != nil {
		return nil, e.NewError500(e.Lesson_update_failed, "Delete existing quiz relations failed: "+err.Error())
	}

	// Create quizzes
	if len(quizRequestsToCreate) > 0 {
		quizWithRelations, appErr := Quiz.CreateMany(user, quizRequestsToCreate)
		if appErr != nil {
			return nil, appErr
		}
		for _, quizWithRelation := range quizWithRelations {
			if content, found := lessonContentsByIDs[quizWithRelation.RelatedEntityID]; found {
				content.Quizzes = append(content.Quizzes, quizWithRelation)
			}
		}
	}

	// Update quizzes
	if len(quizRequestsToUpdate) > 0 {
		quizWithRelations, appErr := Quiz.UpdateMany(quizRequestsToUpdate)
		if appErr != nil {
			return nil, appErr
		}
		for _, quizWithRelation := range quizWithRelations {
			if content, found := lessonContentsByIDs[quizWithRelation.RelatedEntityID]; found {
				content.Quizzes = append(content.Quizzes, quizWithRelation)
			}
		}
	}

	// Update course stats
	if cErr := Course.UpdateStats(course, true); cErr != nil {
		return nil, cErr
	}
	return append(toCreate, toUpdate...), nil
}

func (s *LessonContentService) Create(user *models.User, data *dto.LessonContentRequest) (*models.LessonContent, *e.AppError) {
	lesson := &models.LessonContent{}
	if cErr := copier.Copy(lesson, data); cErr != nil {
		return nil, e.NewError500(e.Lesson_create_failed, "Create lesson copy entity failed: "+cErr.Error())
	}
	lesson.UserID = user.ID
	lesson.OrgID = data.OrgID
	if data.UID != nil {
		lesson.UID = *data.UID
	} else {
		lesson.UID = util.GenerateId()
	}

	if err := models.Repository.LessonContent(s.ctx).Create(lesson, nil); err != nil {
		return nil, e.NewError500(e.Lesson_create_failed, "Create lesson failed: "+err.Error())
	} else {
		if course, cErr := Course.FindOne(&models.CourseQuery{ID: &lesson.CourseID, IncludeDeleted: util.NewBool(false)}, nil); cErr != nil {
			return nil, cErr
		} else {
			if cuErr := Course.UpdateStats(course, true); cuErr != nil {
				return nil, cuErr
			}
		}
		return lesson, nil
	}
}

func (s *LessonContentService) Update(lesson *models.LessonContent, data *dto.LessonContentRequest) (*models.LessonContent, *e.AppError) {
	if cErr := copier.Copy(lesson, data); cErr != nil {
		return nil, e.NewError500(e.Lesson_update_failed, "Update lesson copy entity failed: "+cErr.Error())
	}
	if err := models.Repository.LessonContent(s.ctx).Update(lesson, nil); err != nil {
		return nil, e.NewError500(e.Lesson_update_failed, "Update lesson failed: "+err.Error())
	} else {
		if course, cErr := Course.FindById(lesson.CourseID, false, nil); cErr != nil {
			return nil, cErr
		} else {
			if cuErr := Course.UpdateStats(course, true); cuErr != nil {
				return nil, cuErr
			}
		}
		return lesson, nil
	}
}

func (s *LessonContentService) FindOne(query *models.LessonContentQuery, options *models.FindOneOptions) (*models.LessonContent, *e.AppError) {
	if lesson, err := models.Repository.LessonContent(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Lesson_not_found, err.Error())
		}
		return nil, e.NewError500(e.Lesson_find_one_failed, err.Error())
	} else {
		return lesson, nil
	}
}

func (s *LessonContentService) FindPage(query *models.LessonContentQuery, options *models.FindPageOptions) ([]*models.LessonContent, *models.Pagination, *e.AppError) {
	if lessons, pagination, err := models.Repository.LessonContent(s.ctx).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Lesson_find_page_failed, err.Error())
	} else {
		return lessons, pagination, nil
	}
}

func (s *LessonContentService) FindByCourseID(courseID string, options *models.FindManyOptions) ([]*models.LessonContent, *e.AppError) {
	if lessons, err := models.Repository.LessonContent(s.ctx).FindMany(&models.LessonContentQuery{
		CourseID: &courseID,
	}, options); err != nil {
		return nil, e.NewError500(e.Lesson_find_page_failed, err.Error())
	} else {
		return lessons, nil
	}
}

func (s *LessonContentService) Delete(lesson *models.LessonContent) *e.AppError {
	if err := models.Repository.LessonContent(s.ctx).Delete(lesson.ID, nil); err != nil {
		return e.NewError500(e.Lesson_delete_failed, err.Error())
	}
	return nil
}

func (s *LessonContentService) GetLessonContentByLessons(lessons []*models.Section) ([]*models.Section, *e.AppError) {
	if len(lessons) <= 0 {
		return []*models.Section{}, nil
	}
	lessonIds := lo.Map(lessons, func(item *models.Section, _ int) string {
		return item.ID
	})

	query := &models.LessonContentQuery{
		LessonIDIn:     lessonIds,
		IncludeDeleted: util.NewBool(false),
	}
	options := &models.FindManyOptions{
		Preloads: []string{models.FilesField},
	}
	contents, cErr := models.Repository.LessonContent(s.ctx).FindMany(query, options)
	if cErr != nil {
		return nil, e.NewError500(e.LessonContentFindFailed, cErr.Error())
	}

	if appErr := Quiz.PreloadQuizzesForLessonContents(contents); appErr != nil {
		return nil, appErr
	}

	lo.ForEach(lessons, func(s *models.Section, _ int) {
		s.Contents = lo.Filter(contents, func(c *models.LessonContent, _ int) bool {
			return c.LessonID == s.ID
		})
	})
	return lessons, nil
}

func (s *LessonContentService) FindMany(query *models.LessonContentQuery, options *models.FindManyOptions) ([]*models.LessonContent, *e.AppError) {
	if lessons, err := models.Repository.LessonContent(s.ctx).FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Lesson_find_many_failed, err.Error())
	} else {
		return lessons, nil
	}

}
