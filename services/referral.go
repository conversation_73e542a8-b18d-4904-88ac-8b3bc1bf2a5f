package services

import (
	"context"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"sort"
)

func getNearestBonusCommissions(userID string, commissionID string) (*models.Commission, *e.AppError) {
	bonuses, err := Commission.FindMany(&models.CommissionQuery{
		ParentID: util.NewString(commissionID),
		Enable:   util.NewBool(true),
	}, nil)

	if err != nil {
		log.Error("CreateReferral.getBonusCommissions failed", err.Msg)
		return nil, err
	}

	count, cErr := models.Repository.Referral.Count(&models.ReferralQuery{
		Ref1UserID:   util.NewString(userID),
		CommissionID: util.NewString(commissionID),
	})

	if cErr != nil {
		log.Error("CreateReferral.getBonusCommissions failed", cErr.Error())
		return nil, e.NewError500(e.Referral_count_failed, "count referral failed: "+cErr.Error())
	}

	matches := lo.Filter(bonuses, func(item *models.Commission, _ int) bool {
		return item.Qty1 <= int(count)
	})
	if len(matches) > 0 {
		sort.Slice(matches[:], func(i, j int) bool {
			return matches[i].Qty1 > matches[j].Qty1
		})

		if len(matches) > 0 {
			return matches[0], nil
		}
	}
	return nil, nil
}

func (s *ReferralService) CreateReferral(order *models.Order, courseID string, courseCuid string, save bool) (*dto.CreateReferralResponse, *e.AppError) {
	amount := order.Amount.Sub(order.DiscountAmount)
	var refLinkLevel1 *models.ReferralLink
	var refLinkLevel2 *models.ReferralLink
	var refLinkLevel3 *models.ReferralLink
	var commission *models.Commission
	ref1Amount := decimal.Zero
	ref1ShareAmount := decimal.Zero
	ref1BonusAmount := decimal.Zero
	ref2Amount := decimal.Zero
	ref3Amount := decimal.Zero
	decimal100 := decimal.NewFromInt(100)

	refLink, refLinkErr := ReferralLink.FindByCode(order.ReferralCode)
	if refLinkErr != nil {
		if refLinkErr.ErrCode == e.Referral_link_not_found {
			log.Error("CreateReferral.Find referral link by ref code not found: ", refLinkErr.Msg)
			return nil, e.NewError500(e.Referral_link_not_found, "findref1Link: "+refLinkErr.Msg)
		}
		log.Error("CreateReferral.Find referral link by ref code failed: ", refLinkErr.Msg)
		return nil, e.NewError500(e.Referral_create_failed, "findref1Link: "+refLinkErr.Msg)
	}

	// check referral link is enabled
	if !refLink.Enable {
		log.Error("CreateReferral.Find referral link disabled")
		return nil, e.NewError500(e.Referral_link_disabled, "!refLink.Enable")
	}

	refLinkLevel1 = refLink
	if refLinkLevel1.RefLevel1 != "" {
		ref2, ref2Err := ReferralLink.FindByCode(refLink.RefLevel1)
		if ref2Err != nil {
			log.Error("CreateReferral.Find referral link 1 by ref code failed: ", ref2Err.Msg)
			return nil, e.NewError500(e.Referral_create_failed, "findref2Link: "+ref2Err.Msg)
		} else {
			refLinkLevel2 = ref2
		}
	}
	if refLinkLevel1.RefLevel2 != "" {
		ref3, ref3Err := ReferralLink.FindByCode(refLink.RefLevel2)
		if ref3Err != nil {
			log.Error("CreateReferral.Find referral link 2 by ref code failed: ", ref3Err.Msg)
			return nil, e.NewError500(e.Referral_create_failed, "findref3Link: "+ref3Err.Msg)
		} else {
			refLinkLevel3 = ref3
		}
	}

	baseRateCommission, baseRErr := Commission.FindOne(&models.CommissionQuery{
		CampaignID:   util.NewString(refLinkLevel1.CampaignID),
		IsBaseRate:   util.NewBool(true),
		Enable:       util.NewBool(true),
		ParentIDNull: util.NewBool(true),
	}, nil)
	if baseRErr != nil && baseRErr.ErrCode != e.Commission_not_found {
		return nil, baseRErr
	}

	commission = refLinkLevel1.Commission
	bonus, _ := getNearestBonusCommissions(refLink.UserID, commission.ID)

	log.Debug("commission: ", util.Struct2Json(commission))
	log.Debug("baseRateCommission: ", util.Struct2Json(baseRateCommission))
	log.Debug("bonus: ", util.Struct2Json(bonus))

	// share rate amount
	if refLinkLevel1.ShareRate > 0 {
		rate := decimal.NewFromFloat32(refLinkLevel1.ShareRate)
		ref1ShareAmount = amount.Mul(rate).Div(decimal100)
	}

	if bonus != nil {
		rate := decimal.NewFromFloat32(bonus.Ref1Rate)
		ref1BonusAmount = amount.Mul(rate).Div(decimal100)
	}

	// ref1 amount
	if commission.Ref1Rate > 0 {
		rate := decimal.NewFromFloat32(commission.Ref1Rate)
		ref1OriginAmount := amount.Mul(rate).Div(decimal100)
		ref1Amount = ref1OriginAmount.Sub(ref1ShareAmount).Add(ref1BonusAmount)
	}

	if baseRateCommission != nil {
		// ref2 amount
		if refLinkLevel2 != nil && baseRateCommission.Ref2Rate > 0 {
			rate := decimal.NewFromFloat32(baseRateCommission.Ref2Rate)
			ref2Amount = amount.Mul(rate).Div(decimal100)
		}

		if refLinkLevel3 != nil && baseRateCommission.Ref3Rate > 0 {
			rate := decimal.NewFromFloat32(baseRateCommission.Ref3Rate)
			ref3Amount = amount.Mul(rate).Div(decimal100)
		}
	}

	referral := models.Referral{
		OrgID:        order.OrgID,
		CourseCuid:   courseCuid,
		CourseID:     courseID,
		CampaignID:   refLinkLevel1.CampaignID,
		CommissionID: refLinkLevel1.CommissionID,
		OrderID:      order.ID,
		OrderAmount:  order.Amount,
		Currency:     order.Currency,

		Ref1UserID:  refLinkLevel1.UserID,
		Ref1LinkID:  refLinkLevel1.ID,
		Ref1Rate:    commission.Ref1Rate,
		Ref1Amount:  ref1Amount,
		ShareRate:   refLinkLevel1.ShareRate,
		ShareAmount: ref1ShareAmount,

		Ref2Rate:   baseRateCommission.Ref2Rate,
		Ref2Amount: ref2Amount,
		Ref3Rate:   baseRateCommission.Ref3Rate,
		Ref3Amount: ref3Amount,
	}

	if bonus != nil {
		referral.BonusCommissionID = bonus.ID
		referral.BonusRate = bonus.Ref1Rate
		referral.BonusAmount = ref1BonusAmount
	}

	if refLinkLevel2 != nil {
		referral.Ref2UserID = refLinkLevel2.UserID
		referral.Ref2LinkID = refLinkLevel2.ID
	}

	if refLinkLevel3 != nil {
		referral.Ref3UserID = refLinkLevel3.UserID
		referral.Ref3LinkID = refLinkLevel3.ID
	}

	if save {
		if err := models.Repository.Referral.Create(&referral, nil); err != nil {
			log.Error("CreateReferral.Create referral failed: ", util.Struct2Json(&referral), "----ERROR----", err.Error())
			return nil, e.NewError500(e.Referral_create_failed, "create referral: "+err.Error())
		}

		// add buyer as a purchased referrer
		Referrer.AddPurchasedReferrer(order.UserID, order.OrgID, refLink)
	}

	return &dto.CreateReferralResponse{
		Referral:        &referral,
		Commission:      commission,
		RefLinkLevel1:   refLinkLevel1,
		RefLinkLevel2:   refLinkLevel2,
		RefLinkLevel3:   refLinkLevel3,
		Ref1Amount:      ref1Amount,
		Ref1ShareAmount: ref1ShareAmount,
		Ref2Amount:      ref2Amount,
		Ref3Amount:      ref3Amount,
	}, nil
}

func (s *ReferralService) FindPage(query *models.ReferralQuery, options *models.FindPageOptions) ([]*models.Referral, *models.Pagination, *e.AppError) {
	if referrals, pagination, err := models.Repository.Referral.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Referral_find_page_failed, "find page"+err.Error())
	} else {
		if len(referrals) > 0 {
			courseCuids := lo.Map(referrals, func(item *models.Referral, _ int) string {
				return item.CourseCuid
			})
			courseCuids = lo.Uniq(courseCuids)
			pubCourses, pubErr := models.Repository.PublishCourse(context.TODO()).FindMany(&models.PublishCourseQuery{CourseCuidIn: courseCuids}, nil)
			if pubErr != nil {
				return nil, nil, e.NewError500(e.Referral_find_page_failed, "PublishCourse.FindMany "+pubErr.Error())
			}
			if len(pubCourses) > 0 {
				lo.ForEach(referrals, func(ref *models.Referral, _ int) {
					c, ok := lo.Find(pubCourses, func(pub *models.PublishCourse) bool {
						return pub.CourseCuid == ref.CourseCuid
					})
					if ok {
						ref.PubCourse = c
					}
				})
			}
		}
		return referrals, pagination, nil
	}
}

func (s *ReferralService) SummaryReport(request *models.ReferralSummaryReport) (*models.BaseReferralReport, *e.AppError) {
	var report *models.BaseReferralReport
	switch request.Type {
	case models.ReferralReportTypeOverall:
		out, err := models.Repository.Referral.SumReferralAmounts(request)
		if err != nil {
			return nil, e.NewError404(e.Referral_report_failed, "ReferralReportTypeOverall: "+err.Error())
		}
		report = out
		break
	case models.ReferralReportTypeUser:
		if request.UserID == nil {
			return nil, e.NewError404(e.INVALID_PARAMS, "user_id required")
		}
		results, err := models.Repository.Referral.GetTotalReferralAmountByUser(*request.UserID, request)
		if err != nil {
			return nil, e.NewError404(e.Referral_report_failed, "ReferralReportTypeUser: "+*request.UserID+" == "+err.Error())
		}
		report = &models.BaseReferralReport{
			UserID:      *request.UserID,
			TotalAmount: results.Ref1Amount.Add(results.Ref2Amount).Add(results.Ref3Amount),
		}
		break
	default:
		return nil, e.NewError404(e.INVALID_PARAMS, "invalid report type")
	}
	return report, nil
}

func (s *ReferralService) GetUserSummaryReport(
	userID string,
	query *models.ReferralReportByUserQuery,
	options *models.FindPageOptions) ([]*models.ReferralReportByUser, *models.Pagination, *e.AppError) {
	reports, pagination, err := models.Repository.Referral.GetReferralReportByUser(userID, query, options)
	if err != nil {
		return nil, nil, e.NewError400(e.Referral_report_failed, "GetUserSummaryReport: "+err.Error())
	}
	return reports, pagination, nil
}
