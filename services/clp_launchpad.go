package services

import (
	"errors"
	"fmt"
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/ms_team"
	"openedu-core/pkg/openedu_chain"
	chaindto "openedu-core/pkg/openedu_chain/dto"
	"openedu-core/pkg/openedu_scheduler"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/shopspring/decimal"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

const (
	StartFundingLaunchpadJobName      = "Start funding for launchpad"
	EndFundingLaunchpadJobName        = "End funding for launchpad"
	EndVotingMilestoneJobName         = "End voting milestone for launchpad"
	CheckSettingFundingTimeJobName    = "Check creator set funding time"
	CheckCreatorContinueVotingJobName = "Check creator continue voting after end funding"
	StartFundingPath                  = "/api/v1/cron-jobs/start-funding"
	EndFundingPath                    = "/api/v1/cron-jobs/end-funding"
	EndVotingMilestonePath            = "/api/v1/cron-jobs/end-voting"
	CheckSettingFundingTimePath       = "/api/v1/cron-jobs/check-setting-funding-time"
	CheckCreatorContinueVotingPath    = "/api/v1/cron-jobs/check-creator-continue-voting"
)

func (s *ClpLaunchpadService) Create(org *models.Organization, user *models.User, data *dto.CreateClpLaunchpadRequest) (*models.ClpLaunchpad, *e.AppError) {
	var course *models.Course
	if data.CourseCuid != nil {
		courseAvailable, cErr := s.CourseCanMakeLaunchpad(user, *data.CourseCuid, nil)
		if cErr != nil {
			return nil, cErr
		}

		course = courseAvailable
	}

	slug, err := util.Slugify(data.Name, 5)
	if err != nil {
		return nil, e.NewError500(e.Generate_random_failed, err.Error())
	}

	categories, cErr := categories2Categories(models.TypeCategoryCourse, data.Categories)
	levels, cErr := categories2Categories(models.TypeLevel, data.Levels)
	if cErr != nil {
		return nil, cErr
	}

	if data.Status != nil {
		data.Status = util.NewT(models.LaunchpadSTTDraft)
	}

	launchpad := models.ClpLaunchpad{
		OrgID:               org.ID,
		OrgSchema:           org.Schema,
		UserID:              user.ID,
		Status:              *data.Status,
		Name:                data.Name,
		Description:         data.Description,
		Slug:                slug,
		LearnerOutcome:      data.LearnerOutcome,
		PreviewVideoID:      data.PreviewVideoID,
		ThumbnailID:         data.ThumbnailID,
		Categories:          categories,
		Levels:              levels,
		FundingGoal:         models.FundingProps{TargetFunding: decimal.Zero, Currency: models.CryptoCurrencyUSDT, MinPledge: decimal.NewFromInt(5), ProfitPercentage: 0},
		Enable:              true,
		VotingStartDate:     data.VotingStartDate,
		VotingEndDate:       data.VotingEndDate,
		EstimateFundingDays: data.EstimateFundingDays,
	}

	if data.FundingGoal != nil {
		launchpad.FundingGoal = *data.FundingGoal
	}

	if cErr := models.Repository.ClpLaunchpad(s.ctx).Create(&launchpad, nil); cErr != nil {
		return nil, e.NewError500(e.Create_launchpad_failed, cErr.Error())
	}

	if course != nil {
		courseLaunchpad := models.ClpCourseLaunchpad{
			ClpLaunchpadID: launchpad.ID,
			CourseCuid:     course.Cuid,
			CourseID:       course.ID,
			Enable:         true,
		}

		err := models.Repository.ClpCourseLaunchpad(s.ctx).Create(&courseLaunchpad, nil)
		if err != nil {
			return nil, e.NewError500(e.Create_course_launchpad_failed, "create course launchpad failed "+err.Error())
		}
	}

	return &launchpad, nil
}

func (s *ClpLaunchpadService) FindPage(query *models.LaunchpadQuery, options *models.FindPageOptions) ([]*models.ClpLaunchpad, *models.Pagination, *e.AppError) {
	if len(options.Preloads) > 0 {
		if err := models.Repository.ClpLaunchpad(s.ctx).ValidateFields(options.Preloads); err != nil {
			return nil, nil, e.NewError400(e.INVALID_PARAMS, "Preloads field invalid: "+err.Error())
		}
	}

	shouldPreloadInvestment := false
	if lo.Contains(options.Preloads, models.InvestmentField) {
		shouldPreloadInvestment = true
		options.Preloads = util.RemoveElement(options.Preloads, models.InvestmentField)
	}

	launchpads, pagination, err := models.Repository.ClpLaunchpad(s.ctx).FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Launchpad_find_page_failed, "Find page launchpads error: "+err.Error())
	}

	if shouldPreloadInvestment {
		if appErr := s.GetInvestmentStats(launchpads); appErr != nil {
			return nil, nil, e.NewError500(e.Launchpad_find_page_failed, "Preload investment for launchpad failed: "+err.Error())
		}
	}

	return launchpads, pagination, nil
}

func (s *ClpLaunchpadService) FindOneOptions(query *models.LaunchpadQuery, options *models.FindOneOptions) (*models.ClpLaunchpad, *e.AppError) {
	if len(options.Preloads) > 0 {
		if err := models.Repository.ClpLaunchpad(s.ctx).ValidateFields(options.Preloads); err != nil {
			return nil, e.NewError400(e.INVALID_PARAMS, "Preloads field invalid: "+err.Error())
		}
	}

	launchpad, err := models.Repository.ClpLaunchpad(s.ctx).FindOne(query, options)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Launchpad_find_one_failed, err.Error())
	}

	return launchpad, nil
}

func (s *ClpLaunchpadService) FindByID(id string) (*models.ClpLaunchpad, *e.AppError) {
	launchpad, err := models.Repository.ClpLaunchpad(s.ctx).FindOne(
		&models.LaunchpadQuery{
			ID: util.NewString(id),
		}, &models.FindOneOptions{
			Preloads: []string{
				models.CategoriesField,
				models.LevelsField,
				models.OwnerField,
				models.CoursesField,
				models.VotingMilestonesField,
			},
		})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Launchpad_not_found, "Launchpad not found: "+err.Error())
		}
		return nil, e.NewError500(e.Launchpad_find_one_failed, "Find launchpad by ID error: "+err.Error())
	}

	return launchpad, nil
}

func (s *ClpLaunchpadService) FindOne(query *models.LaunchpadQuery, options *models.FindOneOptions) (*models.ClpLaunchpad, *e.AppError) {
	if len(options.Preloads) > 0 {
		if err := models.Repository.ClpLaunchpad(s.ctx).ValidateFields(options.Preloads); err != nil {
			return nil, e.NewError400(e.INVALID_PARAMS, "Preloads field invalid: "+err.Error())
		}
	}

	if launchpad, err := models.Repository.ClpLaunchpad(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Launchpad_not_found, err.Error())
		}
		return nil, e.NewError500(e.Launchpad_find_one_failed, err.Error())
	} else {
		return launchpad, nil
	}
}

func (s *ClpLaunchpadService) Update(launchpad *models.ClpLaunchpad, data *dto.UpdateClpLaunchpadRequest) (*models.ClpLaunchpad, *e.AppError) {
	//updateFundingTime := false
	if data.Categories != nil {
		categories, cErr := categories2Categories(models.TypeCategoryCourse, data.Categories)
		if cErr != nil {
			return nil, cErr
		}

		launchpad.Categories = categories
	}

	if data.Levels != nil {
		levels, cErr := categories2Categories(models.TypeLevel, data.Levels)
		if cErr != nil {
			return nil, cErr
		}

		launchpad.Levels = levels
	}

	if data.Name != nil {
		launchpad.Name = *data.Name
	}

	if data.Description != nil {
		launchpad.Description = *data.Description
	}

	if data.ThumbnailID != nil {
		launchpad.ThumbnailID = *data.ThumbnailID
	}

	if data.PreviewVideoID != nil {
		launchpad.PreviewVideoID = *data.PreviewVideoID
	}

	if data.EstimateFundingDays != nil {
		launchpad.EstimateFundingDays = *data.EstimateFundingDays
	}

	if data.VotingStartDate != nil {
		launchpad.VotingStartDate = *data.VotingStartDate
	}

	if data.VotingEndDate != nil {
		launchpad.VotingEndDate = *data.VotingEndDate
	}

	if data.MinPledge != nil {
		// TODO
		//if !util.IsMultipleOfFive(*data.MinPledge) {
		//	return nil, e.NewError400(e.Min_pledge_is_invalid, "min pledge must is multiple of five")
		//}

		launchpad.FundingGoal.MinPledge = *data.MinPledge
	}

	if data.Currency != nil {
		launchpad.FundingGoal.Currency = *data.Currency
	}

	if data.TargetFunding != nil {
		if data.TargetFunding.LessThanOrEqual(decimal.Zero) {
			return nil, e.NewError400(e.Update_launchpad_failed, "Target funding invalid")
		}
		launchpad.FundingGoal.TargetFunding = *data.TargetFunding
	}

	if data.ProfitPercentage != nil {
		launchpad.FundingGoal.ProfitPercentage = *data.ProfitPercentage
	}

	if len(data.VotingMilestones) > 0 {
		// Validate milestones
		if err := validateVotingMilestones(data.VotingMilestones, launchpad); err != nil {
			return nil, e.NewError400(e.Voting_milestone_invalid, err.Error())
		}

		// get voting milestone in DB
		currentVotingMilestones, err := models.Repository.ClpVotingMilestone.FindMany(&models.ClpVotingMilestoneQuery{
			ClpLaunchpadID: &launchpad.ID,
		}, nil)
		if err != nil {
			return nil, e.NewError500(e.Voting_milestone_find_many_failed, err.Error())
		}

		// if no voting in DB, create new
		if len(currentVotingMilestones) == 0 {
			newVotingMilestones := make([]*models.ClpVotingMilestone, len(data.VotingMilestones))
			for i, reqVotingMilestone := range data.VotingMilestones {
				newVotingMilestones[i] = &models.ClpVotingMilestone{
					ClpLaunchpadID:        launchpad.ID,
					Order:                 reqVotingMilestone.Order,
					EstimatedOpenVoteDate: reqVotingMilestone.EstimatedOpenVoteDate,
					TargetSection:         reqVotingMilestone.TargetSection,
					Status:                models.VotingMilestonePending,
				}
			}

			// Bulk create
			if err := models.Repository.ClpVotingMilestone.CreateMany(newVotingMilestones, nil); err != nil {
				return nil, e.NewError500(e.Create_voting_milestone_failed, err.Error())
			}

		} else {
			currentVotingMilestoneMap := make(map[string]*models.ClpVotingMilestone)
			for _, votingMilestone := range currentVotingMilestones {
				currentVotingMilestoneMap[votingMilestone.ID] = votingMilestone
			}

			requestVotingMilestoneIDs := make(map[string]bool)
			for _, reqVotingMilestone := range data.VotingMilestones {
				if reqVotingMilestone.ID == "" {
					newVotingMilestone := &models.ClpVotingMilestone{
						ClpLaunchpadID:        launchpad.ID,
						Order:                 reqVotingMilestone.Order,
						EstimatedOpenVoteDate: reqVotingMilestone.EstimatedOpenVoteDate,
						TargetSection:         reqVotingMilestone.TargetSection,
						Status:                models.VotingMilestonePending,
					}

					if err := models.Repository.ClpVotingMilestone.Create(newVotingMilestone, nil); err != nil {
						return nil, e.NewError500(e.Create_voting_milestone_failed, err.Error())
					}
				} else {
					if currentVotingMilestone, exists := currentVotingMilestoneMap[reqVotingMilestone.ID]; exists {
						currentVotingMilestone.Order = reqVotingMilestone.Order
						currentVotingMilestone.EstimatedOpenVoteDate = reqVotingMilestone.EstimatedOpenVoteDate
						currentVotingMilestone.TargetSection = reqVotingMilestone.TargetSection
						currentVotingMilestone.Status = models.VotingMilestonePending

						if err := models.Repository.ClpVotingMilestone.Update(currentVotingMilestone, nil); err != nil {
							return nil, e.NewError500(e.Update_voting_milestone_failed, err.Error())
						}
					} else {
						return nil, e.NewError400(e.Voting_milestone_not_found, "Voting milestone ID not found: "+reqVotingMilestone.ID)
					}
					requestVotingMilestoneIDs[reqVotingMilestone.ID] = true
				}
			}

			// Delete voting milestone in DB
			for _, currentVotingMilestone := range currentVotingMilestones {
				if !requestVotingMilestoneIDs[currentVotingMilestone.ID] {
					if err := models.Repository.ClpVotingMilestone.Delete(currentVotingMilestone.ID, nil); err != nil {
						return nil, e.NewError500(e.Delete_voting_milestone_failed, err.Error())
					}
				}
			}
		}
	}

	//if launchpad.HasPool() && updateFundingTime {
	//	if aErr := s.UpdatePoolFundingTime(&dto.UpdateLpPoolFundingTimeRequest{
	//		PoolID:           launchpad.Props.PoolID,
	//		FundingStartDate: launchpad.FundingStartDate,
	//		FundingEndDate:   launchpad.FundingEndDate,
	//	}); aErr != nil {
	//		return nil, e.NewError500(e.Update_launchpad_failed, "Update launchpad pool funding time failed: "+aErr.Error())
	//	}
	//}

	if ucErr := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, nil); ucErr != nil {
		return nil, e.NewError500(e.Update_launchpad_failed, ucErr.Error())
	}

	return launchpad, nil
}

func (s *ClpLaunchpadService) Delete(launchpad *models.ClpLaunchpad) *e.AppError {
	if err := models.Repository.ClpLaunchpad(s.ctx).Delete(launchpad.ID, nil); err != nil {
		return e.NewError500(e.Delete_launchpad_failed, "Delete launchpad failed "+err.Error())
	}

	return nil
}

func (s *ClpLaunchpadService) CourseCanMakeLaunchpad(
	user *models.User,
	courseCuid string,
	launchpadID *string,
) (*models.Course, *e.AppError) {
	course, err := models.Repository.Course(s.ctx).FindOne(&models.CourseQuery{
		Cuid:    &courseCuid,
		Latest:  util.NewBool(true),
		Version: util.NewInt(1),
	}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Course_not_found, err.Error())
		}

		return nil, e.NewError500(e.Course_find_one_failed, "find course by schema failed "+err.Error())
	}

	// check 1 course can create 1 launchpad
	courseLaunchpadExisted, err := models.Repository.ClpCourseLaunchpad(s.ctx).FindOne(&models.ClpCourseLaunchpadQuery{
		ClpLaunchpadIDNe: launchpadID,
		CourseCuid:       &course.Cuid,
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Course_launchpad_find_one_failed, "find course launchpad failed "+err.Error())
	}

	if courseLaunchpadExisted != nil {
		return nil, e.NewError400(e.Course_launchpad_is_existed, "Course launchpad is existed")
	}

	// owner can make launchpad
	if user.ID != course.UserID && !user.IsSysAdmin() {
		return nil, e.NewError403(e.Not_course_owner, "you are not owner of course")
	}

	if !course.PriceSettings.IsPay {
		return nil, e.NewError400(e.INVALID_PARAMS, "course is must pay to make launchpad")
	}

	// course content must include at least 4 sections
	totalSection, err := models.Repository.Section.Count(&models.SectionQuery{
		ParentIDNull: util.NewBool(true),
		CourseID:     &course.ID,
	})
	if err != nil {
		return nil, e.NewError500(e.Section_count_failed, "count section failed "+err.Error())
	}

	minSections := models.GetConfig[int](models.CourseLaunchpadMinSections)
	if totalSection < int64(minSections) {
		return nil, e.NewError400(e.Not_enough_section_to_make_course_launchpad, "course not enough section")
	}

	return course, nil
}

func validateVotingMilestones(milestones []*dto.VotingMilestoneRequest, launchpad *models.ClpLaunchpad) error {
	if len(milestones) == 0 {
		return fmt.Errorf("milestones cannot be empty")
	}

	// check config target funding with total voting milestone
	votingMilestoneRequired := launchpad.GetMaxVotingPhases()
	if len(milestones) != votingMilestoneRequired {
		return fmt.Errorf("launchpad required %d voting plan", votingMilestoneRequired)
	}

	// Sort milestones by order
	sort.Slice(milestones, func(i, j int) bool {
		return milestones[i].Order < milestones[j].Order
	})

	// Check first milestone order
	if milestones[0].Order != 1 {
		return fmt.Errorf("first milestone must have order 1, got %d", milestones[0].Order)
	}

	// validate publish date of milestones
	for i, milestone := range milestones {
		// Check if order is sequential (should be i+1 after sorting)
		if milestone.Order != i+1 {
			return fmt.Errorf("milestone orders must be sequential, expected order %d but got %d", i+1, milestone.Order)
		}

		// Check publish date with previous milestone
		if i > 0 && milestone.EstimatedOpenVoteDate <= milestones[i-1].EstimatedOpenVoteDate {
			return fmt.Errorf("milestone %d publish date must be greater than previous milestone (current: %d, previous: %d)",
				milestone.Order, milestone.EstimatedOpenVoteDate, milestones[i-1].EstimatedOpenVoteDate)
		}
	}

	return nil
}

func (s *ClpLaunchpadService) GetLaunchpadPartnerProfile(userID string) (*models.LaunchpadPartner, *e.AppError) {
	user, err := models.Repository.User.FindByIDWithOpts(userID, &models.FindOneOptions{
		Preloads: []string{models.SummaryField},
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Error_user_not_found, "User not found")
		}
		return nil, e.NewError500(e.Error_user_find_failed, "Find user by ID failed"+err.Error())
	}

	if userRoles, rErr := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		UserID: &user.ID,
	}, &models.FindManyOptions{}); rErr != nil {
		return nil, e.NewError500(e.Find_user_role_failed, "Find by userID failed"+rErr.Error())
	} else {
		lo.ForEach(userRoles, func(item *models.UserRoleOrg, _ int) {
			org, _ := models.Repository.Organization.FindByID(item.OrgID, nil)
			if org != nil {
				item.OrgDomain = org.Domain
			}
		})
		user.Roles = userRoles
	}

	userProfile, bErr := buildUserProfileResponse(user, user.Summary)
	if bErr != nil {
		return nil, bErr
	}

	// handle return role_id and org_id, org_name
	var orgIDs []string
	lo.ForEach(userProfile.Roles, func(item *models.SimpleUserRole, _ int) {
		orgIDs = append(orgIDs, item.OrgID)
	})

	if len(orgIDs) > 0 {
		// Get many org by ids
		uniqOrgIDs := lo.Uniq(orgIDs)
		orgs, err := models.Repository.Organization.FindMany(
			&models.OrganizationQuery{IDIn: uniqOrgIDs},
			&models.FindManyOptions{})
		if err != nil {
			return nil, e.NewError500(e.Organization_find_page_failed, "find many org failed"+err.Error())
		}

		// set name of org for user role
		mapOrgs := make(map[string]string, len(orgs))
		lo.ForEach(orgs, func(org *models.Organization, _ int) {
			mapOrgs[org.ID] = org.Name

		})

		lo.ForEach(userProfile.Roles, func(us *models.SimpleUserRole, _ int) {
			us.OrgName = mapOrgs[us.OrgID]
		})
	}

	return userProfile.ToLaunchpadPartner(), nil
}

func (s *ClpLaunchpadService) FindLaunchpadPartner(launchpad *models.ClpLaunchpad, options *models.FindPageOptions) (*dto.ListLaunchpadPartnerResponse, *e.AppError) {
	ownerProfile, cErr := s.GetLaunchpadPartnerProfile(launchpad.UserID)
	if cErr != nil {
		return nil, cErr
	}

	resp := &dto.ListLaunchpadPartnerResponse{
		OwnerProfile: ownerProfile,
	}

	query := &models.CoursePartnerQuery{}
	if len(launchpad.Courses) > 0 {
		lo.ForEach(launchpad.Courses, func(item *models.Course, index int) {
			query.CourseIDIn = append(query.CourseIDIn, item.Cuid)
		})

		options.Preloads = []string{"Partner"}
		partners, _, cErr := Course.FindCoursePartners(query, options)
		if cErr != nil {
			return nil, cErr
		}

		userIDs := lo.Uniq(lo.Map(partners, func(p *models.CoursePartner, _ int) string {
			return p.PartnerID
		}))

		// remove owner in list partner
		userIDs = lo.Filter(userIDs, func(userID string, _ int) bool {
			return userID != launchpad.UserID
		})

		for _, userID := range userIDs {
			launchpadPartner, cErr := ClpLaunchpad.GetLaunchpadPartnerProfile(userID)
			if cErr != nil {
				return nil, cErr
			}

			resp.Partners = append(resp.Partners, launchpadPartner)
		}
	}

	return resp, nil
}

func (s *ClpLaunchpadService) AssignUserStatusToLaunchpads(user *models.User, launchpads []*models.ClpLaunchpad) *e.AppError {
	if len(launchpads) == 0 {
		return nil
	}

	launchpadIDs := lo.Uniq(lo.Map(launchpads, func(p *models.ClpLaunchpad, _ int) string { return p.ID }))
	bookmarks, err := models.Repository.Bookmark.FindMany(&models.BookmarkQuery{
		EntityIDIn: launchpadIDs,
		EntityType: util.NewT(models.ClpLaunchpadModelName),
		UserID:     &user.ID,
	}, &models.FindManyOptions{})
	if err != nil {
		return e.NewError500(e.Bookmark_find_failed, "Find the list launchpad bookmarks error: "+err.Error())
	}

	hasBookmarkedByLaunchpadIDs := map[string]bool{}
	for _, bookmark := range bookmarks {
		hasBookmarkedByLaunchpadIDs[bookmark.EntityID] = true
	}

	for _, launchpad := range launchpads {
		launchpad.Bookmarked = hasBookmarkedByLaunchpadIDs[launchpad.ID]
	}
	return nil
}

func (s *ClpLaunchpadService) RequestPublishLaunchpad(req *dto.PublishLaunchpadRequest) (*models.Approval, *e.AppError) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if len(req.Launchpad.Courses) <= 0 || len(req.Launchpad.VotingMilestones) <= 0 {
		tx.Rollback()
		return nil, e.NewError400(e.Launchpad_invalid_status, "Launchpad invalid: have not course or voting milestone invalid")
	}

	if !req.Launchpad.HasPool() {
		tx.Rollback()
		return nil, e.NewError400(e.Launchpad_pool_required, "Launchpad must be initialized the pool before publishing")
	}

	if req.Launchpad.EstimateFundingDays <= 0 {
		tx.Rollback()
		return nil, e.NewError400(e.Launchpad_estimate_funding_days_invalid, "Launchpad estimate funding days invalid")
	}

	var approval *models.Approval
	if req.Launchpad.Status == models.LaunchpadSTTDraft {
		uid := util.GenerateId()
		pub := &dto.CreateApprovalRequest{
			EntityType: models.ClpLaunchpadModelName,
			EntityID:   req.Launchpad.ID,
			Type:       models.ApproveTypePublishOrg,
			RequestUid: uid,
		}

		if a, err := Approval.CreateWithTrans(req.Org, req.Requester, pub, tx); err != nil {
			tx.Rollback()
			return nil, err
		} else {
			approval = a
		}
	}

	// update status launchpad to reviewing
	req.Launchpad.Status = models.LaunchpadSTTReviewing
	if udErr := models.Repository.ClpLaunchpad(s.ctx).Update(req.Launchpad, tx); udErr != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Update_launchpad_failed, "Update launchpad failed: "+udErr.Error())
	}

	// update pool
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &req.Launchpad.Props.WalletID}, nil)
	if aErr != nil {
		tx.Rollback()
		return nil, e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
	}

	_, err := openedu_chain.Transaction.UpdateLpPoolStatus(&chaindto.UpdateLaunchpadPoolStatusRequest{
		PoolID:    req.Launchpad.GetPoolID(),
		Status:    chaindto.PoolStatusInit,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		tx.Rollback()
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return nil, e.NewError500(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return nil, e.NewError500(e.Launchpad_update_pool_status_failed, "Update launchpad pool status error: "+err.Error())
		}
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.ERROR, "Commit transaction error: "+err.Error())
	}
	return approval, nil
}

func (s *ClpLaunchpadService) PushNotificationsForReqPublish(launchpad *models.ClpLaunchpad, approval *models.Approval, requester *models.User) {
	rootOrg := Organization.GetRoot()
	userIDs, err := Organization.FindOrgAdminIDs(rootOrg)
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::PushNotificationsForReqPublish get list org admins failed: %w", err)
		return
	}

	notiProps := s.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, requester)
	notiProps["approval_id"] = approval.ID
	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeNewPublishLaunchpadRequestForAdmin,
		EntityID:   approval.EntityID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: userIDs,
			},
		},
		Props: notiProps,
	}
	if err = communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::PushNotificationsForReqPublish push notifications failed: %w", err)
	}
	return
}

func (s *ClpLaunchpadService) MakeNotificationPropsForLaunchpad(
	launchpad *models.ClpLaunchpad,
	org *models.Organization,
	user *models.User,
) communicationdto.JSONB {

	props := communicationdto.JSONB{
		"org_id":         org.ID,
		"org_name":       org.Name,
		"org_domain":     org.Domain,
		"launchpad_id":   launchpad.ID,
		"launchpad_name": launchpad.Name,
		"launchpad_slug": launchpad.Slug,
	}
	if user != nil {
		props["user_id"] = user.ID
		props["username"] = user.Username
		props["user_name"] = user.Username
	}
	return props
}

func (s *ClpLaunchpadService) RejectPublishLaunchpad(user *models.User, approval *models.Approval) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	launchpad, cErr := models.Repository.ClpLaunchpad(s.ctx).FindOne(&models.LaunchpadQuery{
		ID: &approval.EntityID,
	}, nil)
	if cErr != nil {
		tx.Rollback()
		return e.NewError400(e.Launchpad_reject_failed, "find launchpad failed: "+cErr.Error())
	}

	now := time.Now().UnixMilli()
	if approval.Type == models.ApproveTypePublishOrg {
		launchpad.Props.PubRejectDate = now
		launchpad.Props.RejectOrgReason = approval.Note
	} else if approval.Type == models.ApproveTypePublishRoot {
		launchpad.Props.PubRootRejectDate = now
		launchpad.Props.RejectRootReason = approval.Note
	}

	launchpad.Status = models.LaunchpadSTTDraft
	if udErr := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, tx); udErr != nil {
		tx.Rollback()
		return e.NewError500(e.Course_reject_failed, "Update course current version failed: "+udErr.Error())
	}

	// update pool
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		tx.Rollback()
		return e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
	}

	_, err := openedu_chain.Transaction.UpdateLpPoolStatus(&chaindto.UpdateLaunchpadPoolStatusRequest{
		PoolID:    launchpad.GetPoolID(),
		Status:    chaindto.PoolStatusRejected,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		tx.Rollback()
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return e.NewError500(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return e.NewError500(e.Launchpad_update_pool_status_failed, "Update launchpad pool status error: "+err.Error())
		}
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for reject launchpad error: "+err.Error())
	}

	go func() {
		s.pushNotifForLaunchpadReviewed(launchpad, approval, false)
	}()

	return nil
}

func (s *ClpLaunchpadService) pushNotifForLaunchpadReviewed(launchpad *models.ClpLaunchpad, approval *models.Approval, isApproved bool) {
	rootOrg := Organization.GetRoot()

	launchpadOwner, err := models.Repository.User.FindByID(launchpad.UserID)
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifForLaunchpadRejected Find launchpad owner failed: %v", err)
		return
	}

	code := lo.If(isApproved, communicationdto.CodeRequestPublishLaunchpadApprovedForCreator).Else(communicationdto.CodeRequestPublishLaunchpadRejectedForCreator)
	notiProps := s.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, launchpadOwner)
	notiProps["approval_id"] = approval.ID
	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.NotificationCode(code),
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{launchpad.UserID},
			},
		},
		Props: notiProps,
	}
	if err = communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifForLaunchpadRejected push notifications failed: %v", err)
	}
	return
}

func (s *ClpLaunchpadService) PublishLaunchpad(user *models.User, approval *models.Approval) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	launchpad, cErr := models.Repository.ClpLaunchpad(s.ctx).FindOne(&models.LaunchpadQuery{
		ID: util.NewString(approval.EntityID),
	}, nil)
	if cErr != nil {
		tx.Rollback()
		return e.NewError400(e.Launchpad_not_found, "Find launchpad by ID "+approval.EntityID+" error: "+cErr.Error())
	}

	now := time.Now().UnixMilli()
	if approval.Type == models.ApproveTypePublishRoot {
		launchpad.Props.PubRootDate = now
	} else if approval.Type == models.ApproveTypePublishOrg {
		launchpad.Props.PubDate = now
		launchpad.Props.PubRootDate = now
	}

	//launchpad.Status = models.LaunchpadSTTPublish
	launchpad.Status = models.LaunchpadSTTApproved
	if cErr = models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, tx); cErr != nil {
		tx.Rollback()
		return e.NewError500(e.Update_launchpad_failed, "Update launchpad failed: "+cErr.Error())
	}

	if aErr := s.ApprovePool(&dto.ApproveLaunchpadPoolRequest{
		IsApproved: true,
		Launchpad:  launchpad,
	}); aErr != nil {
		tx.Rollback()
		return e.NewError500(e.Update_launchpad_failed, "Update launchpad pool status failed: "+aErr.Error())
	}

	if aErr := s.createCheckSettingFundingTimeJob(launchpad); aErr != nil {
		log.Errorf("Create scheduled job to check creator set funding time after approving publish launchpad ID %s error: %v", launchpad.ID, aErr)
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for publish launchpad error: "+err.Error())
	}

	go func() {
		s.pushNotifForLaunchpadReviewed(launchpad, approval, true)
	}()

	return nil
}

func (s *ClpLaunchpadService) CancelRequest(launchpad *models.ClpLaunchpad) *e.AppError {
	if launchpad.Status != models.LaunchpadSTTReviewing {
		return e.NewError400(e.Launchpad_is_not_reviewing, "launchpad is not reviewing")
	}

	approvals, aErr := Approval.FindMany(&models.ApprovalQuery{
		EntityType: util.NewString(string(models.ClpLaunchpadModelName)),
		EntityID:   util.NewString(launchpad.ID),
	}, nil)
	if aErr != nil {
		return aErr
	}

	for _, approval := range approvals {
		if approval.Status == models.ApprovalStatusNew {
			approval.Status = models.ApprovalStatusCancel
			if uErr := Approval.Update(approval); uErr != nil {
				return uErr
			}
		}
	}

	launchpad.Status = models.LaunchpadSTTDraft
	if uErr := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, nil); uErr != nil {
		return e.NewError500(e.Update_launchpad_failed, uErr.Error())
	}

	return nil
}

func (s *ClpLaunchpadService) CanUpdate(launchpad *models.ClpLaunchpad, user *models.User) *e.AppError {
	if user.ID != launchpad.UserID && !user.IsSysAdmin() {
		return e.NewError403(e.Not_launchpad_owner, "you are not owner of launchpad")
	}

	return nil
}

func (s *ClpLaunchpadService) GetLaunchpadDetail(launchpadID string, options *models.FindOneOptions) (*models.ClpLaunchpad, *e.AppError) {
	if len(options.Preloads) > 0 {
		if err := models.Repository.ClpLaunchpad(s.ctx).ValidateFields(options.Preloads); err != nil {
			return nil, e.NewError400(e.INVALID_PARAMS, "Preloads field invalid: "+err.Error())
		}
	}

	shouldPreloadInvestment := false
	if lo.Contains(options.Preloads, models.InvestmentField) {
		shouldPreloadInvestment = true
		options.Preloads = util.RemoveElement(options.Preloads, models.InvestmentField)
	}

	shouldPreloadOwnerProfile := false
	if lo.Contains(options.Preloads, models.OwnerProfileField) {
		shouldPreloadOwnerProfile = true
		options.Preloads = util.RemoveElement(options.Preloads, models.OwnerProfileField)
	}

	shouldPreloadOutline := false
	if lo.Contains(options.Preloads, models.OutlineField) {
		shouldPreloadOutline = true
		options.Preloads = util.RemoveElement(options.Preloads, models.OutlineField)
	}

	launchpad, aErr := ClpLaunchpad.FindOne(&models.LaunchpadQuery{
		ID: &launchpadID,
	}, options)
	if aErr != nil {
		return nil, aErr
	}

	if shouldPreloadOwnerProfile {
		ownerProfile, aErr := ClpLaunchpad.GetLaunchpadPartnerProfile(launchpad.UserID)
		if aErr != nil {
			return nil, aErr
		}

		launchpad.OwnerProfile = ownerProfile
	}

	if shouldPreloadOutline && len(launchpad.Courses) > 0 {
		course, aErr := Course.GetOutline(launchpad.Courses[0])
		if aErr != nil {
			return nil, aErr
		}

		launchpad.Outline = append(launchpad.Outline, course.Outline...)
	}

	if shouldPreloadInvestment {
		if appErr := s.GetInvestmentStats([]*models.ClpLaunchpad{launchpad}); appErr != nil {
			return nil, appErr
		}
	}

	return launchpad, nil
}

func (s *ClpLaunchpadService) validateBeforeOpenVoting(req *dto.OpenVotingMilestoneRequest) *e.AppError {
	// Check user is launchpad's owner
	if req.User.ID != req.Launchpad.UserID {
		return e.NewError400(e.Not_launchpad_owner, "Launchpad owner required")
	}

	// Check launchpad status is voting
	if !req.Launchpad.IsStatusVoting() {
		return e.NewError400(e.Course_launchpad_not_allow_open_voting,
			fmt.Sprintf("Launchpad with status %s is not allowed to open voting", req.Launchpad.Status))
	}

	// Check voting milestone status
	if req.VotingMilestone.IsStatusRunning() {
		return e.NewError400(e.Voting_milestone_already_running, "Voting milestone ID "+req.VotingMilestone.ID+" already running")
	}

	if req.VotingMilestone.IsStatusFailed() {
		return e.NewError400(e.Voting_milestone_already_failed, "Voting milestone ID "+req.VotingMilestone.ID+" already failed")
	}

	if req.VotingMilestone.IsStatusCompleted() {
		return e.NewError400(e.Voting_milestone_already_completed, "Voting milestone ID "+req.VotingMilestone.ID+" already completed")
	}

	// Check if any milestones are opening voting
	sort.Slice(req.Launchpad.VotingMilestones, func(i, j int) bool {
		return req.Launchpad.VotingMilestones[i].Order < req.Launchpad.VotingMilestones[j].Order
	})
	for _, milestone := range req.Launchpad.VotingMilestones {
		if milestone.Order >= req.VotingMilestone.Order {
			break
		}

		switch milestone.Status {
		case models.VotingMilestonePending:
			return e.NewError400(e.Voting_milestone_open_wrong_order,
				fmt.Sprintf("Previous voting milestone ID %s (status=%s) must be completed before", milestone.ID, milestone.Status))

		case models.VotingMilestoneRunning:
			return e.NewError400(e.Course_launchpad_already_in_a_voting,
				fmt.Sprintf("Previous voting milestone ID %s (status=%s) must be completed before", milestone.ID, milestone.Status))

		case models.VotingMilestoneFailed:
			return e.NewError400(e.Course_launchpad_already_failed,
				fmt.Sprintf("Launchpad is already failed with the voting milestone ID %s (status=%s)", milestone.ID, milestone.Status))
		}
	}

	// TODO Check course version is different to last voting milestone

	return nil
}

func (s *ClpLaunchpadService) StartVoting(req *dto.OpenVotingMilestoneRequest) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if aErr := s.validateBeforeOpenVoting(req); aErr != nil {
		tx.Rollback()
		return aErr
	}

	// TODO use transaction
	// Update voting milestone
	now := time.Now()
	votingDuration := models.GetConfig[time.Duration](models.CourseLaunchpadVotingDuration)
	milestone := req.VotingMilestone
	milestone.Status = models.VotingMilestoneRunning
	milestone.OpenVoteDate = now.UnixMilli()
	milestone.EndVoteDate = now.Add(votingDuration).UnixMilli()
	milestone.Props.CourseIDs = lo.Map(req.Launchpad.Courses, func(course *models.Course, _ int) string {
		return course.ID
	})
	if err := models.Repository.ClpVotingMilestone.Update(milestone, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Update_voting_milestone_failed, "Update voting milestone error: "+err.Error())
	}

	// Create voting phase
	amountsByMilestoneIDs := s.calcAmountPerVotingMilestone(req.Launchpad)
	votingPhase := &models.ClpVotingPhase{
		ClpLaunchpadID:       req.Launchpad.ID,
		ClpVotingMilestoneID: milestone.ID,
		Status:               "", // TODO
		Amount:               amountsByMilestoneIDs[milestone.ID],
		TotalApprove:         0,
		TotalReject:          0,
	}
	if err := models.Repository.ClpVotingPhase.Create(votingPhase, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Create_voting_phase_failed, "Create voting phase error: "+err.Error())
	}

	// create cron check end voting milestone
	// create job end voting
	curlArgs := &openedu_scheduler.CurlArgs{
		URL:    setting.ServerSetting.HostDomain + EndVotingMilestonePath,
		Method: http.MethodPost,
		Headers: map[string]string{
			"X-api-key": setting.AppSetting.ApiKey,
		},
		Body: map[string]interface{}{
			"milestone_id":    milestone.ID,
			"launchpad_id":    req.Launchpad.ID,
			"voting_phase_id": votingPhase.ID,
		},
	}

	if err := openedu_scheduler.Scheduled.CreateScheduledJob(&openedu_scheduler.ScheduledJobRequest{
		Name:       EndVotingMilestoneJobName,
		Key:        openedu_scheduler.EndVotingMilestoneJobKey,
		Type:       openedu_scheduler.OnceJobType,
		Args:       curlArgs.ToJSONB(),
		ScheduleAt: milestone.EndVoteDate,
		Active:     true,
	}); err != nil {
		if nErr := s.notifyCreateScheduledJobFailed(string(models.ClpLaunchpadModelName), req.Launchpad.ID, EndVotingMilestoneJobName, err); nErr != nil {
			log.Errorf("Notify create scheduled job failed error: %v", nErr)
		}
		log.Errorf("Create scheduled job to end voting milestone %s for launchpad ID %s error: %v", milestone.ID, milestone.ClpLaunchpadID, err)
	}

	// Find list backers

	// TODO Push notifications to backers
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for start voting error: "+err.Error())
	}

	go func(launchpad *models.ClpLaunchpad) {
		s.pushNotifWhenStartVoting(launchpad)
	}(req.Launchpad)

	return nil
}

func (s *ClpLaunchpadService) pushNotifWhenStartVoting(launchpad *models.ClpLaunchpad) {
	rootOrg := Organization.GetRoot()

	investments, err := models.Repository.ClpInvestment(s.ctx).FindMany(&models.ClpInvestmentQuery{
		ClpLaunchpadID: &launchpad.ID,
	}, &models.FindManyOptions{})
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenStartVoting find list investments failed: %w", err)
		return
	}

	if len(investments) == 0 {
		return
	}

	userIDs := lo.Map(investments, func(investment *models.ClpInvestment, _ int) string {
		return investment.UserID
	})
	userIDs = lo.Uniq(userIDs)

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeLaunchpadVotingStartedForBacker,
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: userIDs,
			},
		},
		Props: s.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, nil),
	}
	if err = communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenStartVoting push notifications failed: %w", err)
	}
	return
}

func (s *ClpLaunchpadService) calcAmountPerVotingMilestone(launchpad *models.ClpLaunchpad) map[string]decimal.Decimal {
	result := map[string]decimal.Decimal{}
	sort.Slice(launchpad.VotingMilestones, func(i, j int) bool {
		return launchpad.VotingMilestones[i].Order < launchpad.VotingMilestones[j].Order
	})

	resp, _ := ClpInvestment.FindInvestmentsLaunchpad(launchpad, &models.FindPageOptions{})
	fundingAmount := resp.TotalAmount
	numMilestones := decimal.NewFromInt(int64(len(launchpad.VotingMilestones)))
	sum := decimal.Zero
	for idx, milestone := range launchpad.VotingMilestones {
		if idx == len(launchpad.VotingMilestones)-1 {
			result[milestone.ID] = fundingAmount.Sub(sum)
			continue
		}

		result[milestone.ID] = fundingAmount.Div(numMilestones)
		sum = sum.Add(result[milestone.ID])
	}
	return result
}

func (s *ClpLaunchpadService) GetInvestmentStats(launchpads []*models.ClpLaunchpad) *e.AppError {
	if len(launchpads) == 0 {
		return nil
	}

	launchpadIDs := lo.Uniq(lo.Map(launchpads, func(p *models.ClpLaunchpad, _ int) string { return p.ID }))
	stats, err := models.Repository.ClpInvestment(s.ctx).GetInvestmentStatsByLaunchpadIDs(launchpadIDs)
	if err != nil {
		return e.NewError500(e.Find_investment_failed, "Get investment stats for list launchpad failed "+err.Error())
	}

	mapStats := make(map[string]*models.LaunchpadStats, len(stats))
	if len(stats) > 0 {
		for _, stat := range stats {
			mapStats[stat.ClpLaunchpadID] = stat
		}
	}

	lo.ForEach(launchpads, func(launchpad *models.ClpLaunchpad, _ int) {
		if mapStats[launchpad.ID] != nil {
			launchpad.TotalAmount = util.NewT(mapStats[launchpad.ID].TotalAmount)
			launchpad.TotalBackers = util.NewInt(mapStats[launchpad.ID].TotalBackers)
		} else {
			launchpad.TotalAmount = util.NewT(decimal.Zero)
			launchpad.TotalBackers = util.NewInt(0)
		}
	})

	return nil
}

func (s *ClpLaunchpadService) ApprovePool(req *dto.ApproveLaunchpadPoolRequest) *e.AppError {
	_, err := openedu_chain.Transaction.ApproveLaunchpadPool(&chaindto.ApproveLaunchpadPoolRequest{
		PoolID:     req.Launchpad.Props.PoolID,
		IsApproved: req.IsApproved,
		Network:    chaindto.BlockchainNetwork(models.BlockchainNetworkNEAR),
		IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return e.NewError500(e.Launchpad_init_pool_failed, "Update launchpad pool status error: "+err.Error())
		}
	}
	return nil
}

//func (s *ClpLaunchpadService) UpdatePoolFundingTime(req *dto.UpdateLpPoolFundingTimeRequest) *e.AppError {
//	_, err := openedu_chain.Transaction.UpdateLpPoolFundingTime(&openedu_chain.UpdateLpPoolFundingTimeRequest{
//		PoolID:           req.PoolID,
//		FundingStartDate: req.FundingStartDate,
//		FundingEndDate:   req.FundingEndDate,
//		Network:          openedu_chain.BlockchainNetwork(models.BlockchainNetworkNEAR),
//		IsMainnet:        setting.OpenEduChainSetting.IsMainnet,
//	})
//	if err != nil {
//		switch {
//		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
//			return e.NewError400(e.WalletInsufficientGasFee,
//				"Balance is not enough to cover the gas fee: "+err.Error())
//
//		default:
//			return e.NewError500(e.Launchpad_update_pool_funding_time_failed, "Update launchpad pool funding time error: "+err.Error())
//		}
//	}
//	return nil
//}

func (s *ClpLaunchpadService) Cancel(req *dto.CancelLaunchpadRequest) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if !req.Launchpad.IsStatusDraft() {
		tx.Rollback()
		return e.NewError400(e.Launchpad_invalid_status, "Can only cancel draft launchpad: "+string(req.Launchpad.Status))
	}

	if req.Launchpad.HasPool() {
		wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &req.Launchpad.Props.WalletID}, nil)
		if aErr != nil {
			tx.Rollback()
			return e.NewError500(e.Launchpad_cancel_failed, "Find the wallet failed: "+aErr.Error())
		}

		_, err := openedu_chain.Transaction.CancelLaunchpadPool(&chaindto.CancelLpPoolRequest{
			WalletID:  wallet.BlockchainWalletID,
			PoolID:    req.Launchpad.Props.PoolID,
			IsMainnet: setting.OpenEduChainSetting.IsMainnet,
		})
		if err != nil {
			tx.Rollback()
			switch {
			case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
				return e.NewError400(e.WalletInsufficientGasFee,
					"Balance is not enough to cover the gas fee: "+err.Error())

			default:
				return e.NewError500(e.Launchpad_cancel_failed, "Cancel launchpad error: "+err.Error())
			}
		}
	}

	req.Launchpad.Status = models.LaunchpadSTTCancelled
	if err := models.Repository.ClpLaunchpad(s.ctx).Update(req.Launchpad, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Launchpad_cancel_failed, "Update launchpad status failed: "+err.Error())
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for cancel launchpad error: "+err.Error())
	}

	return nil
}

func (s *ClpLaunchpadService) StartFunding(launchpadID string) *e.AppError {
	launchpad, appErr := s.FindByID(launchpadID)
	if appErr != nil {
		return appErr
	}

	launchpad.Status = models.LaunchpadSTTFunding
	err := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, nil)
	if err != nil {
		return e.NewError500(e.Update_launchpad_failed, "Update status funding failed"+err.Error())
	}

	return nil
}

func (s *ClpLaunchpadService) GetVotingPowers(launchpad *models.ClpLaunchpad) ([]*models.LaunchpadVotingPowerEntry, *e.AppError) {
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		return nil, e.NewError500(e.Launchpad_get_voting_powers_failed, "Find the wallet failed: "+aErr.Error())
	}

	resp, err := openedu_chain.Launchpad.GetVotingPowersByPool(&chaindto.GetVotingPowersRequest{
		PoolID:    launchpad.Props.PoolID,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		default:
			return nil, e.NewError500(e.Launchpad_get_voting_powers_failed, "Get launchpad voting powers error: "+err.Error())
		}
	}

	return lo.Map(resp.VotingPowers, func(item *chaindto.VotingPowerEntry, _ int) *models.LaunchpadVotingPowerEntry {
		return &models.LaunchpadVotingPowerEntry{
			UserID:      item.UserID,
			Address:     item.Address,
			Amount:      item.Amount,
			TotalAmount: item.TotalAmount,
			VotingPower: item.VotingPower,
			Network:     wallet.Network,
		}
	}), nil
}

func (s *ClpLaunchpadService) CheckFundingResult(launchpad *models.ClpLaunchpad) (string, *e.AppError) {
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		return "", e.NewError500(e.Launchpad_cancel_failed, "Find the wallet failed: "+aErr.Error())
	}

	resp, err := openedu_chain.Transaction.CheckLpFundingResult(&chaindto.CheckLpFundingResultRequest{
		PoolID:           launchpad.GetPoolID(),
		IsWaitingFunding: launchpad.IsStatusFunding(),
		Network:          chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet:        setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return "", e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return "", e.NewError500(e.Launchpad_cancel_failed, "Cancel launchpad pool error: "+err.Error())
		}
	}

	return resp.Status, nil
}

func (s *ClpLaunchpadService) EndFunding(launchpad *models.ClpLaunchpad) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if !launchpad.IsStatusFunding() {
		tx.Rollback()
		return e.NewError500(e.Launchpad_invalid_status, "Launchpad status is have not funding: "+string(launchpad.Status))
	}

	status, aErr := s.CheckFundingResult(launchpad)
	if aErr != nil {
		tx.Rollback()
		return aErr
	}

	var statusInvestment models.StatusInvestment
	switch status {
	case chaindto.PoolStatusFailed:
		launchpad.Status = models.LaunchpadSTTFailed
	case chaindto.PoolStatusWaiting:
		launchpad.Status = models.LaunchpadSTTWaiting
		// create job check after 3 days
		if aErr := s.createCheckCreatorContinueVotingJob(launchpad); aErr != nil {
			log.Errorf("Create scheduled job to check creator continue voting when end funding for launchpad ID %s error: %v", launchpad.ID, aErr)
		}

	case chaindto.PoolStatusRefunded:
		launchpad.Status = models.LaunchpadSTTRefunded
		statusInvestment = models.GotRefunded
	case chaindto.PoolStatusVoting:
		launchpad.Status = models.LaunchpadSTTVoting
		statusInvestment = models.VotingStatus
	default:
		tx.Rollback()
		return e.NewError500(e.Launchpad_pool_status_invalid, "Pool status is invalid when end funding: "+status)
	}

	if statusInvestment == models.GotRefunded || statusInvestment == models.VotingStatus {
		err := models.Repository.ClpInvestment(s.ctx).UpdateManyInvestmentStatus(statusInvestment, launchpad.ID, tx)
		if err != nil {
			tx.Rollback()
			return e.NewError500(e.Update_investment_failed, "update status for many investments failed "+err.Error())
		}
	}

	if appErr := s.GetInvestmentStats([]*models.ClpLaunchpad{launchpad}); appErr != nil {
		tx.Rollback()
		return appErr
	}

	launchpad.TotalRefunded = *launchpad.TotalAmount
	if err := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Update_launchpad_failed, "Update status voting failed"+err.Error())
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for end funding error: "+err.Error())
	}

	go func() {
		s.pushNotifWhenEndFunding(launchpad)
	}()

	return nil
}

func (s *ClpLaunchpadService) pushNotifWhenEndFunding(launchpad *models.ClpLaunchpad) {
	rootOrg := Organization.GetRoot()

	launchpadOwner, err := models.Repository.User.FindByID(launchpad.UserID)
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenEndFunding Find launchpad owner failed: %w", err)
		return
	}

	var code communicationdto.NotificationCode
	switch launchpad.Status {
	case models.LaunchpadSTTFailed:
		code = communicationdto.CodeLaunchpadFundingFailedForCreator

	case models.LaunchpadSTTWaiting:
		code = communicationdto.CodeLaunchpadFundingWaitingForCreator

	case models.LaunchpadSTTSuccess:
		code = communicationdto.CodeLaunchpadFundingSuccessForCreator

	default:
		return
	}

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       code,
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{launchpad.UserID},
			},
		},
		Props: s.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, launchpadOwner),
	}
	if err = communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenEndFunding push notifications failed: %v", err)
	}
	return
}

func (s *ClpLaunchpadService) ContinueWithPartialFunds(launchpad *models.ClpLaunchpad, isApproved bool) *e.AppError {
	// Check target funding and investment
	investmentStats, err := models.Repository.ClpInvestment(s.ctx).GetInvestmentStats(&models.ClpInvestmentQuery{
		ClpLaunchpadID: &launchpad.ID,
	})
	if err != nil {
		return e.NewError500(e.Get_investment_stats_failed, "Check investment stats failed: "+err.Error())
	}

	if investmentStats.TotalAmount.GreaterThanOrEqual(launchpad.FundingGoal.TargetFunding) {
		return e.NewError400(e.Course_launchpad_reached_target_funding, "Launchpad reached target funding: "+
			"total_amount("+investmentStats.TotalAmount.String()+")"+
			"target_funding("+launchpad.FundingGoal.TargetFunding.String()+")")
	}

	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		return e.NewError500(e.Launchpad_cancel_failed, "Find the wallet failed: "+aErr.Error())
	}

	_, err = openedu_chain.Transaction.ContinueLpPartialFund(&chaindto.ContinueLpPartialFundRequest{
		PoolID:     launchpad.GetPoolID(),
		IsApproved: isApproved,
		Network:    chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return e.NewError500(e.Course_launchpad_continue_with_partial_fund_failed,
				"Continue launchpad with partial target funding error: "+err.Error())
		}
	}
	return nil
}

func (s *ClpLaunchpadService) AssignPledgeStatusToLaunchpad(user *models.User, launchpad *models.ClpLaunchpad) *e.AppError {
	investment, err := models.Repository.ClpInvestment(s.ctx).FindOne(&models.ClpInvestmentQuery{
		ClpLaunchpadID: util.NewString(launchpad.ID),
		UserID:         util.NewString(user.ID),
	}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return e.NewError500(e.Find_investment_failed, "find investment for launchpad failed: "+err.Error())
	}

	if investment != nil {
		launchpad.Investment = investment
	}

	return nil
}

func (s *ClpLaunchpadService) PreloadVotingProcessLaunchpad(user *models.User, launchpad *models.ClpLaunchpad) *e.AppError {
	if len(launchpad.VotingMilestones) <= 0 {
		return nil
	}

	var milestonesResp []*models.ClpVotingMilestone
	for _, milestone := range launchpad.VotingMilestones {
		if milestone.IsStatusRunning() {
			backers, appErr := ClpLaunchpad.GetVotingPowersOffChain(launchpad)
			if appErr != nil {
				return appErr
			}

			votingProcess, appErr := ClpVotingMilestone.GetVotingResultForVotingMilestone(user.ID, backers, milestone)
			if appErr != nil {
				return appErr
			}

			if votingProcess != nil {
				milestone.Voting = votingProcess
			}
		}

		milestonesResp = append(milestonesResp, milestone)
	}

	launchpad.VotingMilestones = milestonesResp
	return nil
}

func (s *ClpLaunchpadService) SetFundingTime(req *dto.SetFundingTimeLaunchpadRequest) (*models.ClpLaunchpad, *e.AppError) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	launchpad := req.Launchpad
	if !launchpad.IsStatusApproved() {
		tx.Rollback()
		return nil, e.NewError400(e.Course_launchpad_not_allow_setting_funding_time, "Launchpad status is "+string(launchpad.Status))
	}

	launchpad.Status = models.LaunchpadSTTPublish
	launchpad.FundingStartDate = req.FundingStartDate
	launchpad.FundingEndDate = s.calcFundingEndDate(req)
	if err := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, tx); err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Update_launchpad_failed, "Update launchpad funding time error: "+err.Error())
	}

	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &req.Launchpad.Props.WalletID}, nil)
	if aErr != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Launchpad_set_funding_time_failed, "Find the wallet failed: "+aErr.Error())
	}

	_, err := openedu_chain.Transaction.SetLpFundingTime(&chaindto.SetLpFundingTimeRequest{
		WalletID:            wallet.BlockchainWalletID,
		PoolID:              launchpad.GetPoolID(),
		FundingStartDate:    launchpad.FundingStartDate,
		FundingDurationDays: launchpad.EstimateFundingDays,
		IsMainnet:           setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		tx.Rollback()
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return nil, e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return nil, e.NewError500(e.Launchpad_set_funding_time_failed, "Set launchpad funding time error: "+err.Error())
		}
	}

	if aErr := s.createStartFundingJob(launchpad); aErr != nil {
		log.Errorf("Create scheduled job to start funding after approving publish launchpad ID %s error: %v", launchpad.ID, aErr)
	}

	if aErr := s.createEndFundingJob(launchpad); aErr != nil {
		log.Errorf("Create scheduled job to end funding after approving publish launchpad ID %s error: %v", launchpad.ID, aErr)
	}

	// TODO push notification to creator

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Commit_transaction_failed, "Commit transaction for set funding time error: "+err.Error())
	}

	return launchpad, nil
}

func (s *ClpLaunchpadService) calcFundingEndDate(req *dto.SetFundingTimeLaunchpadRequest) int64 {
	// End date = Start date + EstimateFundingDuration
	startTime := time.UnixMilli(req.Launchpad.FundingStartDate)
	testLaunchpadIDs := models.GetConfig[[]string](models.CourseLaunchpadTestIDs)
	if lo.Contains(testLaunchpadIDs, req.Launchpad.ID) {
		return startTime.Add(time.Duration(req.Launchpad.EstimateFundingDays) * time.Minute).UnixMilli()
	}
	return startTime.Add(time.Duration(req.Launchpad.EstimateFundingDays) * 24 * time.Hour).UnixMilli()
}

func (s *ClpLaunchpadService) createStartFundingJob(launchpad *models.ClpLaunchpad) *e.AppError {
	// TODO enhance this (too many code) and save job ID to launchpad to debug
	curlArgs := &openedu_scheduler.CurlArgs{
		URL:    setting.ServerSetting.HostDomain + StartFundingPath,
		Method: http.MethodPost,
		Headers: map[string]string{
			"X-api-key": setting.AppSetting.ApiKey,
		},
		Body: map[string]interface{}{
			"id": launchpad.ID,
		},
	}
	if err := openedu_scheduler.Scheduled.CreateScheduledJob(&openedu_scheduler.ScheduledJobRequest{
		Name:       StartFundingLaunchpadJobName,
		Key:        openedu_scheduler.StartFundingLaunchpadJobKey,
		Type:       openedu_scheduler.OnceJobType,
		Args:       curlArgs.ToJSONB(),
		ScheduleAt: launchpad.FundingStartDate,
		Active:     true,
	}); err != nil {
		if nErr := s.notifyCreateScheduledJobFailed(string(models.ClpLaunchpadModelName), launchpad.ID, StartFundingLaunchpadJobName, err); nErr != nil {
			log.Errorf("Notify create scheduled job failed error: %v", nErr)
		}
		return e.NewError500(e.Launchpad_set_funding_time_failed, "Create scheduled job to start funding error: "+err.Error())
	}
	return nil
}

func (s *ClpLaunchpadService) createEndFundingJob(launchpad *models.ClpLaunchpad) *e.AppError {
	// TODO enhance this (too many code) and save job ID to launchpad to debug
	curlArgs := &openedu_scheduler.CurlArgs{
		URL:    setting.ServerSetting.HostDomain + EndFundingPath,
		Method: http.MethodPost,
		Headers: map[string]string{
			"X-api-key": setting.AppSetting.ApiKey,
		},
		Body: map[string]interface{}{
			"id": launchpad.ID,
		},
	}
	if err := openedu_scheduler.Scheduled.CreateScheduledJob(&openedu_scheduler.ScheduledJobRequest{
		Name:       EndFundingLaunchpadJobName,
		Key:        openedu_scheduler.EndFundingLaunchpadJobKey,
		Type:       openedu_scheduler.OnceJobType,
		Args:       curlArgs.ToJSONB(),
		ScheduleAt: launchpad.FundingEndDate,
		Active:     true,
	}); err != nil {
		if nErr := s.notifyCreateScheduledJobFailed(string(models.ClpLaunchpadModelName), launchpad.ID, EndFundingLaunchpadJobName, err); nErr != nil {
			log.Errorf("Notify create scheduled job failed error: %v", nErr)
		}
		return e.NewError500(e.Launchpad_set_funding_time_failed, "Create scheduled job to end funding error: "+err.Error())
	}
	return nil
}

func (s *ClpLaunchpadService) createCheckSettingFundingTimeJob(launchpad *models.ClpLaunchpad) *e.AppError {
	// TODO enhance this (too many code) and save job ID to launchpad to debug
	curlArgs := &openedu_scheduler.CurlArgs{
		URL:    setting.ServerSetting.HostDomain + CheckSettingFundingTimePath,
		Method: http.MethodPost,
		Headers: map[string]string{
			"X-api-key": setting.AppSetting.ApiKey,
		},
		Body: map[string]interface{}{
			"id": launchpad.ID,
		},
	}

	// get from config
	timeForSettingFunding := models.GetConfig[time.Duration](models.WaitingSettingFundingTimeDays)
	scheduledAt := time.Now().Add(timeForSettingFunding).UnixMilli()
	if err := openedu_scheduler.Scheduled.CreateScheduledJob(&openedu_scheduler.ScheduledJobRequest{
		Name:       CheckSettingFundingTimeJobName,
		Key:        openedu_scheduler.CheckSettingFundingTimeJobKey,
		Type:       openedu_scheduler.OnceJobType,
		Args:       curlArgs.ToJSONB(),
		ScheduleAt: scheduledAt,
		Active:     true,
	}); err != nil {
		if nErr := s.notifyCreateScheduledJobFailed(string(models.ClpLaunchpadModelName), launchpad.ID, CheckSettingFundingTimeJobName, err); nErr != nil {
			log.Errorf("Notify create scheduled job failed error: %v", nErr)
		}
		return e.NewError500(e.Launchpad_set_funding_time_failed, "Create job check creator set funding time error: "+err.Error())
	}
	return nil
}

func (s *ClpLaunchpadService) CheckSettingFundingTime(req *dto.FundingLaunchpadRequest) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	launchpad, err := models.Repository.ClpLaunchpad(s.ctx).FindByID(req.ID, nil)
	if err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError404(e.Launchpad_not_found, "Launchpad not found: "+err.Error())
		}
		return e.NewError500(e.Launchpad_find_one_failed, "Find launchpad by ID error: "+err.Error())
	}

	if !launchpad.IsStatusApproved() {
		tx.Rollback()
		log.Errorf("launchpad id: %s status not approved, status is: %s", launchpad.ID, launchpad.Status)
		return nil
	}

	if launchpad.FundingStartDate == 0 && launchpad.FundingEndDate == 0 {
		launchpad.Status = models.LaunchpadSTTRejected
		if err := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, tx); err != nil {
			tx.Rollback()
			return e.NewError500(e.Update_launchpad_failed, "Update launchpad funding time error: "+err.Error())
		}

		// update pool
		wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
		if aErr != nil {
			tx.Rollback()
			return e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
		}

		_, err := openedu_chain.Transaction.UpdateLpPoolStatus(&chaindto.UpdateLaunchpadPoolStatusRequest{
			PoolID:    launchpad.GetPoolID(),
			Status:    chaindto.PoolStatusRejected,
			Network:   chaindto.BlockchainNetwork(wallet.Network),
			IsMainnet: setting.OpenEduChainSetting.IsMainnet,
		})
		if err != nil {
			tx.Rollback()
			switch {
			case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
				return e.NewError500(e.WalletInsufficientGasFee,
					"Balance is not enough to cover the gas fee: "+err.Error())

			default:
				return e.NewError500(e.Launchpad_update_pool_status_failed, "Update launchpad pool status error: "+err.Error())
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for check creator set funding time error: "+err.Error())
	}

	return nil
}

func (s *ClpLaunchpadService) createCheckCreatorContinueVotingJob(launchpad *models.ClpLaunchpad) *e.AppError {
	// TODO enhance this (too many code) and save job ID to launchpad to debug
	curlArgs := &openedu_scheduler.CurlArgs{
		URL:    setting.ServerSetting.HostDomain + CheckCreatorContinueVotingPath,
		Method: http.MethodPost,
		Headers: map[string]string{
			"X-api-key": setting.AppSetting.ApiKey,
		},
		Body: map[string]interface{}{
			"id": launchpad.ID,
		},
	}

	timeForWaitingCreatorProcess := models.GetConfig[time.Duration](models.ClpWaitingProcessFundingDays)
	// testLaunchpadIDs := models.GetConfig[[]string](models.CourseLaunchpadTestIDs)
	// if lo.Contains(testLaunchpadIDs, launchpad.ID) {
	// 	timeForWaitingCreatorProcess = time.Duration(3 * time.Minute)
	// }

	scheduledAt := time.Now().Add(timeForWaitingCreatorProcess).UnixMilli()
	if err := openedu_scheduler.Scheduled.CreateScheduledJob(&openedu_scheduler.ScheduledJobRequest{
		Name:       CheckCreatorContinueVotingJobName,
		Key:        openedu_scheduler.CheckCreatorContinueVotingJobKey,
		Type:       openedu_scheduler.OnceJobType,
		Args:       curlArgs.ToJSONB(),
		ScheduleAt: scheduledAt,
		Active:     true,
	}); err != nil {
		if nErr := s.notifyCreateScheduledJobFailed(string(models.ClpLaunchpadModelName), launchpad.ID, CheckCreatorContinueVotingJobName, err); nErr != nil {
			log.Errorf("Notify create scheduled job failed error: %v", nErr)
		}
		return e.NewError500(e.ERROR, "Create scheduled job to check creator continue voting when end funding error: "+err.Error())
	}
	return nil
}

func (s *ClpLaunchpadService) CheckCreatorContinueVotingLaunchpad(req *dto.FundingLaunchpadRequest) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	launchpad, err := models.Repository.ClpLaunchpad(s.ctx).FindByID(req.ID, nil)
	if err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError404(e.Launchpad_not_found, "Launchpad not found: "+err.Error())
		}
		return e.NewError500(e.Launchpad_find_one_failed, "Find launchpad by ID error: "+err.Error())
	}

	if !launchpad.IsStatusWaiting() {
		tx.Rollback()
		log.Errorf("launchpad id: %s status not waiting, status is: %s", launchpad.ID, launchpad.Status)
		return nil
	}

	launchpad.Status = models.LaunchpadSTTRefunded
	if err := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Update_launchpad_failed, "Update launchpad status to refunded error: "+err.Error())
	}

	// update pool
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		tx.Rollback()
		return e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
	}

	_, err = openedu_chain.Transaction.UpdateLpPoolStatus(&chaindto.UpdateLaunchpadPoolStatusRequest{
		PoolID:    launchpad.GetPoolID(),
		Status:    chaindto.PoolStatusRefunded,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		tx.Rollback()
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return e.NewError500(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return e.NewError500(e.Launchpad_update_pool_status_failed, "Update launchpad pool status error: "+err.Error())
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for check creator continue voting error: "+err.Error())
	}

	return nil
}

func (s *ClpLaunchpadService) DecideContinueVotingLaunchpad(launchpad *models.ClpLaunchpad, req *dto.DecideContinueVotingRequest) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if !launchpad.IsStatusWaiting() {
		tx.Rollback()
		log.Errorf("launchpad id: %s status not waiting, status is: %s", launchpad.ID, launchpad.Status)
		return e.NewError400(e.Launchpad_invalid_status, "launchpad is not waiting to decide"+string(launchpad.Status))
	}

	var poolStatus string
	var statusInvestment models.StatusInvestment
	if req.IsContinued {
		launchpad.Status = models.LaunchpadSTTVoting
		poolStatus = chaindto.PoolStatusVoting
		statusInvestment = models.VotingStatus
	} else {
		launchpad.Status = models.LaunchpadSTTRefunded
		poolStatus = chaindto.PoolStatusRefunded
		statusInvestment = models.GotRefunded
	}

	if err := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Update_launchpad_failed, "Update launchpad status error: "+err.Error())
	}

	if err := models.Repository.ClpInvestment(s.ctx).UpdateManyInvestmentStatus(statusInvestment, launchpad.ID, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Update_investment_failed, "update status for many investments failed "+err.Error())
	}

	// update pool
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		tx.Rollback()
		return e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
	}

	_, err := openedu_chain.Transaction.UpdateLpPoolStatus(&chaindto.UpdateLaunchpadPoolStatusRequest{
		PoolID:    launchpad.GetPoolID(),
		Status:    poolStatus,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		tx.Rollback()
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return e.NewError500(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return e.NewError500(e.Launchpad_update_pool_status_failed, "Update launchpad pool status error: "+err.Error())
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Commit_transaction_failed, "Commit transaction for decide continue voting error: "+err.Error())
	}

	return nil
}

func (s *ClpLaunchpadService) SyncJobForClpLaunchpad(req *dto.SchedulerClpLaunchpadRequest) *e.AppError {
	switch req.JobKey {
	case openedu_scheduler.StartFundingLaunchpadJobKey:
		if appErr := s.StartFunding(req.LaunchpadID); appErr != nil {
			return appErr
		}

	case openedu_scheduler.EndFundingLaunchpadJobKey:
		launchpad, appErr := s.FindByID(req.LaunchpadID)
		if appErr != nil {
			return appErr
		}

		if appErr := s.EndFunding(launchpad); appErr != nil {
			return appErr
		}

	case openedu_scheduler.EndVotingMilestoneJobKey:
		data := &dto.EndVotingMilestoneRequest{
			LaunchpadID:   req.LaunchpadID,
			VotingPhaseID: req.VotingPhaseID,
			MilestoneID:   req.MilestoneID,
		}
		if appErr := ClpVotingMilestone.EndVoting(data); appErr != nil {
			return appErr
		}

	case openedu_scheduler.CheckSettingFundingTimeJobKey:
		data := &dto.FundingLaunchpadRequest{
			ID: req.LaunchpadID,
		}
		if appErr := s.CheckSettingFundingTime(data); appErr != nil {
			return appErr
		}

	case openedu_scheduler.CheckCreatorContinueVotingJobKey:
		data := &dto.FundingLaunchpadRequest{
			ID: req.LaunchpadID,
		}
		if appErr := s.CheckCreatorContinueVotingLaunchpad(data); appErr != nil {
			return appErr
		}
	}

	return nil
}

func (s *ClpLaunchpadService) notifyCreateScheduledJobFailed(entityObject string, entityID string, jobName string, err error) error {
	stage := os.Getenv("STAGE")
	if stage == "" {
		stage = "local"
	}

	title := fmt.Sprintf("[%s] Create Scheduled Job Failure Alerts", strings.ToUpper(stage))
	body := "Here is detail error: \n " +
		fmt.Sprintf("* Service Name: **%s** \n ", setting.AppSetting.Name) +
		fmt.Sprintf("* Stage: **%s** \n ", stage) +
		fmt.Sprintf("* Scheduled Job Name: **%s** \n ", jobName) +
		fmt.Sprintf("* Scheduled Job For Entity: **%s** Has ID: **%s**\n ", entityObject, entityID)

	return ms_team.Alert(title, body, ms_team.WithCodeBlock(err.Error(), ms_team.PlainText))
}

func (s *ClpLaunchpadService) pushNotificationsForReqPublishCourse(launchpad *models.ClpLaunchpad, approval *models.Approval, requester *models.User) {
	rootOrg := Organization.GetRoot()
	userIDs, err := Organization.FindOrgAdminIDs(rootOrg)
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotificationsForReqPublishCourse get list org admins failed: %w", err)
		return
	}

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeRequestPublishCourseLaunchpadForAdmin,
		EntityID:   approval.EntityID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: userIDs,
			},
		},
		Props: s.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, requester),
	}
	if err = communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::PushNotificationsForReqPublish push notifications failed: %w", err)
	}
	return
}

func (s *ClpLaunchpadService) GetVotingPowersOffChain(launchpad *models.ClpLaunchpad) ([]*models.LaunchpadVotingPowerEntry, *e.AppError) {
	//wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	//if aErr != nil {
	//	return nil, e.NewError500(e.Launchpad_get_voting_powers_failed, "Find the wallet failed: "+aErr.Error())
	//}

	if appErr := s.GetInvestmentStats([]*models.ClpLaunchpad{launchpad}); appErr != nil {
		return nil, appErr
	}

	investments, err := models.Repository.ClpInvestment(s.ctx).FindMany(&models.ClpInvestmentQuery{
		ClpLaunchpadID: &launchpad.ID,
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.Launchpad_get_voting_powers_failed, "Find list investment launchpad error: "+err.Error())
	}

	votingPowers := lo.Map(investments, func(i *models.ClpInvestment, _ int) *chaindto.VotingPowerEntry {
		percentage := i.Amount.Div(*launchpad.TotalAmount).Mul(decimal.NewFromFloat(100))
		votingPowerFloat, _ := percentage.Float64()
		return &chaindto.VotingPowerEntry{
			UserID:      i.UserID,
			Amount:      i.Amount,
			TotalAmount: *launchpad.TotalAmount,
			VotingPower: votingPowerFloat,
		}
	})

	return lo.Map(votingPowers, func(item *chaindto.VotingPowerEntry, _ int) *models.LaunchpadVotingPowerEntry {
		return &models.LaunchpadVotingPowerEntry{
			UserID: item.UserID,
			//Address:     item.Address,
			Amount:      item.Amount,
			TotalAmount: item.TotalAmount,
			VotingPower: item.VotingPower,
			//Network:     wallet.Network,
			Network: models.BlockchainNetworkNEAR,
		}
	}), nil
}
