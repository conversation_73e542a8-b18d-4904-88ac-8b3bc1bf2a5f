package services

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
)

func (s *EmailService) SendEmail(user *models.User, org *models.Organization, event models.EventType, extendDatas models.MapEmailParams, isQueue bool) *e.AppError {
	if _, err := communication.Email.SendEmail(&communicationdto.SendEmailRequest{
		User:        user.IntoComm(),
		Org:         org.IntoComm(),
		Event:       event.IntoComm(),
		ExtendDatas: extendDatas.IntoComm(),
		IsQueue:     isQueue,
	}); err != nil {
		return e.NewError500(e.Error_ses_send_mail_failed, "Send email error: "+err.Error())
	}
	return nil
}

func (s *EmailService) InitDefaultEmailTemplateForOrg(org *models.Organization) *e.AppError {
	if err := communication.Email.InitDefaultEmailTemplateForOrg(org.IntoComm()); err != nil {
		return e.NewError500(e.Organization_init_default_email_templates, "Init default email templates for org error: "+err.Error())
	}
	return nil
}

func (s *EmailService) CreateEmailTemplate(req *dto.CreateEmailTemplateRequest, user *models.User, org *models.Organization) ([]byte, *e.AppError) {
	if template, err := communication.Email.CreateEmailTemplate(req.IntoComm(user, org)); err != nil {

		return nil, e.NewError500(e.Email_template_create_failed, err.Error())
	} else {
		return template, nil
	}
}

func (s *EmailService) UpdateEmailTemplate(req *dto.UpdateEmailTemplateRequest, templateID string, user *models.User, org *models.Organization) *e.AppError {
	if _, uErr := communication.Email.UpdateEmailTemplate(req.IntoComm(user, org), templateID); uErr != nil {
		return e.NewError500(e.Email_template_update_failed, uErr.Error())
	}

	return nil
}

func (s *EmailService) FindOneEmailTemplate(templateID string) (*communicationdto.EmailTemplate, *e.AppError) {
	if template, err := communication.Email.FindOneEmailTemplate(templateID); err != nil {
		return nil, e.NewError500(e.Email_template_find_one_failed, err.Error())
	} else {
		return template, nil
	}
}

func (s *EmailService) FindPageEmailTemplate(query *models.EmailTemplateQuery, options *models.FindPageOptions) ([]*communicationdto.EmailTemplate, *communicationdto.Pagination, *e.AppError) {
	if templates, pagination, err := communication.Email.FindPageEmailTemplate(query.IntoComm(), options.IntoComm()); err != nil {
		return nil, nil, e.NewError500(e.Email_template_find_page_failed, err.Error())
	} else {
		return templates, pagination, nil
	}
}

func (s *EmailService) DeleteEmailTemplate(templateID string, user *models.User, org *models.Organization) *e.AppError {
	req := communicationdto.DeleteEmailTemplateRequest{
		TemplateID: templateID,
		User:       user.IntoComm(),
		Org:        org.IntoComm(),
	}
	if _, err := communication.Email.DeleteEmailTemplate(&req); err != nil {
		return e.NewError500(e.Email_template_delete_failed, err.Error())
	}

	return nil
}

func (s *EmailService) PreviewEmailTemplate(org *models.Organization, templateID string, req *dto.PreviewEmailTemplateRequest) ([]byte, *e.AppError) {
	commReq := communicationdto.PreviewEmailTemplateRequest{
		Org:        org.IntoComm(),
		Emails:     req.Emails,
		Data:       req.Data.IntoComm(),
		TemplateID: templateID,
	}
	if template, err := communication.Email.PreviewEmailTemplate(&commReq); err != nil {
		return nil, e.NewError500(e.Email_template_preview_failed, err.Error())
	} else {
		return template, nil
	}
}

func (s *EmailService) FindEmailTemplateVariables() (map[communicationdto.EmailCodeType][]communicationdto.EmailTemplateVarName, *e.AppError) {
	if m, err := communication.Email.FindEmailTemplateVariables(); err != nil {
		return nil, e.NewError500(e.Email_template_get_variables_failed, err.Error())
	} else {
		return m, nil
	}
}
