package services

import (
	"context"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
)

func (s *BookmarkService) Create(req *dto.BookmarkRequest) (*models.Bookmark, *e.AppError) {
	if appErr := s.validateBookmarkRequest(req); appErr != nil {
		return nil, appErr
	}

	bookmark := models.Bookmark{
		Name:       req.Name,
		EntityID:   req.EntityID,
		EntityType: req.EntityType,
		UserID:     req.UserID,
		ParentID:   req.ParentID,
		Link:       req.Link,
	}
	if err := models.Repository.Bookmark.Create(&bookmark, nil); err != nil {
		return nil, e.NewError500(e.Bookmark_create_failed, "Create bookmark error: "+err.Error())
	}
	return &bookmark, nil
}

func (s *BookmarkService) FindPage(query *models.BookmarkQuery, options *models.FindPageOptions) ([]*models.Bookmark, *models.Pagination, *e.AppError) {
	if save, pagination, err := models.Repository.Bookmark.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Bookmark_find_failed, "Find page bookmarks error: "+err.Error())
	} else {
		return save, pagination, nil
	}
}

func (s *BookmarkService) FindOne(query *models.BookmarkQuery, options *models.FindOneOptions) (*models.Bookmark, *e.AppError) {
	bookmark, err := models.Repository.Bookmark.FindOne(query, options)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.Bookmark_not_found, "Bookmark not found: "+err.Error())
		}
		return nil, e.NewError500(e.Bookmark_find_one_failed, "Find bookmark error: "+err.Error())
	}
	return bookmark, nil
}

func (s *BookmarkService) FindByID(id string, options *models.FindOneOptions) (*models.Bookmark, *e.AppError) {
	bookmark, err := models.Repository.Bookmark.FindByID(id, options)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.Bookmark_not_found, "Bookmark not found: "+err.Error())
		}
		return nil, e.NewError500(e.Bookmark_find_one_failed, "Find bookmark error: "+err.Error())
	}
	return bookmark, nil
}

func (s *BookmarkService) Update(bookmark *models.Bookmark, req *dto.BookmarkRequest) (*models.Bookmark, *e.AppError) {
	if appErr := s.validateBookmarkRequest(req); appErr != nil {
		return nil, appErr
	}
	bookmark.Name = req.Name
	bookmark.EntityID = req.EntityID
	bookmark.EntityType = req.EntityType
	bookmark.ParentID = req.ParentID
	bookmark.Link = req.Link
	if err := models.Repository.Bookmark.Update(bookmark, nil); err != nil {
		return nil, e.NewError500(e.Bookmark_update_failed, "Update bookmark error: "+err.Error())
	}
	return bookmark, nil
}

func (s *BookmarkService) Delete(bookmark *models.Bookmark) *e.AppError {
	err := models.Repository.Bookmark.Delete(bookmark.ID, nil)
	if err != nil {
		return e.NewError500(e.Bookmark_delete_failed, err.Error())
	}
	return nil
}

func (s *BookmarkService) validateBookmarkRequest(req *dto.BookmarkRequest) *e.AppError {
	switch req.EntityType {
	case models.CourseModelName:
		_, err := models.Repository.PublishCourse(context.TODO()).FindOne(&models.PublishCourseQuery{
			CourseCuid:     &req.EntityID,
			IncludeDeleted: util.NewBool(false),
		}, nil)
		if err != nil {
			if models.IsRecordNotFound(err) {
				return e.NewError400(e.Bookmark_invalid_data, "Course not found: "+err.Error())
			}
			return e.NewError500(e.Course_find_one_failed, "Find publish course by CUID error: "+err.Error())
		}
	}

	if req.ParentID != nil {
		_, err := models.Repository.Bookmark.FindByID(*req.ParentID, nil)
		if err != nil {
			if models.IsRecordNotFound(err) {
				return e.NewError400(e.Bookmark_invalid_data, "Parent bookmark not found: "+err.Error())
			}
			return e.NewError500(e.Bookmark_find_one_failed, "Find parent bookmark error: "+err.Error())
		}
	}
	return nil
}
