package services

import (
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func (s *ResourceUsageService) Create(usage *models.ResourceUsage) (*models.ResourceUsage, *e.AppError) {
	if err := models.Repository.ResourceUsage.Create(usage, nil); err != nil {
		return nil, e.NewError500(e.ResourceUsageCreateFailed, "create: "+err.Error())
	} else {
		return usage, nil
	}
}

func (s *ResourceUsageService) Update(usage *models.ResourceUsage) (*models.ResourceUsage, *e.AppError) {
	if err := models.Repository.ResourceUsage.Update(usage, nil); err != nil {
		return nil, e.NewError500(e.ResourceUsageUpdateFailed, "create: "+err.Error())
	} else {
		return usage, nil
	}
}

func (s *ResourceUsageService) FindOne(query *models.ResourceUsageQuery, options *models.FindOneOptions) (*models.ResourceUsage, *e.AppError) {
	if resourceUsage, err := models.Repository.ResourceUsage.FindOne(query, options); err != nil {
		return nil, e.NewError500(e.ResourceUsageFindOneFailed, "FindOne: "+err.Error())
	} else {
		return resourceUsage, nil
	}
}

func (s *ResourceUsageService) FindById(id string, options *models.FindOneOptions) (*models.ResourceUsage, *e.AppError) {
	if resourceUsage, err := models.Repository.ResourceUsage.FindOne(&models.ResourceUsageQuery{ID: util.NewString(id)}, options); err != nil {
		return nil, e.NewError500(e.ResourceUsageFindOneFailed, "FindById: "+err.Error())
	} else {
		return resourceUsage, nil
	}
}

func (s *ResourceUsageService) FindPage(query *models.ResourceUsageQuery, options *models.FindPageOptions) ([]*models.ResourceUsage, *models.Pagination, *e.AppError) {
	if resourceUsages, pagination, err := models.Repository.ResourceUsage.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.ResourceUsageFindPageFailed, "FindPage: "+err.Error())
	} else {
		return resourceUsages, pagination, nil
	}
}

func (s *ResourceUsageService) FindMany(query *models.ResourceUsageQuery, options *models.FindManyOptions) ([]*models.ResourceUsage, *e.AppError) {
	if resourceUsages, err := models.Repository.ResourceUsage.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.ResourceUsageFindManyFailed, "FindMany: "+err.Error())
	} else {
		return resourceUsages, nil
	}
}

func (s *ResourceUsageService) Delete(id string) *e.AppError {
	if err := models.Repository.ResourceUsage.Delete(id, nil); err != nil {
		return e.NewError500(e.ResourceUsageDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}

func (s *ResourceUsageService) AddAiUsage(user *models.User, amount decimal.Decimal, aiModelName string, orgID string) *e.AppError {
	userPlan, planErr := Subscription.GetActivePlanByUser(user)
	if planErr != nil {
		return planErr
	}

	usage, usageErr := s.GetAiUsage(user, userPlan, orgID)
	if usageErr != nil {
		return usageErr
	}

	aiModel, appErr := AIModel.FindOne(&models.AIModelQuery{
		Name:           &aiModelName,
		IncludeDeleted: util.NewBool(false),
	}, nil)
	if appErr != nil {
		return appErr
	}

	aiPlanLimitModelMapByModelID := map[string]*models.AILimitModel{}
	for _, a := range userPlan.AiLimitation.Models {
		aiPlanLimitModelMapByModelID[a.AiModelID] = a
	}

	aiResourceUsagesMapByAIModelID := map[string]*models.AiResourceUsage{}
	for _, u := range usage.AiUsages {
		aiResourceUsagesMapByAIModelID[u.AIModelID] = u
	}

	currentAIPlan := aiPlanLimitModelMapByModelID[aiModel.ID]
	currentAIUsage := aiResourceUsagesMapByAIModelID[aiModel.ID]

	currentAIUsage.BalanceUsed = currentAIUsage.BalanceUsed.Add(amount)
	currentAIUsage.RequestUsed = currentAIUsage.RequestUsed + 1

	currentAIUsage.BalanceLimit = currentAIPlan.BalanceAmount
	currentAIUsage.RequestLimit = currentAIPlan.RequestAmount

	if _, uErr := s.Update(usage); uErr != nil {
		return uErr
	}

	return nil
}

func (s *ResourceUsageService) GetAiUsage(user *models.User, plan *models.PricingPlan, orgID string) (*models.ResourceUsage, *e.AppError) {
	cycle := plan.AiLimitation.Cycle
	today := util.GetStartOfMonth()
	query := models.ResourceUsageQuery{
		UserID: util.NewString(user.ID),
		Time:   util.NewInt(int(today.UnixMilli())),
		Cycle:  util.NewT(cycle),
	}
	usage, err := models.Repository.ResourceUsage.FindOne(&query, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.ResourceUsageFindOneFailed, "GetAiUsage: "+err.Error())
	}

	allAiModels, err := models.Repository.AIModel.FindMany(&models.AIModelQuery{
		IncludeDeleted: util.NewBool(false),
	}, &models.FindManyOptions{})
	if err != nil {
		return nil, e.NewError500(e.AIModel_find_many_failed, "GetAIModel: "+err.Error())
	}

	if len(allAiModels) == 0 {
		return nil, e.NewError500(e.AIModel_not_found, "GetAIModel: "+e.GetMsg(e.AIModel_not_found))
	}

	aiModelMap := make(map[string]*models.AIModel)
	for _, model := range allAiModels {
		aiModelMap[model.ID] = model
	}

	activeModelIDs := lo.Map(lo.Filter(allAiModels, func(item *models.AIModel, index int) bool {
		return item.Enabled
	}), func(item *models.AIModel, index int) string {
		return item.ID
	})

	aiLimitModelMapByModelID := map[string]*models.AILimitModel{}
	for _, a := range plan.AiLimitation.Models {
		aiLimitModelMapByModelID[a.AiModelID] = a
	}

	if usage == nil {
		aiResourceUsage := []*models.AiResourceUsage{}
		for _, model := range allAiModels {
			aiResourceUsage = append(aiResourceUsage, &models.AiResourceUsage{
				AIModelID:   model.ID,
				AIModelName: string(model.Name),
				Enable:      model.Enabled,
				RequestUsed: 0,
				BalanceUsed: decimal.NewFromInt(0),
				RequestLimit: func() int64 {
					if limit, ok := aiLimitModelMapByModelID[model.ID]; ok {
						return limit.RequestAmount
					}
					return 0
				}(),
				BalanceLimit: func() decimal.Decimal {
					if limit, ok := aiLimitModelMapByModelID[model.ID]; ok {
						return limit.BalanceAmount
					}
					return decimal.NewFromInt(0)
				}(),
				Bonus: decimal.NewFromInt(util.DefaultBonus),
			})
		}
		usage = &models.ResourceUsage{
			UserID:   user.ID,
			Cycle:    cycle,
			Time:     int(today.UnixMilli()),
			AiUsages: aiResourceUsage,
		}

		return s.Create(usage)
	}

	needUpdateUsage := false
	aiResourceUsage := []*models.AiResourceUsage{}
	for _, u := range usage.AiUsages {
		if model, exists := aiModelMap[u.AIModelID]; exists {
			if u.Enable != model.Enabled {
				u.Enable = model.Enabled
				needUpdateUsage = true
			}

			if lo.Contains(activeModelIDs, u.AIModelID) {
				if limit, ok := aiLimitModelMapByModelID[u.AIModelID]; ok {
					if u.RequestLimit != limit.RequestAmount || !u.BalanceLimit.Equal(limit.BalanceAmount) {
						u.RequestLimit = limit.RequestAmount
						u.BalanceLimit = limit.BalanceAmount
						needUpdateUsage = true
					}
				}
			}

			aiResourceUsage = append(aiResourceUsage, u)
		} else {
			u.Enable = false
			aiResourceUsage = append(aiResourceUsage, u)
			needUpdateUsage = true
		}
	}

	// Add new model
	existingModelMap := make(map[string]bool)
	for _, u := range aiResourceUsage {
		existingModelMap[u.AIModelID] = true
	}

	for _, model := range allAiModels {
		if !existingModelMap[model.ID] {
			aiResourceUsage = append(aiResourceUsage, &models.AiResourceUsage{
				AIModelID:   model.ID,
				AIModelName: string(model.Name),
				Enable:      model.Enabled,
				RequestUsed: 0,
				BalanceUsed: decimal.NewFromInt(0),
				RequestLimit: func() int64 {
					if limit, ok := aiLimitModelMapByModelID[model.ID]; ok {
						return limit.RequestAmount
					}
					return 0
				}(),
				BalanceLimit: func() decimal.Decimal {
					if limit, ok := aiLimitModelMapByModelID[model.ID]; ok {
						return limit.BalanceAmount
					}
					return decimal.NewFromInt(0)
				}(),
				Bonus: decimal.NewFromInt(util.DefaultBonus),
			})
			needUpdateUsage = true
		}
	}

	usage.AiUsages = aiResourceUsage
	if needUpdateUsage {
		return s.Update(usage)
	}

	return usage, nil
}

func (s *ResourceUsageService) CheckAiLimitationUsage(user *models.User, plan *models.PricingPlan, aiModelID string, orgID string) (*dto.LimitResourceResponse, *e.AppError) {
	usage, aErr := s.GetAiUsage(user, plan, orgID)
	if aErr != nil {
		return nil, aErr
	}

	// find if this model is in resource usage
	aiModels, err := models.Repository.AIModel.FindMany(&models.AIModelQuery{
		Enabled:        util.NewBool(true),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindManyOptions{})
	if err != nil {
		return nil, e.NewError500(e.AIModel_find_many_failed, "GetAIModel: "+err.Error())
	}

	if len(aiModels) == 0 {
		return nil, e.NewError500(e.AIModel_not_found, "GetAIModel: "+e.GetMsg(e.AIModel_not_found))
	}

	aiPlanLimitModelMapByModelID := map[string]*models.AILimitModel{}
	for _, a := range plan.AiLimitation.Models {
		aiPlanLimitModelMapByModelID[a.AiModelID] = a
	}

	aiResourceUsagesMapByAIModelID := map[string]*models.AiResourceUsage{}
	for _, u := range usage.AiUsages {
		aiResourceUsagesMapByAIModelID[u.AIModelID] = u
	}

	currentAIPlan := aiPlanLimitModelMapByModelID[aiModelID]
	currentAIUsage := aiResourceUsagesMapByAIModelID[aiModelID]

	remainingBalance := currentAIPlan.BalanceAmount.Add(currentAIUsage.Bonus).Sub(currentAIUsage.BalanceUsed)
	remainingRequest := currentAIPlan.RequestAmount - currentAIUsage.RequestUsed

	if remainingBalance.LessThanOrEqual(decimal.NewFromInt(0)) {
		return nil, e.NewError400(e.ResourceUsageAiReachLimit,
			fmt.Sprintf("reach limit ai usage"))
	}

	return &dto.LimitResourceResponse{
		BalanceUsed:      currentAIUsage.BalanceUsed,
		BalanceLimit:     currentAIPlan.BalanceAmount,
		Bonus:            currentAIUsage.Bonus,
		BalanceRemaining: remainingBalance,

		RequestUsed:      currentAIUsage.RequestUsed,
		RequestLimit:     currentAIPlan.RequestAmount,
		RequestRemaining: remainingRequest,
	}, nil
}

func (s *ResourceUsageService) CheckValidUsage(user *models.User, resourceType models.ResourceType, aiModelID string, orgID string) (*dto.ValidUsageResponse, *e.AppError) {
	userPlan, planErr := Subscription.GetActivePlanByUser(user)
	if planErr != nil {
		return nil, planErr
	}
	resp := dto.ValidUsageResponse{}
	switch resourceType {
	case models.ResourceTypeAi:
		aiResp, aiErr := s.CheckAiLimitationUsage(user, userPlan, aiModelID, orgID)
		if aiErr != nil {
			return nil, aiErr
		}
		resp.AiUsage = aiResp
	default:
		break
	}
	return &resp, nil
}
