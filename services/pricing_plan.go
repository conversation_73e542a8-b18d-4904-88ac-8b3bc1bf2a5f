package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"gorm.io/gorm"
)

func (s *PricingPlanService) Create(request *dto.PricingPlanRequest) (*models.PricingPlan, *e.AppError) {
	plan := &models.PricingPlan{
		Tier:         request.Tier,
		Name:         request.Name,
		Description:  request.Description,
		MonthlyPrice: request.MonthlyPrice,
		AnnualPrice:  request.AnnualPrice,
		Enable:       false,
		UserID:       request.User.ID,
		Order:        request.Order,
		AiLimitation: request.AiLimitation,
		Period:       request.Period,
		PeriodUnit:   request.PeriodUnit,
		Cycle:        request.Cycle,
	}
	if err := models.Repository.PricingPlan.Create(plan, nil); err != nil {
		return nil, e.NewError500(e.PricingPlanCreateFailed, "create: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *PricingPlanService) Update(plan *models.PricingPlan, request *dto.PricingPlanRequest) (*models.PricingPlan, *e.AppError) {
	plan.Tier = request.Tier
	plan.Name = request.Name
	plan.Description = request.Description
	plan.MonthlyPrice = request.MonthlyPrice
	plan.AnnualPrice = request.AnnualPrice
	plan.Order = request.Order
	plan.AiLimitation = request.AiLimitation
	plan.Period = request.Period
	plan.PeriodUnit = request.PeriodUnit
	plan.Cycle = request.Cycle
	if err := models.Repository.PricingPlan.Update(plan, nil); err != nil {
		return nil, e.NewError500(e.PricingPlanUpdateFailed, "create: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *PricingPlanService) FindOne(query *models.PricingPlanQuery, options *models.FindOneOptions) (*models.PricingPlan, *e.AppError) {
	if plan, err := models.Repository.PricingPlan.FindOne(query, options); err != nil {
		return nil, e.NewError500(e.PricingPlanFindOneFailed, "FindOne: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *PricingPlanService) FindById(id string, options *models.FindOneOptions) (*models.PricingPlan, *e.AppError) {
	if plan, err := models.Repository.PricingPlan.FindOne(&models.PricingPlanQuery{ID: util.NewString(id)}, options); err != nil {
		return nil, e.NewError500(e.PricingPlanFindByIDFailed, "FindById: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *PricingPlanService) FindPage(query *models.PricingPlanQuery, options *models.FindPageOptions) ([]*models.PricingPlan, *models.Pagination, *e.AppError) {
	if plans, pagination, err := models.Repository.PricingPlan.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.PricingPlanFindPageFailed, "FindPage: "+err.Error())
	} else {
		return plans, pagination, nil
	}
}

func (s *PricingPlanService) FindMany(query *models.PricingPlanQuery, options *models.FindManyOptions) ([]*models.PricingPlan, *e.AppError) {
	if plans, err := models.Repository.PricingPlan.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.PricingPlanFindManyFailed, "FindMany: "+err.Error())
	} else {
		return plans, nil
	}
}

func (s *PricingPlanService) Delete(id string) *e.AppError {
	if err := models.Repository.PricingPlan.Delete(id, nil); err != nil {
		return e.NewError500(e.PricingPlanDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}

func (s *PricingPlanService) EnablePlan(plan *models.PricingPlan, enable bool) *e.AppError {
	if enable {
		query := models.PricingPlanQuery{
			Tier:   util.NewT(plan.Tier),
			Enable: util.NewBool(true),
			IDNot:  util.NewString(plan.ID),
		}
		sameEnableTier, err := models.Repository.PricingPlan.FindOne(&query, nil)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError500(e.PricingPlanFindOneFailed, "EnablePlan: "+err.Error())
		}
		if sameEnableTier != nil {
			return e.NewError400(e.PricingPlanSameTierEnabled, "same tier enabled")
		}
	}
	plan.Enable = enable
	if uErr := models.Repository.PricingPlan.Update(plan, nil); uErr != nil {
		return e.NewError500(e.PricingPlanUpdateFailed, "EnablePlan: "+uErr.Error())
	}
	return nil
}

func (s *PricingPlanService) GetFreePlan() (*models.PricingPlan, *e.AppError) {
	query := models.PricingPlanQuery{
		Tier:   util.NewT(models.FreePlanTier),
		Enable: util.NewBool(true),
	}
	if plan, err := s.FindOne(&query, nil); err != nil {
		return nil, err
	} else {
		return plan, nil
	}
}

func (s *PricingPlanService) GetInternalPlan() (*models.PricingPlan, *e.AppError) {
	query := models.PricingPlanQuery{
		Tier:   util.NewT(models.InternalPlanTier),
		Enable: util.NewBool(true),
	}
	if plan, err := s.FindOne(&query, nil); err != nil {
		return nil, err
	} else {
		return plan, nil
	}
}
