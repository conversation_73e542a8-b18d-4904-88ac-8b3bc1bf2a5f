package services

import (
	"errors"
	"openedu-core/models"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *ExternalService) GetPreloadUserActionAndFollowers(loggedUser *models.User, users []*communicationdto.UserResp) *e.AppError {
	targetIDs := lo.Map(users, func(item *communicationdto.UserResp, _ int) string {
		return item.ID
	})

	mapUserIDUserResp := make(map[string]*communicationdto.UserResp)
	for _, user := range users {
		mapUserIDUserResp[user.ID] = user
	}

	if loggedUser != nil {
		query := models.UserActionQuery{
			UserID:         &loggedUser.ID,
			TargetUserIDIn: targetIDs,
		}

		// user action
		userActions, aErr := models.Repository.UserAction.FindMany(&query, &models.FindManyOptions{})
		if aErr != nil {
			if !errors.Is(aErr, gorm.ErrRecordNotFound) {
				return e.NewError400(e.External_call_error, aErr.Error())
			}
		} else {
			for _, item := range userActions {
				if userResp, ok := mapUserIDUserResp[item.TargetUserID]; ok {
					userResp.FollowStatus = communicationdto.ActionType(item.Action)
				}
			}
		}
	}
	// following
	targetSummary, findErr := models.Repository.UserSummary.FindMany(&models.UserSummaryQuery{UserIDIn: targetIDs}, &models.FindManyOptions{})
	if findErr != nil {
		if !errors.Is(findErr, gorm.ErrRecordNotFound) {
			return e.NewError500(e.External_call_error, findErr.Error())
		}
	} else {
		for _, summary := range targetSummary {
			if userResp, ok := mapUserIDUserResp[summary.UserID]; ok {
				userResp.Followers = summary.Followers
			}
		}
	}

	return nil

}
