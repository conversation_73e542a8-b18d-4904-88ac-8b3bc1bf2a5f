package services

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strconv"
	"time"
)

func (s *OEReferralCodeService) AddReferralCode(userID string, customCode string) (*models.OEReferralCode, *e.AppError) {
	code, err := models.Repository.OEReferralCode(s.ctx).FindOne(&models.OEReferralCodeQuery{
		UserID: util.NewString(userID),
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.New<PERSON>rror500(e.OEReferralCodeFindOneFailed, "find user code failed: "+err.Error())
	}

	if code != nil {
		return code, nil
	}

	strCode := util.GenerateId()
	if customCode != "" {
		strCode = customCode
	}
	loopCount := 0
	for {
		loopCount++
		found, _ := models.Repository.OEReferralCode(s.ctx).FindOne(&models.OEReferralCodeQuery{
			Code: util.NewString(strCode),
		}, nil)
		if found == nil || loopCount > 20 {
			break
		}
		strCode += strconv.Itoa(loopCount)
	}

	newCode := &models.OEReferralCode{
		UserID: userID,
		Code:   strCode,
		Props:  models.JSONB{},
	}

	cErr := models.Repository.OEReferralCode(s.ctx).Create(newCode, nil)
	if cErr != nil {
		return nil, e.NewError500(e.OEReferralCodeCreateFailed, "create: "+cErr.Error())
	}
	models.Cache.OEReferralCode.Set(fmt.Sprintf("user:%s", newCode.UserID), strCode)
	return newCode, nil
}

func (s *OEReferralCodeService) AddReferrerForReferee(referee *models.User, referrer *models.User, campaign *models.OEPointCampaign) (*models.OEReferralCode, *e.AppError) {
	refCode, cErr := s.GetUserReferralCode(referee.ID)
	if cErr != nil {
		return nil, cErr
	}
	if refCode.ReferrerID != nil {
		if *refCode.ReferrerID == referrer.ID {
			return nil, e.NewError400(e.OEReferralCodeReferrerAdded, "referrer already exists")
		} else {
			return nil, e.NewError400(e.OEReferralCodeReferrerAddedByOtherUser, "referrer already exists by other user")
		}
	}
	refCode.ReferrerID = util.NewString(referee.ID)
	refCode.CampaignID = util.NewString(campaign.ID)
	if uCode, uErr := s.Update(refCode); uErr != nil {
		return nil, uErr
	} else {
		return uCode, nil
	}
}

func (s *OEReferralCodeService) GetUserReferralCode(userID string) (*models.OEReferralCode, *e.AppError) {
	res := &models.OEReferralCode{}
	cKey := fmt.Sprintf("user:%s", userID)
	if err := models.Cache.OEReferralCode.Get(cKey, res); err != nil {
		log.Error("get user referral code failed: " + err.Error())
	}
	if res.ID != "" {
		return res, nil
	}
	refCode, err := s.FindOne(&models.OEReferralCodeQuery{
		UserID: util.NewString(userID),
	}, &models.FindOneOptions{
		Preloads: []string{models.ReferrerField},
	})
	if err != nil && err.ErrCode != e.OEReferralCodeNotFound {
		return nil, err
	}
	if refCode == nil {
		if newCode, newErr := s.AddReferralCode(userID, ""); newErr != nil {
			return nil, newErr
		} else {
			refCode = newCode
		}
	}
	if refCode.Referrer != nil {
		refCode.Referrer = refCode.Referrer.Sanitize()
	}

	models.Cache.OEReferralCode.Set(cKey, refCode)
	return refCode, nil
}

func (s *OEReferralCodeService) UpdateCustomCode(refCode *models.OEReferralCode, customCode string) (*models.OEReferralCode, *e.AppError) {
	loopCount := 0
	for {
		loopCount++
		found, _ := models.Repository.OEReferralCode(s.ctx).FindOne(&models.OEReferralCodeQuery{
			Code: util.NewString(customCode),
		}, nil)
		if found == nil {
			break
		}
		if loopCount > 20 {
			return nil, e.NewError400(e.OEReferralCodeExisted, "custom code existed")
		}
		customCode += strconv.Itoa(loopCount)
	}

	if refCode.Props == nil {
		refCode.Props = models.JSONB{}
	}
	var oldCodes []interface{}
	if refCode.Props["old_codes"] != nil {
		oldCodes = refCode.Props["old_codes"].([]interface{})
	}
	oldCodes = append(oldCodes, map[string]int64{refCode.Code: time.Now().UnixMilli()})
	refCode.Props["old_codes"] = oldCodes
	refCode.Code = customCode
	updatedCode, uErr := s.Update(refCode)
	if uErr != nil {
		return nil, uErr
	}
	models.Cache.OEReferralCode.Set(fmt.Sprintf("user:%s", refCode.UserID), refCode)
	return updatedCode, nil
}

func (s *OEReferralCodeService) Create(request *models.OEReferralCode) (*models.OEReferralCode, *e.AppError) {
	if err := models.Repository.OEReferralCode(s.ctx).Create(request, nil); err != nil {
		return nil, e.NewError500(e.OEReferralCodeCreateFailed, "create: "+err.Error())
	} else {
		return request, nil
	}
}

func (s *OEReferralCodeService) Update(request *models.OEReferralCode) (*models.OEReferralCode, *e.AppError) {
	if err := models.Repository.OEReferralCode(s.ctx).Update(request, nil); err != nil {
		return nil, e.NewError500(e.OEReferralCodeUpdateFailed, "create: "+err.Error())
	} else {
		return request, nil
	}
}

func (s *OEReferralCodeService) FindOne(query *models.OEReferralCodeQuery, options *models.FindOneOptions) (*models.OEReferralCode, *e.AppError) {
	fmt.Println("query:", util.Struct2Json(query))
	if plan, err := models.Repository.OEReferralCode(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OEReferralCodeNotFound, "FindOne notfound: "+err.Error())
		}
		return nil, e.NewError500(e.OEReferralCodeFindOneFailed, "FindOne: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEReferralCodeService) FindById(id string, options *models.FindOneOptions) (*models.OEReferralCode, *e.AppError) {
	if plan, err := models.Repository.OEReferralCode(s.ctx).FindOne(&models.OEReferralCodeQuery{ID: util.NewString(id)}, options); err != nil {
		return nil, e.NewError500(e.OEReferralCodeFindByIDFailed, "FindById: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEReferralCodeService) FindByCode(code string, options *models.FindOneOptions) (*models.OEReferralCode, *e.AppError) {
	if plan, err := models.Repository.OEReferralCode(s.ctx).FindOne(&models.OEReferralCodeQuery{Code: util.NewString(code)}, options); err != nil {
		return nil, e.NewError500(e.OEReferralCodeFindByIDFailed, "FindById: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEReferralCodeService) FindPage(query *models.OEReferralCodeQuery, options *models.FindPageOptions) ([]*models.OEReferralCode, *models.Pagination, *e.AppError) {
	if plans, pagination, err := models.Repository.OEReferralCode(s.ctx).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.OEReferralCodeFindPageFailed, "FindPage: "+err.Error())
	} else {
		return plans, pagination, nil
	}
}

func (s *OEReferralCodeService) FindMany(query *models.OEReferralCodeQuery, options *models.FindManyOptions) ([]*models.OEReferralCode, *e.AppError) {
	if plans, err := models.Repository.OEReferralCode(s.ctx).FindMany(query, options); err != nil {
		return nil, e.NewError500(e.OEReferralCodeFindManyFailed, "FindMany: "+err.Error())
	} else {
		return plans, nil
	}
}

func (s *OEReferralCodeService) Delete(id string) *e.AppError {
	if err := models.Repository.OEReferralCode(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.OEReferralCodeDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}
