package services

import (
	"context"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/ai"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/payment"
	"openedu-core/pkg/queue/consumer"
	"openedu-core/pkg/queue/producer"
	"openedu-core/pkg/upload"
	"openedu-core/pkg/util"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Service struct{}

type ServiceIface interface{}

type BaseService struct {
	ctx context.Context
}

type AuthService struct{}
type SnsAccountService struct{}
type UserService struct{ BaseService }
type UploadService struct{}
type OrganizationService struct{}
type EmailService struct{}
type CourseService struct {
	BaseService
}
type PublishCourseService struct {
	BaseService
}
type PublishBlogService struct{}
type PaymentService struct{}
type PaymentMethodService struct{}
type OrderService struct{}
type OrderItemService struct{}
type SectionService struct{}
type LessonContentService struct {
	BaseService
}
type CategoryService struct{}
type CategoryRelationService struct{}
type HashtagService struct{}
type HashtagRelationService struct{}
type CouponService struct{}
type CouponHistoryService struct{}
type FormService struct{}
type FormRelationService struct{}
type FormAnswerService struct{}
type FormQuestionService struct{}
type FormSessionService struct{}
type SystemConfigService struct{}
type BlogService struct{}
type ApprovalService struct{}
type UserActionService struct{}
type PageConfigService struct{}
type PageAccessService struct{}
type RoleService struct{}
type WalletService struct{}
type TransactionService struct {
	queueConsumer consumer.Consumer
}
type UserTokenService struct{}
type QuizService struct{}
type QuizSubmissionService struct{}
type CourseEnrollmentService struct{}
type UserSettingService struct{}
type AffiliateCampaignService struct{}
type ReferrerService struct{}
type CommissionService struct{}
type ReferralService struct{}
type ReferralLinkService struct{}
type SessionService struct{}
type BookmarkService struct{}
type CertificateService struct{}
type HtmlTemplateService struct{}
type WebhookService struct{}
type AIBlogRewriteService struct{}
type UserSummaryService struct{}
type ExternalService struct{}
type ExchangeRateService struct{}
type FileRelationService struct{}
type ReportService struct{}
type AICourseService struct{}
type AIHistoryService struct{}
type PricingPlanService struct{}
type SubscriptionService struct{}
type ResourceUsageService struct{}
type AIModelService struct{}
type ChainService struct{}
type OEPointHistoryService struct {
	BaseService
}
type OEPointCampaignService struct {
	BaseService
}

type OEReferralService struct {
	BaseService
}

type OEReferralCodeService struct {
	BaseService
}

type OEReferralLeaderBoardService struct {
	BaseService
}

type OEAIGovCampaignService struct {
	BaseService
}

type FeaturedContentService struct {
	BaseService
}
type ClpLaunchpadService struct {
	BaseService
}
type ClpCourseLaunchpadService struct {
	BaseService
}
type ClpInvestmentService struct {
	BaseService
}
type ClpVotingMilestoneService struct {
	BaseService
}

type ReferralProgramSettingService struct{}
type ReferralHistoryService struct{}
type ReferralProgramService struct{}

type LearningStatusService struct {
	BaseService
}

type ScheduleService struct {
	BaseService
}

type EventScheduleService struct {
	BaseService
}

type SessionServiceIface interface {
	FindOne(query *models.SessionQuery, options *models.FindOneOptions) (*models.Session, *e.AppError)
	Upsert(session *models.Session) *e.AppError
}
type UserRoleOrgService struct{}
type AIAgentPromptService struct{}

type OEReferralReportService struct {
	BaseService
}

type AuthServiceIface interface {
	Authentication(params *dto.AuthParams) (*dto.AuthResponse, *e.AppError)
	ParseAccessToken(token string) (*Claims, error)
	RefreshToken(params *dto.RefreshTokenParams, session *models.Session) (*dto.AuthResponse, int)
	SocialSignUp(snsAccount *models.SnsAccount, org *models.Organization) (*models.User, error)
	Register(params *dto.RegisterParams) (*dto.RegisterResponse, *e.AppError)
	ResendMail(org *models.Organization, params *dto.ResendMailPrams) *e.AppError
	Verify(params *dto.VerifyUserParams) (*dto.VerifyUserResponse, *models.User, *e.AppError)
	ForgotPassword(org *models.Organization, email string, allowFieldsData map[string]interface{}) *e.AppError
	ConfirmResetPassword(params *dto.ConfirmResetParams) (*dto.ConfirmResetResponse, *e.AppError)
	ExternalRegister(org *models.Organization, params *dto.ExternalRegisterParams) *e.AppError
	SetPassword(params *dto.SetPasswordParams) (*dto.SetPasswordResponse, *e.AppError)
	AddDefaultRole(userId string, orgId string) (*models.UserRoleOrg, *e.AppError)
	RequestOTP(user *models.User, org *models.Organization) *e.AppError
	ValidateUserByEmail(email string) (*models.User, *e.AppError)
}

type SnsAccountServiceIface interface {
	UpsertSnsAccount(snsAccount *models.SnsAccount) (*models.SnsAccount, error)
	GetLinkedSnsAccountsForSettings(user *models.User) ([]*models.SimpleSnsAccount, error)
	SortMainAccountToTopList(snsAccounts []*models.SimpleSnsAccount) []*models.SimpleSnsAccount
	UnlinkSnsAccount(snsAccount *models.SnsAccount) *e.AppError
}

type UserServiceIface interface {
	Create(user *models.User) (*models.User, *e.AppError)
	CheckAndCreateUserFromSnsAccountData(snsAccount *models.SnsAccount, org *models.Organization, domain string) (*models.User, error)
	GetUserByID(userID string) (*models.User, *e.AppError)
	GetUserProfile(user *models.User, org *models.Organization) (*dto.UserProfileResponse, *e.AppError)
	FindByID(id string, options *models.FindOneOptions) (*models.User, *e.AppError)
	GetUserProfileByID(params *dto.GetUserProfileByIDParams, org *models.Organization) (*dto.UserProfileResponse, *e.AppError)
	Update(user *models.User) (*models.User, *e.AppError)
	WithdrawUser(user *models.User) error
	CheckProviderExisted(provider util.SNSProvider, userID string) (bool, error)
	CreateUserForOrg(params *dto.CreateUser, role ...string) (*models.User, *e.AppError)
	FindPage(query *models.UserQuery, options *models.FindPageOptions) ([]*models.User, *models.Pagination, *e.AppError)
	FindMany(query *models.UserQuery, options *models.FindManyOptions) ([]*models.User, *e.AppError)
	ChangePassword(params *dto.ChangePasswordParams) (*models.SimpleUser, *e.AppError)
	HandleApproveCreator(org *models.Organization, params *dto.CreateCreatorRequest) (*models.User, string, *e.AppError)
	HandleApproveWriter(org *models.Organization, params *dto.CreateWriterRequest) (*models.User, string, *e.AppError)
	HandleInviteCreator(org *models.Organization, params *dto.InviteCreatorRequest) *e.AppError
	HandleAcceptInviteCreator(org *models.Organization, params *dto.AcceptInviteRequest) (*models.User, string, *e.AppError)
	UpdateByAdmin(u *models.User, data *dto.UpdateUserRequest) (*models.User, *e.AppError)
	FindPageUserOrg(query *models.UserQuery, option *models.FindPageOptions) ([]*models.SimpleProfile, *models.Pagination, *e.AppError)
	HandleAddRemoveUserRole(updater *models.User, data *dto.AddRemoveRoleRequest) *e.AppError
	HandleAcceptInvitationUser(org *models.Organization, data *dto.AcceptInviteUserRequest) (*models.User, string, *e.AppError)
	HandleInviteUser(org *models.Organization, data *dto.InviteUserRequest) *e.AppError
	FindUserByUsername(username string) (*models.User, *e.AppError)
	FindOne(query *models.UserQuery, options *models.FindOneOptions) (*models.User, *e.AppError)
}
type UploadServiceIface interface {
	GetStatusUploadFromBunny(file *models.File) (*string, *e.AppError)
	UploadFiles(files []*upload.File, userID string, pathPrefix string) ([]*models.File, *e.AppError)
	DeleteFiles(files []*models.File) *e.AppError
	GetFileData(file *models.File) ([]byte, *e.AppError)
	DownLoadAndUploadFile(url string, userID string) (*models.File, *e.AppError)
}

type OrganizationServiceIface interface {
	Create(data *dto.CreateOrgRequest, shouldSendEmail bool) (*models.Organization, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Organization, *e.AppError)
	FindOne(query *models.OrganizationQuery, options *models.FindOneOptions) (*models.Organization, *e.AppError)
	FindPage(query *models.OrganizationQuery, options *models.FindPageOptions) ([]*models.Organization, *models.Pagination, *e.AppError)
	Update(org *models.Organization) *e.AppError
	Delete(org *models.Organization) *e.AppError
	UpdateOrg(org *models.Organization, data *dto.UpdateOrgRequest, user *models.User) *e.AppError
	GetRoot() *models.Organization
	CheckValidation(data *dto.CheckOrgValidationRequest) *e.AppError
	CalcRetroactiveForAvail(airdropAmount decimal.Decimal) (*dto.CalcRetroactiveForAvailResponse, *e.AppError)
	InitOrgConfigs(user *models.User, org *models.Organization) *e.AppError
	FindOrgAdminIDs(org *models.Organization) ([]string, error)
}

type CourseServiceIface interface {
	Create(org *models.Organization, user *models.User, data *dto.CreateCourseRequest, defaultSection bool) (*models.Course, *e.AppError)
	Update(course *models.Course, org *models.Organization, user *models.User, data *dto.UpdateCourseRequest) (*models.Course, *e.AppError)
	FindById(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Course, *e.AppError)
	FindOne(query *models.CourseQuery, options *models.FindOneOptions) (*models.Course, *e.AppError)
	FindPageByPartner(query *models.CourseQuery, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError)
	Delete(course *models.Course) *e.AppError
	FindCoursePartners(query *models.CoursePartnerQuery, options *models.FindPageOptions) ([]*models.CoursePartner, *models.Pagination, *e.AppError)
	FindManyCoursePartners(query *models.CoursePartnerQuery, options *models.FindManyOptions) ([]*models.CoursePartner, *e.AppError)
	UpdateCoursePartners(course *models.Course, org *models.Organization, user *models.User, data []*dto.PartnerRequest) ([]*models.CoursePartner, *e.AppError)
	DeleteCoursePartners(course *models.Course, org *models.Organization, user *models.User, data []string) *e.AppError
	GetOutline(course *models.Course) (*models.Course, *e.AppError)
	UpdateStats(course *models.Course, requireSave bool) *e.AppError
	CanUpdateCourse(course *models.Course, user *models.User, permission models.CoursePermission) *e.AppError
	RequestPublishCourse(request *dto.PublishCourseRequest) (*models.Course, *e.AppError)
	PublishCourse(user *models.User, approval *models.Approval) *e.AppError
	UnPublishCourse(user *models.User, cuid string, target models.UnPublishCourseTarget) *e.AppError
	CancelRequest(course *models.Course) *e.AppError
	RejectPublishCourse(user *models.User, approval *models.Approval) *e.AppError
	FindLearnerPublishCourses(user *models.User, org *models.Organization, query *models.PublishCourseQuery, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError)
	FindCourseListItems(user *models.User, org *models.Organization, query *models.PublishCourseQuery, options *models.FindPageOptions) ([]*models.CourseListItem, *models.Pagination, *e.AppError)
	FindPublishCourses(user *models.User, org *models.Organization, query *models.PublishCourseQuery, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError)
	CloneNewVersion(course *models.Course, user *models.User) (*models.Course, *e.AppError)
	ExtendResponseForOutline(course *models.Course, user *models.User) (err *e.AppError)
	GetCourseFromPublishCourse(cuid string, id string) (*models.Course, *models.PublishCourse, *e.AppError)
	FindPage(query *models.CourseQuery, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError)
	FindPageCoursesByUser(req *dto.GetMyCoursesRequest, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError)
	CountCoursesByUser(req *dto.CountMyCoursesRequest) (int64, *e.AppError)
	ReplyFeedbackPublishCourse(approval *models.Approval, course *models.Course, feedback *dto.ApprovalFeedback) (*models.Course, *e.AppError)
	DuplicateCourse(org *models.Organization, course *models.Course, user *models.User) (*models.Course, *e.AppError)
	ExtendOutlineLearningProgressOverview(user *models.User, course *models.Course) *e.AppError
	FindByIdForOutline(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Course, *e.AppError)
	RequestPublishCourseLaunchpad(request *dto.PublishCourseRequest) (*models.Course, *e.AppError)
	GetCourseRevenueSummaryReport(query *models.CourseRevenueSummaryQuery, user *models.User) (*models.CourseRevenueSummary, *e.AppError)
	GetCourseRevenueDetailReport(query *models.CourseRevenueDetailQuery, user *models.User, options *models.FindPageOptions) ([]*models.CourseRevenueOrderDetail, *models.Pagination, *e.AppError)
	CronJobCreateCourseRevenue(timestamp int64, period models.TimePeriod) *e.AppError
	GetCourseRevenueGraph(query *dto.CourseRevenueGraphReq, user *models.User, opt *models.FindPageOptions) ([]*models.CourseRevenuePoint, *models.Pagination, *e.AppError)
}

type PaymentServiceIface interface {
	Create(user *models.User, data *dto.CreatePaymentRequest) (*models.Payment, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Payment, *e.AppError)
	FindOne(query *models.PaymentQuery, options *models.FindOneOptions) (*models.Payment, *e.AppError)
	FindPage(query *models.PaymentQuery, options *models.FindPageOptions) ([]*models.Payment, *models.Pagination, *e.AppError)
	Update(p *models.Payment) *e.AppError
	CanUpdatePayment(p *models.Payment, user *models.User) bool
	Verify(serviceName payment.PaymentServiceName, data *dto.SepayWebhookRequest) *e.AppError
}

type PaymentMethodServiceIface interface {
	Create(user *models.User, data *dto.PaymentMethodRequest) (*models.PaymentMethod, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.PaymentMethod, *e.AppError)
	FindOne(query *models.PaymentMethodQuery, options *models.FindOneOptions) (*models.PaymentMethod, *e.AppError)
	FindPage(query *models.PaymentMethodQuery, options *models.FindPageOptions) ([]*models.PaymentMethod, *models.Pagination, *e.AppError)
	Update(paymentMethod *models.PaymentMethod) *e.AppError
	Delete(paymentMethod *models.PaymentMethod) *e.AppError
	CanUpdatePaymentMethod(p *models.PaymentMethod, user *models.User) bool
}

type OrderServiceIface interface {
	Create(user *models.User, data *dto.CreateOrderRequest) (*models.Order, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Order, *e.AppError)
	FindOne(query *models.OrderQuery, options *models.FindOneOptions) (*models.Order, *e.AppError)
	FindPage(query *models.OrderQuery, options *models.FindPageOptions) ([]*models.Order, *models.Pagination, *e.AppError)
	Update(o *models.Order) *e.AppError
	CanUpdateOrder(o *models.Order, user *models.User) bool
	IsPurchased(userId string, data *dto.CreateOrderRequest) (*models.Order, *e.AppError)
	ApplyCoupon(org *models.Organization, user *models.User, coupon *models.Coupon, order *models.Order) *e.AppError
	MarkOrderSuccessByCoupon(order *models.Order, request *dto.VerifyPayment) *e.AppError
	HandleOrderSuccess(order *models.Order, orderItems []*models.OrderItem) *e.AppError
	FindMany(query *models.OrderQuery, options *models.FindManyOptions) ([]*models.Order, *e.AppError)
	ChangeReferralCode(order *models.Order, newRefCode string) *e.AppError
	ChangePaymentMethod(order *models.Order, newPaymentMethod *models.PaymentMethod) *e.AppError
	CalculateOrderAndSave(order *models.Order, coupon *models.Coupon, needSave bool) *e.AppError
	ValidateBeforeProcessPayment(user *models.User, order *models.Order, wallet *models.Wallet) *e.AppError
	PayThroughWallet(order *models.Order, wallet *models.Wallet) *e.AppError
	RemoveCoupon(order *models.Order, keepOrder bool) (*models.Order, *e.AppError)
}

type OrderItemServiceIface interface {
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.OrderItem, *e.AppError)
	FindOne(query *models.OrderItemQuery, options *models.FindOneOptions) (*models.OrderItem, *e.AppError)
	FindPage(query *models.OrderItemQuery, options *models.FindPageOptions) ([]*models.OrderItem, *models.Pagination, *e.AppError)
	Find(query *models.OrderItemQuery, options *models.FindManyOptions) ([]*models.OrderItem, *e.AppError)
}

type SectionServiceIface interface {
	Create(user *models.User, data *dto.CreateSectionRequest, defaultLesson bool) (*models.Section, *e.AppError)
	Update(section *models.Section, data *dto.UpdateSectionRequest) (*models.Section, *e.AppError)
	UpsertManyLesson(course *models.Course, user *models.User, data []*dto.CreateSectionRequest) ([]*models.Section, *e.AppError)
	FindOne(query *models.SectionQuery, options *models.FindOneOptions) (*models.Section, *e.AppError)
	FindPage(query *models.SectionQuery, options *models.FindPageOptions) ([]*models.Section, *models.Pagination, *e.AppError)
	Delete(section *models.Section) *e.AppError
	UpdateStats(section *models.Section, requireSave bool) *e.AppError
	BulkUpdate(request *dto.BulkUpdateSectionRequest, user *models.User) ([]*models.Section, *e.AppError)
	FindMany(query *models.SectionQuery, options *models.FindManyOptions) ([]*models.Section, *e.AppError)
	UpdateStatsLesson(section *models.Section, requireSave bool) *e.AppError
	CheckLessonIfExistInCondition(course *models.Course, lessonUID string) *e.AppError
}

type LessonContentServiceIface interface {
	UpsertMany(course *models.Course, user *models.User, data []*dto.LessonContentRequest) ([]*models.LessonContent, *e.AppError)
	Create(user *models.User, data *dto.LessonContentRequest) (*models.LessonContent, *e.AppError)
	Update(lesson *models.LessonContent, data *dto.LessonContentRequest) (*models.LessonContent, *e.AppError)
	FindOne(query *models.LessonContentQuery, options *models.FindOneOptions) (*models.LessonContent, *e.AppError)
	FindPage(query *models.LessonContentQuery, options *models.FindPageOptions) ([]*models.LessonContent, *models.Pagination, *e.AppError)
	FindMany(query *models.LessonContentQuery, options *models.FindManyOptions) ([]*models.LessonContent, *e.AppError)
	Delete(lesson *models.LessonContent) *e.AppError
	GetLessonContentByLessons(lessons []*models.Section) ([]*models.Section, *e.AppError)
	FindByCourseID(courseID string, options *models.FindManyOptions) ([]*models.LessonContent, *e.AppError)
}

type CategoryServiceIface interface {
	UpsertMany(reqs *dto.CreateCategoriesParams) *e.AppError
	FindPage(query *models.CategoryQuery, options *models.FindPageOptions) ([]*models.Category, *models.Pagination, *e.AppError)
	GetTree(query *models.CategoryQuery) ([]*models.TreeCategory, *e.AppError)
	DeleteMany(ids []string, orgID string) *e.AppError
	FindAll() ([]*models.Category, *e.AppError)
	FindOne(query *models.CategoryQuery, options *models.FindOneOptions) (*models.Category, *e.AppError)
	CheckPermissionUpsertCategories(user *models.User, org *models.Organization, req *dto.UpsertCategoriesRequest) *e.AppError
	FindMany(query *models.CategoryQuery, options *models.FindManyOptions) ([]*models.Category, *e.AppError)
}

type CategoryRelationServiceIface interface {
	Upsert(entity *dto.CreateCategoryRelationParams, needClearCache bool) *e.AppError
	FindBy(query *models.CategoryRelationQuery, options *models.FindManyOptions) ([]*models.CategoryRelation, *e.AppError)
	FindById(id string, includeDeleted bool, options *models.FindOneOptions) (*models.CategoryRelation, *e.AppError)
	Delete(id string) *e.AppError
	DeleteMany(query *models.CategoryRelationQuery) *e.AppError
	FindMany(query *models.CategoryRelationQuery, options *models.FindManyOptions) ([]*models.CategoryRelation, *e.AppError)
	FindPageJoinBlog(query *models.CategoryRelationQuery, options *models.FindPageOptions) ([]*models.CategoryRelation, *models.Pagination, *e.AppError)
	FindManyJoinBlog(query *models.CategoryRelationQuery, options *models.FindManyOptions) ([]*models.CategoryRelation, *e.AppError)
}

type HashtagServiceIface interface {
	CreateMany(hashtags []*models.Hashtag, orgID string) *e.AppError
	FindMany(query *models.HashtagQuery, options *models.FindManyOptions) ([]*models.Hashtag, *e.AppError)
	DeleteMany(query *models.HashtagQuery) *e.AppError
	FindPage(query *models.HashtagQuery, options *models.FindPageOptions) ([]*models.Hashtag, *models.Pagination, *e.AppError)
}

type HashtagRelationServiceIface interface {
	CreateMany(entities []*dto.CreateHashtagRelationParams) *e.AppError
	Create(entity *dto.CreateHashtagRelationParams) *e.AppError
	FindPage(query *models.HashtagRelationQuery, options *models.FindPageOptions) ([]*models.HashtagRelation, *models.Pagination, *e.AppError)
	Delete(id string) *e.AppError
	DeleteMany(query *models.HashtagRelationQuery) *e.AppError
	FindMany(query *models.HashtagRelationQuery, options *models.FindManyOptions) ([]*models.HashtagRelation, *e.AppError)
	BuildHashtagEntities(names []string, orgID string, relatedID string, relatedType models.ModelName) ([]*dto.CreateHashtagRelationParams, *e.AppError)
	FindManyJoinBlog(query *models.HashtagRelationQuery, options *models.FindManyOptions) ([]*models.HashtagRelation, *e.AppError)
}

type PageConfigServiceIface interface {
	Create(data *dto.PageConfigRequest) (*models.PageConfig, *e.AppError)
	CreateOrUpdateMany(data *dto.BulkCreatePageConfigRequest) ([]*models.PageConfig, *e.AppError)
	Delete(data *models.PageConfig) *e.AppError
	FindPage(query *models.PageConfigQuery, options *models.FindPageOptions) ([]*models.PageConfig, *models.Pagination, *e.AppError)
	FindOne(query *models.PageConfigQuery, options *models.FindOneOptions) (*models.PageConfig, *e.AppError)
}

type PageAccessServiceIface interface {
	Create(data *dto.PageAccessRequest, org *models.Organization) (*models.PageAccess, *e.AppError)                                               // FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Order, *e.AppError)
	CreateOrUpdate(data *dto.BulkCreatePageAccessRequest, org *models.Organization) ([]*models.PageAccess, []*dto.PageAccessRequest, *e.AppError) // FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Order, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.PageAccess, *e.AppError)
	FindPage(query *models.PageAccessQuery, options *models.FindPageOptions) ([]*models.PageAccess, *models.Pagination, *e.AppError)
	FindAll() ([]*models.PageAccess, *e.AppError)
	GetUserPermission(user *models.User, orgID string) ([]*models.PageAccess, *e.AppError)
	FindOne(query *models.PageAccessQuery, options *models.FindOneOptions) (*models.PageAccess, *e.AppError)
}

type CouponServiceIface interface {
	Create(user *models.User, data *dto.CreateCouponRequest) (*models.Coupon, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Coupon, *e.AppError)
	FindOne(query *models.CouponQuery, options *models.FindOneOptions) (*models.Coupon, *e.AppError)
	FindPage(query *models.CouponQuery, options *models.FindPageOptions) ([]*models.Coupon, *models.Pagination, *e.AppError)
	Update(c *models.Coupon, data *dto.UpdateCouponRequest) *e.AppError
	Delete(c *models.Coupon) *e.AppError
	CanUpdateCoupon(coupon *models.Coupon, user *models.User, org *models.Organization) bool
	Verify(org *models.Organization, user *models.User, order *models.Order, coupon *models.Coupon) (*models.Coupon, *e.AppError)
	ListCouponsByCreator(creator *models.User, query *models.CouponQuery, options *models.FindPageOptions) ([]*models.Coupon, *models.Pagination, *e.AppError)
}

type CouponHistoryServiceIface interface {
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.CouponHistory, *e.AppError)
	Update(history *models.CouponHistory) *e.AppError
	Create(user *models.User, order *models.Order, coupon *models.Coupon) *e.AppError
	FindOne(query *models.CouponHistoryQuery, options *models.FindOneOptions) (*models.CouponHistory, *e.AppError)
	FindPage(query *models.CouponHistoryQuery, options *models.FindPageOptions) ([]*models.CouponHistory, *models.Pagination, *e.AppError)
	FindMany(query *models.CouponHistoryQuery, options *models.FindManyOptions) ([]*models.CouponHistory, *e.AppError)
	CleanupExpiredReservations() *e.AppError
}

type FormServiceIface interface {
	Create(org *models.Organization, user *models.User, data *dto.CreateFormRequest) (*models.Form, *e.AppError)
	FindById(id string) (*models.Form, *e.AppError)
	FindMany(query *models.FormQuery, options *models.FindManyOptions) ([]*models.Form, *e.AppError)
	FindPage(query *models.FormQuery, options *models.FindPageOptions) ([]*models.Form, *models.Pagination, *e.AppError)
	FindBySlug(slug string) (*models.Form, *e.AppError)
	FindOne(query *models.FormQuery, options *models.FindOneOptions) (*models.Form, *e.AppError)
	Submit(user *models.User, form *models.Form, data *dto.SubmitFormRequest) (*models.FormSession, *e.AppError)
	Update(form *models.Form, data *dto.UpdateFormRequest) (*models.Form, *e.AppError)
	Summary(form *models.Form) (*models.FormSummary, *e.AppError)
	Duplicate(form *models.Form, isCloneNewVersion bool) (*models.Form, *e.AppError)
	Delete(form *models.Form) *e.AppError
	CheckFormCanBeSubmitted(org *models.Organization, user *models.User, form *models.Form) *e.AppError
	HandleEventForCourse(user *models.User, course *models.Course, event models.FormEvent) *e.AppError
	HandleEventsAfterSubmit(form *models.Form, formSession *models.FormSession, org *models.Organization, source string, ref string, refUser string) *e.AppError
	GetEmptyForm() (*models.Form, *e.AppError)
	HandleDuplicatedFormAnswer(form *models.Form, data *dto.SubmitFormRequest) *e.AppError
}

type FormRelationServiceIface interface {
	Create(req *dto.FormRelationRequest) (*models.FormRelation, *e.AppError)
	FindById(id string) (*models.FormRelation, *e.AppError)
	FindPage(query *models.FormRelationQuery, options *models.FindPageOptions) ([]*models.FormRelation, *models.Pagination, *e.AppError)
	Update(formRelation *models.FormRelation, req *dto.FormRelationRequest) (*models.FormRelation, *e.AppError)
	Delete(formRelation *models.FormRelation) *e.AppError
}

type FormAnswerServiceIface interface {
	FindBySessionID(sessionID string, options *models.FindManyOptions) ([]*models.FormAnswer, *e.AppError)
	FindPageAnswerStats(query *models.FormAnswerStatsQuery, options *models.FindPageOptions) ([]*models.FormAnswerStats, *models.Pagination, *e.AppError)
}

type FormQuestionServiceIface interface {
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.FormQuestion, *e.AppError)
	FindOne(query *models.FormQuestionQuery, options *models.FindOneOptions) (*models.FormQuestion, *e.AppError)
}

type FormSessionServiceIface interface {
	FindMany(query *models.FormSessionQuery, options *models.FindManyOptions) ([]*models.FormSession, *e.AppError)
	FindPage(query *models.FormSessionQuery, options *models.FindPageOptions) ([]*models.FormSession, *models.Pagination, *e.AppError)
	FindOne(query *models.FormSessionQuery, options *models.FindOneOptions) (*models.FormSession, *e.AppError)
	Update(session *models.FormSession) *e.AppError
	UpdateStatus(user *models.User, registerUser *models.User, org *models.Organization, session *models.FormSession, status models.FormSessionStatus, reason string) *e.AppError
	FindByID(id string, options *models.FindOneOptions) (*models.FormSession, *e.AppError)
	//HandleApproveFormSessionCreator(form *models.FormSession, data *dto.ApproveFormSessionCreator) *e.AppError
}

type SystemConfigServiceIface interface {
	Create(user *models.User, req *dto.SystemConfigRequest) (*models.SystemConfig, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.SystemConfig, *e.AppError)
	FindOne(query *models.SystemConfigQuery, options *models.FindOneOptions) (*models.SystemConfig, *e.AppError)
	FindPage(query *models.SystemConfigQuery, options *models.FindPageOptions) ([]*models.SystemConfig, *models.Pagination, *e.AppError)
	Update(user *models.User, s *models.SystemConfig, req *dto.SystemConfigRequest, curOrgID string) (*models.SystemConfig, *e.AppError)
	Delete(s *models.SystemConfig) *e.AppError
	CanUpdateSystemConfig(c *models.SystemConfig, user *models.User) bool
	FindAll(keys []*string, orgIDs []*string, domains []*string, locales []*string) ([]*models.SimpleConfig, *e.AppError)
	FindOneOptions(query *models.SystemConfigQuery, options *models.FindOneOptions) (*models.SystemConfig, *e.AppError)
}

type BlogServiceIface interface {
	Create(org *models.Organization, user *models.User, data *dto.CreateBlogRequest) (*models.Blog, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Blog, *e.AppError)
	FindOne(query *models.BlogQuery, options *models.FindOneOptions) (*models.Blog, *e.AppError)
	FindPage(query *models.BlogQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError)
	FindMany(query *models.BlogQuery, options *models.FindManyOptions) ([]*models.Blog, *e.AppError)
	Update(b *models.Blog, data *dto.UpdateBlogRequest, org *models.Organization) *e.AppError
	UpdateMany(query *models.BlogQuery, data map[string]interface{}, trans *gorm.DB) *e.AppError
	CanUpdateBlog(o *models.Blog, user *models.User, org *models.Organization) bool
	Delete(b *models.Blog) *e.AppError
	PublishBlog(user *models.User, approval *models.Approval) *e.AppError
	TogglePin(b *models.Blog, data *dto.PinBlogRequest) *e.AppError
	RejectPublishBlog(user *models.User, approval *models.Approval) *e.AppError
	GetPublishBlog(query *models.PublishBlogQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError)
	RequestPublishBlog(org *models.Organization, request *dto.PublishBlogRequest, isPassApproval bool) (*models.Blog, *e.AppError)
	HandlePublishBlogTypePersonal(user *models.User, org *models.Organization, blog *models.Blog) (*models.Blog, *e.AppError)
	UnpublishBlogOrg(user *models.User, org *models.Organization, cuid string) *e.AppError
	UnpublishBlogPerson(user *models.User, org *models.Organization, cuid string) *e.AppError
	CanPassApprovals(user *models.User, org *models.Organization) bool
	CanApproveOrgBlog(user *models.User, orgID string) bool
	CanPublishOrgBlog(user *models.User, orgID string) bool
	GetPublishedPersonalBlogsWithHighestVersion(*models.BlogQuery, *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError)
	GetPublishBlogByCategory(org *models.Organization, query *models.CategoryRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError)
	GetPublishBlogByCategoryParent(org *models.Organization, parentID string, eachCategoriesOptions *models.FindManyOptions) (map[string]*dto.BlogTreeByCateResponse, *e.AppError)
	GetRecommendPublishBlogByCategory(query *models.CategoryRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError)
	GetRecommendPublishBlogByHashtag(query *models.HashtagRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError)
	GetPublishBlogByHashtag(org *models.Organization, query *models.HashtagRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError)
	CanAddRoleWriter(user *models.User, org *models.Organization) bool
	CanAddRoleEditor(user *models.User, org *models.Organization) bool
	AddRemoveRoleWriter(org *models.Organization, data *dto.AddRoleWriterRequest) *e.AppError
	AddRemoveRoleEditor(org *models.Organization, data *dto.AddRoleEditorRequest) *e.AppError
	DeleteMany(query *models.BlogQuery) *e.AppError
	GetPublishBlogMany(query *models.PublishBlogQuery, options *models.FindManyOptions) ([]*models.Blog, *e.AppError)
}

type ApprovalServiceIface interface {
	Create(org *models.Organization, requester *models.User, data *dto.CreateApprovalRequest) (*models.Approval, *e.AppError)
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Approval, *e.AppError)
	FindOne(query *models.ApprovalQuery, options *models.FindOneOptions) (*models.Approval, *e.AppError)
	FindPage(query *models.ApprovalQuery, options *models.FindPageOptions) ([]*models.Approval, *models.Pagination, *e.AppError)
	FindMany(query *models.ApprovalQuery, options *models.FindManyOptions) ([]*models.Approval, *e.AppError)
	Update(approval *models.Approval) *e.AppError
	CanUpdateApproval(approval *models.Approval, org *models.Organization) bool
	Delete(approval *models.Approval) *e.AppError
	CanApprove(approval *models.Approval, user *models.User, request *dto.ApprovalRequest) *e.AppError
	CanRequest(requester *models.User, data *dto.CreateApprovalRequest, org *models.Organization) *e.AppError
	CanModifyApproval(approval *models.Approval, user *models.User) *e.AppError
	Approve(user *models.User, approval *models.Approval) *e.AppError
	Reject(user *models.User, approval *models.Approval) *e.AppError
	CanGetListApprovals(user *models.User, org *models.Organization) bool
	ApprovalSendFeedback(approval *models.Approval, user *models.User, feedback *dto.ApprovalFeedback) *e.AppError
	CancelOldRequest(approval *models.Approval) *e.AppError
	CreateWithTrans(org *models.Organization, requester *models.User, data *dto.CreateApprovalRequest, trans *gorm.DB) (*models.Approval, *e.AppError)
}

type UserActionServiceIface interface {
	FollowUser(params *dto.ActionParams) *e.AppError
	UnFollowUser(params *dto.ActionParams) *e.AppError
	BlockUser(params *dto.ActionParams) *e.AppError
	UnblockUser(params *dto.ActionParams) *e.AppError
	ReportUser(params *dto.ReportUserParams) *e.AppError
	FindPage(query *models.UserActionQuery, options *models.FindPageOptions) ([]*models.UserAction, *models.Pagination, *e.AppError)
	BlockListUsers(user *models.User, userBlockIDs []string) *e.AppError
}

type UserTokenServiceIface interface {
	FindPage(query *models.UserTokenQuery, options *models.FindPageOptions) ([]*models.UserToken, *models.Pagination, *e.AppError)
	FindOne(query *models.UserTokenQuery, options *models.FindOneOptions) (*models.UserToken, *e.AppError)
	FindMany(query *models.UserTokenQuery, options *models.FindManyOptions) ([]*models.UserToken, *e.AppError)
	ResendMail(data *dto.ResendMailInvitationRequest, org *models.Organization) *e.AppError
	Upsert(userToken *models.UserToken, trans *gorm.DB) error
}

type RoleServiceIface interface {
	Create(data *dto.RoleRequest) (*models.Role, *e.AppError)
	Update(data *models.Role) *e.AppError
	Delete(data *models.Role, orgId *string) *e.AppError
	FindByID(id string) (*models.Role, *e.AppError)
	FindPage(query *models.RoleQuery, options *models.FindPageOptions) ([]*models.Role, *models.Pagination, *e.AppError)
	InitNewOrganizationAccess(org *models.Organization) *e.AppError
	FindAll() ([]*models.Role, *e.AppError)
}

type WalletServiceIface interface {
	InitUserWallets(org *models.Organization, user *models.User) *e.AppError
	GetWalletsByUserID(userID string) ([]*models.Wallet, *e.AppError)
	GetWalletByUserIDAndType(userID string, walletType models.AssetType) (*models.Wallet, *e.AppError)
	GetWalletByUserIDAndCurrency(userID string, currency models.Currency) (*models.Wallet, *e.AppError)
	FindOne(query *models.WalletQuery, options *models.FindOneOptions) (*models.Wallet, *e.AppError)
	RequestToWithdraw(request *dto.CreateWithdrawRequest) *e.AppError
	ApproveWithdrawRequest(user *models.User, approval *models.Approval) *e.AppError
	RejectWithdrawRequest(user *models.User, approval *models.Approval) *e.AppError
	Sync(req *dto.WalletSyncRequest) (*models.Wallet, *e.AppError)
}

type TransactionServiceIface interface {
	FindPage(query *models.TransactionQuery, options *models.FindPageOptions) ([]*models.Transaction, *models.Pagination, *e.AppError)
	OrderSuccess(request *dto.CreateTransactionRequest, paymentMethod *models.PaymentMethod) *e.AppError
	ReferralEarn(request *dto.CreateTransactionRequest, paymentMethod *models.PaymentMethod) *e.AppError
	ReceivePoint(request *dto.CreateTransactionRequest) *e.AppError
	UsePoint(wallet *models.Wallet, amount decimal.Decimal, orgID string) *e.AppError
	Withdraw(request *dto.CreateTransactionRequest) (*models.Transaction, *e.AppError)
	Sync(req *dto.TransactionSyncRequest) ([]*models.Transaction, *e.AppError)
	ClaimEarning(req *dto.ClaimEarningsRequest) (*models.Transaction, *e.AppError)
	RetroactiveForAvail(wallet *models.Wallet, org *models.Organization, fileContent []byte) (*models.Transaction, *e.AppError)
	DepositNftSponsorGasFee(req *dto.DepositSponsorGasFeeRequest) (*models.Transaction, *e.AppError)
	WithdrawNftSponsorGasFee(req *dto.WithdrawSponsorGasFeeRequest) (*models.Transaction, *e.AppError)
	Pledge(req *dto.CreateInvestmentRequest) (*models.Transaction, *e.AppError)
	InitLaunchpadPool(req *dto.InitLaunchpadPoolRequest) (*models.Transaction, *e.AppError)
	ClaimLaunchpadRefund(req *dto.ClaimLaunchpadRefundRequest) (*models.Transaction, *e.AppError)
	LaunchpadProfitEarn(request *dto.CreateTransactionRequest, paymentMethod *models.PaymentMethod) *e.AppError
}

type CourseEnrollmentServiceIface interface {
	FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.CourseEnrollment, *e.AppError)
	FindOne(query *models.CourseEnrollmentQuery, options *models.FindOneOptions) (*models.CourseEnrollment, *e.AppError)
	FindPage(query *models.CourseEnrollmentQuery, options *models.FindPageOptions) ([]*models.CourseEnrollment, *models.Pagination, *e.AppError)
	FindMany(query *models.CourseEnrollmentQuery, options *models.FindManyOptions) ([]*models.CourseEnrollment, *e.AppError)
	Update(courseEnrollment *models.CourseEnrollment, data *dto.UpdateCourseEnrollmentRequest) *e.AppError
	VerifyCreateCourseEnrollment(orgID string, orgSchema string, user *models.User, course *models.Course, source string, refCode string) (*models.CourseEnrollment, *e.AppError)
	CanActionCourseEnrollment(courseCuid string, currentUser *models.User, org *models.Organization) *e.AppError
	EnrollUserFromFormSession(form *models.Form, formSession *models.FormSession, org *models.Organization, source string, ref string, refUser string) (*models.CourseEnrollment, *e.AppError)
}

type PublishCourseServiceIface interface {
	FindOne(query *models.PublishCourseQuery, options *models.FindOneOptions) (*models.PublishCourse, *e.AppError)
	Delete(id string) *e.AppError
	AddPublishCourse(course *models.Course, org *models.Organization, scope *models.PublishScope) *e.AppError
	UnPublishCourse(courseCuid string, target models.UnPublishCourseTarget) (*models.PublishCourse, *e.AppError)
	FindPagePublishCourse(query *models.PublishCourseQuery, options *models.FindPageOptions) ([]*models.PublishCourse, *models.Pagination, *e.AppError)
	ChangeStage(course *models.Course, request *dto.ChangeCourseStageRequest) *e.AppError
	FindBySlug(slug string, options *models.FindOneOptions) (*models.PublishCourse, *e.AppError)
}

type PublishBlogServiceIface interface {
	FindOne(query *models.PublishBlogQuery, options *models.FindOneOptions) (*models.PublishBlog, *e.AppError)
	Delete(id string) *e.AppError
	AddPublishBlog(blog *models.Blog, org *models.Organization) *e.AppError
	FindPage(query *models.PublishBlogQuery, options *models.FindPageOptions) ([]*models.PublishBlog, *models.Pagination, *e.AppError)
	UnpublishBlog(blogCuid string) (*models.PublishBlog, *e.AppError)
	Update(blog *models.PublishBlog) *e.AppError
	FindMany(query *models.PublishBlogQuery, options *models.FindManyOptions) ([]*models.PublishBlog, *e.AppError)
}

type QuizServiceIface interface {
	Create(creator *models.User, data *dto.QuizRequest) (*models.Quiz, *models.QuizRelation, *e.AppError)
	CreateMany(creator *models.User, quizRequests []*dto.QuizRequest) ([]*models.QuizWithRelation, *e.AppError)
	Update(quizRequest *dto.QuizRequest) (*models.Quiz, *models.QuizRelation, *e.AppError)
	UpdateMany(quizRequests []*dto.QuizRequest) ([]*models.QuizWithRelation, *e.AppError)
	Duplicate(user *models.User, quiz *models.Quiz, newRelation *dto.QuizRelationRequest) (*models.Quiz, *models.QuizRelation, *e.AppError)
	FindByID(quizID string) (*models.Quiz, *e.AppError)
	FindOne(query *models.QuizQuery, options *models.FindOneOptions) (*models.Quiz, *e.AppError)
	PreloadQuizzesForLessonContents(lessonContents []*models.LessonContent) *e.AppError
	FindQuizRelationByLessonContent(lessonContent *models.LessonContent) ([]*models.QuizRelation, *e.AppError)
	Delete(quiz *models.Quiz) *e.AppError
	CheckUserCanDoQuiz(user *models.User, quiz *models.Quiz) *e.AppError
	ConvertQuizToQuizRequest(quizWithRelation *models.QuizWithRelation) *dto.QuizRequest
}

type QuizSubmissionServiceIface interface {
	Create(user *models.User, quiz *models.Quiz, course *models.Course) (*models.QuizSubmission, *e.AppError)
	Update(submission *models.QuizSubmission) (*models.QuizSubmission, *e.AppError)
	FindOne(query *models.QuizSubmissionQuery, options *models.FindOneOptions) (*models.QuizSubmission, *e.AppError)
	FindByID(submissionID string, options *models.FindOneOptions) (*models.QuizSubmission, *e.AppError)
	FindPage(query *models.QuizSubmissionQuery, options *models.FindPageOptions) ([]*models.QuizSubmission, *models.Pagination, *e.AppError)
	FindCurrentQuestion(submission *models.QuizSubmission) (*models.QuizAnswer, *models.QuizQuestion, *e.AppError)
	SubmitAnswer(submission *models.QuizSubmission, req *dto.QuizQuestionSubmissionRequest) (*models.QuizAnswer, *e.AppError)
	FindRanksByQuizUID(quizUID string, userID string, top int) ([]*models.QuizSubmissionRank, *e.AppError)
	UpdateStatus(submission *models.QuizSubmission, newStatus models.QuizSubmissionStatus) (*models.QuizSubmission, *e.AppError)
	CheckFinalQuizCompletePercent(quiz *models.Quiz, submission *models.QuizSubmission, percentRequired int) bool
	FindTotalPointForUserByCourse(courseCuid, userID string) (int, *e.AppError)
	GetSubmissionSummary(user *models.User, quizIDs []string) ([]dto.QuizSubmissionSummary, *e.AppError)
}

type UserSettingServiceIface interface {
	CreateMany(org *models.Organization, user *models.User, data *dto.CreateSettingRequest) ([]*models.UserSetting, *e.AppError)
	UpdateMany(data *dto.CreateSettingRequest) ([]*models.UserSetting, *e.AppError)
	DeleteMany(ids []string) *e.AppError
	FindPage(query *models.UserSettingQuery, options *models.FindPageOptions) ([]*models.UserSetting, *models.Pagination, *e.AppError)
	FindById(id string, includeDeleted bool, options *models.FindOneOptions) (*models.UserSetting, *e.AppError)
	BuildCertificateSetting(userID *string, org *models.Organization, options *models.FindPageOptions) (*dto.SettingProfileResponse, *e.AppError)
	BuildCourseSetting(user *models.User, org *models.Organization, options *models.FindPageOptions) (*dto.SettingProfileResponse, *e.AppError)
	BuildBlogSetting(userID *string, org *models.Organization, options *models.FindPageOptions) (*dto.SettingProfileResponse, *e.AppError)
	UpsertSettingProfile(userID string, org *models.Organization, data *dto.UpdateSettingProfileRequest) *e.AppError
	UpsertSettingOldUsername(userID string, oldUsername string) *e.AppError
	CheckUsernameInputValid(username string) (bool, *e.AppError)
}

type AffiliateCampaignServiceIface interface {
	Create(request *dto.AffiliateCampaignRequest) (*models.AffiliateCampaign, *e.AppError)
	Update(campaign *models.AffiliateCampaign, request *dto.AffiliateCampaignRequest) (*models.AffiliateCampaign, *e.AppError)
	FindOne(query *models.AffiliateCampaignQuery, options *models.FindOneOptions) (*models.AffiliateCampaign, *e.AppError)
	FindPage(query *models.AffiliateCampaignQuery, options *models.FindPageOptions) ([]*models.AffiliateCampaign, *models.Pagination, *e.AppError)
	Delete(campaign *models.AffiliateCampaign) *e.AppError
	CanUpdateAffiliateCampaign(user *models.User, campaign *models.AffiliateCampaign) *e.AppError
	AddCourses(user *models.User, campaign *models.AffiliateCampaign, request *dto.AffiliateCampaignCourse) *e.AppError
	RemoveCourses(request *dto.DefaultDeleteManyRequest) *e.AppError
	FindCourseByCampaign(
		request *dto.FindCourseCampaignRequest,
		query *models.CourseCampaignQuery,
		options *models.FindPageOptions,
	) ([]*models.CourseCampaign, *models.Pagination, *e.AppError)
	FindPublishCourseByCampaign(
		campaign *models.AffiliateCampaign,
		query *models.PublishCourseQuery,
		options *models.FindPageOptions,
	) ([]*models.CourseCampaign, *models.Pagination, *e.AppError)
	FindCampaignByUser(
		userID string,
		query *models.UserCamapaignQuery,
		options *models.FindPageOptions,
	) ([]*models.UserCampaign, *models.Pagination, *e.AppError)
	GetAvailableCampaign(id string) (*models.AffiliateCampaign, *e.AppError)
}

type ReferrerServiceIface interface {
	CreateOrUpdateMany(campaign *models.AffiliateCampaign, request *dto.AffiliateCampaignReferrerRequest) ([]*dto.InviteReferrerResponse, *e.AppError)
	AddPurchasedReferrer(userID string, orgID string, link *models.ReferralLink) *e.AppError
	AddPurchasedUserOrderSuccess(user *models.User, org *models.Organization, course *models.Course) *e.AppError
	DeleteMany(request *dto.RemoveReferrerRequest) *e.AppError
	FindPage(query *models.ReferrerQuery, options *models.FindPageOptions) ([]*models.Referrer, *models.Pagination, *e.AppError)
	FindMany(query *models.ReferrerQuery, options *models.FindManyOptions) ([]*models.Referrer, *e.AppError)
	FindOne(query *models.ReferrerQuery, options *models.FindOneOptions) (*models.Referrer, *e.AppError)
	InviteReferrerSuccess(user *models.User, refererID string) *e.AppError
}

type CommissionServiceIface interface {
	Create(campaign *models.AffiliateCampaign, request *dto.CommissionRequest) (*models.Commission, *e.AppError)
	Update(commission *models.Commission, request *dto.CommissionRequest) (*models.Commission, *e.AppError)
	Delete(request *dto.CommissionDeleteRequest) *e.AppError
	FindPage(query *models.CommissionQuery, options *models.FindPageOptions) ([]*models.Commission, *models.Pagination, *e.AppError)
	FindMany(query *models.CommissionQuery, options *models.FindManyOptions) ([]*models.Commission, *e.AppError)
	FindOne(query *models.CommissionQuery, options *models.FindOneOptions) (*models.Commission, *e.AppError)
	GetApplicableCommission(user *models.User, campaign *models.AffiliateCampaign) ([]*models.Commission, *e.AppError)
	FindUserCommissionByCampaign(user *models.User, campaign *models.AffiliateCampaign) *e.AppError
	GetCommissionByCampaign(campaign *models.AffiliateCampaign) ([]*models.Commission, *e.AppError)
}

type ReferralServiceIface interface {
	CreateReferral(order *models.Order, courseID string, courseCuid string, save bool) (*dto.CreateReferralResponse, *e.AppError)
	FindPage(query *models.ReferralQuery, options *models.FindPageOptions) ([]*models.Referral, *models.Pagination, *e.AppError)
	SummaryReport(request *models.ReferralSummaryReport) (*models.BaseReferralReport, *e.AppError)
	GetUserSummaryReport(
		userID string,
		query *models.ReferralReportByUserQuery,
		options *models.FindPageOptions) ([]*models.ReferralReportByUser, *models.Pagination, *e.AppError)
}

type ReferralLinkServiceIface interface {
	CreateReferralLink(campaign *models.AffiliateCampaign, user *models.User, org *models.Organization) (*models.ReferralLink, *e.AppError)
	CreateReferralLinkByCommission(commission *models.Commission, user *models.User, org *models.Organization) (*models.ReferralLink, *e.AppError)
	CreateExtendReferral(user *models.User, fromLink *models.ReferralLink) (*models.ReferralLink, *e.AppError)
	UserEditRefLink(user *models.User, link *models.ReferralLink, request *dto.UpdateReferralLink) (*models.ReferralLink, *e.AppError)
	GetExtendedLink(user *models.User, campaign *models.AffiliateCampaign) (*models.ReferralLink, *e.AppError)
	FindPage(query *models.ReferralLinkQuery, options *models.FindPageOptions) ([]*models.ReferralLink, *models.Pagination, *e.AppError)
	FindMany(query *models.ReferralLinkQuery, options *models.FindManyOptions) ([]*models.ReferralLink, *e.AppError)
	FindOne(query *models.ReferralLinkQuery, options *models.FindOneOptions) (*models.ReferralLink, *e.AppError)
	FindByCode(ReferralCode string) (*models.ReferralLink, *e.AppError)
	GetLinkByUserAndCampaign(user *models.User, campaign *models.AffiliateCampaign) ([]*models.ReferralLink, *e.AppError)
	ValidateReferralCode(req *dto.ValidateReferralCodeRequest) (*models.ReferralLink, *e.AppError)
}
type UserRoleOrgServiceIface interface {
	FindByUserId(userID string) ([]*models.UserRoleOrg, *e.AppError)
	UpsertManyRole(urs []*models.UserRoleOrg, trans *gorm.DB) *e.AppError
}

type BookmarkServiceIface interface {
	Create(req *dto.BookmarkRequest) (*models.Bookmark, *e.AppError)
	FindPage(query *models.BookmarkQuery, options *models.FindPageOptions) ([]*models.Bookmark, *models.Pagination, *e.AppError)
	FindOne(query *models.BookmarkQuery, options *models.FindOneOptions) (*models.Bookmark, *e.AppError)
	FindByID(id string, options *models.FindOneOptions) (*models.Bookmark, *e.AppError)
	Update(bookmark *models.Bookmark, req *dto.BookmarkRequest) (*models.Bookmark, *e.AppError)
	Delete(bookmark *models.Bookmark) *e.AppError
}

type CertificateServiceIface interface {
	Create(req *dto.ClaimCertificateRequest) (*models.Certificate, *e.AppError)
	FindPage(query *models.CertificateQuery, options *models.FindPageOptions) ([]*models.Certificate, *models.Pagination, *e.AppError)
	Delete(*models.CertificateQuery) *e.AppError
	CheckingCertificateCondition(data *dto.CheckingCertificateConditionRequest) (bool, *e.AppError)
	FindOneOptions(query *models.CertificateQuery, options *models.FindOneOptions) (*models.Certificate, *e.AppError)
	FindByID(id string) (*models.Certificate, *e.AppError)
	PushNotificationReceiveCertificate(course *models.Course, user *models.User, org *models.Organization)
	MintNFT(req *dto.MintCertificateNFTRequest) (*models.Certificate, *e.AppError)
	EstimatedMintNFTFees(certificate *models.Certificate) (*dto.CertificateNFTFeesResponse, *e.AppError)
}

type HtmlTemplateServiceIface interface {
	Create(data *dto.HtmlTemplateRequest, userId string, orgId string) (*models.HtmlTemplate, *e.AppError)
	FindByID(id string) (*models.HtmlTemplate, *e.AppError)
	FindPage(org *models.Organization, options *models.FindPageOptions) ([]*models.HtmlTemplate, *models.Pagination, *e.AppError)
	DeleteByID(id string) *e.AppError
	Update(input dto.UpdateHtmlTemplateRequest, template *models.HtmlTemplate) *e.AppError
	FindOne(query *models.HtmlTemplateQuery, options *models.FindOneOptions) (*models.HtmlTemplate, *e.AppError)
	FindOneOptions(query *models.HtmlTemplateQuery, options *models.FindOneOptions) (*models.HtmlTemplate, *e.AppError)
	FindPageCertificateTemplateForCourse(course *models.Course, options *models.FindPageOptions, org *models.Organization) ([]*models.HtmlTemplate, *models.Pagination, *e.AppError)
	EnableCertificateLayer(template *models.HtmlTemplate, course *models.Course, userID string, orgID string, data *dto.EnableCertificateRequest) *e.AppError
	CheckPermAndCreateHTMLTemplates(data *dto.HtmlTemplateRequest, user *models.User, orgID string) (*models.HtmlTemplate, *e.AppError)
	CheckPermDeleteHTMLTemplates(template *models.HtmlTemplate, user *models.User, org *models.Organization) *e.AppError
	CheckPermUpdateHTMLTemplates(template *models.HtmlTemplate, user *models.User, orgID string) *e.AppError
	UpdateTemplateConfig(templateID string, orgID string, isDefault bool) *e.AppError
	EnableCertificateTemplateForOrg(templateID string, org *models.Organization, user *models.User) *e.AppError
	GetCertificateLayerForCourse(course *models.Course, user *models.User) (*models.HtmlTemplate, *e.AppError)
}

type WebhookServiceIface interface {
	HandleBunnyVideoWebhook(params *dto.BunnyVideoWebhookRequest) *e.AppError
}

type AIBlogRewriteServiceIface interface {
	FindOne(query *models.AIBlogRewriteQuery, options *models.FindOneOptions) (*models.AIBlogRewrite, *e.AppError)
	FindPage(query *models.AIBlogRewriteQuery, options *models.FindPageOptions) ([]*models.AIBlogRewrite, *models.Pagination, *e.AppError)
	Create(data *models.AIBlogRewrite) (*models.AIBlogRewrite, *e.AppError)
	Update(data *models.AIBlogRewrite) (*models.AIBlogRewrite, *e.AppError)
	FindMany(query *models.AIBlogRewriteQuery, options *models.FindManyOptions) ([]*models.AIBlogRewrite, *e.AppError)
	OfferHandleCreateBlogByAI(data []dto.CreateBlogByAI, org *models.Organization, currentUser *models.User, locale string, tone models.AITone) *e.AppError
	CronJobHandleGenerateBlog() *e.AppError
	HandleRewriteBlog(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError
	OfferRewriteBlogFromLink(link string, locale string, blogCuid string, org *models.Organization, user *models.User, tone models.AITone) (*dto.OfferRewriteBlogDataResponse, *e.AppError)
	OfferRewriteParagraph(text string, blogCuid string, org *models.Organization, user *models.User) (*dto.OfferRewriteBlogDataResponse, *e.AppError)
	GetRewriteBlogData(rewriteID string, user *models.User) (*dto.RewriteBlogByAIResponse, *e.AppError)
}

type UserSummaryServiceIface interface {
	GetOrgsAsWriter(user *models.User) ([]*models.SimpleOrganization, *e.AppError)
	FindMany(query *models.UserSummaryQuery, options *models.FindManyOptions) ([]*models.UserSummary, *e.AppError)
	FindOne(query *models.UserSummaryQuery, options *models.FindOneOptions) (*models.UserSummary, *e.AppError)
	Upsert(userSummary *models.UserSummary, trans *gorm.DB) *e.AppError
}

type ExternalServiceIface interface {
	GetPreloadUserActionAndFollowers(loggedUser *models.User, users []*communicationdto.UserResp) *e.AppError
}

type FileRelationServiceIface interface {
	CreateMany(rfs []*models.FileRelation) *e.AppError
	FindPage(query *models.FileRelationQuery, options *models.FindPageOptions) ([]*models.FileRelation, *models.Pagination, *e.AppError)
	FindOne(query *models.FileRelationQuery, options *models.FindOneOptions) (*models.FileRelation, *e.AppError)
	FindMany(query *models.FileRelationQuery, options *models.FindManyOptions) ([]*models.FileRelation, *e.AppError)
}

type ReportServiceIface interface {
	ReportUserEnrollmentCourse(data *models.ReportCourseEnrollmentRequest) ([]byte, *e.AppError)
	ReportOrderedAndEnrolled(query *models.ReportCourseEnrollmentRequest) ([]byte, *e.AppError)
	CanGetReport(data *models.ReportCourseEnrollmentRequest, currentUser *models.User, org *models.Organization) *e.AppError
	ReportReferralLinkByUser(data *models.ReportReferralLinkByUserRequest) ([]byte, *e.AppError)
	ReportReferralLinkAllUser(data *models.ReportReferralLinkByUserRequest) ([]byte, *e.AppError)
	FindStructuresReportType() map[string]map[string]interface{}
	ReportCourseEngagement() ([]byte, *e.AppError)
	ReportCourseQuiz() ([]byte, *e.AppError)
	ReportAIUserUsage(req *models.ReportAIUserUsageRequest) ([]byte, *e.AppError)
	ReportCourseLearningStatus(data *models.ReportCourseEnrollmentRequest) ([]byte, *e.AppError)
	HandleFindTrackingByChunk(query *models.TrackingQuery, chunksize int) ([]*communicationdto.TrackingResponse, *e.AppError)
}

type ExchangeRateServiceIface interface {
	GetExchangeRates() (*models.ExchangeRates, *e.AppError)
}

type AICourseServiceIface interface {
	FindOne(query *models.AICourseQuery, options *models.FindOneOptions) (*models.AICourse, *e.AppError)
	FindPage(query *models.AICourseQuery, options *models.FindPageOptions) ([]*models.AICourse, *models.Pagination, *e.AppError)
	Create(data *models.AICourse) (*models.AICourse, *e.AppError)
	Update(data *models.AICourse) (*models.AICourse, *e.AppError)
	FindMany(query *models.AICourseQuery, options *models.FindManyOptions) ([]*models.AICourse, *e.AppError)
	GenerateCourseByYoutube(params *dto.GenerateCourseFromYoutubeParams) (*models.Course, *e.AppError)
	CronJobGenerateAICourse() *e.AppError
	GenerateCourseByLearnerDescription(params *dto.GenerateCourseFromLearnerDescriptionParams) (*models.Course, *e.AppError)
	GenerateCourseThumbnail(params *dto.GenerateCourseThumbnailParams) (*models.Course, *e.AppError)
	GenerateOutlineFromLearnerDescription(params *dto.GenerateOutlineFromLearnerDescriptionParams) (*models.Course, *e.AppError)
}

type AIHistoryServiceIface interface {
	FindOne(query *models.AIHistoryQuery, options *models.FindOneOptions) (*models.AIHistory, *e.AppError)
	FindPage(query *models.AIHistoryQuery, options *models.FindPageOptions) ([]*models.AIHistory, *models.Pagination, *e.AppError)
	Create(data *models.AIHistory) (*models.AIHistory, *e.AppError)
	Update(data *models.AIHistory) (*models.AIHistory, *e.AppError)
	FindMany(query *models.AIHistoryQuery, options *models.FindManyOptions) ([]*models.AIHistory, *e.AppError)
	FindAndCreateHistory(params *dto.CreateAIHistoryParams) (*models.AIHistory, *e.AppError)
	CreateMany(data []*models.AIHistory) ([]*models.AIHistory, *e.AppError)
	GetCourseStatusByAIStatus(status ai.GenerateStatus) models.AIStatus
	GetQuestionTypeByAIQuizType(aiQuizType ai.QuizType) models.QuizQuestionType
	GetAIQuizTypeMapByQuestionType(quizQuestionType models.QuizQuestionType) ai.QuizType
	GetToneMapByAITone(tone ai.Tone) models.AITone
	GetAiToneMapByModelTone(tone models.AITone) ai.Tone
	GetAIDurationTypeMapByModelDurationType(durationType models.AICourseDurationType) ai.DurationType
	GetAIThumbnailStyleMapByModelThumbnailStyle(style models.AIThumbnailStyle) ai.ThumbnailStyle
}

type PricingPlanServiceIface interface {
	Create(request *dto.PricingPlanRequest) (*models.PricingPlan, *e.AppError)
	Update(plan *models.PricingPlan, request *dto.PricingPlanRequest) (*models.PricingPlan, *e.AppError)
	FindOne(query *models.PricingPlanQuery, options *models.FindOneOptions) (*models.PricingPlan, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.PricingPlan, *e.AppError)
	FindPage(query *models.PricingPlanQuery, options *models.FindPageOptions) ([]*models.PricingPlan, *models.Pagination, *e.AppError)
	FindMany(query *models.PricingPlanQuery, options *models.FindManyOptions) ([]*models.PricingPlan, *e.AppError)
	Delete(id string) *e.AppError
	GetFreePlan() (*models.PricingPlan, *e.AppError)
	EnablePlan(plan *models.PricingPlan, enable bool) *e.AppError
	GetInternalPlan() (*models.PricingPlan, *e.AppError)
}

type SubscriptionServiceIface interface {
	Create(subscription *models.Subscription) (*models.Subscription, *e.AppError)
	Update(subscription *models.Subscription, request *dto.SubscriptionRequest) (*models.Subscription, *e.AppError)
	FindOne(query *models.SubscriptionQuery, options *models.FindOneOptions) (*models.Subscription, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.Subscription, *e.AppError)
	FindPage(query *models.SubscriptionQuery, options *models.FindPageOptions) ([]*models.Subscription, *models.Pagination, *e.AppError)
	FindMany(query *models.SubscriptionQuery, options *models.FindManyOptions) ([]*models.Subscription, *e.AppError)
	Delete(id string) *e.AppError
	Subscribe(data *dto.SubscribePlanRequest) (*models.Subscription, *e.AppError)
	GetActivePlanByUser(user *models.User) (*models.PricingPlan, *e.AppError)
	GetCurrentSubscriptionInfo(user *models.User, orgID string) (*dto.GetSubscriptionInfo, *e.AppError)
	MigrateWhitelistAISubscription(whitelistEmails []string, orgID string) *e.AppError
	handleWhitelistSubscription(users []*models.User, isWhitelist bool) *e.AppError
	RemindExpirationByPeriod(period int) *e.AppError
	RemindExpiration() *e.AppError
	ExpiredSucriptions() *e.AppError
	HandleAutoSubscribeAIPlan(org *models.Organization, user *models.User) *e.AppError
	FindOneOptions(query *models.SubscriptionQuery, options *models.FindOneOptions) (*models.Subscription, *e.AppError)
}

type ResourceUsageServiceIface interface {
	Create(usage *models.ResourceUsage) (*models.ResourceUsage, *e.AppError)
	Update(usage *models.ResourceUsage) (*models.ResourceUsage, *e.AppError)
	FindOne(query *models.ResourceUsageQuery, options *models.FindOneOptions) (*models.ResourceUsage, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.ResourceUsage, *e.AppError)
	FindPage(query *models.ResourceUsageQuery, options *models.FindPageOptions) ([]*models.ResourceUsage, *models.Pagination, *e.AppError)
	FindMany(query *models.ResourceUsageQuery, options *models.FindManyOptions) ([]*models.ResourceUsage, *e.AppError)
	Delete(id string) *e.AppError
	AddAiUsage(user *models.User, amount decimal.Decimal, aiModelName string, orgID string) *e.AppError
	GetAiUsage(user *models.User, plan *models.PricingPlan, orgID string) (*models.ResourceUsage, *e.AppError)
	CheckAiLimitationUsage(user *models.User, plan *models.PricingPlan, aiModelID string, orgID string) (*dto.LimitResourceResponse, *e.AppError)
	CheckValidUsage(user *models.User, resourceType models.ResourceType, aiModelID string, orgID string) (*dto.ValidUsageResponse, *e.AppError)
}

type AIModelServiceIface interface {
	FindOne(query *models.AIModelQuery, options *models.FindOneOptions) (*models.AIModel, *e.AppError)
	FindPage(query *models.AIModelQuery, options *models.FindPageOptions) ([]*models.AIModel, *models.Pagination, *e.AppError)
	Create(data *dto.AIModelRequest) (*models.AIModel, *e.AppError)
	CreateMany(data []*models.AIModel) ([]*models.AIModel, *e.AppError)
	Update(data *models.AIModel) (*models.AIModel, *e.AppError)
	FindMany(query *models.AIModelQuery, options *models.FindManyOptions) ([]*models.AIModel, *e.AppError)
	GetAvailableAIModel(user *models.User, org *models.Organization) ([]*dto.GetAvailableAIModel, *e.AppError)
	MigrateAIModel() *e.AppError
	FindOneAvailableAIModel(user *models.User, orgID string, extendedThinking bool) (*dto.GetAvailableAIModel, *e.AppError)
}

type EmailServiceIface interface {
	SendEmail(user *models.User, org *models.Organization, event models.EventType, extendDatas models.MapEmailParams, isQueue bool) *e.AppError
	InitDefaultEmailTemplateForOrg(org *models.Organization) *e.AppError
	CreateEmailTemplate(req *dto.CreateEmailTemplateRequest, user *models.User, org *models.Organization) ([]byte, *e.AppError)
	UpdateEmailTemplate(req *dto.UpdateEmailTemplateRequest, templateID string, user *models.User, org *models.Organization) *e.AppError
	FindOneEmailTemplate(templateID string) (*communicationdto.EmailTemplate, *e.AppError)
	FindPageEmailTemplate(query *models.EmailTemplateQuery, options *models.FindPageOptions) ([]*communicationdto.EmailTemplate, *communicationdto.Pagination, *e.AppError)
	DeleteEmailTemplate(templateID string, user *models.User, org *models.Organization) *e.AppError
	PreviewEmailTemplate(org *models.Organization, templateID string, req *dto.PreviewEmailTemplateRequest) ([]byte, *e.AppError)
	FindEmailTemplateVariables() (map[communicationdto.EmailCodeType][]communicationdto.EmailTemplateVarName, *e.AppError)
}

type ChainServiceIface interface {
	GetAccountInfo(req *dto.GetAccountInfoRequest) (*dto.GetAccountInfoResponse, *e.AppError)
}

type OEPointCampaignServiceIface interface {
	GetPointSystemConfig() *models.OEPointSystemConfig
	ReferralUserCampaign(request *dto.RefUserConfigRequest, config *models.OEPointCampaign) (*models.OEPointCampaign, *e.AppError)
	GetActiveReferralUserCampaign() ([]*models.OEPointCampaign, *e.AppError)
	Update(campaign *models.OEPointCampaign) (*models.OEPointCampaign, *e.AppError)
	FindOne(query *models.OEPointCampaignQuery, options *models.FindOneOptions) (*models.OEPointCampaign, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.OEPointCampaign, *e.AppError)
	FindPage(query *models.OEPointCampaignQuery, options *models.FindPageOptions) ([]*models.OEPointCampaign, *models.Pagination, *e.AppError)
	FindMany(query *models.OEPointCampaignQuery, options *models.FindManyOptions) ([]*models.OEPointCampaign, *e.AppError)
	Delete(id string) *e.AppError
}

type OEPointHistoryServiceIface interface {
	Create(history *models.OEPointHistory) (*models.OEPointHistory, *e.AppError)
	Update(history *models.OEPointHistory) (*models.OEPointHistory, *e.AppError)
	FindOne(query *models.OEPointHistoryQuery, options *models.FindOneOptions) (*models.OEPointHistory, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.OEPointHistory, *e.AppError)
	FindPage(query *models.OEPointHistoryQuery, options *models.FindPageOptions) ([]*models.OEPointHistory, *models.Pagination, *e.AppError)
	FindMany(query *models.OEPointHistoryQuery, options *models.FindManyOptions) ([]*models.OEPointHistory, *e.AppError)
	Delete(id string) *e.AppError
	ClaimPoint(request *dto.UserClaimPointRequest) (*models.OEPointHistory, *e.AppError)
	UsePoint(user *models.User, amount int, entityID string, entityType models.ModelName) *e.AppError
	HandleExpiredPoint() *e.AppError
	PointExpirationReminder(period int) *e.AppError
	GetUserPoints(userID string) (int, *e.AppError)
	GetUserReferralEarnedPoints(userID string) (int, *e.AppError)
	Count(query *models.OEPointHistoryQuery) (int64, *e.AppError)
	GetReferralProgramPoints(campaign *models.OEPointCampaign) (*dto.UserPointResponse, *e.AppError)
	ConfirmClaimNewPointsBySources(sources []models.PointSource) *e.AppError
}

type FeaturedContentServiceIface interface {
	UpsertManyFeaturedContent(request *dto.BulkUpdateFeaturedContent) ([]*models.FeaturedContent, *e.AppError)
	FindByEntityTypeAndType(
		orgID string,
		entityType models.ModelName,
		contentType models.FeaturedContentType,
	) ([]*models.FeaturedContent, *e.AppError)
	FindPage(
		query *models.FeaturedContentQuery,
		options *models.FindPageOptions) ([]*models.FeaturedContent, *models.Pagination, *e.AppError)
}

type ClpLaunchpadServiceIface interface {
	Create(org *models.Organization, user *models.User, data *dto.CreateClpLaunchpadRequest) (*models.ClpLaunchpad, *e.AppError)
	FindPage(query *models.LaunchpadQuery, options *models.FindPageOptions) ([]*models.ClpLaunchpad, *models.Pagination, *e.AppError)
	FindOne(query *models.LaunchpadQuery, options *models.FindOneOptions) (*models.ClpLaunchpad, *e.AppError)
	FindOneOptions(query *models.LaunchpadQuery, options *models.FindOneOptions) (*models.ClpLaunchpad, *e.AppError)
	FindByID(id string) (*models.ClpLaunchpad, *e.AppError)
	Delete(launchpad *models.ClpLaunchpad) *e.AppError
	Update(launchpad *models.ClpLaunchpad, data *dto.UpdateClpLaunchpadRequest) (*models.ClpLaunchpad, *e.AppError)
	GetLaunchpadPartnerProfile(userID string) (*models.LaunchpadPartner, *e.AppError)
	FindLaunchpadPartner(launchpad *models.ClpLaunchpad, options *models.FindPageOptions) (*dto.ListLaunchpadPartnerResponse, *e.AppError)
	CourseCanMakeLaunchpad(user *models.User, courseCuid string, launchpadID *string) (*models.Course, *e.AppError)
	AssignUserStatusToLaunchpads(user *models.User, launchpads []*models.ClpLaunchpad) *e.AppError
	RequestPublishLaunchpad(req *dto.PublishLaunchpadRequest) (*models.Approval, *e.AppError)
	RejectPublishLaunchpad(user *models.User, approval *models.Approval) *e.AppError
	PublishLaunchpad(user *models.User, approval *models.Approval) *e.AppError
	CancelRequest(launchpad *models.ClpLaunchpad) *e.AppError
	CanUpdate(launchpad *models.ClpLaunchpad, user *models.User) *e.AppError
	GetLaunchpadDetail(launchpadID string, options *models.FindOneOptions) (*models.ClpLaunchpad, *e.AppError)
	GetInvestmentStats(launchpads []*models.ClpLaunchpad) *e.AppError
	StartVoting(req *dto.OpenVotingMilestoneRequest) *e.AppError
	ApprovePool(req *dto.ApproveLaunchpadPoolRequest) *e.AppError
	GetVotingPowers(launchpad *models.ClpLaunchpad) ([]*models.LaunchpadVotingPowerEntry, *e.AppError)
	StartFunding(launchpadID string) *e.AppError
	EndFunding(launchpad *models.ClpLaunchpad) *e.AppError
	CheckFundingResult(launchpad *models.ClpLaunchpad) (string, *e.AppError)
	AssignPledgeStatusToLaunchpad(user *models.User, launchpad *models.ClpLaunchpad) *e.AppError
	PreloadVotingProcessLaunchpad(user *models.User, launchpad *models.ClpLaunchpad) *e.AppError
	SetFundingTime(req *dto.SetFundingTimeLaunchpadRequest) (*models.ClpLaunchpad, *e.AppError)
	CheckSettingFundingTime(req *dto.FundingLaunchpadRequest) *e.AppError
	Cancel(req *dto.CancelLaunchpadRequest) *e.AppError
	CheckCreatorContinueVotingLaunchpad(req *dto.FundingLaunchpadRequest) *e.AppError
	DecideContinueVotingLaunchpad(launchpad *models.ClpLaunchpad, req *dto.DecideContinueVotingRequest) *e.AppError
	SyncJobForClpLaunchpad(req *dto.SchedulerClpLaunchpadRequest) *e.AppError
	PushNotificationsForReqPublish(launchpad *models.ClpLaunchpad, approval *models.Approval, requester *models.User)
	MakeNotificationPropsForLaunchpad(launchpad *models.ClpLaunchpad, org *models.Organization, user *models.User) communicationdto.JSONB
	GetVotingPowersOffChain(launchpad *models.ClpLaunchpad) ([]*models.LaunchpadVotingPowerEntry, *e.AppError)
	//UpdatePoolFundingTime(req *dto.UpdateLpPoolFundingTimeRequest) *e.AppError
}

type ClpCourseLaunchpadServiceIface interface {
	Create(course *models.Course, launchpad *models.ClpLaunchpad) (*models.ClpCourseLaunchpad, *e.AppError)
}

type ClpInvestmentServiceIface interface {
	Create(data *dto.CreateInvestmentRequest) (*models.ClpInvestment, *e.AppError)
	FindInvestmentsLaunchpad(launchpad *models.ClpLaunchpad, options *models.FindPageOptions) (*dto.ListClpInvestmentResponse, *e.AppError)
	FindByID(id string) (*models.ClpInvestment, *e.AppError)
	FindMyLaunchpads(user *models.User, query *models.ClpInvestmentQuery, options *models.FindPageOptions) (*dto.MyLaunchpadsResponse, *e.AppError)
}

type ClpVotingMilestoneServiceIface interface {
	FindByID(id string) (*models.ClpVotingMilestone, *e.AppError)
	VotingForLaunchpad(req *dto.VotingLaunchpadRequest) *e.AppError
	EndVoting(req *dto.EndVotingMilestoneRequest) *e.AppError
	AutoVotingApprove(data *dto.VotingLaunchpadRequest, investments []*models.ClpInvestment) *e.AppError
	GetVotingResultForVotingMilestone(userID string, backers []*models.LaunchpadVotingPowerEntry, milestone *models.ClpVotingMilestone) (*models.VotingProcess, *e.AppError)
	PushNotifForBackerWhenCreatorEndLaunchpad(launchpad *models.ClpLaunchpad)
}

type AIAgentPromptServiceIface interface {
	FindPage(query *models.AIPromptQuery, options *models.FindPageOptions) ([]*models.AIPrompt, *models.Pagination, *e.AppError)
	FindOneOptions(query *models.AIPromptQuery, options *models.FindOneOptions) (*models.AIPrompt, *e.AppError)
	FindByID(id string) (*models.AIPrompt, *e.AppError)
	Create(data *dto.AIPromptRequest) (*models.AIPrompt, *e.AppError)
	CreateMany(data *dto.AIPromptsRequest, userID string) *e.AppError
}

type OEReferralServiceIface interface {
	Create(history *models.OEReferral) (*models.OEReferral, *e.AppError)
	Update(history *models.OEReferral) (*models.OEReferral, *e.AppError)
	FindOne(query *models.OEReferralQuery, options *models.FindOneOptions) (*models.OEReferral, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.OEReferral, *e.AppError)
	FindPage(query *models.OEReferralQuery, options *models.FindPageOptions) ([]*models.OEReferral, *models.Pagination, *e.AppError)
	FindMany(query *models.OEReferralQuery, options *models.FindManyOptions) ([]*models.OEReferral, *e.AppError)
	Delete(id string) *e.AppError
	RefNewUserProgram(code string, referee *models.User) *e.AppError
	RefUserReward(request *dto.OEReferralRequest) *e.AppError
	InviteReferee(request dto.OEReferralInviteRefereeRequest) *e.AppError
}

type OEReferralCodeServiceIface interface {
	AddReferralCode(userID string) (*models.OEReferralCode, *e.AppError)
	AddReferrerForReferee(referee *models.User, referrer *models.User, campaign *models.OEPointCampaign) (*models.OEReferralCode, *e.AppError)
	GetUserReferralCode(userID string) (*models.OEReferralCode, *e.AppError)
	Create(history *models.OEReferralCode) (*models.OEReferralCode, *e.AppError)
	Update(history *models.OEReferralCode) (*models.OEReferralCode, *e.AppError)
	FindOne(query *models.OEReferralCodeQuery, options *models.FindOneOptions) (*models.OEReferralCode, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.OEReferralCode, *e.AppError)
	FindByCode(code string, options *models.FindOneOptions) (*models.OEReferralCode, *e.AppError)
	FindPage(query *models.OEReferralCodeQuery, options *models.FindPageOptions) ([]*models.OEReferralCode, *models.Pagination, *e.AppError)
	FindMany(query *models.OEReferralCodeQuery, options *models.FindManyOptions) ([]*models.OEReferralCode, *e.AppError)
	Delete(id string) *e.AppError
}

type LearningStatusServiceIface interface {
	AddLearningStatus(lp *dto.CreateLearningProgressParams) (*models.LearningProgressOverview, *models.Course, *e.AppError)
	UpdateLatestLesson(req *dto.UpdateCurrentLessonRequest) (*models.LearningProgressOverview, *models.Course, *e.AppError)
	GetUserLearningStatusByCourse(courseSlug string, userID string) (*models.LearningProgressOverview, *models.Course, *e.AppError)
	HandleUserRefGame(orgID string, userID string, courseCuid string) *e.AppError
	FindMany(query *models.LearningStatusQuery, options *models.FindManyOptions) ([]*models.LearningStatus, *e.AppError)
	FindOne(query *models.LearningStatusQuery, options *models.FindOneOptions) (*models.LearningStatus, *e.AppError)
	CheckCompleteCourse(req dto.CheckUserCompleteCourse) ([]dto.CheckUserCompleteCourseResp, *e.AppError)
	HandleRemindLearner()
}

type ScheduleServiceIface interface {
	Create(s *models.Schedule) (*models.Schedule, *e.AppError)
	Update(s *models.Schedule) (*models.Schedule, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.Schedule, *e.AppError)
	FindMany(query *models.ScheduleQuery, options *models.FindManyOptions) ([]*models.Schedule, *e.AppError)
	Delete(id string) *e.AppError
}

type EventScheduleServiceIface interface {
	Create(s *models.EventSchedule) (*models.EventSchedule, *e.AppError)
	Update(s *models.EventSchedule) (*models.EventSchedule, *e.AppError)
	FindOne(query *models.EventScheduleQuery, options *models.FindOneOptions) (*models.EventSchedule, *e.AppError)
	FindById(id string, options *models.FindOneOptions) (*models.EventSchedule, *e.AppError)
	FindPage(query *models.EventScheduleQuery, options *models.FindPageOptions) ([]*models.EventSchedule, *models.Pagination, *e.AppError)
	FindMany(query *models.EventScheduleQuery, options *models.FindManyOptions) ([]*models.EventSchedule, *e.AppError)
	Delete(id string) *e.AppError
}

type OEReferralLeaderBoardServiceIface interface{}

type OEReferralReportServiceIface interface {
	GenerateReports(req *dto.GenerateOEReferralReportsRequest) *e.AppError
	FindWidgetStatistic(campaignKey string, req *dto.OERefWidgetStatisticRequest) (*dto.OERefWidgetStatisticResponse, *e.AppError)
	FindLearnerGrowthStatistic(campaignKey string, req *dto.OERefLearnerGrowthStatisticRequest) (*dto.OERefLearnerGrowthStatisticResponse, *e.AppError)
	FindSectionCompletionStatistic(campaignKey string, req *dto.OERefSectionCompletionRequest) (dto.OERefSectionCompletionResponse, *e.AppError)
	FindLearnerCountByProvinces(campaignKey string, req *dto.OERefLearnerCountByProvinceRequest) (dto.OERefLearnerCountByProvinceResponse, *e.AppError)
	FindDetailStatsByProvinces(campaignKey string, req *dto.OERefDetailsByProvinceStatsRequest) ([]*dto.OERefByProvinceStatItem, *e.AppError)
	FindPageLearners(campaignKey string, req *dto.OERefLearnersByCampaignRequest) (*dto.OERefLearnersByCampaignResponse, *e.AppError)
}

var AppServices ServiceIface
var Auth AuthServiceIface
var SnsAccount SnsAccountServiceIface
var User UserServiceIface
var Upload UploadServiceIface
var Organization OrganizationServiceIface
var Course CourseServiceIface
var PublishCourse PublishCourseServiceIface
var PublishBlog PublishBlogServiceIface
var Payment PaymentServiceIface
var PaymentMethod PaymentMethodServiceIface
var Order OrderServiceIface
var OrderItem OrderItemServiceIface
var Section SectionServiceIface
var LessonContent LessonContentServiceIface
var Category CategoryServiceIface
var CategoryRelation CategoryRelationServiceIface
var Hashtag HashtagServiceIface
var HashtagRelation HashtagRelationServiceIface
var Coupon CouponServiceIface
var CouponHistory CouponHistoryServiceIface
var Form FormServiceIface
var FormRelation FormRelationServiceIface
var FormQuestion FormQuestionServiceIface
var FormSession FormSessionServiceIface
var FormAnswer FormAnswerServiceIface
var SystemConfig SystemConfigServiceIface
var Blog BlogServiceIface
var Approval ApprovalServiceIface
var UserAction UserActionServiceIface
var PageConfig PageConfigServiceIface
var PageAccess PageAccessServiceIface
var Role RoleServiceIface
var Wallet WalletServiceIface
var Transaction TransactionServiceIface
var UserToken UserTokenServiceIface
var Quiz QuizServiceIface
var QuizSubmission QuizSubmissionServiceIface
var CourseEnrollment CourseEnrollmentServiceIface
var UserSetting UserSettingServiceIface
var Session SessionServiceIface
var Bookmark BookmarkServiceIface
var Certificate CertificateServiceIface
var HtmlTemplate HtmlTemplateServiceIface
var AffiliateCampaign AffiliateCampaignServiceIface
var Referrer ReferrerServiceIface
var Commission CommissionServiceIface
var Referral ReferralServiceIface
var ReferralLink ReferralLinkServiceIface
var UserRoleOrg UserRoleOrgServiceIface
var Webhook WebhookServiceIface
var AIBlogRewrite AIBlogRewriteServiceIface
var UserSummary UserSummaryServiceIface
var External ExternalServiceIface
var FileRelation FileRelationServiceIface
var Report ReportServiceIface
var ExchangeRate ExchangeRateServiceIface
var AICourse AICourseServiceIface
var AIHistory AIHistoryServiceIface
var PricingPlan PricingPlanServiceIface
var Subscription SubscriptionServiceIface
var ResourceUsage ResourceUsageServiceIface
var Email EmailServiceIface
var AIModel AIModelServiceIface
var Chain ChainServiceIface
var ClpLaunchpad ClpLaunchpadServiceIface
var ClpCourseLaunchpad ClpCourseLaunchpadServiceIface
var ClpInvestment ClpInvestmentServiceIface
var ClpVotingMilestone ClpVotingMilestoneServiceIface
var AIAgentPrompt AIAgentPromptServiceIface
var QueueProducer producer.Producer
var LearningStatus LearningStatusServiceIface

func Setup() {
	queueConsumer, err := consumer.NewConsumer(consumer.RabbitMQ)
	if err != nil {
		log.Fatalf("Init queue consumer failed: %v", err)
	}

	QueueProducer, err = producer.NewProducer(producer.RabbitMQ)
	if err != nil {
		log.Error("failed to create producer: %v", err)
	}

	AppServices = &Service{}
	Auth = &AuthService{}
	Upload = &UploadService{}
	SnsAccount = &SnsAccountService{}
	User = &UserService{}
	Organization = &OrganizationService{}
	Course = &CourseService{BaseService{
		ctx: context.Background(),
	}}
	PublishCourse = &PublishCourseService{BaseService{
		ctx: context.Background(),
	}}
	PublishBlog = &PublishBlogService{}
	Payment = &PaymentService{}
	PaymentMethod = &PaymentMethodService{}
	Order = &OrderService{}
	OrderItem = &OrderItemService{}
	Section = &SectionService{}
	LessonContent = &LessonContentService{BaseService{
		ctx: context.Background(),
	}}
	Category = &CategoryService{}
	CategoryRelation = &CategoryRelationService{}
	Hashtag = &HashtagService{}
	HashtagRelation = &HashtagRelationService{}
	Coupon = &CouponService{}
	CouponHistory = &CouponHistoryService{}
	Form = &FormService{}
	FormRelation = &FormRelationService{}
	FormQuestion = &FormQuestionService{}
	FormAnswer = &FormAnswerService{}
	FormSession = &FormSessionService{}
	SystemConfig = &SystemConfigService{}
	Blog = &BlogService{}
	Approval = &ApprovalService{}
	UserAction = &UserActionService{}
	PageConfig = &PageConfigService{}
	PageAccess = &PageAccessService{}
	Role = &RoleService{}
	Wallet = &WalletService{}
	Transaction = &TransactionService{queueConsumer: queueConsumer}
	UserToken = &UserTokenService{}
	CourseEnrollment = &CourseEnrollmentService{}
	Quiz = &QuizService{}
	QuizSubmission = &QuizSubmissionService{}
	UserSetting = &UserSettingService{}
	Session = &SessionService{}
	Bookmark = &BookmarkService{}
	Certificate = &CertificateService{}
	HtmlTemplate = &HtmlTemplateService{}
	AffiliateCampaign = &AffiliateCampaignService{}
	Referrer = &ReferrerService{}
	Commission = &CommissionService{}
	Referral = &ReferralService{}
	ReferralLink = &ReferralLinkService{}
	UserRoleOrg = &UserRoleOrgService{}
	Webhook = &WebhookService{}
	AIBlogRewrite = &AIBlogRewriteService{}
	UserSummary = &UserSummaryService{}
	External = &ExternalService{}
	FileRelation = &FileRelationService{}
	Report = &ReportService{}
	ExchangeRate = &ExchangeRateService{}
	AICourse = &AICourseService{}
	AIHistory = &AIHistoryService{}
	PricingPlan = &PricingPlanService{}
	Subscription = &SubscriptionService{}
	ResourceUsage = &ResourceUsageService{}
	Email = &EmailService{}
	AIModel = &AIModelService{}
	Chain = &ChainService{}
	ClpLaunchpad = &ClpLaunchpadService{}
	ClpCourseLaunchpad = &ClpCourseLaunchpadService{}
	ClpInvestment = &ClpInvestmentService{}
	ClpVotingMilestone = &ClpVotingMilestoneService{}
	AIAgentPrompt = &AIAgentPromptService{}
	LearningStatus = &LearningStatusService{}
}

func NewCourse(ctx context.Context) *CourseService {
	return &CourseService{
		BaseService{ctx: ctx},
	}
}

func NewFeaturedContentService(ctx context.Context) *FeaturedContentService {
	return &FeaturedContentService{
		BaseService{ctx: ctx},
	}
}

func OEPointHistory(ctx context.Context) *OEPointHistoryService {
	return &OEPointHistoryService{
		BaseService{ctx: ctx},
	}
}

func OEPointCampaign(ctx context.Context) *OEPointCampaignService {
	return &OEPointCampaignService{
		BaseService{ctx: ctx},
	}
}

func OEReferral(ctx context.Context) *OEReferralService {
	return &OEReferralService{
		BaseService{ctx: ctx},
	}
}

func OEReferralCode(ctx context.Context) *OEReferralCodeService {
	return &OEReferralCodeService{
		BaseService{ctx: ctx},
	}
}

func OEAIGovCampaign(ctx context.Context) *OEAIGovCampaignService {
	return &OEAIGovCampaignService{
		BaseService{ctx: ctx},
	}
}

func NewLearningStatus(ctx context.Context) LearningStatusServiceIface {
	return &LearningStatusService{
		BaseService{ctx: ctx},
	}
}

func NewPublishCourse(ctx context.Context) PublishCourseServiceIface {
	return &PublishCourseService{
		BaseService{ctx: ctx},
	}
}
func NewLessonContent(ctx context.Context) LessonContentServiceIface {
	return &LessonContentService{
		BaseService{ctx: ctx},
	}
}

func OEReferralLeaderBoard(ctx context.Context) *OEReferralLeaderBoardService {
	return &OEReferralLeaderBoardService{
		BaseService{ctx: ctx},
	}
}

func NewUser(ctx context.Context) *UserService {
	return &UserService{
		BaseService{ctx: ctx},
	}
}

func OEReferralReport(ctx context.Context) OEReferralReportServiceIface {
	return &OEReferralReportService{
		BaseService{ctx: ctx},
	}
}
func NewSchedule(ctx context.Context) *ScheduleService {
	return &ScheduleService{
		BaseService{ctx: ctx},
	}
}

func NewEventSchedule(ctx context.Context) *EventScheduleService {
	return &EventScheduleService{
		BaseService{ctx: ctx},
	}
}
