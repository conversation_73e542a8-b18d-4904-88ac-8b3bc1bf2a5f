package services

import (
	"errors"
	"net/http"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"time"

	"gorm.io/gorm"
)

func (s *SnsAccountService) UpsertSnsAccount(snsAccount *models.SnsAccount) (*models.SnsAccount, error) {
	// cover case SNS Account has no email
	if snsAccount.Email == "" {
		snsAccount.Email = snsAccount.AccountID + setting.DefaultEmailDomain()
	}

	accountFind, err := models.Repository.SnsAccount.FindByAccountIDAndProvider(snsAccount.AccountID, string(snsAccount.Provider))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			snsAccount.LastLogin = time.Now().UnixMilli()
			accountNew, createErr := models.Repository.SnsAccount.Create(snsAccount)
			if createErr != nil {
				return nil, createErr
			}

			return accountNew, nil
		}

		return nil, err
	}

	accountFind.Username = snsAccount.Username
	accountFind.Avatar = snsAccount.Avatar
	accountFind.Email = snsAccount.Email
	accountFind.Profile = snsAccount.Profile
	accountFind.Provider = snsAccount.Provider
	accountFind.AccessToken = snsAccount.AccessToken
	accountFind.RefreshToken = snsAccount.RefreshToken
	accountFind.LastLogin = time.Now().UnixMilli()

	accountUpdated, updateErr := models.Repository.SnsAccount.Update(accountFind, nil)
	if updateErr != nil {
		return nil, updateErr
	}

	return accountUpdated, nil
}

func (s *SnsAccountService) GetLinkedSnsAccountsForSettings(user *models.User) ([]*models.SimpleSnsAccount, error) {
	// Find linked sns accounts for user
	querySnsAccounts := &models.FindSnsAccountsQuery{
		UserID:    &user.ID,
		IsDeleted: util.NewBool(false),
	}
	linkedSnsAccounts, err := models.Repository.SnsAccount.FindMany(querySnsAccounts)
	if err != nil {
		return nil, err
	}

	result := make([]*models.SimpleSnsAccount, len(linkedSnsAccounts))
	for i, snsAccount := range linkedSnsAccounts {
		// Convert the SnsAccount to SimpleSnsAccount
		simpleSnsAccount := snsAccount.ToSimple()
		result[i] = simpleSnsAccount
	}

	return result, nil
}

func (s *SnsAccountService) SortMainAccountToTopList(snsAccounts []*models.SimpleSnsAccount) []*models.SimpleSnsAccount {
	defaultIndex := -1
	for i, account := range snsAccounts {
		if account.IsDefault {
			if i == 0 {
				break
			}

			defaultIndex = i
			break
		}
	}

	if defaultIndex != -1 {
		snsAccounts[0], snsAccounts[defaultIndex] = snsAccounts[defaultIndex], snsAccounts[0]
	}

	return snsAccounts
}

func (s *SnsAccountService) UnlinkSnsAccount(snsAccount *models.SnsAccount) *e.AppError {
	if snsAccount.IsDefault {
		findQuery := &models.FindSnsAccountsQuery{
			UserID:    &snsAccount.UserID,
			IsDefault: util.NewBool(false),
			IsDeleted: util.NewBool(false),
		}

		snsAccounts, err := models.Repository.SnsAccount.FindMany(findQuery)
		if err != nil {
			return e.NewError(
				e.Error_sns_accounts_find_failed,
				http.StatusInternalServerError,
				err.Error())
		}

		if len(snsAccounts) == 0 {
			return e.NewError(
				e.INVALID_PARAMS,
				http.StatusBadRequest,
				"You must have more than one linked account before unlink")
		} else {
			snsAccounts[0].IsDefault = true
			_, err := models.Repository.SnsAccount.Update(snsAccounts[0], nil)
			if err != nil {
				return e.NewError(
					e.Error_update_sns_account_by_id_failed,
					http.StatusInternalServerError, err.Error())
			}

		}
	}

	err := models.Repository.SnsAccount.DeleteByID(snsAccount.ID)
	if err != nil {
		log.Debug("Api::User.Unlink delete sns account failed ", err)
		return e.NewError(
			e.Error_sns_account_delete_failed,
			http.StatusInternalServerError, err.Error())
	}

	return nil
}
