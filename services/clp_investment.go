package services

import (
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"strings"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func (s *ClpInvestmentService) Create(req *dto.CreateInvestmentRequest) (investment *models.ClpInvestment, appErr *e.AppError) {
	if !req.Launchpad.IsStatusFunding() {
		return nil, e.NewError400(e.INVALID_PARAMS,
			"Launchpad status is not funding: "+string(req.Launchpad.Status))
	}

	if req.Amount.LessThan(req.Launchpad.FundingGoal.MinPledge) {
		return nil, e.NewError400(e.INVALID_PARAMS,
			"Amount pledge invalid: pledge amount must be greater than or equal "+req.Launchpad.FundingGoal.MinPledge.String())
	}

	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			appErr = e.NewError500(e.Create_investment_failed, fmt.Sprintf("Panic recovered: %v", r))
			tx.Rollback()
		}
	}()

	investmentExist, err := models.Repository.ClpInvestment(s.ctx).FindOne(&models.ClpInvestmentQuery{
		UserID:         util.NewString(req.User.ID),
		ClpLaunchpadID: util.NewString(req.LaunchpadID),
	}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		tx.Rollback()
		return nil, e.NewError500(e.Find_investment_failed, "Find one investment failed"+err.Error())
	}

	if investmentExist != nil {
		// update
		if cErr := models.Repository.ClpInvestment(s.ctx).IncreaseAmount(investmentExist, req.Amount, tx); cErr != nil {
			tx.Rollback()
			return nil, e.NewError500(e.Update_investment_failed, cErr.Error())
		}

		investment = investmentExist
	} else {
		// create new
		newInvestment := &models.ClpInvestment{
			UserID:         req.User.ID,
			ClpLaunchpadID: req.LaunchpadID,
			Amount:         req.Amount,
			Currency:       req.Launchpad.FundingGoal.Currency,
			Status:         models.PledgedStatus,
		}

		if cErr := models.Repository.ClpInvestment(s.ctx).Create(newInvestment, tx); cErr != nil {
			tx.Rollback()
			return nil, e.NewError500(e.Create_investment_failed, cErr.Error())
		}

		investment = newInvestment
	}

	if maximumPercentage, actualPercentage, ok := s.checkMaximumPledge(investment.Amount, req.Launchpad.FundingGoal.TargetFunding); !ok {
		tx.Rollback()
		return nil, e.NewError400(e.Pledge_amount_invalid,
			"Pledge amount is greater than maximum percentage: pledge_amount="+investment.Amount.String()+
				" target_funding_amount="+req.Launchpad.FundingGoal.TargetFunding.String()+
				" maximum_pledge_percentage="+maximumPercentage.String()+
				" actual_pledge_percentage="+actualPercentage.String(),
		)
	}

	if _, aErr := Transaction.Pledge(req); aErr != nil {
		tx.Rollback()
		return nil, aErr
	}

	if aErr := s.createOrder(investment, req, tx); aErr != nil {
		tx.Rollback()
		return nil, aErr
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Create_investment_failed, "create or update investment failed "+err.Error())
	}

	investment.User = req.User.ToSimpleUser()
	return investment, nil
}

func (s *ClpInvestmentService) createOrder(
	investment *models.ClpInvestment,
	req *dto.CreateInvestmentRequest,
	tx *gorm.DB,
) *e.AppError {
	code, codeErr := generateOrderCode()
	if codeErr != nil {
		return codeErr
	}

	// create order
	order := models.Order{
		OrgID:          req.Org.ID,
		PayFromOrgID:   req.Org.ID,
		UserID:         req.User.ID,
		Amount:         req.Amount,
		ActualAmount:   req.Amount,
		Paid:           req.Amount,
		MissingAmount:  decimal.Zero,
		DiscountAmount: decimal.Zero,
		Code:           strings.ToUpper(*code),
		Status:         models.OrderStatusSuccess,
		Currency:       req.Launchpad.FundingGoal.Currency,
	}
	if oErr := models.Repository.Order.Create(&order, tx); oErr != nil {
		return e.NewError500(e.OrderCreateFailed, "Create order error: "+oErr.Error())
	}

	orderItem := models.OrderItem{
		OrderID:        order.ID,
		EntityID:       investment.ID,
		EntityType:     models.ClpInvestmentModelName,
		PayFromOrgID:   req.Org.ID,
		OrgID:          req.Org.ID,
		OrgSchema:      req.Org.Schema,
		UserID:         req.User.ID,
		Amount:         req.Amount,
		ActualAmount:   req.Amount,
		DiscountAmount: decimal.Zero,
		Status:         models.OrderStatusSuccess,
		Currency:       req.Launchpad.FundingGoal.Currency,
	}
	if oErr := models.Repository.OrderItem.Create(&orderItem, tx); oErr != nil {
		return e.NewError500(e.OrderCreateFailed, "Create order error: "+oErr.Error())
	}

	// create payment for tracking
	paymentMethod, err := models.Repository.PaymentMethod.FindOne(&models.PaymentMethodQuery{
		Network:     &req.Wallet.Network,
		PaymentType: &req.Wallet.Currency,
		Type:        util.NewT(models.PaymentMethodTypeOpenEduWallet),
	}, &models.FindOneOptions{})
	if err != nil {
		return e.NewError500(e.Payment_method_find_one_failed, "Find payment method error: "+err.Error())
	}

	payment := models.Payment{
		UserID:          req.User.ID,
		OrderID:         order.ID,
		PaymentMethodID: paymentMethod.ID,
		Amount:          req.Amount,
		Currency:        req.Launchpad.FundingGoal.Currency,
		Status:          models.PaymentStatusSuccess,
		OrgID:           req.Org.ID,
		PayFromOrgID:    req.Org.ID,
	}
	if err = models.Repository.Payment.Create(&payment, tx); err != nil {
		return e.NewError500(e.Create_payment_failed, err.Error())
	}
	return nil
}

func (s *ClpInvestmentService) FindInvestmentsLaunchpad(launchpad *models.ClpLaunchpad, options *models.FindPageOptions) (*dto.ListClpInvestmentResponse, *e.AppError) {
	investments, pagination, stats, err := models.Repository.ClpInvestment(s.ctx).FindPageWithStats(&models.ClpInvestmentQuery{
		ClpLaunchpadID: util.NewString(launchpad.ID),
	}, options)
	if err != nil {
		return nil, e.NewError500(e.Launchpad_find_page_failed, "Find page with stats launchpads error: "+err.Error())
	}

	resp := &dto.ListClpInvestmentResponse{
		TotalAmount:  stats.TotalAmount,
		TotalBackers: stats.TotalBackers,
		FundingGoal:  launchpad.FundingGoal,
		Results:      investments,
		Pagination:   pagination,
	}

	return resp, nil
}

func (s *ClpInvestmentService) FindByID(id string) (*models.ClpInvestment, *e.AppError) {
	investment, err := models.Repository.ClpInvestment(s.ctx).FindOne(
		&models.ClpInvestmentQuery{
			ID: util.NewString(id),
		}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Investment_not_found, "Investment not found: "+err.Error())
		}
		return nil, e.NewError500(e.Find_investment_failed, "Find investmentLaunchpadSTTWaiting by ID error: "+err.Error())
	}

	return investment, nil
}

func (s *ClpInvestmentService) FindMyLaunchpads(user *models.User, query *models.ClpInvestmentQuery, options *models.FindPageOptions) (*dto.MyLaunchpadsResponse, *e.AppError) {
	options.Preloads = append(options.Preloads, models.ClpLaunchpadField)
	options.Preloads = lo.Uniq(options.Preloads)
	query.UserID = util.NewString(user.ID)

	// TODO: write func use raw query join to get list launchpad for investment
	investments, pagination, err := models.Repository.ClpInvestment(s.ctx).FindPage(query, options)
	if err != nil {
		return nil, e.NewError500(e.Find_investment_failed, "find page investments failed "+err.Error())
	}

	myLaunchpads := lo.Map(investments, func(investment *models.ClpInvestment, _ int) *dto.MyLaunchpadResponse {
		return &dto.MyLaunchpadResponse{
			Launchpad:  investment.ClpLaunchpad,
			Investment: investment.Sanitize(),
		}
	})

	resp := &dto.MyLaunchpadsResponse{
		Results:    myLaunchpads,
		Pagination: pagination,
	}

	return resp, nil
}

func (s *ClpInvestmentService) checkMaximumPledge(amount decimal.Decimal, target decimal.Decimal) (decimal.Decimal, decimal.Decimal, bool) {
	maximum := decimal.NewFromInt(int64(models.GetConfig[int](models.ClpMaximumPledgePercentage)))
	pledgePercentage := amount.Div(target).Mul(decimal.NewFromInt(100))
	return maximum, pledgePercentage, pledgePercentage.LessThanOrEqual(maximum)
}
