package services

import (
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *ReferrerService) InviteReferrerSuccess(user *models.User, refererID string) *e.AppError {
	referrer, refErr := models.Repository.Referrer.FindOne(&models.ReferrerQuery{
		ID: util.NewString(refererID),
	}, nil)
	if refErr != nil && !errors.Is(refErr, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Referrer_create_failed, "InviteReferrerSuccess: find referrer failed: "+refErr.Error())
	}
	referrer.UserID = user.ID
	referrer.InviteStatus = models.DefaultStatusAccepted
	if updateErr := models.Repository.Referrer.Update(referrer, nil); updateErr != nil {
		return e.NewError500(e.Referrer_create_failed, "AddReferrer: update failed: "+updateErr.Error())
	}
	return nil
}

func getUserByIdOrEmail(email string, id *string) (*models.User, *e.AppError) {
	var user *models.User
	if id != nil {
		userById, fErr := models.Repository.User.FindByID(*id)
		if fErr != nil && !errors.Is(fErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError500(e.Referrer_find_failed, "find user by id failed:  "+fErr.Error())
		}
		user = userById
	}
	if user == nil {
		userByEmail, fErr := models.Repository.User.FindByEmailWithOpts(email, nil)
		if fErr != nil && !errors.Is(fErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError500(e.Referrer_find_failed, "find user by email failed:  "+fErr.Error())
		}
		user = userByEmail
	}
	return user, nil
}

func (s *ReferrerService) CreateOrUpdateMany(campaign *models.AffiliateCampaign, request *dto.AffiliateCampaignReferrerRequest) ([]*dto.InviteReferrerResponse, *e.AppError) {
	if len(request.Referrers) <= 0 {
		return nil, nil
	}

	var statuses []*dto.InviteReferrerResponse
	for _, req := range request.Referrers {
		referrer, refErr := models.Repository.Referrer.FindOne(&models.ReferrerQuery{
			Email:      util.NewString(req.Email),
			Type:       util.NewT(req.Type),
			CampaignID: util.NewString(campaign.ID),
		}, nil)
		if refErr != nil && !errors.Is(refErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError500(e.Referrer_create_failed, "AddReferrer: find referrer failed: "+refErr.Error())
		}

		if referrer != nil && referrer.InviteStatus == models.DefaultStatusAccepted && referrer.Type == req.Type {
			statuses = append(statuses, &dto.InviteReferrerResponse{
				Email:  req.Email,
				Type:   req.Type,
				Status: models.DefaultStatusExisted,
			})
			continue
		}

		user, uErr := getUserByIdOrEmail(req.Email, req.UserID)
		if uErr != nil {
			return nil, uErr
		}

		userID := ""
		if user != nil {
			userID = user.ID
		}

		inviteStatus := lo.If(userID == "", models.DefaultStatusPending).Else(models.DefaultStatusAccepted)
		if referrer == nil {
			createReferrer := &models.Referrer{
				Email:        req.Email,
				UserID:       userID,
				OrgID:        request.Org.ID,
				CampaignID:   campaign.ID,
				Type:         req.Type,
				Enable:       req.Enable,
				InviteStatus: inviteStatus,
			}
			if createErr := models.Repository.Referrer.Create(createReferrer, nil); createErr != nil {
				return nil, e.NewError500(e.Referrer_create_failed, "AddReferrer: create failed: "+createErr.Error())
			}
			referrer = createReferrer
		} else {
			referrer.Type = req.Type
			referrer.Enable = req.Enable
			referrer.UserID = userID
			referrer.InviteStatus = inviteStatus
			if updateErr := models.Repository.Referrer.Update(referrer, nil); updateErr != nil {
				return nil, e.NewError500(e.Referrer_create_failed, "AddReferrer: update failed: "+updateErr.Error())
			}
		}

		statuses = append(statuses, &dto.InviteReferrerResponse{
			Email:  req.Email,
			Type:   req.Type,
			Status: inviteStatus,
		})

		if user == nil {
			inviteReq := dto.InviteUserRequest{
				UserEmails: []string{req.Email},
				Event:      models.EventInviteReferrer,
				ObjectType: util.NewT(models.ReferrerModelName),
				ObjectID:   util.NewString(referrer.ID),
			}
			iErr := User.HandleInviteUser(request.Org, &inviteReq)
			if iErr != nil {
				return nil, e.NewError500(e.Referrer_find_failed, "invite user failed:  "+iErr.Msg)
			}
			user = &models.User{DisplayName: "User", Email: req.Email}
		}

		go func() {
			var eErr error
			defer func() {
				if r := recover(); r != nil {
					eErr = fmt.Errorf("panic: %v", r)
				}

				if eErr != nil {
					log.ErrorWithAlertf("ReferrerService.CreateOrUpdateMany::Send email become affiliate failed: %v", eErr)
				}
			}()

			_, eErr = communication.Email.SendEmail(&commdto.SendEmailRequest{
				User:  user.IntoComm(),
				Org:   request.Org.IntoComm(),
				Event: models.EventBecomeAffiliate.IntoComm(),
				ExtendDatas: commdto.MapEmailParams{
					commdto.EmailParamAffiliateCode: campaign.ID,
					commdto.EmailParamAffiliateLink: s.buildAffiliateLink(request.Org),
				},
				IsQueue: true,
			})
		}()
	}
	return statuses, nil
}

func (s *ReferrerService) buildAffiliateLink(org *models.Organization) string {
	return fmt.Sprintf("%s/%s", org.LandingPageURL(), setting.AppSetting.AffiliatePath)
}

func (s *ReferrerService) AddPurchasedReferrer(userID string, orgID string, link *models.ReferralLink) *e.AppError {
	user, uErr := User.FindByID(userID, nil)
	if uErr != nil {
		return e.NewError500(e.Referrer_create_failed, "AddPurchasedReferrer: find user by ID failed: "+uErr.Msg)
	}
	referrer, refErr := models.Repository.Referrer.FindOne(&models.ReferrerQuery{
		Email:      util.NewString(user.Email),
		Type:       util.NewT(models.ReferrerTypePurchasedUser),
		CampaignID: util.NewString(link.CampaignID),
	}, nil)
	if refErr != nil && !errors.Is(refErr, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Referrer_create_failed, "AddPurchasedReferrer: find referrer failed: "+refErr.Error())
	}

	if referrer == nil {
		createReferrer := &models.Referrer{
			Email:                 user.Email,
			UserID:                user.ID,
			OrgID:                 orgID,
			CampaignID:            link.CampaignID,
			Type:                  models.ReferrerTypePurchasedUser,
			Enable:                true,
			InviteStatus:          models.DefaultStatusAccepted,
			PurchasedLinkID:       &link.ID,
			PurchasedCommissionID: &link.CommissionID,
		}
		if createErr := models.Repository.Referrer.Create(createReferrer, nil); createErr != nil {
			return e.NewError500(e.Referrer_create_failed, "AddPurchasedReferrer: create failed: "+createErr.Error())
		}
	} else {
		referrer.PurchasedLinkID = &link.ID
		referrer.PurchasedCommissionID = &link.CommissionID
		if updateErr := models.Repository.Referrer.Update(referrer, nil); updateErr != nil {
			return e.NewError500(e.Referrer_create_failed, "AddPurchasedReferrer: update failed: "+updateErr.Error())
		}
	}

	return nil
}

func (s *ReferrerService) AddPurchasedUserOrderSuccess(user *models.User, org *models.Organization, course *models.Course) *e.AppError {
	courseCampaigns, _, cErr := models.Repository.CourseCampaign.FindPage(&models.CourseCampaignQuery{
		CourseCuid: util.NewString(course.Cuid),
	}, &models.FindPageOptions{PerPage: util.PerPageMax, Preloads: []string{models.CampaignField}})
	if cErr != nil {
		return e.NewError500(e.Referrer_create_failed, "AddPurchasedUserOrderSuccess: CourseCampaignFindPage failed: "+cErr.Error())
	}

	if len(courseCampaigns) > 0 {
		campaignIds := lo.Map(courseCampaigns, func(item *models.CourseCampaign, _ int) string {
			return item.CampaignID
		})

		referrers, refErr := models.Repository.Referrer.FindMany(&models.ReferrerQuery{
			Email:        util.NewString(user.Email),
			Type:         util.NewT(models.ReferrerTypePurchasedUser),
			CampaignIDIn: campaignIds,
		}, nil)
		if refErr != nil {
			return e.NewError500(e.Referrer_create_failed, "AddPurchasedUserOrderSuccess: find referrers failed: "+refErr.Error())
		}

		for _, cc := range courseCampaigns {
			referrer, _ := lo.Find(referrers, func(item *models.Referrer) bool {
				return item.CampaignID == cc.CampaignID
			})
			if referrer == nil {
				createReferrer := &models.Referrer{
					Email:        user.Email,
					UserID:       user.ID,
					OrgID:        org.ID,
					CampaignID:   cc.CampaignID,
					Type:         models.ReferrerTypePurchasedUser,
					Enable:       true,
					InviteStatus: models.DefaultStatusAccepted,
				}
				if createErr := models.Repository.Referrer.Create(createReferrer, nil); createErr != nil {
					return e.NewError500(e.Referrer_create_failed, "AddPurchasedReferrer: create failed: "+createErr.Error())
				}
			}
		}
	}

	return nil
}

func (s *ReferrerService) DeleteMany(request *dto.RemoveReferrerRequest) *e.AppError {
	if len(request.IDs) > 0 {
		for _, id := range request.IDs {
			if uErr := models.Repository.Referrer.Delete(id, nil); uErr != nil {
				return e.NewError500(e.Referrer_remove_failed, "remove: "+uErr.Error())
			}
		}
		return nil
	}
	return nil
}

func (s *ReferrerService) FindPage(query *models.ReferrerQuery, options *models.FindPageOptions) ([]*models.Referrer, *models.Pagination, *e.AppError) {
	if referrers, pagination, err := models.Repository.Referrer.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Referrer_find_failed, "find page"+err.Error())
	} else {
		return referrers, pagination, nil
	}
}

func (s *ReferrerService) FindMany(query *models.ReferrerQuery, options *models.FindManyOptions) ([]*models.Referrer, *e.AppError) {
	if referrers, err := models.Repository.Referrer.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Referrer_find_failed, "find many"+err.Error())
	} else {
		return referrers, nil
	}
}

func (s *ReferrerService) FindOne(query *models.ReferrerQuery, options *models.FindOneOptions) (*models.Referrer, *e.AppError) {
	if referrer, err := models.Repository.Referrer.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Referrer_not_found, "findOne")
		}
		return nil, e.NewError500(e.Referrer_find_failed, "findOne: "+err.Error())
	} else {
		return referrer, nil
	}
}
