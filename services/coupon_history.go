package services

import (
	"errors"
	"github.com/samber/lo"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"time"

	"gorm.io/gorm"
)

func (s *CouponHistoryService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.CouponHistory, *e.AppError) {
	query := &models.CouponHistoryQuery{
		ID:             util.NewString(id),
		IncludeDeleted: util.NewBool(includeDeleted),
	}
	if coupon, err := models.Repository.CouponHistory.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.CouponNotFound, err.Error())
		}
		return nil, e.NewError500(e.CouponHistoryFindFailed, err.Error())
	} else {
		return coupon, nil
	}

}

func (s *CouponHistoryService) FindOne(query *models.CouponHistoryQuery, options *models.FindOneOptions) (*models.CouponHistory, *e.AppError) {
	if ch, err := models.Repository.CouponHistory.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.CouponHistoryNotFound, err.Error())
		}
		return nil, e.NewError500(e.CouponHistoryFindFailed, err.Error())
	} else {
		return ch, nil
	}
}

func (s *CouponHistoryService) FindPage(query *models.CouponHistoryQuery, options *models.FindPageOptions) ([]*models.CouponHistory, *models.Pagination, *e.AppError) {
	if coupons, pagination, err := models.Repository.CouponHistory.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.CouponHistoryFindFailed, err.Error())
	} else {
		return coupons, pagination, nil
	}
}

func (s *CouponHistoryService) FindMany(query *models.CouponHistoryQuery, options *models.FindManyOptions) ([]*models.CouponHistory, *e.AppError) {
	if coupons, err := models.Repository.CouponHistory.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.CouponHistoryFindFailed, "findMany "+err.Error())
	} else {
		return coupons, nil
	}
}

func (s *CouponHistoryService) Update(history *models.CouponHistory) *e.AppError {
	if err := models.Repository.CouponHistory.Update(history, nil); err != nil {
		return e.NewError500(e.CouponHistoryUpdateFailed, "update history failed: "+err.Error())
	}
	return nil
}

func (s *CouponHistoryService) Create(user *models.User, order *models.Order, coupon *models.Coupon) *e.AppError {
	// Create coupon history
	couponHistory := &models.CouponHistory{
		UserID:                user.ID,
		CouponID:              coupon.ID,
		Status:                models.CouponStatusInUse,
		OrderID:               order.ID,
		OrgID:                 coupon.OrgID,
		DiscountPercent:       coupon.FiatDiscountPercentage,
		CryptoDiscountPercent: coupon.CryptoDiscountPercentage,
		//CryptoDiscountAmount:  coupon.CryptoDiscountAmount,
		//DiscountAmount:        coupon.DiscountAmount,
	}

	if err := models.Repository.CouponHistory.Create(couponHistory, nil); err != nil {
		return e.NewError500(e.CouponHistoryCreateFailed, err.Error())
	}
	return nil
}

func (s *CouponHistoryService) CleanupExpiredReservations() *e.AppError {
	page := 1
	expiredAt := time.Now().Add(-setting.AppSetting.CouponReserveDuration).UnixMilli()
	for {
		couponHistories, pagination, err := models.Repository.CouponHistory.FindPage(&models.CouponHistoryQuery{
			Status:      util.NewT(models.CouponStatusInUse),
			CreateAtLte: util.NewInt64(expiredAt),
		},
			&models.FindPageOptions{
				Page:     page,
				PerPage:  setting.AppSetting.DefaultPerPage,
				Preloads: []string{models.CouponField},
				Sort:     []string{models.CreateAtASC},
			},
		)
		if err != nil {
			return e.NewError500(e.CouponHistoryFindFailed, err.Error())
		}

		if len(couponHistories) == 0 {
			break
		}

		couponHistoryByOrderID := map[string]*models.CouponHistory{}
		for _, couponHistory := range couponHistories {
			couponHistoryByOrderID[couponHistory.OrderID] = couponHistory
		}

		orderIDs := lo.Map(couponHistories, func(history *models.CouponHistory, _ int) string {
			return history.OrderID
		})
		orders, err := models.Repository.Order.FindMany(&models.OrderQuery{
			IDIn: lo.Uniq(orderIDs),
		}, nil)
		for _, order := range orders {
			couponHistory, found := couponHistoryByOrderID[order.ID]
			if !found {
				continue
			}
			if _, err = s.cleanUpCouponReservation(order, couponHistory); err != nil {
				log.Errorf("Cleanup expired coupon reservation from order ID %s error: %v", order.ID, err)
			}
		}

		if !pagination.HasNextPage() {
			break
		}
		page++
	}
	return nil
}

func (s *CouponHistoryService) cleanUpCouponReservation(order *models.Order, usedCoupon *models.CouponHistory) (*models.Order, *e.AppError) {
	usedCoupon.Status = models.CouponStatusExpired
	usedCoupon.DeleteAt = int(time.Now().UnixMilli())
	if err := models.Repository.CouponHistory.Update(usedCoupon, nil); err != nil {
		return order, e.NewError500(e.CouponHistoryDeleteFailed, err.Error())
	}
	if err := models.Repository.Coupon.Decrement(usedCoupon.Coupon, "total_used", 1, nil); err != nil {
		return order, e.NewError500(e.CouponUpdateFailed, "Decrement total used coupon error: "+err.Error())
	}
	if err := Order.CalculateOrderAndSave(order, nil, true); err != nil {
		return order, err
	}
	return order, nil
}
