package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"regexp"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *UserSettingService) CreateMany(org *models.Organization, user *models.User, data *dto.CreateSettingRequest) ([]*models.UserSetting, *e.AppError) {
	var agencies []*models.UserSetting
	if len(data.Settings) > 0 {
		agencies = lo.Map(data.Settings, func(item dto.UserSettingRequest, _ int) *models.UserSetting {
			return &models.UserSetting{
				UserID: user.ID,
				OrgID:  org.ID,
				Type:   item.Type,
				Enable: item.Enable,
				Value:  item.Value,
			}
		})

		if err := models.Repository.UserSetting.CreateMany(agencies, nil); err != nil {
			return nil, e.NewError500(e.User_setting_create_failed, "[Create] many failed: "+err.Error())
		}
		return agencies, nil
	}
	return nil, nil
}

func (s *UserSettingService) UpdateMany(data *dto.CreateSettingRequest) ([]*models.UserSetting, *e.AppError) {
	if len(data.Settings) > 0 {
		ids := lo.Map(data.Settings, func(item dto.UserSettingRequest, _ int) *string {
			return item.ID
		})
		ids = lo.Uniq(ids)
		settings, err := models.Repository.UserSetting.FindMany(&models.UserSettingQuery{IDIn: ids}, nil)
		if err != nil {
			return nil, e.NewError500(e.User_setting_find_failed, "Update:  "+err.Error())
		}
		for _, item := range settings {
			req, ok := lo.Find(data.Settings, func(ua dto.UserSettingRequest) bool {
				return *ua.ID == item.ID
			})
			if ok {
				item.Type = req.Type
				item.Enable = req.Enable
				item.Value = req.Value
				if uErr := models.Repository.UserSetting.Update(item, nil); uErr != nil {
					return nil, e.NewError500(e.User_setting_update_failed, "update: "+uErr.Error())
				}
			}
		}
		return settings, nil
	}
	return nil, nil
}

func (s *UserSettingService) DeleteMany(userIds []string) *e.AppError {
	if len(userIds) > 0 {
		for _, id := range userIds {
			if uErr := models.Repository.UserSetting.Delete(id, nil); uErr != nil {
				return e.NewError500(e.User_setting_update_failed, "delete: "+uErr.Error())
			}
		}
		return nil
	}
	return nil
}

func (s *UserSettingService) FindPage(query *models.UserSettingQuery, options *models.FindPageOptions) ([]*models.UserSetting, *models.Pagination, *e.AppError) {
	if settings, pagination, err := models.Repository.UserSetting.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.User_setting_find_failed, err.Error())
	} else {
		return settings, pagination, nil
	}
}

func (s *UserSettingService) FindById(id string, includeDeleted bool, options *models.FindOneOptions) (*models.UserSetting, *e.AppError) {
	query := &models.UserSettingQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}
	if setting, err := models.Repository.UserSetting.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.User_setting_not_found, err.Error())
		}
		return nil, e.NewError500(e.User_setting_find_failed, err.Error())
	} else {
		return setting, nil
	}
}

func (s *UserSettingService) BuildCertificateSetting(userID *string, org *models.Organization, options *models.FindPageOptions) (*dto.SettingProfileResponse, *e.AppError) {
	query := &models.CertificateQuery{
		UserID: userID,
	}

	orgID := org.ID
	if !org.IsRoot() {
		query.OrgID = &orgID
	}

	certificates, pagination, sErr := Certificate.FindPage(query, options)
	if sErr != nil {
		return nil, sErr
	}
	// get setting certificates
	certificateSetting, err := models.Repository.UserSetting.FindOne(
		&models.UserSettingQuery{UserID: userID, OrgID: &org.ID, Type: util.NewString(string(models.UserSettingTypeCertificates))},
		&models.FindOneOptions{})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.User_setting_find_failed, err.Error())
	}

	ids := []string{}
	enable := false
	if certificateSetting != nil {
		certificateIDs, err := models.ExtractStringSliceFromJSONB(certificateSetting.Value, models.SettingCertificateKey)
		if err != nil {
			return nil, e.NewError500(e.Error_extract_string_slice_from_jsonb_failed, err.Error())
		}

		enable = certificateSetting.Enable
		ids = lo.Uniq(certificateIDs)
	}

	mapIDs := make(map[string]bool, len(ids))
	lo.ForEach(ids, func(id string, _ int) {
		mapIDs[id] = true
	})

	responses := []*dto.CertificateSettingResponse{}
	lo.ForEach(certificates, func(item *models.Certificate, _ int) {
		isShow := false
		if mapIDs[item.ID] {
			isShow = true
		}
		responses = append(responses, &dto.CertificateSettingResponse{
			Certificate: item,
			IsShow:      isShow,
		})
	})

	return dto.MakeSettingProfileResponse(responses, pagination, enable), nil
}

func (s *UserSettingService) BuildCourseSetting(user *models.User, org *models.Organization, options *models.FindPageOptions) (*dto.SettingProfileResponse, *e.AppError) {
	courses, pagination, appErr := Course.FindLearnerPublishCourses(user, org,
		&models.PublishCourseQuery{UserID: &user.ID}, options)
	if appErr != nil {
		return nil, appErr
	}

	courseSetting, err := models.Repository.UserSetting.FindOne(
		&models.UserSettingQuery{UserID: &user.ID, OrgID: &org.ID, Type: util.NewString(string(models.UserSettingTypeCourses))},
		&models.FindOneOptions{})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.User_setting_find_failed, err.Error())

	}

	ids := []string{}
	enable := false
	if courseSetting != nil {
		courseIDs, err := models.ExtractStringSliceFromJSONB(courseSetting.Value, models.SettingCourseKey)
		if err != nil {
			return nil, e.NewError500(e.Error_extract_string_slice_from_jsonb_failed, err.Error())
		}

		ids = lo.Uniq(courseIDs)
		enable = courseSetting.Enable
	}

	mapIDs := make(map[string]bool, len(ids))
	lo.ForEach(ids, func(id string, _ int) {
		mapIDs[id] = true
	})

	responses := []*dto.CourseSettingResponse{}
	lo.ForEach(courses, func(item *models.Course, _ int) {
		isShow := false
		if mapIDs[item.Cuid] {
			isShow = true
		}
		responses = append(responses, &dto.CourseSettingResponse{
			SimpleCourse: item.Sanitize(),
			IsShow:       isShow,
		})
	})

	return dto.MakeSettingProfileResponse(responses, pagination, enable), nil
}

func (s *UserSettingService) BuildBlogSetting(userID *string, org *models.Organization, options *models.FindPageOptions) (*dto.SettingProfileResponse, *e.AppError) {
	query := &models.PublishBlogQuery{
		AuthorID: userID,
	}

	orgID := org.ID
	if !org.IsRoot() {
		query.OrgID = &orgID
	}

	options.Preloads = append(options.Preloads, models.BlogPreloadsUser)
	blogs, pagination, appErr := Blog.GetPublishBlog(query, options)
	if appErr != nil {
		return nil, appErr
	}

	blogSetting, err := models.Repository.UserSetting.FindOne(
		&models.UserSettingQuery{UserID: userID, OrgID: &org.ID, Type: util.NewString(string(models.UserSettingTypeBlogs))},
		&models.FindOneOptions{})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.User_setting_find_failed, err.Error())
	}

	ids := []string{}
	enable := false
	if blogSetting != nil {
		blogIDs, err := models.ExtractStringSliceFromJSONB(blogSetting.Value, models.SettingBlogKey)
		if err != nil {
			return nil, e.NewError500(e.Error_extract_string_slice_from_jsonb_failed, err.Error())
		}

		ids = lo.Uniq(blogIDs)
		enable = blogSetting.Enable
	}

	mapIDs := make(map[string]bool, len(ids))
	lo.ForEach(ids, func(id string, _ int) {
		mapIDs[id] = true
	})

	responses := []*dto.BlogSettingResponse{}
	lo.ForEach(blogs, func(item *models.Blog, _ int) {
		isShow := false
		if mapIDs[item.Cuid] {
			isShow = true
		}
		responses = append(responses, &dto.BlogSettingResponse{
			SimpleBlog: item.Sanitize(),
			IsShow:     isShow,
		})
	})

	return dto.MakeSettingProfileResponse(responses, pagination, enable), nil
}

func (s *UserSettingService) UpsertSettingProfile(userID string, org *models.Organization, data *dto.UpdateSettingProfileRequest) *e.AppError {
	settingQuery := &models.UserSettingQuery{
		UserID: &userID,
		OrgID:  &org.ID,
		Type:   (*string)(&data.Type),
	}
	settingFind, err := models.Repository.UserSetting.FindOne(settingQuery, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			userSetting := &models.UserSetting{
				UserID: userID,
				OrgID:  org.ID,
				Type:   data.Type,
			}

			if data.Enable != nil {
				userSetting.Enable = *data.Enable
			} else {
				userSetting.Enable = false
			}

			if data.Value != nil {
				userSetting.Value = *data.Value
			} else {
				makeValueDefaultForSettingProfile(userSetting)
			}

			createErr := models.Repository.UserSetting.Create(userSetting, nil)
			if createErr != nil {
				return e.NewError500(e.User_setting_create_failed, "[Create] user setting failed: "+createErr.Error())
			}

			return nil
		}

		return e.NewError500(e.User_setting_find_failed, "[Find] user setting failed: "+err.Error())
	}

	if data.Enable != nil {
		settingFind.Enable = *data.Enable
	}

	if data.Value != nil {
		settingFind.Value = *data.Value
	}

	if err := models.Repository.UserSetting.Update(settingFind, nil); err != nil {
		return e.NewError500(e.User_setting_update_failed, "[Update] user setting failed: "+err.Error())
	}

	return nil
}

func makeValueDefaultForSettingProfile(us *models.UserSetting) {
	switch us.Type {
	case models.UserSettingTypeCertificates:
		us.Value = map[string]interface{}{
			models.SettingCertificateKey: []interface{}{},
		}
	case models.UserSettingTypeBlogs:
		us.Value = map[string]interface{}{
			models.SettingBlogKey: []interface{}{},
		}
	case models.UserSettingTypeCourses:
		us.Value = map[string]interface{}{
			models.SettingCourseKey: []interface{}{},
		}
	}
}

func (s *UserSettingService) UpsertSettingOldUsername(userID string, oldUsername string) *e.AppError {
	settingQuery := &models.UserSettingQuery{
		UserID: &userID,
		Type:   util.NewString(string(models.UserSettingTypeOldUsername)),
	}
	settingFind, err := models.Repository.UserSetting.FindOne(settingQuery, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			userSetting := &models.UserSetting{
				UserID: userID,
				Type:   models.UserSettingTypeOldUsername,
				Enable: true,
			}

			userSetting.Value = map[string]interface{}{
				models.SettingOldUsernameKey: []string{oldUsername},
			}

			createErr := models.Repository.UserSetting.Create(userSetting, nil)
			if createErr != nil {
				return e.NewError500(e.User_setting_create_failed, "[Create] user setting old username failed: "+createErr.Error())
			}

			return nil
		}

		return e.NewError500(e.User_setting_find_failed, "[Find] user setting old username failed: "+err.Error())
	}

	usernames, err := models.ExtractStringSliceFromJSONB(settingFind.Value, models.SettingOldUsernameKey)
	if err != nil {
		return e.NewError500(e.Error_extract_string_slice_from_jsonb_failed, err.Error())
	}
	usernames = append(usernames, oldUsername)
	settingFind.Value = map[string]interface{}{
		models.SettingOldUsernameKey: usernames,
	}

	if err := models.Repository.UserSetting.Update(settingFind, nil); err != nil {
		return e.NewError500(e.User_setting_update_failed, "[Update] user setting old username failed: "+err.Error())
	}

	return nil
}

func (s *UserSettingService) CheckUsernameInputValid(username string) (bool, *e.AppError) {
	if ok := isValidUsername(username); !ok {
		return false, e.NewError400(e.Error_auth_invalid_username, "username input is invalid")
	}

	ok, err := models.Repository.UserSetting.CanUseUsername(username, nil)
	if err != nil {
		return false, e.NewError500(e.Check_old_username_failed, "[CanUseUsername] user setting and user failed: "+err.Error())
	}

	return ok, nil
}

func isValidUsername(username string) bool {
	regex := regexp.MustCompile(`^[a-zA-Z0-9]+$`)
	if !regex.MatchString(username) {
		return false
	}

	return true
}
