package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *ApprovalService) Create(org *models.Organization, requester *models.User, data *dto.CreateApprovalRequest) (*models.Approval, *e.AppError) {
	approval := models.Approval{
		EntityType:    data.EntityType,
		EntityID:      data.EntityID,
		EntitySchema:  org.Schema,
		ConfirmByID:   "",
		ConfirmDate:   0,
		Status:        models.ApprovalStatusNew,
		Type:          data.Type,
		RequesterID:   requester.ID,
		RequestDate:   time.Now().UnixMilli(),
		OrgID:         org.ID,
		RequestValue:  data.Value,
		Note:          data.Note,
		Props:         data.Props,
		RequestUid:    data.RequestUid,
		EntityVersion: data.EntityVersion,
		EntityUID:     data.EntityUID,
	}

	props := approval.Props
	if data.PreVersion != nil {
		props.PreVersion = *data.PreVersion
	}

	if data.PreID != nil {
		props.PreID = *data.PreID
	}

	approval.Props = props
	if err := models.Repository.Approval.Create(&approval, nil); err != nil {
		return nil, e.NewError500(e.Create_approval_failed, err.Error())
	}

	return &approval, nil
}

func (s *ApprovalService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Approval, *e.AppError) {
	query := &models.ApprovalQuery{
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
		ID:             util.NewString(id),
	}
	if approval, err := models.Repository.Approval.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Approval_not_found, err.Error())
		}
		return nil, e.NewError500(e.Approval_find_one_failed, err.Error())
	} else {
		return approval, nil
	}
}

func (s *ApprovalService) FindOne(query *models.ApprovalQuery, options *models.FindOneOptions) (*models.Approval, *e.AppError) {
	if approval, err := models.Repository.Approval.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Approval_not_found, err.Error())
		}
		return nil, e.NewError500(e.Approval_find_one_failed, err.Error())
	} else {
		return approval, nil
	}

}

func (s *ApprovalService) FindPage(query *models.ApprovalQuery, options *models.FindPageOptions) ([]*models.Approval, *models.Pagination, *e.AppError) {
	if approvals, pagination, err := models.Repository.Approval.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_approval_failed, err.Error())
	} else {
		return approvals, pagination, nil
	}
}

func (s *ApprovalService) FindMany(query *models.ApprovalQuery, options *models.FindManyOptions) ([]*models.Approval, *e.AppError) {
	if approvals, err := models.Repository.Approval.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Find_page_approval_failed, "find many failed"+err.Error())
	} else {
		return approvals, nil
	}
}

func (s *ApprovalService) Update(approval *models.Approval) *e.AppError {
	if err := models.Repository.Approval.Update(approval, nil); err != nil {
		return e.NewError500(e.Update_approval_failed, err.Error())

	}
	return nil
}

func (s *ApprovalService) CanGetListApprovals(user *models.User, org *models.Organization) bool {
	return user.IsSysAdmin() || user.IsOrgAdmin(org.ID) || user.IsOrgEditor(org.ID)
}

func (s *ApprovalService) CanUpdateApproval(approval *models.Approval, org *models.Organization) bool {
	return approval.OrgID == org.ID
}

func (s *ApprovalService) Delete(a *models.Approval) *e.AppError {
	if err := models.Repository.Approval.Delete(a.ID, nil); err != nil {
		return e.NewError500(e.Delete_approval_failed, err.Error())
	}
	return nil
}

func (s *ApprovalService) CanRequest(requester *models.User, data *dto.CreateApprovalRequest, org *models.Organization) *e.AppError {
	entity, gErr := models.Repository.Approval.FindEntityRelation(data.EntityType, data.EntityID)
	if gErr != nil {
		if errors.Is(gErr, gorm.ErrRecordNotFound) {
			return e.NewError404(e.Entity_not_found, gErr.Error())
		}
		return e.NewError500(e.Find_entity_failed, gErr.Error())
	}

	switch data.Type {
	case models.ApproveTypePublishAll, models.ApproveTypePublishRoot:
		if !requester.IsOrgAdmin(org.ID) {
			return e.NewError400(e.INVALID_PARAMS, "Only org admin, moderator on entity's org can request publish all")
		}
	case models.ApproveTypePublishOrg:
		if !requester.IsApprovalOwner(entity) {
			return e.NewError400(e.INVALID_PARAMS, "Only entity's owner can request publish org")
		}
	default:
		return e.NewError400(e.INVALID_PARAMS, "Invalid approval type")
	}
	return nil
}

func (s *ApprovalService) CanApprove(approval *models.Approval, user *models.User, request *dto.ApprovalRequest) *e.AppError {
	switch approval.Type {
	case models.ApproveTypePublishAll, models.ApproveTypePublishRoot:
		if !user.IsSysAdmin() && !(request.Org.IsRoot() && user.IsOrgAdmin(request.Org.ID)) {
			return e.NewError400(e.INVALID_PARAMS, "Only sysadmin, admin, moderator can approve publish all")
		}
		break
	case models.ApproveTypePublishOrg:
		if !user.IsOrgAdmin(approval.OrgID) && !user.IsSysAdmin() {
			return e.NewError400(e.INVALID_PARAMS, "Only org admin, moderator on entity's org can approve publish org")
		}
		break
	case models.ApproveTypeWalletFiatWithdraw, models.ApproveTypeWalletCryptoWithdraw:
		if !user.IsSysAdmin() && !user.IsOpeneduAdmin(approval.OrgID) {
			return e.NewError400(e.INVALID_PARAMS, "Only admin, moderator on entity's org can approve publish org")
		}
		if request == nil || request.Value == nil {
			return e.NewError400(e.INVALID_PARAMS, "Param `value` required!")
		}
		break
	default:
		return e.NewError400(e.INVALID_PARAMS, "Invalid approval type")
	}
	return nil
}

func (s *ApprovalService) CanModifyApproval(approval *models.Approval, user *models.User) *e.AppError {
	if !user.IsOrgAdmin(approval.OrgID) && !user.IsSysAdmin() && approval.RequesterID != user.ID {
		return e.NewError400(e.INVALID_PARAMS, "Admin or request owner only")
	}
	return nil
}

func (s *ApprovalService) Approve(user *models.User, approval *models.Approval) *e.AppError {
	var err *e.AppError
	switch approval.EntityType {
	case models.CourseModelName:
		err = Course.PublishCourse(user, approval)
		break
	case models.WalletModelName:
		err = Wallet.ApproveWithdrawRequest(user, approval)
		break
	case models.BlogModelName:
		err = Blog.PublishBlog(user, approval)
	case models.ClpLaunchpadModelName:
		err = ClpLaunchpad.PublishLaunchpad(user, approval)
	default:
		break
	}

	if err != nil {
		return err
	}

	approval.Status = models.ApprovalStatusApprove
	approval.ConfirmDate = time.Now().UnixMilli()
	approval.ConfirmByID = user.ID
	approval.Props.IsAdminFeedback = true
	discussion := approval.Props.Discussion
	newMgs := &models.ApprovalSingleSend{
		ID:            user.ID,
		Email:         user.Email,
		Username:      user.Username,
		Content:       approval.Note,
		SendDate:      time.Now().UnixMilli(),
		EntityID:      approval.EntityID,
		EntityVersion: &approval.EntityVersion,
		Action:        models.ApprovalStatusApprove,
	}
	discussion = append(discussion, newMgs)
	approval.Props.Discussion = discussion

	if err := models.Repository.Approval.Update(approval, nil); err != nil {
		return e.NewError500(e.Update_approval_failed, err.Error())
	}

	return nil
}

func (s *ApprovalService) Reject(user *models.User, approval *models.Approval) *e.AppError {
	var err *e.AppError
	switch approval.EntityType {
	case models.CourseModelName:
		err = Course.RejectPublishCourse(user, approval)
	case models.BlogModelName:
		err = Blog.RejectPublishBlog(user, approval)
	case models.WalletModelName:
		err = Wallet.RejectWithdrawRequest(user, approval)
	case models.ClpLaunchpadModelName:
		err = ClpLaunchpad.RejectPublishLaunchpad(user, approval)
	default:
		break
	}

	if err != nil {
		return err
	}

	approval.Status = models.ApprovalStatusReject
	approval.ConfirmDate = time.Now().UnixMilli()
	approval.ConfirmByID = user.ID
	approval.Props.IsAdminFeedback = true
	discussion := approval.Props.Discussion
	newMgs := &models.ApprovalSingleSend{
		ID:            user.ID,
		Email:         user.Email,
		Username:      user.Username,
		Content:       approval.Note,
		SendDate:      time.Now().UnixMilli(),
		EntityID:      approval.EntityID,
		EntityVersion: &approval.EntityVersion,
		Action:        models.ApprovalStatusReject,
	}
	discussion = append(discussion, newMgs)
	approval.Props.Discussion = discussion

	if err := models.Repository.Approval.Update(approval, nil); err != nil {
		return e.NewError500(e.Update_approval_failed, err.Error())
	}

	return nil
}

func (s *ApprovalService) ApprovalSendFeedback(approval *models.Approval, user *models.User, feedback *dto.ApprovalFeedback) *e.AppError {
	props := approval.Props
	if len(props.Discussion) <= 0 {
		props.Discussion = []*models.ApprovalSingleSend{}
	}
	discussion := props.Discussion

	newMgs := &models.ApprovalSingleSend{
		ID:            user.ID,
		Email:         user.Email,
		Username:      user.Username,
		Content:       feedback.Content,
		SendDate:      time.Now().UnixMilli(),
		EntityID:      approval.EntityID,
		EntityVersion: &approval.EntityVersion,
		Action:        models.ApprovalStatusFeedback,
	}
	if feedback.EntityID != nil {
		newMgs.EntityID = *feedback.EntityID
		newMgs.EntityVersion = feedback.EntityVersion
	}
	discussion = append(discussion, newMgs)
	approval.Props.Discussion = discussion
	approval.Props.IsAdminFeedback = feedback.IsAdminFeedback
	approval.Status = models.ApprovalStatusPending
	if feedback.IsAdminFeedback {
		approval.Props.AdminID = user.ID
	}
	if feedback.EntityID != nil {
		approval.EntityID = *feedback.EntityID
		approval.EntityVersion = *feedback.EntityVersion
		approval.Props.IsIncludeChange = true
	} else {
		approval.Props.IsIncludeChange = false
	}

	if err := models.Repository.Approval.Update(approval, nil); err != nil {
		return e.NewError500(e.Update_approval_failed, err.Error())
	}

	if feedback.IsAdminFeedback {
		notificationReq := &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeAdminFeedback,
			EntityID:   approval.EntityID, // Jump to course
			EntityType: communicationdto.CourseEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{approval.RequesterID},
				},
			},
			Props: communicationdto.JSONB{},
		}

		if err := communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to user ID %s after admin feedback course ID %s error: %v", approval.RequesterID, approval.EntityID, err)
		}
	} else {
		if approval.Props.AdminID != "" {
			notificationReq := &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeCreatorReplyFeedback,
				EntityID:   approval.ID, // Jump to course
				EntityType: communicationdto.ApprovalEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{approval.Props.AdminID},
					},
				},
				Props: communicationdto.JSONB{},
			}
			if err := communication.Notification.PushNotification(notificationReq); err != nil {
				log.Errorf("Push notification to user ID %s after creator reply course ID %s error: %v", approval.Props.AdminID, approval.ID, err)
			}
		}
	}

	return nil
}

func (s *ApprovalService) CancelOldRequest(approval *models.Approval) *e.AppError {
	if approval.EntityUID != "" {
		if err := models.Repository.Approval.CancelRequestByEntity(approval.ID, approval.EntityType, approval.EntityUID); err != nil {
			return e.NewError500(e.Approval_cancelled, "CancelOldRequest:"+err.Error())
		}
		return nil
	}
	return nil
}

func (s *ApprovalService) CreateWithTrans(org *models.Organization, requester *models.User, data *dto.CreateApprovalRequest, trans *gorm.DB) (*models.Approval, *e.AppError) {
	approval := models.Approval{
		EntityType:    data.EntityType,
		EntityID:      data.EntityID,
		EntitySchema:  org.Schema,
		ConfirmByID:   "",
		ConfirmDate:   0,
		Status:        models.ApprovalStatusNew,
		Type:          data.Type,
		RequesterID:   requester.ID,
		RequestDate:   time.Now().UnixMilli(),
		OrgID:         org.ID,
		RequestValue:  data.Value,
		Note:          data.Note,
		Props:         data.Props,
		RequestUid:    data.RequestUid,
		EntityVersion: data.EntityVersion,
		EntityUID:     data.EntityUID,
	}

	props := approval.Props
	if data.PreVersion != nil {
		props.PreVersion = *data.PreVersion
	}

	if data.PreID != nil {
		props.PreID = *data.PreID
	}

	approval.Props = props
	if err := models.Repository.Approval.Create(&approval, trans); err != nil {
		return nil, e.NewError500(e.Create_approval_failed, err.Error())
	}

	return &approval, nil
}
