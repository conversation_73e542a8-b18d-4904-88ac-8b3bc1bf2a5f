package services

import (
	"context"
	"openedu-core/models"
	"openedu-core/pkg/log"
	"sort"

	"github.com/samber/lo"
)

func MakeNotificationPropsForAICourse(org *models.Organization, course *models.Course, aiCourse *models.AICourse) models.JSONB {
	return models.JSONB{
		"org_id":      org.ID,
		"org_name":    org.Name,
		"org_domain":  org.Domain,
		"course_id":   course.ID,
		"course_cuid": course.Cuid,
		"course_name": course.Name,
		"course_slug": course.Slug,
		"provider":    aiCourse.OfferType,
	}
}

func MakeNotificationPropsForCourse(course *models.Course, org *models.Organization, user *models.User) models.JSONB {
	return models.JSONB{
		"org_id":      org.ID,
		"org_name":    org.Name,
		"org_domain":  org.Domain,
		"course_id":   course.ID,
		"course_cuid": course.Cuid,
		"course_name": course.Name,
		"course_slug": course.Slug,
		"user_id":     user.ID,
		"username":    user.Username,
	}
}

func MakeNotificationPropsForNextLesson(course *models.Course, org *models.Organization, user *models.User, status *models.LearningStatus) models.JSONB {
	mapSectionUIDSectionOrder := make(map[string]int)
	mapLessonUIDLessonOrder := make(map[string]int)

	mapOrderSectionUID := make(map[int]string)
	mapOrderLessonUID := make(map[int]string)

	sort.Slice(course.Outline, func(i, j int) bool { return course.Outline[i].Order < course.Outline[j].Order })

	for i, section := range course.Outline {
		//sort section and reassign order
		mapSectionUIDSectionOrder[section.UID] = i
		mapOrderSectionUID[i] = section.UID
		//sort lesson and reassign order
		sort.Slice(section.Lessons, func(i, j int) bool { return section.Lessons[i].Order < section.Lessons[j].Order })
		for j, lesson := range section.Lessons {
			mapLessonUIDLessonOrder[lesson.UID] = j
			mapOrderLessonUID[j] = lesson.UID
		}
	}

	currentSection, ok := lo.Find(course.Outline, func(item *models.Section) bool {
		return item.UID == status.LatestSectionUID
	})
	if !ok {
		log.ErrorWithAlertf("service/util/makePropsNotification: Cannot find current section")
		return nil
	}

	var nextLessonUID string
	var nextSectionUID string

	if mapLessonUIDLessonOrder[status.LatestLessonUID]+1 < len(currentSection.Lessons) {
		nextLessonUID = mapOrderLessonUID[mapLessonUIDLessonOrder[status.LatestLessonUID]+1]
		nextSectionUID = currentSection.UID
	} else {
		nextSectionUID = mapOrderSectionUID[mapSectionUIDSectionOrder[status.LatestSectionUID]+1]
		nextSection, ok := lo.Find(course.Outline, func(item *models.Section) bool {
			return item.UID == nextSectionUID
		})
		if !ok {
			log.ErrorWithAlertf("service/util/makePropsNotification: Cannot find next section")
			return nil
		}

		if len(nextSection.Lessons) > 0 {
			sort.Slice(nextSection.Lessons, func(i, j int) bool { return nextSection.Lessons[i].Order < nextSection.Lessons[j].Order })
			nextLessonUID = nextSection.Lessons[0].UID
		} else {
			log.ErrorWithAlertf("service/util/makePropsNotification: Cannot find next lesson")
			return nil
		}
	}

	return models.JSONB{
		"org_id":           org.ID,
		"org_name":         org.Name,
		"org_domain":       org.Domain,
		"course_id":        course.ID,
		"course_cuid":      course.Cuid,
		"course_name":      course.Name,
		"course_slug":      course.Slug,
		"user_id":          user.ID,
		"username":         user.Username,
		"next_section_uid": nextSectionUID,
		"next_lesson_uid":  nextLessonUID,
	}

}
func MakeNotificationPropsForLastestLesson(ls *models.LearningStatus) models.JSONB {
	course, err := models.Repository.Course(context.Background()).FindOne(&models.CourseQuery{Cuid: &ls.CourseCuid}, &models.FindOneOptions{Sort: []string{"version desc"}})
	if err != nil {
		log.ErrorWithAlertf("error when get course: %#v", err)
	}

	org, err := models.Repository.Organization.FindOne(&models.OrganizationQuery{ID: &ls.OrgID}, &models.FindOneOptions{})
	if err != nil {
		log.ErrorWithAlertf("error when get course: %#v", err)
	}

	return models.JSONB{
		"org_id":              org.ID,
		"org_name":            org.Name,
		"org_domain":          org.Domain,
		"course_id":           course.ID,
		"course_cuid":         course.Cuid,
		"course_name":         course.Name,
		"course_slug":         course.Slug,
		"user_id":             ls.UserID,
		"current_section_uid": ls.CurrentSectionUID,
		"current_lesson_uid":  ls.CurrentLessonUID,
	}

}
