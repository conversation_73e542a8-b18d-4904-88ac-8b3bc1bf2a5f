package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/e"

	"gorm.io/gorm"
)

func (s *ClpCourseLaunchpadService) Create(course *models.Course, launchpad *models.ClpLaunchpad) (*models.ClpCourseLaunchpad, *e.AppError) {
	courseLaunchpadExisted, err := models.Repository.ClpCourseLaunchpad(s.ctx).FindOne(&models.ClpCourseLaunchpadQuery{
		ClpLaunchpadID: &launchpad.ID,
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Course_launchpad_find_one_failed, "find course launchpad failed "+err.Error())
	}

	// 1 launch pad has 1 course
	if courseLaunchpadExisted != nil {
		err := models.Repository.ClpCourseLaunchpad(s.ctx).Delete(courseLaunchpadExisted.ID, nil)
		if err != nil {
			return nil, e.NewError500(e.Delete_course_launchpad_failed, "Delete course launchpad failed"+err.Error())
		}
	}

	courseLaunchpad := &models.ClpCourseLaunchpad{
		ClpLaunchpadID: launchpad.ID,
		CourseCuid:     course.Cuid,
		CourseID:       course.ID,
		Enable:         true,
	}

	err = models.Repository.ClpCourseLaunchpad(s.ctx).Create(courseLaunchpad, nil)
	if err != nil {
		return nil, e.NewError500(e.Create_course_launchpad_failed, "create course launchpad failed "+err.Error())
	}

	return courseLaunchpad, nil
}
