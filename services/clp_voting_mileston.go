package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	chaindto "openedu-core/pkg/openedu_chain/dto"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *ClpVotingMilestoneService) FindByID(id string) (*models.ClpVotingMilestone, *e.AppError) {
	milestone, err := models.Repository.ClpVotingMilestone.FindOne(
		&models.ClpVotingMilestoneQuery{
			ID: util.NewString(id),
		}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Voting_milestone_not_found, "Voting Milestone not found: "+err.Error())
		}
		return nil, e.New<PERSON>rror500(e.Voting_milestone_find_one_failed, "Find voting milestone by ID error: "+err.Error())
	}

	return milestone, nil
}

func (s *ClpVotingMilestoneService) VotingForLaunchpad(req *dto.VotingLaunchpadRequest) *e.AppError {
	if req.Launchpad.Status != models.LaunchpadSTTVoting {
		return e.NewError400(e.Launchpad_invalid_status, "Launchpad status not voting")
	}

	if !req.Milestone.IsStatusRunning() {
		return e.NewError400(e.Voting_milestone_invalid, "Voting milestone not running")
	}

	investment, err := models.Repository.ClpInvestment(s.ctx).FindOne(&models.ClpInvestmentQuery{
		ClpLaunchpadID: util.NewString(req.Launchpad.ID),
		UserID:         util.NewString(req.User.ID),
	}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError404(e.Investment_not_found, "Investment not found: "+err.Error())
		}
		return e.NewError500(e.Find_investment_failed, "Find investment by ID error: "+err.Error())
	}

	votingExisted, err := models.Repository.ClpVotingLaunchpad(s.ctx).FindOne(&models.ClpVotingLaunchpadQuery{
		ClpVotingMilestoneID: util.NewString(req.Milestone.ID),
		ClpInvestmentID:      util.NewString(investment.ID),
		ClpLaunchpadID:       util.NewString(req.Launchpad.ID),
	}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return e.NewError500(e.Voting_launchpad_find_one_failed, err.Error())
	}

	// TODO handle investment can voting many times
	// Get config from setting
	if votingExisted != nil {
		return e.NewError400(e.Create_voting_launchpad_failed, "investment voting launchpad existed")
	}

	votingPhase, err := models.Repository.ClpVotingPhase.FindOne(&models.ClpVotingPhaseQuery{
		ClpLaunchpadID:       util.NewString(req.Launchpad.ID),
		ClpVotingMilestoneID: util.NewString(req.Milestone.ID),
	}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		if models.IsRecordNotFound(err) {
			return e.NewError404(e.Voting_phase_not_found, "voting phase not found")
		}
		return e.NewError500(e.Voting_launchpad_find_one_failed, err.Error())
	}

	voting := &models.ClpVotingLaunchpad{
		ClpInvestmentID:      investment.ID,
		ClpLaunchpadID:       req.Launchpad.ID,
		ClpVotingMilestoneID: req.Milestone.ID,
		Status:               req.Status,
	}
	err = models.Repository.ClpVotingLaunchpad(s.ctx).Create(voting, nil)
	if err != nil {
		return e.NewError500(e.Create_voting_launchpad_failed, err.Error())
	}

	if req.Status == models.StatusVotingReject {
		votingPhase.TotalReject = votingPhase.TotalReject + 1
	} else {
		votingPhase.TotalApprove = votingPhase.TotalApprove + 1
	}

	err = models.Repository.ClpVotingPhase.Update(votingPhase, nil)
	if err != nil {
		return e.NewError500(e.Update_voting_phase_failed, "update voting phase failed "+err.Error())
	}

	return nil
}

func (s *ClpVotingMilestoneService) EndVoting(req *dto.EndVotingMilestoneRequest) *e.AppError {
	milestones, err := models.Repository.ClpVotingMilestone.FindMany(&models.ClpVotingMilestoneQuery{
		ClpLaunchpadID: &req.LaunchpadID,
	}, nil)
	if err != nil {
		return e.NewError500(e.Voting_milestone_find_many_failed, "find many milestones of launchpad failed "+err.Error())
	}

	milestone, found := lo.Find(milestones, func(m *models.ClpVotingMilestone) bool {
		return m.ID == req.MilestoneID
	})
	if !found {
		return e.NewError404(e.Voting_milestone_not_found, "milestone not found")
	}

	if milestone.Status != models.VotingMilestoneRunning {
		return e.NewError500(e.Voting_milestone_invalid, "milestone status not running")
	}

	launchpad, err := models.Repository.ClpLaunchpad(s.ctx).FindOne(&models.LaunchpadQuery{
		ID: util.NewString(milestone.ClpLaunchpadID),
	}, &models.FindOneOptions{
		Preloads: []string{models.CoursesField, models.UserField},
	})
	if err != nil {
		if models.IsRecordNotFound(err) {
			return e.NewError404(e.Launchpad_not_found, "launchpad not found "+err.Error())
		}

		return e.NewError500(e.Launchpad_find_one_failed, "launchpad find failed "+err.Error())
	}

	votingPhase, err := models.Repository.ClpVotingPhase.FindOne(&models.ClpVotingPhaseQuery{
		ClpVotingMilestoneID: util.NewString(milestone.ID),
	}, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return e.NewError404(e.Voting_phase_not_found, "voting phase not found "+err.Error())
		}

		return e.NewError500(e.Voting_phase_find_one_failed, "voting phase find failed "+err.Error())
	}

	votingLaunchpads, err := models.Repository.ClpVotingLaunchpad(s.ctx).FindMany(&models.ClpVotingLaunchpadQuery{
		ClpVotingMilestoneID: util.NewString(milestone.ID),
	}, &models.FindManyOptions{
		Preloads: []string{models.InvestmentField},
	})
	if err != nil {
		return e.NewError500(e.Voting_launchpad_find_many_failed, "find many voting launchpad failed "+err.Error())
	}

	votedInvestmentIDs := lo.Map(votingLaunchpads, func(item *models.ClpVotingLaunchpad, _ int) string {
		return item.ClpInvestmentID
	})

	investments, err := models.Repository.ClpInvestment(s.ctx).FindMany(&models.ClpInvestmentQuery{
		ClpLaunchpadID: util.NewString(req.LaunchpadID),
		IDNotIn:        votedInvestmentIDs,
	}, nil)
	if err != nil {
		return e.NewError500(e.Find_investment_failed, "find many investment haven't vote for launchpad failed "+err.Error())
	}

	// auto vote approve for investments
	if len(investments) > 0 {
		appErr := s.AutoVotingApprove(&dto.VotingLaunchpadRequest{
			Launchpad:   launchpad,
			Milestone:   milestone,
			VotingPhase: votingPhase,
		}, investments)
		if appErr != nil {
			return appErr
		}
	}

	// step 2: Get voting power
	backers, appErr := ClpLaunchpad.GetVotingPowersOffChain(launchpad)
	if appErr != nil {
		return appErr
	}

	investmentsReject := lo.FilterMap(votingLaunchpads, func(votingLaunchpad *models.ClpVotingLaunchpad, _ int) (*models.ClpInvestment, bool) {
		return votingLaunchpad.ClpInvestment, votingLaunchpad.Status == models.StatusVotingReject
	})

	rejectVotingPowerMap := make(map[string]float64)
	for _, entry := range backers {
		rejectVotingPowerMap[entry.UserID] = entry.VotingPower
	}

	totalRejectVotingPower := 0.0
	for _, investment := range investmentsReject {
		if votingPower, ok := rejectVotingPowerMap[investment.UserID]; ok {
			totalRejectVotingPower += votingPower
		}
	}

	targetReject := float64(models.GetConfig[int](models.CourseLaunchpadMinRejectPercentage))
	if totalRejectVotingPower >= targetReject {
		return s.handleVotingMilestoneFailed(launchpad, milestone)
	}

	return s.handleVotingMilestoneSuccess(launchpad, milestone, milestones, votingPhase)
}

func (s *ClpVotingMilestoneService) pushNotifWhenVotingMilestoneSuccess(launchpad *models.ClpLaunchpad) {
	rootOrg := Organization.GetRoot()

	launchpadOwner, err := models.Repository.User.FindByID(launchpad.UserID)
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenVotingMilestoneSuccess Find launchpad owner failed: %w", err)
		return
	}

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeLaunchpadVotingPassedForCreator,
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{launchpadOwner.ID},
			},
		},
		Props: ClpLaunchpad.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, nil),
	}
	if err = communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenVotingMilestoneSuccess push notifications failed: %w", err)
	}
	return
}

func (s *ClpVotingMilestoneService) handleVotingMilestoneFailed(
	launchpad *models.ClpLaunchpad,
	milestone *models.ClpVotingMilestone,
) *e.AppError {

	// Update pool status to REFUNDED
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		return e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
	}

	_, err := openedu_chain.Transaction.UpdateLpPoolStatus(&chaindto.UpdateLaunchpadPoolStatusRequest{
		PoolID:    launchpad.GetPoolID(),
		Status:    chaindto.PoolStatusRefunded,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return e.NewError500(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return e.NewError500(e.Launchpad_update_pool_status_failed, "Update launchpad pool status error: "+err.Error())
		}
	}

	// Update course launchpad status
	launchpad.Status = models.LaunchpadSTTFailed
	if err := models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, nil); err != nil {
		return e.NewError500(e.Update_launchpad_failed, "Update launchpad failed "+err.Error())
	}

	// Update voting milestone status
	milestone.Status = models.VotingMilestoneFailed
	if err := models.Repository.ClpVotingMilestone.Update(milestone, nil); err != nil {
		return e.NewError500(e.Update_voting_milestone_failed, "Update milestone failed "+err.Error())
	}

	// Update status refunded for investments
	if err := models.Repository.ClpInvestment(s.ctx).UpdateManyInvestmentStatus(models.GotRefunded, launchpad.ID, nil); err != nil {
		return e.NewError500(e.Update_investment_failed, "Update status for many investments failed "+err.Error())
	}

	// push notification to creator
	go func() {
		s.pushNotifWhenVotingMilestoneFailed(launchpad)
	}()

	// push notification to backer to claim refund
	go func() {
		s.pushNotifForBackerWhenVotingFailed(launchpad)
	}()

	return nil
}

func (s *ClpVotingMilestoneService) pushNotifWhenVotingMilestoneFailed(launchpad *models.ClpLaunchpad) {
	rootOrg := Organization.GetRoot()

	launchpadOwner, err := models.Repository.User.FindByID(launchpad.UserID)
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenVotingMilestoneFailed Find launchpad owner failed: %w", err)
		return
	}

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeLaunchpadVotingFailedForCreator,
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{launchpadOwner.ID},
			},
		},
		Props: ClpLaunchpad.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, nil),
	}
	if err = communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenVotingMilestoneFailed push notifications failed: %w", err)
	}
	return
}

func (s *ClpVotingMilestoneService) handleVotingMilestoneSuccess(
	launchpad *models.ClpLaunchpad,
	milestone *models.ClpVotingMilestone,
	milestones []*models.ClpVotingMilestone,
	votingPhase *models.ClpVotingPhase,
) *e.AppError {

	// Update voting milestone status
	milestone.Status = models.VotingMilestoneCompleted
	err := models.Repository.ClpVotingMilestone.Update(milestone, nil)
	if err != nil {
		return e.NewError500(e.Update_voting_milestone_failed, "update milestone failed "+err.Error())
	}

	// Withdraw launchpad fund to creator
	if appErr := s.withdrawFundToCreator(launchpad, votingPhase); appErr != nil {
		return appErr
	}

	launchpad.TotalRefunded = launchpad.TotalRefunded.Sub(votingPhase.Amount)
	// If milestone is the last, set launchpad is success
	if isLastMilestoneByOrder(milestones, milestone.ID) {
		if appErr := s.handleLaunchpadSuccess(launchpad); appErr != nil {
			return appErr
		}
	} else {
		if err = models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, nil); err != nil {
			return e.NewError500(e.Update_launchpad_failed, "Update launchpad failed "+err.Error())
		}

		// TODO push notification to creator
		go func() {
			s.pushNotifWhenVotingMilestoneSuccess(launchpad)
		}()
	}
	return nil
}

func (s *ClpVotingMilestoneService) withdrawFundToCreator(
	launchpad *models.ClpLaunchpad,
	votingPhase *models.ClpVotingPhase,
) *e.AppError {
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		return e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
	}

	_, wErr := openedu_chain.Transaction.WithdrawLpFundToCreator(&chaindto.WithdrawLaunchpadFundToCreatorRequest{
		PoolID:    launchpad.GetPoolID(),
		Token:     chaindto.BlockchainToken(launchpad.FundingGoal.Currency),
		Amount:    votingPhase.Amount,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if wErr != nil {
		switch {
		case errors.Is(wErr, openedu_chain.ErrInsufficientGasFee):
			return e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+wErr.Error())

		default:
			return e.NewError500(e.Course_launchpad_continue_with_partial_fund_failed,
				"Withdraw launchpad fund to creator error: "+wErr.Error())
		}
	}
	return nil
}

func (s *ClpVotingMilestoneService) handleLaunchpadSuccess(launchpad *models.ClpLaunchpad) *e.AppError {
	// Publish course
	_, appErr := Course.RequestPublishCourseLaunchpad(&dto.PublishCourseRequest{
		Status:    models.CourseSTTPublicRoot,
		Course:    launchpad.Courses[0],
		Org:       Organization.GetRoot(),
		Requester: launchpad.User,
	})
	if appErr != nil {
		return appErr
	}

	// Update pool status
	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &launchpad.Props.WalletID}, nil)
	if aErr != nil {
		return e.NewError500(e.WalletFindFailed, "Find the wallet failed: "+aErr.Error())
	}

	_, err := openedu_chain.Transaction.UpdateLpPoolStatus(&chaindto.UpdateLaunchpadPoolStatusRequest{
		PoolID:    launchpad.GetPoolID(),
		Status:    chaindto.PoolStatusSuccess,
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return e.NewError500(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		default:
			return e.NewError500(e.Launchpad_update_pool_status_failed, "Update launchpad pool status error: "+err.Error())
		}
	}

	// Update launchpad status
	launchpad.Status = models.LaunchpadSTTSuccess
	if err = models.Repository.ClpLaunchpad(s.ctx).Update(launchpad, nil); err != nil {
		return e.NewError500(e.Update_launchpad_failed, "Update launchpad failed "+err.Error())
	}

	// Update many status success for investment
	err = models.Repository.ClpInvestment(s.ctx).UpdateManyInvestmentStatus(models.GotRevenueStatus, launchpad.ID, nil)
	if err != nil {
		return e.NewError500(e.Update_investment_failed, "Update status for many investments failed "+err.Error())
	}

	return nil
}

func isLastMilestoneByOrder(milestones []*models.ClpVotingMilestone, milestoneID string) bool {
	var maxOrder int
	var targetOrder int

	// Find max order and target milestone's order
	for _, m := range milestones {
		if m.Order > maxOrder {
			maxOrder = m.Order
		}
		if m.ID == milestoneID {
			targetOrder = m.Order
		}
	}

	return targetOrder == maxOrder
}

func (s *ClpVotingMilestoneService) AutoVotingApprove(data *dto.VotingLaunchpadRequest, investments []*models.ClpInvestment) *e.AppError {
	uniqInvestmentIDs := lo.Uniq(lo.Map(investments, func(investment *models.ClpInvestment, _ int) string {
		return investment.ID
	}))
	votingLaunchpads := make([]*models.ClpVotingLaunchpad, 0, len(uniqInvestmentIDs))

	// Create a ClpVotingLaunchpad for each investment ID
	for _, investmentID := range uniqInvestmentIDs {
		votingLaunchpad := &models.ClpVotingLaunchpad{
			ClpInvestmentID:      investmentID,
			ClpLaunchpadID:       data.Launchpad.ID,
			ClpVotingMilestoneID: data.Milestone.ID,
			Status:               models.StatusVotingAutoApprove,
		}
		votingLaunchpads = append(votingLaunchpads, votingLaunchpad)
	}

	// Create many records at once
	err := models.Repository.ClpVotingLaunchpad(s.ctx).CreateMany(votingLaunchpads, nil)
	if err != nil {
		return e.NewError500(e.Create_voting_launchpad_failed, err.Error())
	}

	data.VotingPhase.TotalApprove = data.VotingPhase.TotalApprove + len(votingLaunchpads)
	err = models.Repository.ClpVotingPhase.Update(data.VotingPhase, nil)
	if err != nil {
		return e.NewError500(e.Update_voting_phase_failed, "update voting phase failed "+err.Error())
	}

	// Push notifications to backers
	go func() {
		s.pushNotifWhenAutoApproveVotes(data.Launchpad, investments)
	}()

	return nil
}

func (s *ClpVotingMilestoneService) pushNotifWhenAutoApproveVotes(launchpad *models.ClpLaunchpad, investments []*models.ClpInvestment) {
	if len(investments) == 0 {
		return
	}

	rootOrg := Organization.GetRoot()

	userIDs := lo.Map(investments, func(investment *models.ClpInvestment, _ int) string {
		return investment.UserID
	})
	userIDs = lo.Uniq(userIDs)

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeLaunchpadVoteAutoApprovedForBacker,
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: userIDs,
			},
		},
		Props: ClpLaunchpad.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, nil),
	}
	if err := communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifWhenStartVoting push notifications failed: %w", err)
	}
	return
}

func (s *ClpVotingMilestoneService) GetVotingResultForVotingMilestone(userID string, backers []*models.LaunchpadVotingPowerEntry, milestone *models.ClpVotingMilestone) (*models.VotingProcess, *e.AppError) {
	_, err := models.Repository.ClpVotingPhase.FindOne(&models.ClpVotingPhaseQuery{
		ClpVotingMilestoneID: util.NewString(milestone.ID),
	}, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, nil
		}

		return nil, e.NewError500(e.Voting_phase_find_one_failed, "find voting phase failed "+err.Error())
	}

	votingLaunchpads, err := models.Repository.ClpVotingLaunchpad(s.ctx).FindMany(&models.ClpVotingLaunchpadQuery{
		ClpVotingMilestoneID: util.NewString(milestone.ID),
	}, &models.FindManyOptions{
		Preloads: []string{models.InvestmentField},
	})
	if err != nil {
		return nil, e.NewError500(e.Voting_launchpad_find_many_failed, "find many voting launchpad failed "+err.Error())
	}

	investmentsReject := make([]*models.ClpInvestment, 0)
	investmentsApprove := make([]*models.ClpInvestment, 0)
	isVoted := false
	if len(votingLaunchpads) > 0 {
		lo.ForEach(votingLaunchpads, func(votingLaunchpad *models.ClpVotingLaunchpad, _ int) {
			if votingLaunchpad.ClpInvestment == nil {
				return
			}

			if userID == votingLaunchpad.ClpInvestment.UserID {
				isVoted = true
			}

			switch votingLaunchpad.Status {
			case models.StatusVotingReject:
				investmentsReject = append(investmentsReject, votingLaunchpad.ClpInvestment)
			case models.StatusVotingApprove:
				investmentsApprove = append(investmentsApprove, votingLaunchpad.ClpInvestment)
			}
		})
	}

	votingPowerMap := make(map[string]float64)
	for _, entry := range backers {
		votingPowerMap[entry.UserID] = entry.VotingPower
	}

	totalRejectVotingPower := 0.0
	for _, investment := range investmentsReject {
		if votingPower, ok := votingPowerMap[investment.UserID]; ok {
			totalRejectVotingPower += votingPower
		}
	}

	totalApproveVotingPower := 0.0
	for _, investment := range investmentsApprove {
		if votingPower, ok := votingPowerMap[investment.UserID]; ok {
			totalApproveVotingPower += votingPower
		}
	}

	votingProcess := &models.VotingProcess{
		IsVoted:           isVoted,
		TotalVote:         len(votingLaunchpads),
		ApprovePercentage: totalApproveVotingPower,
		RejectPercentage:  totalRejectVotingPower,
	}

	return votingProcess, nil
}

func (s *ClpVotingMilestoneService) pushNotifForBackerWhenVotingFailed(launchpad *models.ClpLaunchpad) {
	rootOrg := Organization.GetRoot()
	investments, err := models.Repository.ClpInvestment(s.ctx).FindMany(&models.ClpInvestmentQuery{
		ClpLaunchpadID: &launchpad.ID,
	}, &models.FindManyOptions{})
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifForBackerWhenVotingFailed find list investments failed: %w", err)
		return
	}

	if len(investments) == 0 {
		return
	}

	userIDs := lo.Map(investments, func(investment *models.ClpInvestment, _ int) string {
		return investment.UserID
	})
	userIDs = lo.Uniq(userIDs)

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeLaunchpadVotingFailedForBacker,
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: userIDs,
			},
		},
		Props: ClpLaunchpad.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, nil),
	}
	if err := communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifForBackerWhenVotingFailed push notifications failed: %w", err)
	}
	return
}

func (s *ClpVotingMilestoneService) PushNotifForBackerWhenCreatorEndLaunchpad(launchpad *models.ClpLaunchpad) {
	rootOrg := Organization.GetRoot()
	investments, err := models.Repository.ClpInvestment(s.ctx).FindMany(&models.ClpInvestmentQuery{
		ClpLaunchpadID: &launchpad.ID,
	}, &models.FindManyOptions{})
	if err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifForBackerWhenCreatorEndLaunchpad find list investments failed: %w", err)
		return
	}

	if len(investments) == 0 {
		return
	}

	userIDs := lo.Map(investments, func(investment *models.ClpInvestment, _ int) string {
		return investment.UserID
	})
	userIDs = lo.Uniq(userIDs)

	notiReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeLaunchpadEndByCreatorForBacker,
		EntityID:   launchpad.ID, // Jump to launchpad
		EntityType: communicationdto.ClpLaunchpadEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: userIDs,
			},
		},
		Props: ClpLaunchpad.MakeNotificationPropsForLaunchpad(launchpad, rootOrg, nil),
	}
	if err := communication.Notification.PushNotification(notiReq); err != nil {
		log.ErrorWithAlertf("ClpLaunchpadService::pushNotifForBackerWhenCreatorEndLaunchpad push notifications failed: %w", err)
	}
	return
}
