package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *CouponService) Create(user *models.User, req *dto.CreateCouponRequest) (*models.Coupon, *e.AppError) {

	exists, cErr := Coupon.FindOne(&models.CouponQuery{CouponCode: &req.CouponCode}, &models.FindOneOptions{})

	if (cErr != nil) && (cErr.ErrCode != e.CouponNotFound) {
		return nil, cErr
	}

	if exists != nil {
		return nil, e.NewError400(e.CouponCodeAlreadyExists, "Coupon code already exists")
	}

	coupon := models.Coupon{
		Name:                     req.Name,
		OrgID:                    req.OrgID,
		Description:              req.Description,
		FiatDiscountEnabled:      req.FiatDiscountEnabled,
		FiatDiscountPercentage:   req.FiatDiscountPercentage,
		FiatMinAmountToUse:       req.FiatMinAmountToUse,
		FiatAllowMaximumDiscount: req.FiatAllowMaximumDiscount,
		//DiscountAmount:             req.DiscountAmount,
		CryptoDiscountEnabled:      req.CryptoDiscountEnabled,
		CryptoDiscountPercentage:   req.CryptoDiscountPercentage,
		CryptoMinAmountToUse:       req.CryptoMinAmountToUse,
		CryptoAllowMaximumDiscount: req.CryptoAllowMaximumDiscount,
		//CryptoDiscountAmount:       req.CryptoDiscountAmount,
		MaximumTotalUsage: req.MaximumTotalUsage,
		AllowCourses:      models.StringArray(req.AllowCourses),
		AllowTeams:        models.StringArray(req.AllowTeams),
		IsActive:          req.IsActive,
		Type:              req.Type,
		Method:            req.Method,
		StartDate:         req.StartDate,
		EndDate:           req.EndDate,
		CouponCode:        req.CouponCode,
		CreatedBy:         user.ID,
	}

	if err := models.Repository.Coupon.Create(&coupon, nil); err != nil {
		return nil, e.NewError500(e.CouponCreateFailed, err.Error())
	}
	return &coupon, nil
}

func (s *CouponService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Coupon, *e.AppError) {
	query := &models.CouponQuery{
		ID:             util.NewString(id),
		IncludeDeleted: util.NewBool(includeDeleted),
	}
	if coupon, err := models.Repository.Coupon.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.CouponNotFound, err.Error())
		}
		return nil, e.NewError500(e.CouponFindFailed, err.Error())
	} else {
		return coupon, nil
	}
}

func (s *CouponService) FindOne(query *models.CouponQuery, options *models.FindOneOptions) (*models.Coupon, *e.AppError) {
	if coupon, err := models.Repository.Coupon.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.CouponNotFound, err.Error())
		}
		return nil, e.NewError500(e.CouponFindFailed, err.Error())
	} else {
		return coupon, nil
	}
}

func (s *CouponService) FindPage(query *models.CouponQuery, options *models.FindPageOptions) ([]*models.Coupon, *models.Pagination, *e.AppError) {
	if coupons, pagination, err := models.Repository.Coupon.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.CouponFindFailed, err.Error())
	} else {
		return coupons, pagination, nil
	}
}

func (s *CouponService) Update(c *models.Coupon, req *dto.UpdateCouponRequest) *e.AppError {
	c.Name = req.Name
	c.CouponCode = req.CouponCode
	c.Type = models.CouponType(req.Type)
	c.Method = models.CouponMethod(req.Method)
	c.StartDate = req.StartDate
	c.EndDate = req.EndDate

	c.FiatDiscountEnabled = req.FiatDiscountEnabled
	c.FiatDiscountPercentage = req.FiatDiscountPercentage
	c.FiatMinAmountToUse = req.FiatMinAmountToUse
	c.FiatAllowMaximumDiscount = req.FiatAllowMaximumDiscount
	//c.DiscountAmount = req.DiscountAmount

	c.CryptoDiscountEnabled = req.CryptoDiscountEnabled
	c.CryptoDiscountPercentage = req.CryptoDiscountPercentage
	c.CryptoMinAmountToUse = req.CryptoMinAmountToUse
	c.CryptoAllowMaximumDiscount = req.CryptoAllowMaximumDiscount
	//c.CryptoDiscountAmount = req.CryptoDiscountAmount

	c.MaximumTotalUsage = req.MaximumTotalUsage
	c.Description = req.Description
	c.AllowCourses = req.AllowCourses
	c.AllowTeams = req.AllowTeams
	c.IsActive = req.IsActive
	c.OrgID = req.OrgID

	if err := models.Repository.Coupon.Update(c, nil); err != nil {
		return e.NewError500(e.CouponUpdateFailed, err.Error())
	}
	return nil
}

func (s *CouponService) CanUpdateCoupon(coupon *models.Coupon, user *models.User, org *models.Organization) bool {
	// admin, sysadmin, mod
	if coupon.CreatedBy != user.ID {
		return false
	}

	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Find UserRoleOrg failed: ", err.Error())
		return false
	}
	if len(userRoles) > 0 {
		for _, ur := range userRoles {
			if lo.Contains[string](editRoles, ur.RoleID) {
				return true
			}
		}
	}
	/// check org admin update own coupon
	if coupon.OrgID == org.ID && user.CanActCoupon(org.ID) {
		return true
	}

	return false

}

func (s *CouponService) Delete(c *models.Coupon) *e.AppError {
	if err := models.Repository.Coupon.Delete(c.ID, nil); err != nil {
		return e.NewError500(e.CouponDeleteFailed, err.Error())
	}
	return nil
}

func (s *CouponService) Verify(org *models.Organization, user *models.User, order *models.Order, coupon *models.Coupon) (*models.Coupon, *e.AppError) {
	// Check coupon is active or not
	if !coupon.IsActive {
		return nil, e.NewError400(e.Coupon_not_available, "Coupon not available")
	}

	// Check organizer coupon
	if coupon.OrgID != "" && coupon.OrgID != org.ID {
		return nil, e.NewError400(e.Coupon_not_available_in_this_org, "Coupon not available in this org")
	}

	//Check maximum total usage
	if coupon.MaximumTotalUsage != 0 && coupon.TotalUsed >= coupon.MaximumTotalUsage {
		return nil, e.NewError400(e.Coupon_maximum_usage, "Coupon maximum usage")
	}

	// Check coupon is support order's payment method
	orderCurrencyInfo, found := models.GetCurrencyInfo(order.Currency)
	if !found {
		return nil, e.NewError400(e.OrderMismatchCurrencies, "Unsupported order currency: "+string(order.Currency))
	}

	switch orderCurrencyInfo.Type {
	case models.AssetTypeFiat:
		if !coupon.FiatDiscountEnabled {
			return nil, e.NewError400(e.CouponFiatDiscountDisabled, "The fiat discount is disabled")
		}

		if order.Amount.LessThan(coupon.FiatMinAmountToUse) {
			return nil, e.NewError400(e.CouponNotEnoughMinAmountToUse, "Order amount not enough to use coupon")
		}

	case models.AssetTypeCrypto:
		if !coupon.CryptoDiscountEnabled {
			return nil, e.NewError400(e.CouponCryptoDiscountDisabled, "The crypto discount is disabled")
		}

		if order.Amount.LessThan(coupon.CryptoMinAmountToUse) {
			return nil, e.NewError400(e.CouponNotEnoughMinAmountToUse, "Order amount not enough to use coupon")
		}
	}

	//Check Expired
	now := time.Now().UnixMilli()

	log.Debugf("StartDate: %+v", coupon.StartDate)
	log.Debugf("Now: %+v", now)
	log.Debugf("EndDate: %+v", coupon.EndDate)

	// check start date and end date
	switch {
	case coupon.StartDate != 0 && coupon.EndDate != 0:
		if now < coupon.StartDate || now > coupon.EndDate {
			return nil, e.NewError400(e.Coupon_expired, "Coupon expired")
		}
	case coupon.StartDate != 0:
		if now < coupon.StartDate {
			return nil, e.NewError400(e.CouponNotAvailableYet, "Coupon not available yet")
		}
	case coupon.EndDate != 0:
		if now > coupon.EndDate {
			return nil, e.NewError400(e.Coupon_expired, "Coupon expired")
		}
	}

	//Check allow course
	if len(coupon.AllowCourses) > 0 {
		courseIDs, fcErr := models.Repository.Order.FindCourseIDsByOrderID(order.ID)
		if fcErr != nil {
			return nil, e.NewError500(e.OrderFindCourseByOrderIDFailed, "Find course IDs by order ID "+order.ID+" error: "+fcErr.Error())
		}

		if !util.IsSubsetArrayString(coupon.AllowCourses, courseIDs) {
			return nil, e.NewError400(e.Coupon_not_allow_course, "Coupon not allow course")
		}
	}

	//Check allow user
	if len(coupon.AllowTeams) > 0 {
		if !util.IsSubsetArrayString(coupon.AllowTeams, []string{user.ID}) {
			return nil, e.NewError400(e.Coupon_not_allow_user, "Coupon not allow user")
		}
	}

	// todo??
	//Check used coupon
	exists, _ := models.Repository.CouponHistory.FindOne(&models.CouponHistoryQuery{
		UserID:   &user.ID,
		CouponID: &coupon.ID,
		Status:   util.NewT(models.CouponStatusUsed),
	}, &models.FindOneOptions{})
	if exists != nil {
		return nil, e.NewError400(e.Coupon_already_used, "Coupon already used")
	}

	return coupon, nil
}

func (s *CouponService) ListCouponsByCreator(creator *models.User, query *models.CouponQuery, options *models.FindPageOptions) ([]*models.Coupon, *models.Pagination, *e.AppError) {
	// Find all course that user is partner
	coursePartners, err := models.Repository.CoursePartner.FindMany(&models.CoursePartnerQuery{
		PartnerID: &creator.ID,
	}, &models.FindManyOptions{})
	if err != nil {
		return nil, nil, nil
	}
	courseCuids := lo.Map(coursePartners, func(item *models.CoursePartner, _ int) string {
		return item.CourseID
	})

	query.CourseIDs = courseCuids
	coupons, pagination, err := models.Repository.Coupon.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError400(e.CouponFindPageFailed, err.Error())
	}
	return coupons, pagination, nil
}
