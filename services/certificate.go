package services

import (
	"context"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	chaindto "openedu-core/pkg/openedu_chain/dto"

	"github.com/samber/lo"
	"gorm.io/gorm"

	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"
)

func (s *CertificateService) Create(req *dto.ClaimCertificateRequest) (*models.Certificate, *e.AppError) {
	if req.CheckCertCondition {
		canReceive, appErr := s.CheckingCertificateCondition(&dto.CheckingCertificateConditionRequest{
			UserID: req.User.ID,
			Course: req.Course,
			OrgID:  req.Cert.OrgID,
		})
		if appErr != nil {
			log.Debug("Checking certificate condition failed")
			return nil, appErr
		}

		if !canReceive {
			return nil, e.NewError400(e.CertificateNotCompleteConditions, "not complete certificate condition")
		}
	}
	
	if err := models.Repository.Certificate.Create(req.Cert, nil); err != nil {
		return nil, e.NewError500(e.CertificateCreateFailed, "Create certificate error: "+err.Error())
	}

	createdCert, err := models.Repository.Certificate.FindOne(
		&models.CertificateQuery{CourseCuid: &req.Cert.CourseCuid, UserID: &req.Cert.UserID, OrgID: &req.Cert.OrgID},
		&models.FindOneOptions{Preloads: []string{models.FilesField}})
	if err != nil {
		return nil, e.NewError500(e.CertificateFindFailed, "Find certificate by ID error: "+err.Error())
	}

	// Push notification
	go func(cert *models.Certificate, u *models.User, c *models.Course) {
		notificationReq := &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeNewCertificateReceived,
			EntityID:   cert.ID,
			EntityType: communicationdto.CertificateEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{req.User.ID},
				},
			},
			Props: s.makeNotificationPropsForCertificate(cert, u, c).IntoComm(),
		}
		if err = communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to user ID %s for new certificate received error: %v", u.ID, err)
		}
	}(createdCert, req.User, req.Course)
	// Send email
	aigov := models.GetConfig[models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	if req.Org.ID == aigov.OrgID {
		go func() {
			//SendEmail
			emailReq := &communicationdto.SendEmailRequest{
				User: req.User.IntoComm(),
				Org:  req.Org.IntoComm(),
				Code: util.NewT(communicationdto.EmailCodeGetCertificate),
				ExtendDatas: communicationdto.MapEmailParams{
					"username":       req.User.Username,
					"user_email":     req.User.Email,
					"course_name":    req.Course.Name,
					"certificate_id": createdCert.ID,
					"full_name":      req.User.DisplayName,
				},
				IsQueue: false,
				From:    req.Org.Settings.SenderEmail,
			}
			if _, err := communication.Email.SendEmail(emailReq); err != nil {
				log.ErrorWithAlertf("GetCertificate::Send email code: %v failed: %v", communicationdto.EmailCodeGetCertificate, err)
			}
		}()

	}

	// Auto mint NFT if course settings enabled mint NFT and platform sponsor gas for its
	if req.Course.IsMintNFTEnabled() {
		sponsoredCourseCUIDs := models.GetConfig[[]string](models.PlatformSponsorNFTGasCourses)
		if _, sponsored := lo.Find(sponsoredCourseCUIDs, func(cuid string) bool {
			return cuid == req.Course.Cuid
		}); sponsored {
			go func() {
				_, aErr := s.MintNFT(&dto.MintCertificateNFTRequest{
					Certificate: createdCert,
					User:        req.User,
					GasFeePayer: models.Platform,
				})
				if aErr != nil {
					log.Errorf("CertificateService::Create Minting NFT automatically for user ID %s in course CUID %s failed: %v",
						req.User.ID, req.Course.Cuid, aErr)
				}
			}()
		}
	}

	return createdCert, nil
}

func (s *CertificateService) makeNotificationPropsForCertificate(cert *models.Certificate, user *models.User, course *models.Course) models.JSONB {
	return models.JSONB{
		"user_id":      user.ID,
		"username":     user.Username,
		"display_name": user.DisplayName,
		"course_name":  course.Name,
		"course_slug":  course.Slug,
		"org_id":       cert.OrgID,
	}
}

func (s *CertificateService) FindPage(query *models.CertificateQuery, options *models.FindPageOptions) ([]*models.Certificate, *models.Pagination, *e.AppError) {
	certificates, pagination, err := models.Repository.Certificate.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.CertificateFindPageFailed, "Find page certificate error: "+err.Error())
	}
	return certificates, pagination, nil
}

func (s *CertificateService) Delete(query *models.CertificateQuery) *e.AppError {
	getCert, _ := models.Repository.Certificate.FindOne(query, &models.FindOneOptions{})
	if getCert == nil {
		return e.NewError400(e.CertificateNotFound, "Certificate not found")
	}

	if err := models.Repository.Certificate.Delete(getCert.ID, nil); err != nil {
		return e.NewError500(e.CertificateDeleteFailed, err.Error())
	}

	return nil
}

func (s *CertificateService) FindOneOptions(query *models.CertificateQuery, options *models.FindOneOptions) (*models.Certificate, *e.AppError) {
	cert, err := models.Repository.Certificate.FindOne(query, options)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.CertificateFindFailed, err.Error())
	}

	return cert, nil
}

func (s *CertificateService) FindByID(id string) (*models.Certificate, *e.AppError) {
	cert, err := models.Repository.Certificate.FindOne(
		&models.CertificateQuery{
			ID: util.NewString(id),
		}, &models.FindOneOptions{
			Preloads: []string{models.FilesField, models.ImageField},
		})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.CertificateNotFound, "Certificate not found: "+err.Error())
		}
		return nil, e.NewError500(e.CertificateFindFailed, "Find certificate by ID error: "+err.Error())
	}

	return cert, nil
}

func (s *CertificateService) CheckingCertificateCondition(data *dto.CheckingCertificateConditionRequest) (bool, *e.AppError) {
	course := data.Course
	// check conditions in goroutines
	// complete course with percentage`
	if course.Props.CertificateCondition.CompletedCourse {
		completedLessonCount, err := models.Repository.LearningStatus(context.TODO()).
			CountCompletedLessonsByUsers([]string{course.ID}, []string{data.UserID})

		if err != nil {
			return false, e.NewError500(e.Course_find_publish_failed, "Get complete lesson from learning progress failed: "+err.Error())
		}

		if len(completedLessonCount) <= 0 {
			if course.Props.CertificateCondition.CourseCompletionPercentage <= 0 {
				return true, nil
			}

			return false, nil
		}

		completedPercentage := float64(completedLessonCount[0].CompletedLessons) / float64(completedLessonCount[0].TotalLessons) * 100
		requiredPercentage := float64(course.Props.CertificateCondition.CourseCompletionPercentage)
		log.Debug("complete percent:", completedPercentage)
		log.Debug("condition required:", requiredPercentage)
		if completedPercentage < requiredPercentage {

			return false, nil
		}
	}

	if course.Props.CertificateCondition.CompletedAllQuizzes && course.QuizCount > 0 {
		lessonContents, err := models.Repository.LessonContent(context.TODO()).FindMany(&models.LessonContentQuery{
			CourseID:       &course.ID,
			IncludeDeleted: util.NewBool(false),
			Type:           util.NewString(string(models.LessonTypeQuiz)),
		}, nil)
		if err != nil {
			return false, e.NewError500(e.Lesson_find_page_failed, err.Error())
		}

		if len(lessonContents) > 0 {
			lessonContentIds := lo.Map(lessonContents, func(item *models.LessonContent, _ int) string {
				return item.ID
			})

			quizTbl := models.GetTblName(models.QuizTbl)
			quizRelationTbl := models.GetTblName(models.QuizRelationTbl)
			query := fmt.Sprintf(`
				SELECT q.uid
				FROM %[1]s qr
					LEFT JOIN %[2]s q ON qr.quiz_id = q.id
				WHERE qr.related_entity_type = @entityType AND qr.related_entity_id IN @entityIDs
				`,
				quizRelationTbl, quizTbl,
			)

			args := map[string]interface{}{
				"entityType": models.QuizRelationEntityLessonContent,
				"entityIDs":  lessonContentIds,
			}

			var quizUIDs []string
			if err := models.DB.Debug().Raw(query, args).Scan(&quizUIDs).Error; err != nil {
				return false, e.NewError500(e.Lesson_find_page_failed, err.Error())
			}

			if len(quizUIDs) > 0 {
				count, err := models.Repository.QuizSubmission.Count(&models.QuizSubmissionQuery{
					UserID:    &data.UserID,
					QuizUIDIn: quizUIDs,
					Status:    util.NewT(models.QuizSubmissionStatusDone),
					Passed:    util.NewBool(true),
				})
				if err != nil {
					return false, e.NewError500(e.Quiz_submission_find_failed, err.Error())
				}

				if count < int64(course.QuizCount) {
					return false, nil
				}
			}
		}
	}

	if course.Props.CertificateCondition.CompletedFinalQuiz {
		// get all quiz lessons content of course sort by create at DESC => final quiz of course
		finalQuiz, err := models.Repository.LessonContent(context.TODO()).FindOne(
			&models.LessonContentQuery{
				Type:           util.NewString(string(models.LessonTypeQuiz)),
				IncludeDeleted: util.NewBool(false),
				CourseID:       util.NewString(course.ID),
			}, &models.FindOneOptions{Sort: []string{models.CreateAtDESC}})
		if err != nil {
			if models.IsRecordNotFound(err) {
				return true, nil
			}
			return false, e.NewError500(e.LessonContentFindFailed, err.Error())
		}

		// finalQuiz := lessonQuizzes[0]
		quizRelation, err := models.Repository.QuizRelation.FindOne(
			&models.QuizRelationQuery{
				RelatedEntityType: util.NewT(models.QuizRelationEntityLessonContent),
				RelationType:      util.NewT(models.QuizRelationType(models.QuizRelationTypeIs)),
				RelatedEntityID:   &finalQuiz.ID}, nil)
		if err != nil {
			return false, e.NewError500(e.Quiz_relation_find_failed, err.Error())
		}

		quiz, err := models.Repository.Quiz.FindOne(&models.QuizQuery{
			ID: util.NewString(quizRelation.QuizID),
		}, nil)
		if err != nil {
			return false, e.NewError500(e.Quiz_relation_find_failed, err.Error())
		}

		quizFinalSubmit, err := models.Repository.QuizSubmission.FindOne(
			&models.QuizSubmissionQuery{
				QuizUID: &quiz.UID,
				UserID:  &data.UserID,
			}, &models.FindOneOptions{
				Preloads: []string{models.AnswersField},
				Sort:     []string{models.CreateAtDESC},
			})
		if err != nil {
			if models.IsRecordNotFound(err) {
				return false, nil
			}
			return false, e.NewError500(e.Quiz_submission_find_failed, err.Error())
		}

		ok := QuizSubmission.CheckFinalQuizCompletePercent(quiz, quizFinalSubmit, course.Props.CertificateCondition.FinalQuizCompletionPercentage)
		if !ok {
			return false, nil
		}
	}

	if course.Props.CertificateCondition.CompletedRequiredLesson {
		requiredLessonUID := course.Props.CertificateCondition.RequiredLessonUID
		if requiredLessonUID != "" {
			ls, err := models.Repository.LearningStatus(context.TODO()).FindOne(&models.LearningStatusQuery{
				CourseCuid: &course.Cuid,
				UserID:     &data.UserID,
			}, nil)
			if err != nil {
				if models.IsRecordNotFound(err) {
					return false, nil
				}
				return false, e.NewError500(e.Find_learning_progress_failed, "Find learning progress of lesson for user failed: "+err.Error())
			}

			completedRequiredLesson := false
			for _, section := range ls.Sections {
				for _, lesson := range section.Lessons {
					if lesson.LessonUID == requiredLessonUID && lesson.CompleteAt > 0 {
						completedRequiredLesson = true
					}
				}
			}
			if !completedRequiredLesson {
				return false, nil
			}
		}
	}
	return true, nil
}

func (s *CertificateService) PushNotificationReceiveCertificate(course *models.Course, user *models.User, org *models.Organization) {
	isReceived := false
	canReceive := false

	if course.HasCertificate && course.MarkAsCompleted {
		cert, appErr := Certificate.FindOneOptions(&models.CertificateQuery{
			CourseCuid: &course.Cuid,
			UserID:     &user.ID,
			OrgID:      &org.ID}, nil)
		if appErr != nil {
			log.Error("Find certificate exist failed", appErr)
			return
		}

		if cert != nil {
			isReceived = true
		} else {
			ok, appErr := Certificate.CheckingCertificateCondition(
				&dto.CheckingCertificateConditionRequest{
					UserID: user.ID,
					Course: course,
					OrgID:  org.ID,
				})
			if appErr != nil {
				log.Error("Checking certificate condition failed", appErr)
				return
			}

			canReceive = ok
		}
	}

	bodyMsg := &communicationdto.WebsocketMessageRequest{
		Event: communicationdto.WebsocketEventCertificate,
		Broadcast: communicationdto.WebsocketBroadcastParams{
			UserID: user.ID,
		},
		Data: map[string]interface{}{
			"can_receive": canReceive,
			"is_received": isReceived,
		},
	}

	if err := communication.Websocket.SendMsgToUserWebSocket(bodyMsg); err != nil {
		log.Error("Send message to user ws error")
		return
	}
}

func (s *CertificateService) validateBeforeMintNFT(req *dto.MintCertificateNFTRequest) (*models.Course, *e.AppError) {
	if req.User.ID != req.Certificate.UserID {
		return nil, e.NewError400(e.CertificateNotAllowMintNFT, "Owner required to mint NFT")
	}

	if req.Certificate.IsMintedNFT() {
		return nil, e.NewError400(e.CertificateAlreadyMintedNFT,
			"Certificate already had NFT token ID "+req.Certificate.NftTokenID+" on network "+
				strings.ToUpper(string(req.Certificate.NftNetwork))+" with transaction hash "+req.Certificate.NftTxHash)
	}

	// Check whether course settings enabled mint NFT
	publishCourse, err := models.Repository.PublishCourse(context.TODO()).FindOne(&models.PublishCourseQuery{
		CourseCuid: &req.Certificate.CourseCuid,
	}, &models.FindOneOptions{})
	if err != nil {
		return nil, e.NewError500(e.Course_find_publish_failed,
			"Find the publish course cuid "+req.Certificate.CourseCuid+"error: "+err.Error())
	}

	course, err := models.Repository.Course(context.TODO()).FindOne(&models.CourseQuery{
		ID: &publishCourse.CourseID,
	}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if err != nil {
		return nil, e.NewError500(e.Course_find_one_failed,
			"Find the course ID "+publishCourse.CourseID+" in schema "+publishCourse.OrgSchema+" error: "+err.Error())
	}

	if !course.IsMintNFTEnabled() {
		return nil, e.NewError400(e.CertificateNotAllowMintNFT,
			"This certificate is not allowed to mint NFT")
	}

	isPaymasterOnBase := req.GasFeePayer == models.Paymaster &&
		course.Props.MintCertNFTSettings.Network == models.BlockchainNetworkBASE

	if req.GasFeePayer != course.Props.MintCertNFTSettings.GasFeePayer &&
		req.GasFeePayer != models.Learner &&
		!isPaymasterOnBase {
		return nil, e.NewError400(e.CertificateWrongGasFeePayer,
			"Wrong gas fee payer: expected '"+string(course.Props.MintCertNFTSettings.GasFeePayer)+
				"' but received '"+string(req.GasFeePayer)+"'")
	}

	return course, nil
}

func (s *CertificateService) MintNFT(req *dto.MintCertificateNFTRequest) (*models.Certificate, *e.AppError) {
	course, appErr := s.validateBeforeMintNFT(req)
	if appErr != nil {
		return nil, appErr
	}

	network := course.CertMintNFTNetwork()

	wallet, err := models.Repository.Wallet.FindOne(&models.WalletQuery{
		UserID:   &req.User.ID,
		Type:     util.NewT(models.AssetTypeCrypto),
		Network:  util.NewT(network),
		Currency: util.NewT(models.CryptoCurrencyNEAR),
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.WalletFindFailed, "Find default crypto wallet to mint NFT error: "+err.Error())
	}

	ownerWallet, err := models.Repository.Wallet.FindOne(&models.WalletQuery{
		UserID:   &course.Owner.ID,
		Type:     util.NewT(models.AssetTypeCrypto),
		Network:  util.NewT(network),
		Currency: util.NewT(models.CryptoCurrencyNEAR),
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.WalletFindFailed, "Find default crypto wallet to mint NFT error: "+err.Error())
	}

	// Mint NFT
	txn := &models.Transaction{
		Model: models.Model{
			ID: util.GenerateId(),
		},
		UserID:     req.Certificate.UserID,
		WalletID:   wallet.ID,
		Network:    wallet.Network,
		Type:       models.TransactionTypeMintNFT,
		Status:     models.TransactionStatusPending,
		ErrorCode:  0,
		TxHash:     "",
		OrgID:      req.Certificate.OrgID,
		Data:       models.JSONB{},
		EntityType: models.CertificateModelName,
		EntityID:   req.Certificate.ID,
	}

	resp, err := openedu_chain.Transaction.MintNFT(&chaindto.MintNFTRequest{
		GasFeePayer:        string(req.GasFeePayer),
		CourseCuid:         course.Cuid,
		CourseOwnerAddress: ownerWallet.Address,
		ReceiverWalletID:   wallet.BlockchainWalletID,
		CoreTxID:           txn.ID,
		IsMainnet:          setting.OpenEduChainSetting.IsMainnet,
		Network:            chaindto.BlockchainNetwork(network),
		TokenMetadata: chaindto.TokenMetadataRequest{
			Title:       models.NftCertificateDefaultTitle,
			Description: models.NftCertificateDefaultDescription,
			MediaURL:    req.Certificate.GetImageURL(),
		},
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return nil, e.NewError400(e.CertificateInsufficientGasToMintNFT,
				"Balance is not enough to cover the gas fee: "+err.Error())

		case errors.Is(err, openedu_chain.ErrInsufficientBalance):
			return nil, e.NewError400(e.CertificateInsufficientBalanceToMintNFT,
				"Balance is not enough to mint NFT: "+err.Error())

		default:
			return nil, e.NewError500(e.CertificateMintNFTFailed, "Mint NFT error: "+err.Error())
		}
	}

	txn.BlockchainTxID = resp.ID
	txn.TxHash = resp.TxHash
	txn.Status = models.TransactionStatus(resp.Status)
	txn.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Create(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionCreateFailed, "Create transaction error: "+err.Error())
	}

	if txn.IsSuccess() {
		req.Certificate.NftTxHash = txn.TxHash
		req.Certificate.NftNetwork = txn.Network
		req.Certificate.NftTokenID = resp.Props.NftTokenID
		req.Certificate.Props.NftMintAt = resp.CreateAt
		req.Certificate.Props.NftStorageCost = resp.Props.StorageCost
		req.Certificate.Props.NftGasCost = resp.Props.GasCost
		req.Certificate.Props.NftTotalGasCost = resp.Props.TotalGasCost
		req.Certificate.Props.GasFeePayer = req.GasFeePayer
		if uErr := models.Repository.Certificate.Update(req.Certificate, nil); uErr != nil {
			return nil, e.NewError500(e.CertificateUpdateFailed, "Update certificate error: "+uErr.Error())
		}
		return req.Certificate, nil
	}

	return req.Certificate, nil
}

func (s *CertificateService) EstimatedMintNFTFees(certificate *models.Certificate) (*dto.CertificateNFTFeesResponse, *e.AppError) {
	publishCourse, err := models.Repository.PublishCourse(context.TODO()).FindOne(&models.PublishCourseQuery{
		CourseCuid: &certificate.CourseCuid,
	}, &models.FindOneOptions{})
	if err != nil {
		return nil, e.NewError500(e.Course_find_publish_failed, "Find the publish course cuid "+certificate.CourseCuid+"error: "+err.Error())
	}

	course, err := models.Repository.Course(context.TODO()).FindOne(&models.CourseQuery{
		ID: &publishCourse.CourseID,
	}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if err != nil {
		return nil, e.NewError500(e.Course_find_one_failed,
			"Find the course ID "+publishCourse.CourseID+" in schema "+publishCourse.OrgSchema+" error: "+err.Error())
	}

	network := course.CertMintNFTNetwork()

	wallet, appErr := Wallet.FindOne(&models.WalletQuery{
		UserID:   &course.Owner.ID,
		Currency: util.NewT(models.CryptoCurrencyNEAR),
		Network:  util.NewT(network),
	}, &models.FindOneOptions{})
	if appErr != nil {
		return nil, appErr
	}

	sponsorBalance, bErr := openedu_chain.Wallet.GetGasSponsorBalance(&chaindto.GetWalletGasSponsorBalanceRequest{
		WalletID:   wallet.BlockchainWalletID,
		CourseCuid: course.Cuid,
		IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
	})
	if bErr != nil {
		return nil, e.NewError500(e.Course_find_one_failed, "Get sponsor sponsorBalance error: "+bErr.Error())
	}

	enabled := false
	gasFeePayer := models.Learner
	if course.Props.MintCertNFTSettings != nil {
		enabled = course.Props.MintCertNFTSettings.Enabled
		gasFeePayer = course.Props.MintCertNFTSettings.GasFeePayer
	}

	resp := dto.CertificateNFTFeesResponse{
		MintNFTEnabled:       enabled,
		GasFeePayerInSetting: gasFeePayer,
		ActualGasFeePayer:    gasFeePayer,
		EstimatedFee:         models.EstimatedNEARsToMintNFT,
		SponsorBalance:       sponsorBalance,
		Network:              network,
	}

	// For BASE network, allow paymaster as a gas fee payer option
	// For other networks, check creator balance
	if resp.ActualGasFeePayer == models.Creator {
		resp.SponsorBalance = sponsorBalance
		if sponsorBalance.LessThan(models.EstimatedNEARsToMintNFT) {
			resp.ActualGasFeePayer = models.Learner
		}
	}
	return &resp, nil
}
