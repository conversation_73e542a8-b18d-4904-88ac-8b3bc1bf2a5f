package services

import (
	"openedu-core/models"
	"openedu-core/pkg/e"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/upload"
	"openedu-core/pkg/util"
)

func (s *UploadService) UploadFiles(files []*upload.File, userID string, pathPrefix string) ([]*models.File, *e.AppError) {
	var privateVideoFiles []*upload.File
	var publicVideoFiles []*upload.File
	var nonVideoFiles []*upload.File

	for _, file := range files {
		if file.IsVideo() {
			if file.Public {
				publicVideoFiles = append(publicVideoFiles, file)
			} else {
				privateVideoFiles = append(privateVideoFiles, file)
			}
		} else {
			nonVideoFiles = append(nonVideoFiles, file)
		}
	}

	// check if no content is video or other file
	var fileInfos []*upload.UploadedFileInfo
	if len(privateVideoFiles) > 0 {
		videoFileInfos, err := upload.PrivateBunnyProvider.UploadFiles(privateVideoFiles, pathPrefix)
		if err != nil {
			return nil, e.NewError500(e.Error_file_upload_fail, err.Error())
		}
		fileInfos = append(fileInfos, videoFileInfos...)
	}

	if len(publicVideoFiles) > 0 {
		videoFileInfos, err := upload.PublicBunnyProvider.UploadFiles(publicVideoFiles, pathPrefix)
		if err != nil {
			return nil, e.NewError500(e.Error_file_upload_fail, err.Error())
		}
		fileInfos = append(fileInfos, videoFileInfos...)
	}

	if len(nonVideoFiles) > 0 {
		nonVideoFileInfos, err := upload.DefaultProvider.UploadFiles(nonVideoFiles, pathPrefix)
		if err != nil {
			return nil, e.NewError500(e.Error_file_upload_fail, err.Error())
		}
		fileInfos = append(fileInfos, nonVideoFileInfos...)
	}

	var fileModels []*models.File
	for _, fileInfo := range fileInfos {
		fileModels = append(fileModels, &models.File{
			Name:           fileInfo.Name,
			Mime:           fileInfo.Mime,
			Ext:            fileInfo.Ext,
			Width:          fileInfo.Width,
			Height:         fileInfo.Height,
			Size:           fileInfo.Size,
			URL:            fileInfo.URL,
			ThumbnailURL:   fileInfo.ThumbnailURL,
			BunnyVideoID:   fileInfo.BunnyVideoID,
			BunnyLibraryID: fileInfo.BunnyLibraryID,
			Duration:       fileInfo.Duration,
			UserID:         userID,
		})
	}

	if len(fileInfos) <= 0 {
		return []*models.File{}, nil
	}

	if err := models.Repository.File.CreateMany(fileModels, nil); err != nil {
		return nil, e.NewError500(e.Error_create_file_failed, err.Error())
	}

	return fileModels, nil
}

func (s *UploadService) DeleteFiles(files []*models.File) *e.AppError {
	// uploadedFileInfos := make([]*upload.UploadedFileInfo, len(files))
	var uploadedFileInfos []*upload.UploadedFileInfo
	var privateBunnyFileInfos []*upload.UploadedFileInfo
	var publicBunnyFileInfos []*upload.UploadedFileInfo

	var fileIDs []string
	for _, file := range files {
		if file.IsBunnyVideo() {
			if file.BunnyLibraryID == setting.UploadSetting.UploadPrivateBunnyLibraryID {
				privateBunnyFileInfos = append(privateBunnyFileInfos, &upload.UploadedFileInfo{Name: file.Name})
			} else {
				publicBunnyFileInfos = append(publicBunnyFileInfos, &upload.UploadedFileInfo{Name: file.Name})
			}
		} else {
			uploadedFileInfos = append(uploadedFileInfos, &upload.UploadedFileInfo{Name: file.Name})
		}
		fileIDs = append(fileIDs, file.ID)
	}

	_, err := models.Repository.File.DeleteMany(&models.FileQuery{
		IDIn: fileIDs,
	}, nil)
	if err != nil {
		return e.NewError500(e.Error_file_delete_fail, err.Error())
	}

	// Remove file from providers
	if len(uploadedFileInfos) > 0 {
		if err = upload.DefaultProvider.RemoveFiles(uploadedFileInfos, ""); err != nil {
			return e.NewError500(e.Error_remove_file_failed, err.Error())
		}
	}

	if len(privateBunnyFileInfos) > 0 {
		if err = upload.PrivateBunnyProvider.RemoveFiles(privateBunnyFileInfos, ""); err != nil {
			return e.NewError500(e.Error_remove_file_failed, err.Error())
		}
	}

	if len(publicBunnyFileInfos) > 0 {
		if err = upload.PublicBunnyProvider.RemoveFiles(publicBunnyFileInfos, ""); err != nil {
			return e.NewError500(e.Error_remove_file_failed, err.Error())
		}
	}
	return nil
}

func (s *UploadService) GetStatusUploadFromBunny(file *models.File) (*string, *e.AppError) {
	var provider *upload.BunnyUploadProvider
	if file.IsBunnyVideo() {
		if file.BunnyLibraryID == setting.UploadSetting.UploadPrivateBunnyLibraryID {
			provider = upload.GetPrivateBunnyProvider()
		} else {
			provider = upload.GetPublicBunnyProvider()
		}
	} else {
		return nil, e.NewError400(e.Error_file_is_not_bunny_video, "file is not video upload from bunny")
	}

	videoUploaded, err := provider.GetVideoById(&upload.GetVideoRequest{
		VideoID: file.BunnyVideoID,
	})
	if err != nil {
		return nil, e.NewError500(e.Error_get_file_info_failed, err.Error())
	}

	status := util.BunnyVideoStatus(videoUploaded.Status).StatusString()

	return &status, nil
}

func (s *UploadService) GetFileData(file *models.File) ([]byte, *e.AppError) {
	if file.IsFromBunny() {
		return nil, e.NewError500(e.Error_file_cannot_get_data, "Bunny does not support get filed data")
	}

	uploadedFileInfo := convertFileToUploadedFileInfo(file)
	fileData, err := upload.DefaultProvider.GetFileData(uploadedFileInfo)
	if err != nil {
		return nil, e.NewError500(e.Error_file_cannot_get_data, "Get file data error: "+err.Error())
	}

	return fileData, nil
}

func convertFileToUploadedFileInfo(file *models.File) *upload.UploadedFileInfo {
	return &upload.UploadedFileInfo{
		Name:           file.Name,
		Mime:           file.Mime,
		Ext:            file.Ext,
		URL:            file.URL,
		ThumbnailURL:   file.ThumbnailURL,
		Width:          file.Width,
		Size:           file.Size,
		BunnyVideoID:   file.BunnyVideoID,
		BunnyLibraryID: file.BunnyLibraryID,
		Duration:       file.Duration,
	}
}

func (s *UploadService) DownLoadAndUploadFile(url string, userID string) (*models.File, *e.AppError) {
	if url == "" {
		return nil, e.NewError400(e.INVALID_PARAMS, "invalid url")
	}

	httpClient := httpclient.NewClient(url, nil)

	resp, body, err := httpClient.Get("", map[string]string{}, map[string]interface{}{})
	if err != nil {
		log.Debugf("Call Get Generate Course From Youtube Failed:  %v", err.Error())
		return nil, e.NewError500(e.Error_get_file_info_failed, err.Error())
	}
	defer resp.Body.Close()

	uploadFile := &upload.File{
		Name:    util.GetFileNameFromURL(url),
		Mime:    resp.Header.Get("Content-Type"),
		Content: body,
		Public:  true,
	}

	files, aErr := s.UploadFiles([]*upload.File{uploadFile}, userID, "")
	if aErr != nil {
		return nil, aErr
	}

	if len(files) > 0 {
		return files[0], nil
	}

	return nil, e.NewError500(e.Error_file_not_found, e.GetMsg(e.Error_file_not_found))
}
