package services

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/excel"
	chaindto "openedu-core/pkg/openedu_chain/dto"

	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/cenkalti/backoff/v4"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
)

func (s *TransactionService) Create(transaction *models.Transaction) (*models.Transaction, *e.AppError) {
	if err := models.Repository.Transaction.Create(transaction, nil); err != nil {
		return nil, e.NewError500(e.TransactionCreateFailed, "create transaction error: "+err.Error())
	} else {
		return transaction, nil
	}
}

func (s *TransactionService) FindPage(query *models.TransactionQuery, options *models.FindPageOptions) ([]*models.Transaction, *models.Pagination, *e.AppError) {
	if transactions, pagination, err := models.Repository.Transaction.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.TransactionFindFailed, err.Error())
	} else {
		return transactions, pagination, nil
	}
}

func (s *TransactionService) OrderSuccess(request *dto.CreateTransactionRequest, paymentMethod *models.PaymentMethod) *e.AppError {
	transaction := models.Transaction{
		UserID:         request.Wallet.UserID,
		OrgID:          request.OrgID,
		WalletID:       request.Wallet.ID,
		CurrencyType:   request.Wallet.Type,
		Currency:       request.Wallet.Currency,
		ToAddress:      request.ToAddress,
		Network:        request.Network,
		TxHash:         request.TxHash,
		Amount:         request.Amount,
		Type:           models.TransactionTypeSale,
		Status:         models.TransactionStatusSuccess,
		Data:           models.JSONB{},
		EntityType:     request.EntityType,
		EntityID:       request.EntityID,
		Note:           request.Note,
		BlockchainTxID: request.BlockchainTxID,
	}

	if paymentMethod.Type == models.PaymentMethodTypeOpenEduWallet &&
		paymentMethod.Service == models.PaymentServiceCrypto {

		if cErr := models.Cache.WalletEarning.DeleteByUserID(request.Wallet.UserID); cErr != nil {
			log.Errorf("transactionService::OrderSuccess Delete wallet earning cache by user ID %s error: %v", request.Wallet.UserID, cErr)
		}

		//transaction.Type = models.TransactionTypeSaleEarn
		transaction.Type = models.TransactionTypeSale
		if err := models.Repository.Transaction.Create(&transaction, nil); err != nil {
			return e.NewError500(e.TransactionCreateFailed, "OrderSuccess:: create transaction error: "+err.Error())
		}
		return nil
	}

	if err := models.Repository.Transaction.CreateAndUpdateBalance(&transaction, true); err != nil {
		log.Error(fmt.Sprintf("ERROR::ReferralEarn: wallet %s, amount: %s, orgID: %s, err: ",
			request.Wallet.ID, request.Amount.String(), request.OrgID), err.Error())
		return e.NewError500(e.WalletUpdateBalanceFailed, "OrderSuccess:: update user balance failed: "+err.Error())
	}
	return nil
}

func (s *TransactionService) ReferralEarn(request *dto.CreateTransactionRequest, paymentMethod *models.PaymentMethod) *e.AppError {
	transaction := models.Transaction{
		UserID:         request.Wallet.UserID,
		OrgID:          request.OrgID,
		WalletID:       request.Wallet.ID,
		CurrencyType:   request.Wallet.Type,
		Currency:       request.Wallet.Currency,
		ToAddress:      request.ToAddress,
		Network:        request.Network,
		TxHash:         request.TxHash,
		Amount:         request.Amount,
		Type:           models.TransactionTypeReferral,
		Status:         models.TransactionStatusSuccess,
		Data:           models.JSONB{},
		EntityType:     request.EntityType,
		EntityID:       request.EntityID,
		Note:           request.Note,
		BlockchainTxID: request.BlockchainTxID,
	}

	if paymentMethod.Type == models.PaymentMethodTypeOpenEduWallet &&
		paymentMethod.Service == models.PaymentServiceCrypto {

		if cErr := models.Cache.WalletEarning.DeleteByUserID(request.Wallet.UserID); cErr != nil {
			log.Errorf("transactionService::ReferralEarn Delete wallet earning cache by user ID %s error: %v", request.Wallet.UserID, cErr)
		}

		if err := models.Repository.Transaction.Create(&transaction, nil); err != nil {
			return e.NewError500(e.TransactionCreateFailed, "OrderSuccess:: create transaction error: "+err.Error())
		}
		return nil
	}

	if err := models.Repository.Transaction.CreateAndUpdateBalance(&transaction, true); err != nil {
		log.Error(fmt.Sprintf("ERROR::ReferralEarn: wallet %s, amount: %s, orgID: %s, err: ",
			request.Wallet.ID, request.Amount.String(), request.OrgID), err.Error())
		return e.NewError500(e.WalletUpdateBalanceFailed, "ReferralEarn:: update user balance failed: "+err.Error())
	}
	return nil
}

func (s *TransactionService) LaunchpadProfitEarn(request *dto.CreateTransactionRequest, paymentMethod *models.PaymentMethod) *e.AppError {
	transaction := models.Transaction{
		UserID:         request.Wallet.UserID,
		OrgID:          request.OrgID,
		WalletID:       request.Wallet.ID,
		CurrencyType:   request.Wallet.Type,
		Currency:       request.Wallet.Currency,
		ToAddress:      request.ToAddress,
		Network:        request.Network,
		TxHash:         request.TxHash,
		Amount:         request.Amount,
		Type:           models.TransactionTypeLaunchpadProfitEarn,
		Status:         models.TransactionStatusSuccess,
		Data:           models.JSONB{},
		EntityType:     request.EntityType,
		EntityID:       request.EntityID,
		Note:           request.Note,
		BlockchainTxID: request.BlockchainTxID,
	}

	if paymentMethod.Type == models.PaymentMethodTypeOpenEduWallet &&
		paymentMethod.Service == models.PaymentServiceCrypto {

		if cErr := models.Cache.WalletEarning.DeleteByUserID(request.Wallet.UserID); cErr != nil {
			log.Errorf("transactionService::LaunchpadProfitEarn Delete wallet earning cache by user ID %s error: %v", request.Wallet.UserID, cErr)
		}

		if err := models.Repository.Transaction.Create(&transaction, nil); err != nil {
			return e.NewError500(e.TransactionCreateFailed, "OrderSuccess:: create transaction error: "+err.Error())
		}
		return nil
	}

	if err := models.Repository.Transaction.CreateAndUpdateBalance(&transaction, true); err != nil {
		log.Error(fmt.Sprintf("ERROR::LaunchpadProfitEarn: wallet %s, amount: %s, orgID: %s, err: ",
			request.Wallet.ID, request.Amount.String(), request.OrgID), err.Error())
		return e.NewError500(e.WalletUpdateBalanceFailed, "LaunchpadProfitEarn:: update user balance failed: "+err.Error())
	}
	return nil
}

func (s *TransactionService) ReceivePoint(request *dto.CreateTransactionRequest) *e.AppError {
	transaction := models.Transaction{
		UserID:       request.Wallet.UserID,
		OrgID:        request.OrgID,
		WalletID:     request.Wallet.ID,
		CurrencyType: request.Wallet.Type,
		Currency:     models.PointCurrencyP,
		Amount:       request.Amount,
		Type:         models.TransactionTypeClaim,
		Status:       models.TransactionStatusSuccess,
		Data:         models.JSONB{},
		EntityType:   request.EntityType,
		EntityID:     request.EntityID,
		Note:         request.Note,
	}
	if err := models.Repository.Transaction.CreateAndUpdateBalance(&transaction, false); err != nil {
		log.Error(fmt.Sprintf("ERROR::ReceivePoint: wallet %s, amount: %s, orgID: %s, err: ",
			request.Wallet.ID, request.Amount.String(), request.OrgID), err.Error())
		return e.NewError500(e.WalletUpdateBalanceFailed, "ReceivePoint:: update user balance failed: "+err.Error())
	}
	return nil
}

// TODO: COMPLETE IT
func (s *TransactionService) UsePoint(wallet *models.Wallet, amount decimal.Decimal, orgID string) *e.AppError {
	transaction := models.Transaction{
		UserID:       wallet.UserID,
		OrgID:        orgID,
		WalletID:     wallet.ID,
		CurrencyType: wallet.Type,
		Currency:     models.PointCurrencyP,
		Amount:       amount,
		Type:         models.TransactionTypeUsePoint,
		Status:       models.TransactionStatusSuccess,
		Data:         models.JSONB{},
	}
	if err := models.Repository.Transaction.CreateAndUpdateBalance(&transaction, true); err != nil {
		log.Error(fmt.Sprintf("ERROR::UsePoint: wallet %s, amount: %s, orgID: %s, err: ", wallet.ID, amount.String(), orgID), err.Error())
		return e.NewError500(e.WalletUpdateBalanceFailed, "UsePoint:: update user balance failed: "+err.Error())
	}
	return nil
}

func (s *TransactionService) Withdraw(request *dto.CreateTransactionRequest) (*models.Transaction, *e.AppError) {
	transaction := &models.Transaction{
		UserID:       request.Wallet.UserID,
		OrgID:        request.OrgID,
		WalletID:     request.Wallet.ID,
		CurrencyType: request.Wallet.Type,
		Currency:     lo.If(request.Wallet.Type == models.AssetTypeFiat, request.Wallet.Currency).Else(request.Currency),
		ToAddress:    request.ToAddress,
		Network:      lo.If(request.Network != "", request.Network).Else(request.Wallet.Network),
		Amount:       request.Amount.Neg(),
		Type:         models.TransactionTypeWithdraw,
		Status:       models.TransactionStatusSuccess,
		Data:         models.JSONB{},
		EntityType:   request.EntityType,
		EntityID:     request.EntityID,
		Note:         request.Note,
		Files:        request.Files,
	}

	if request.Wallet.IsCrypto() {
		if err := models.Repository.Transaction.Create(transaction, nil); err != nil {
			return nil, e.NewError500(e.TransactionCreateFailed, "Create transaction failed: "+err.Error())
		}

		resp, err := openedu_chain.Transaction.Transfer(&chaindto.SingleTransferRequest{
			SenderWalletID: request.Wallet.BlockchainWalletID,
			CoreTxID:       transaction.ID,
			Amount:         request.Amount,
			Token:          chaindto.BlockchainToken(request.Currency), // TODO
			ToAddress:      request.ToAddress,
			Network:        chaindto.BlockchainNetwork(request.Network), // TODO
			ContractID:     request.ContractID,
			IsMainnet:      request.IsMainnet,
		})
		if err != nil {
			return nil, s.handleCryptoWithdrawError(transaction, err)
		}

		transaction.ToAddress = resp.ToAddress
		transaction.BlockchainTxID = resp.ID
		transaction.TxHash = resp.TxHash
		transaction.Status = models.TransactionStatus(resp.Status)
		transaction.ErrorCode = resp.ErrorCode
		if err = models.Repository.Transaction.Update(transaction, nil); err != nil {
			return nil, e.NewError500(e.TransactionUpdateFailed, "Update transaction by ID failed: "+err.Error())
		}

	} else {
		// Do not subtract available balance, it already subtracted when creating withdraw request
		if err := models.Repository.Transaction.CreateAndUpdateBalance(transaction, false); err != nil {
			log.Error(fmt.Sprintf("ERROR::Withdraw: wallet %s, amount: %s, orgID: %s, err: ",
				request.Wallet.ID, request.Amount.String(), request.OrgID), err.Error())
			return nil, e.NewError500(e.WalletUpdateBalanceFailed, "Withdraw:: update user balance failed: "+err.Error())
		}
	}
	return transaction, nil
}

func (s *TransactionService) handleCryptoWithdrawError(txn *models.Transaction, err error) *e.AppError {
	switch {
	case errors.Is(err, openedu_chain.ErrInvalidAddress):
		if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
			log.Errorf("TransactionService::Withdraw Delete transaction failed: %v", dErr)
		}
		return e.NewError400(e.WalletWithdrawInvalidAddress,
			"Invalid address to withdraw: "+err.Error())

	case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
		if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
			log.Errorf("TransactionService::Withdraw Delete transaction failed: %v", dErr)
		}
		return e.NewError400(e.WalletInsufficientGasFee,
			"Balance is not enough to cover the gas fee: "+err.Error())

	case errors.Is(err, openedu_chain.ErrInsufficientBalance):
		if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
			log.Errorf("TransactionService::Withdraw Delete transaction failed: %v", dErr)
		}
		return e.NewError400(e.WalletBalanceNotEnough,
			"Balance is not enough to withdraw: "+err.Error())

	default:
		txn.Status = models.TransactionStatusFailed
		if dErr := models.Repository.Transaction.Update(txn, nil); dErr != nil {
			log.Errorf("TransactionService::Withdraw Update transaction failed: %v", dErr)
		}
		return e.NewError500(e.WalletWithdrawFailed, "Withdraw error: "+err.Error())
	}
}

func (s *TransactionService) handleMintNFTSuccess(transaction *models.Transaction) *e.AppError {
	// Update certificate's NFT info
	certificate, appErr := Certificate.FindByID(transaction.EntityID)
	if appErr != nil {
		return e.NewError500(e.CertificateFindFailed, "Find certificate by ID failed: "+appErr.Error())
	}

	certificate.NftTxHash = transaction.TxHash
	certificate.NftNetwork = transaction.Network
	if err := models.Repository.Certificate.Update(certificate, nil); err != nil {
		return e.NewError500(e.CertificateUpdateFailed, "Update certificate's NFT info failed: "+err.Error())
	}

	// Send notification to user
	go func(tx *models.Transaction, cert *models.Certificate) {

		user, err := models.Repository.User.FindByID(tx.UserID)
		if err != nil {
			log.Errorf("Push notification to user ID %s for new NFT certificate minted error: %v", tx.UserID, err)
			return
		}

		notificationReq := &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeNewNFTCertificateMinted,
			EntityID:   cert.ID,
			EntityType: communicationdto.CertificateEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{tx.UserID},
				},
			},
			Props: communicationdto.JSONB{
				"user_id":      user.ID,
				"username":     user.Username,
				"display_name": user.DisplayName,
				"course_name":  cert.CourseName,
				"org_id":       cert.OrgID,
				"tx_hash":      tx.TxHash,
				"network":      tx.Network,
			},
		}
		if err = communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to user ID %s for new NFT certificate mint error: %v", user.ID, err)
		}
	}(transaction, certificate)

	return nil
}

func (s *TransactionService) Sync(req *dto.TransactionSyncRequest) ([]*models.Transaction, *e.AppError) {
	var transactions []*models.Transaction
	for _, coreTxID := range req.CoreTxIDs {
		transaction, err := models.Repository.Transaction.FindByID(coreTxID, nil)
		if err != nil {
			return nil, e.NewError500(e.TransactionFindFailed, "Find transaction by ID failed: "+err.Error())
		}

		if req.Status == models.TransactionStatusPending &&
			(transaction.Status == models.TransactionStatusSuccess || transaction.Status == models.TransactionStatusFailed) {
			log.Infof("Skip sync transaction ID %s due to it already synced before", transaction.ID)
			continue
		}

		if req.DecimalAmount != nil {
			transaction.Amount = *req.DecimalAmount
		}

		transaction.BlockchainTxID = req.BlockchainTxID
		transaction.TxHash = req.TxHash
		transaction.Status = req.Status
		transaction.ErrorCode = req.ErrorCode
		if err = models.Repository.Transaction.Update(transaction, nil); err != nil {
			return nil, e.NewError500(e.TransactionUpdateFailed, "Update transaction by ID failed: "+err.Error())
		}

		switch transaction.Type {
		case models.TransactionTypeMintNFT:
			if transaction.IsSuccess() {
				if hErr := s.handleMintNFTSuccess(transaction); hErr != nil {
					log.Errorf("Handle mint nft success after syncing transaction error: %v", hErr)
				}
			}

		}

		transactions = append(transactions, transaction)
	}

	return transactions, nil
}

func (s *TransactionService) ClaimEarning(req *dto.ClaimEarningsRequest) (*models.Transaction, *e.AppError) {
	if !req.Wallet.IsOwnedBy(req.User) {
		return nil, e.NewError400(e.WalletOwnerRequired, "Wallet owner required")
	}

	if !req.Wallet.IsCrypto() {
		return nil, e.NewError400(e.WalletNotAllowClaimEarnings, "Only crypto wallet can claim earnings")
	}

	claimedTx := &models.Transaction{
		UserID:       req.User.ID,
		OrgID:        req.Org.ID,
		WalletID:     req.Wallet.ID,
		CurrencyType: req.Wallet.Type,
		Currency:     req.Wallet.Currency,
		Network:      req.Wallet.Network,
		Amount:       decimal.Zero, // Will be updated when syncing transaction from OpenEdu Chain
		Type:         models.TransactionTypeClaimEarning,
		Status:       models.TransactionStatusPending,
		Data:         models.JSONB{},
		EntityID:     req.Wallet.ID,
		EntityType:   models.WalletModelName,
	}
	if err := models.Repository.Transaction.Create(claimedTx, nil); err != nil {
		return nil, e.NewError500(e.TransactionCreateFailed, "Create transaction error: "+err.Error())
	}

	var token chaindto.BlockchainToken
	switch req.Wallet.Currency {
	case models.CryptoCurrencyUSDT:
		token = chaindto.BlockchainTokenUSDT

	case models.CryptoCurrencyUSDC:
		token = chaindto.BlockchainTokenUSDC

	default:
		return nil, e.NewError400(e.WalletUnknownCurrency, "Unknown currency: "+string(req.Wallet.Currency))
	}

	if err := openedu_chain.Transaction.Create(&chaindto.CreateTransactionRequest{
		Type: chaindto.TxnClaimEarning,
		Data: chaindto.ClaimEarningRequest{
			WalletID:  req.Wallet.BlockchainWalletID,
			CoreTxID:  claimedTx.ID,
			Token:     token,
			IsMainnet: setting.OpenEduChainSetting.IsMainnet,
		},
	}); err != nil {
		return nil, e.NewError500(e.WalletClaimEarningsFailed, "Claim earnings into the wallet ID "+req.Wallet.ID+" error: "+err.Error())
	}

	expBackoff := util.NewDefaultExpBackoff()
	syncedTx, _ := backoff.RetryWithData(func() (*models.Transaction, error) {
		tx, tErr := models.Repository.Transaction.FindByID(claimedTx.ID, nil)
		if tErr != nil {
			return nil, tErr
		}

		if !tx.IsPending() {
			return tx, nil
		}

		return nil, fmt.Errorf("transaction is still pending, continue check status")
	}, expBackoff)

	if cErr := models.Cache.WalletEarning.DeleteByUserID(req.User.ID); cErr != nil {
		log.Errorf("transactionService::OrderSuccess Delete wallet earning cache by user ID %s error: %v", req.User.ID, cErr)
	}

	if syncedTx.IsSuccess() {
		return syncedTx, nil
	}

	if dErr := models.Repository.Transaction.Delete(syncedTx.ID, nil); dErr != nil {
		log.Errorf("TransactionService::ClaimEarning Delete transaction to rollback failed: %v", dErr)
	}

	switch syncedTx.ErrorCode {
	case openedu_chain.TransactionInsufficientGasFee:
		return nil, e.NewError400(e.WalletInsufficientGasFee, "Insufficient wallet balance to cover the gas fee")

	case openedu_chain.TransactionInsufficientEarningToClaim:
		return nil, e.NewError400(e.WalletInsufficientEarningToClaim, "Insufficient wallet earning for claiming")

	default:
		return nil, e.NewError500(e.WalletClaimEarningsFailed, fmt.Sprintf("Claim earnings into the wallet failed with error code: %d", syncedTx.ErrorCode))
	}
}

func (s *TransactionService) RetroactiveForAvail(wallet *models.Wallet, org *models.Organization, fileContent []byte) (*models.Transaction, *e.AppError) {
	f, err := excelize.OpenReader(bytes.NewReader(fileContent))
	if err != nil {
		return nil, e.NewError500(e.WalletRetroactiveFailed, "Open retroactive file reader error: "+err.Error())
	}
	defer f.Close()

	distributions, err := excel.ParseExcel[dto.RetroactiveDistribution](f, models.AvailAirDropDistributionSheetName)
	if err != nil {
		return nil, e.NewError500(e.WalletRetroactiveFailed, "Parse retroactive excel rows error: "+err.Error())
	}

	totalAmount := decimal.Zero
	for _, distribution := range distributions {
		totalAmount = totalAmount.Add(distribution.Amount)
	}

	log.Infof("Distribute retroactive distributions: %+v", distributions)

	transaction := &models.Transaction{
		Model:        models.Model{ID: util.GenerateId()},
		UserID:       wallet.UserID,
		OrgID:        org.ID,
		WalletID:     wallet.ID,
		CurrencyType: wallet.Type,
		Currency:     wallet.Currency,
		Amount:       totalAmount.Neg(),
		Type:         models.TransactionTypeRetroactive,
		Status:       models.TransactionStatusPending,
		Data: models.JSONB{
			"distributions": distributions,
		},
		EntityType: models.WalletModelName,
		EntityID:   wallet.ID,
	}
	if err = models.Repository.Transaction.Create(transaction, nil); err != nil {
		return nil, e.NewError500(e.WalletRetroactiveFailed, "Create retroactive transaction error: "+err.Error())
	}

	resp, err := openedu_chain.Transaction.BatchTransfer(&chaindto.BatchTransferRequest{
		SenderWalletID: wallet.BlockchainWalletID,
		CoreTxID:       transaction.ID,
		Recipients: lo.Map(distributions, func(item *dto.RetroactiveDistribution, index int) *chaindto.BatchTransferRecipient {
			return &chaindto.BatchTransferRecipient{
				Address: item.Address,
				Amount:  item.Amount,
			}
		}),
		Token:     chaindto.BlockchainToken(wallet.Currency),
		Network:   chaindto.BlockchainNetwork(wallet.Network),
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		return nil, s.handleRetroactiveError(transaction, err)
	}

	transaction.ToAddress = resp.ToAddress
	transaction.BlockchainTxID = resp.ID
	transaction.TxHash = resp.TxHash
	transaction.Status = models.TransactionStatus(resp.Status)
	transaction.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Update(transaction, nil); err != nil {
		return nil, e.NewError500(e.TransactionUpdateFailed, "Update transaction by ID failed: "+err.Error())
	}

	if transaction.IsSuccess() {
		go s.pushNotificationsAfterRetroactive(distributions)
	}
	return transaction, nil
}

func (s *TransactionService) handleRetroactiveError(txn *models.Transaction, err error) *e.AppError {
	switch {
	case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
		if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
			log.Errorf("TransactionService::Withdraw Delete transaction failed: %v", dErr)
		}
		return e.NewError400(e.WalletInsufficientGasFee,
			"Balance is not enough to cover the gas fee: "+err.Error())

	case errors.Is(err, openedu_chain.ErrInsufficientBalance):
		if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
			log.Errorf("TransactionService::Withdraw Delete transaction failed: %v", dErr)
		}
		return e.NewError400(e.WalletBalanceNotEnough,
			"Balance is not enough to withdraw: "+err.Error())

	default:
		return e.NewError500(e.WalletRetroactiveFailed, "Retroactive error: "+err.Error())
	}
}

func (s *TransactionService) pushNotificationsAfterRetroactive(distributions []*dto.RetroactiveDistribution) {
	for _, distribution := range distributions {
		wallet, err := models.Repository.Wallet.FindOne(&models.WalletQuery{
			Address: &distribution.Address,
			Network: util.NewT(models.BlockchainNetworkAVAIL),
		}, &models.FindOneOptions{})
		if err != nil {
			log.Errorf("Push notification to user ID %s after Avail retroactive failed: find the wallet with address %s on network %s failed: %v",
				distribution.UserID, distribution.Address, distribution.Network, err)
			continue
		}

		notificationReq := &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeNewRetroactiveReceived,
			EntityID:   wallet.ID,
			EntityType: communicationdto.WalletEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{distribution.UserID},
				},
			},
			Props: communicationdto.JSONB{
				"amount":   distribution.Amount.String(),
				"currency": models.CryptoCurrencyAVAIL,
			},
		}
		if err = communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to user ID %s after Avail retroactive failed: push notification failed: %v",
				distribution.UserID, err)
		}
	}
}

func (s *TransactionService) InitLaunchpadPool(req *dto.InitLaunchpadPoolRequest) (*models.Transaction, *e.AppError) {
	if aErr := s.validateBeforeInitPool(req); aErr != nil {
		return nil, aErr
	}

	txn := &models.Transaction{
		UserID:       req.User.ID,
		WalletID:     req.Wallet.ID,
		Network:      req.Wallet.Network,
		CurrencyType: req.Wallet.Type,
		Currency:     req.Wallet.Currency,
		Type:         models.TransactionTypeInitLaunchpadPool,
		Status:       models.TransactionStatusPending,
		ErrorCode:    0,
		OrgID:        req.Org.ID,
		Data:         models.JSONB{},
		EntityType:   models.ClpLaunchpadModelName,
		EntityID:     req.Launchpad.ID,
	}
	if err := models.Repository.Transaction.Create(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionCreateFailed, "Create transaction error: "+err.Error())
	}

	resp, err := openedu_chain.Transaction.InitLaunchpadPool(&chaindto.InitLaunchpadPoolRequest{
		WalletID:         req.Wallet.BlockchainWalletID,
		CoreTxID:         txn.ID,
		LaunchpadID:      req.Launchpad.ID,
		Token:            chaindto.BlockchainToken(req.Launchpad.FundingGoal.Currency),
		MinPledge:        req.Launchpad.FundingGoal.MinPledge,
		FundingStartDate: req.Launchpad.FundingStartDate,
		FundingEndDate:   req.Launchpad.FundingEndDate,
		TargetFunding:    req.Launchpad.FundingGoal.TargetFunding,
		IsMainnet:        setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
				log.Errorf("ClpLaunchpadService::InitPool Delete transaction failed: ")
			}
			return nil, e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		case errors.Is(err, openedu_chain.ErrInsufficientBalance):
			if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
				log.Errorf("ClpLaunchpadService::InitPool Delete transaction failed: ")
			}
			return nil, e.NewError400(e.WalletBalanceNotEnough,
				"Balance is not enough to init launchpad pool: "+err.Error())

		default:
			return nil, e.NewError500(e.Launchpad_init_pool_failed, "Init launchpad pool error: "+err.Error())
		}
	}

	txn.ToAddress = resp.ToAddress
	txn.Amount = resp.Deposit.Neg()
	txn.BlockchainTxID = resp.ID
	txn.TxHash = resp.TxHash
	txn.Status = models.TransactionStatus(resp.Status)
	txn.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Update(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionUpdateFailed, "Update transaction error: "+err.Error())
	}

	req.Launchpad.Props.PoolID = resp.Props.PoolID
	req.Launchpad.Props.WalletID = req.Wallet.ID
	if err = models.Repository.ClpLaunchpad(context.TODO()).Update(req.Launchpad, nil); err != nil {
		return nil, e.NewError500(e.Launchpad_init_pool_failed, "Update launchpad error: "+err.Error())
	}

	return txn, nil
}

func (s *TransactionService) validateBeforeInitPool(req *dto.InitLaunchpadPoolRequest) *e.AppError {
	if req.User.ID != req.Wallet.UserID {
		return e.NewError400(e.WalletOwnerRequired, "Wallet owner required")
	}

	if req.Launchpad.HasPool() {
		return e.NewError400(e.Launchpad_already_had_pool,
			fmt.Sprintf("Launchpad ID %s already had the pool ID %s", req.Launchpad.ID, req.Launchpad.Props.PoolID))
	}
	return nil
}

func (s *TransactionService) Pledge(req *dto.CreateInvestmentRequest) (*models.Transaction, *e.AppError) {
	txn := &models.Transaction{
		UserID:       req.User.ID,
		WalletID:     req.Wallet.ID,
		Network:      req.Wallet.Network,
		CurrencyType: req.Wallet.Type,
		Currency:     req.Wallet.Currency,
		Amount:       req.Amount.Neg(),
		Type:         models.TransactionTypePledgeLaunchpad,
		Status:       models.TransactionStatusPending,
		ErrorCode:    0,
		OrgID:        req.Org.ID,
		Data:         models.JSONB{},
		EntityType:   models.ClpLaunchpadModelName,
		EntityID:     req.Launchpad.ID,
	}
	if err := models.Repository.Transaction.Create(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionCreateFailed, "Create transaction error: "+err.Error())
	}

	resp, err := openedu_chain.Transaction.PledgeLaunchpad(&chaindto.PledgeLaunchpadRequest{
		WalletID:  req.Wallet.BlockchainWalletID,
		CoreTxID:  txn.ID,
		Token:     chaindto.BlockchainToken(req.Launchpad.FundingGoal.Currency),
		PoolID:    req.Launchpad.Props.PoolID,
		Amount:    req.Amount,
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
				log.Errorf("ClpLaunchpadService::InitPool Delete transaction failed: ")
			}
			return nil, e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		case errors.Is(err, openedu_chain.ErrInsufficientBalance):
			if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
				log.Errorf("ClpLaunchpadService::InitPool Delete transaction failed: ")
			}
			return nil, e.NewError400(e.WalletBalanceNotEnough,
				"Balance is not enough to init launchpad pool: "+err.Error())

		default:
			return nil, e.NewError500(e.Launchpad_pledge_failed, "Pledge launchpad error: "+err.Error())
		}
	}

	txn.ToAddress = resp.ToAddress
	txn.BlockchainTxID = resp.ID
	txn.TxHash = resp.TxHash
	txn.Status = models.TransactionStatus(resp.Status)
	txn.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Update(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionUpdateFailed, "Update transaction error: "+err.Error())
	}

	return txn, nil
}

func (s *TransactionService) ClaimLaunchpadRefund(req *dto.ClaimLaunchpadRefundRequest) (*models.Transaction, *e.AppError) {
	if !req.Launchpad.IsStatusFailed() {
		return nil, e.NewError400(e.Course_launchpad_not_allow_claim_refund, "Launchpad status is "+string(req.Launchpad.Status))
	}

	pledgedTxn, err := models.Repository.Transaction.FindOne(&models.TransactionQuery{
		UserID:     &req.User.ID,
		Status:     util.NewT(models.TransactionStatusSuccess),
		Type:       util.NewT(models.TransactionTypePledgeLaunchpad),
		EntityType: util.NewT(models.ClpLaunchpadModelName),
		EntityID:   &req.Launchpad.ID,
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.Course_launchpad_claim_refund_failed, "Find the pledge transaction failed: "+err.Error())
	}

	claimedTxn, err := models.Repository.Transaction.FindOne(&models.TransactionQuery{
		UserID:     &req.User.ID,
		Status:     util.NewT(models.TransactionStatusSuccess),
		Type:       util.NewT(models.TransactionTypePledgeLaunchpad),
		EntityType: util.NewT(models.ClpLaunchpadModelName),
		EntityID:   &req.Launchpad.ID,
	}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return nil, e.NewError500(e.Course_launchpad_claim_refund_failed, "Find the claimed transaction failed: "+err.Error())
	}

	if claimedTxn != nil {
		return nil, e.NewError400(e.Course_launchpad_not_allow_claim_refund, "You claimed refund before")
	}

	wallet, aErr := Wallet.FindOne(&models.WalletQuery{ID: &pledgedTxn.WalletID}, nil)
	if aErr != nil {
		return nil, e.NewError500(e.Course_launchpad_claim_refund_failed, "Find the wallet failed: "+aErr.Error())
	}

	investment, err := models.Repository.ClpInvestment(context.TODO()).FindOne(&models.ClpInvestmentQuery{
		UserID:         &req.User.ID,
		ClpLaunchpadID: &req.Launchpad.ID,
	}, &models.FindOneOptions{})
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.Course_launchpad_not_allow_claim_refund, "Investment required")
		}
		return nil, e.NewError500(e.Course_launchpad_claim_refund_failed, "Find the investment failed: "+err.Error())
	}

	txn := &models.Transaction{
		UserID:       req.User.ID,
		WalletID:     wallet.ID,
		Network:      wallet.Network,
		CurrencyType: wallet.Type,
		Currency:     wallet.Currency,
		Amount:       investment.Amount,
		Type:         models.TransactionTypeClaimLaunchpadRefund,
		Status:       models.TransactionStatusPending,
		ErrorCode:    0,
		OrgID:        req.Launchpad.OrgID,
		Data:         models.JSONB{},
		EntityType:   models.ClpLaunchpadModelName,
		EntityID:     req.Launchpad.ID,
	}
	if err := models.Repository.Transaction.Create(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionCreateFailed, "Create transaction error: "+err.Error())
	}

	resp, err := openedu_chain.Transaction.ClaimLaunchpadRefund(&chaindto.ClaimLaunchpadRefundRequest{
		WalletID:  wallet.BlockchainWalletID,
		CoreTxID:  txn.ID,
		Token:     chaindto.BlockchainToken(req.Launchpad.FundingGoal.Currency),
		PoolID:    req.Launchpad.Props.PoolID,
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
				log.Errorf("ClpLaunchpadService::InitPool Delete transaction failed: ")
			}
			return nil, e.NewError400(e.WalletInsufficientGasFee,
				"Balance is not enough to cover the gas fee: "+err.Error())

		case errors.Is(err, openedu_chain.ErrInsufficientBalance):
			if dErr := models.Repository.Transaction.Delete(txn.ID, nil); dErr != nil {
				log.Errorf("ClpLaunchpadService::InitPool Delete transaction failed: ")
			}
			return nil, e.NewError400(e.WalletBalanceNotEnough,
				"Balance is not enough to init launchpad pool: "+err.Error())

		default:
			return nil, e.NewError500(e.Course_launchpad_claim_refund_failed, "Pledge launchpad error: "+err.Error())
		}
	}

	txn.Amount = resp.Deposit
	txn.ToAddress = resp.ToAddress
	txn.BlockchainTxID = resp.ID
	txn.TxHash = resp.TxHash
	txn.Status = models.TransactionStatus(resp.Status)
	txn.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Update(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionUpdateFailed, "Update transaction error: "+err.Error())
	}

	return txn, nil

}

func (s *TransactionService) DepositNftSponsorGasFee(req *dto.DepositSponsorGasFeeRequest) (*models.Transaction, *e.AppError) {
	txn := &models.Transaction{
		Model: models.Model{
			ID: util.GenerateId(),
		},
		UserID:       req.Wallet.UserID,
		OrgID:        req.Org.ID,
		WalletID:     req.Wallet.ID,
		CurrencyType: req.Wallet.Type,
		Currency:     req.Wallet.Currency,
		Network:      req.Wallet.Network,
		Amount:       req.Amount.Neg(),
		Type:         models.TransactionTypeDepositSponsorGas,
		Status:       models.TransactionStatusPending,
		Data:         models.JSONB{},
		EntityType:   models.CourseModelName,
		EntityID:     req.Course.ID,
	}

	resp, err := openedu_chain.Transaction.DepositSponsorGas(&chaindto.DepositSponsorGasRequest{
		WalletID:   req.Wallet.BlockchainWalletID,
		CoreTxID:   txn.ID,
		Amount:     req.Amount,
		Token:      chaindto.BlockchainToken(req.Wallet.Currency),
		Network:    chaindto.BlockchainNetwork(req.Wallet.Network),
		CourseCuid: req.Course.Cuid,
		IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientBalance):
			return nil, e.NewError400(e.WalletBalanceNotEnough, "Balance is not enough to sponsor gas fee: "+err.Error())

		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return nil, e.NewError400(e.WalletInsufficientGasFee, "Balance is not enough to cover gas fee: "+err.Error())

		default:
			return nil, e.NewError500(e.WalletDepositSponsorGasFailed, "Deposit sponsor gas fee error: "+err.Error())
		}
	}

	txn.BlockchainTxID = resp.ID
	txn.TxHash = resp.TxHash
	txn.Status = models.TransactionStatus(resp.Status)
	txn.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Create(txn, nil); err != nil {
		return nil, e.NewError500(e.WalletDepositSponsorGasFailed, "Create transaction error: "+err.Error())
	}

	return txn, nil
}

func (s *TransactionService) WithdrawNftSponsorGasFee(req *dto.WithdrawSponsorGasFeeRequest) (*models.Transaction, *e.AppError) {
	txn := &models.Transaction{
		Model: models.Model{
			ID: util.GenerateId(),
		},
		UserID:       req.Wallet.UserID,
		OrgID:        req.Org.ID,
		WalletID:     req.Wallet.ID,
		CurrencyType: req.Wallet.Type,
		Currency:     req.Wallet.Currency,
		Network:      req.Wallet.Network,
		Amount:       req.Amount,
		Type:         models.TransactionTypeWithdrawSponsorGas,
		Status:       models.TransactionStatusPending,
		Data:         models.JSONB{},
		EntityType:   models.CourseModelName,
		EntityID:     req.Course.ID,
	}

	resp, err := openedu_chain.Transaction.WithdrawSponsorGas(&chaindto.WithdrawSponsorGasRequest{
		WalletID:   req.Wallet.BlockchainWalletID,
		CoreTxID:   txn.ID,
		Amount:     req.Amount,
		Token:      chaindto.BlockchainToken(req.Wallet.Currency),
		Network:    chaindto.BlockchainNetwork(req.Wallet.Network),
		CourseCuid: req.Course.Cuid,
		IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientBalance):
			return nil, e.NewError400(e.WalletBalanceNotEnough, "Balance is not enough to sponsor gas fee: "+err.Error())

		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return nil, e.NewError400(e.WalletInsufficientGasFee, "Balance is not enough to cover gas fee: "+err.Error())

		default:
			return nil, e.NewError500(e.WalletWithdrawSponsorGasFailed, "Withdraw sponsor gas fee error: "+err.Error())
		}
	}

	txn.BlockchainTxID = resp.ID
	txn.TxHash = resp.TxHash
	txn.Status = models.TransactionStatus(resp.Status)
	txn.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Create(txn, nil); err != nil {
		return nil, e.NewError500(e.WalletWithdrawSponsorGasFailed, "Create transaction error: "+err.Error())
	}

	return txn, nil
}
