package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *OrderItemService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.OrderItem, *e.AppError) {
	query := &models.OrderItemQuery{ID: util.NewString(id), IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil)}

	if orderItem, err := models.Repository.OrderItem.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OrderItemNotFound, err.Error())
		}
		return nil, e.NewError500(e.OrderItemFindOneFailed, err.Error())
	} else {
		return orderItem, nil
	}

}

func (s *OrderItemService) FindOne(query *models.OrderItemQuery, options *models.FindOneOptions) (*models.OrderItem, *e.AppError) {
	if order, err := models.Repository.OrderItem.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OrderItemNotFound, err.Error())
		}
		return nil, e.NewError500(e.OrderItemFindOneFailed, err.Error())
	} else {
		return order, nil
	}
}

func (s *OrderItemService) FindPage(query *models.OrderItemQuery, options *models.FindPageOptions) ([]*models.OrderItem, *models.Pagination, *e.AppError) {
	if order, pagination, err := models.Repository.OrderItem.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.OrderItemFindPageFailed, err.Error())
	} else {
		return order, pagination, nil
	}
}

func (s *OrderItemService) Find(query *models.OrderItemQuery, options *models.FindManyOptions) ([]*models.OrderItem, *e.AppError) {
	if order, err := models.Repository.OrderItem.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.OrderItemFindPageFailed, err.Error())
	} else {
		return order, nil
	}
}
