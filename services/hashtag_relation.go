package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *HashtagRelationService) CreateMany(entities []*dto.CreateHashtagRelationParams) *e.AppError {
	// Find hashtags in entities
	hashtagIDs := lo.Map(entities, func(entity *dto.CreateHashtagRelationParams, idx int) string {
		return entity.HashtagID
	})

	hashtags, err := models.Repository.Hashtag.FindMany(&models.HashtagQuery{HashIn: hashtagIDs}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Hashtag_not_found, err.Error())
		}
		return e.NewError500(e.Hashtag_find_many_failed, err.Error())
	}

	hashtagMap := lo.KeyBy(hashtags, func(hashtag *models.Hashtag) string {
		return hashtag.Hash
	})

	var relations []*models.HashtagRelation
	for _, entity := range entities {
		if hashtag, exists := hashtagMap[entity.HashtagID]; exists {
			relation := &models.HashtagRelation{
				HashtagID:   hashtag.Hash,
				RelatedID:   entity.RelatedID,
				RelatedType: models.ModelName(entity.RelatedType),
			}
			relations = append(relations, relation)
		}
	}
	// Add many hashtag to entities
	if len(relations) > 0 {
		if err := models.Repository.HashtagRelation.CreateMany(relations, nil); err != nil {
			return e.NewError500(e.Hashtag_create_many_failed, err.Error())
		}

		for _, relation := range relations {
			if err := models.Repository.Hashtag.IncreaseHashtag(hashtagMap[relation.HashtagID], util.HashtagCount, util.HashtagUnit, nil); err != nil {
				return e.NewError500(e.Hashtag_increase_failed, err.Error())
			}
		}
	}

	return nil
}

func (s *HashtagRelationService) Create(entity *dto.CreateHashtagRelationParams) *e.AppError {
	// Find hashtag in entity
	hashtag, err := models.Repository.Hashtag.FindOne(&models.HashtagQuery{Hash: &entity.HashtagID}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Hashtag_not_found, err.Error())
		}
		return e.NewError500(e.Hashtag_find_one_failed, err.Error())
	}

	// Add hashtag to entity
	if err := models.Repository.HashtagRelation.Create(&models.HashtagRelation{
		HashtagID:   hashtag.Hash,
		RelatedID:   entity.RelatedID,
		RelatedType: models.ModelName(entity.RelatedType),
	}, nil); err != nil {
		return e.NewError500(e.Hashtag_relation_create_failed, err.Error())
	}

	if err := models.Repository.Hashtag.IncreaseHashtag(hashtag, util.HashtagCount, util.HashtagUnit, nil); err != nil {
		return e.NewError500(e.Hashtag_increase_failed, err.Error())
	}

	return nil
}

func (s *HashtagRelationService) FindPage(query *models.HashtagRelationQuery, options *models.FindPageOptions) ([]*models.HashtagRelation, *models.Pagination, *e.AppError) {
	options.Preloads = []string{"Hashtag"}
	if HashtagRelations, pagination, err := models.Repository.HashtagRelation.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Hashtag_find_page_failed, err.Error())
	} else {
		return HashtagRelations, pagination, nil
	}
}

func (s *HashtagRelationService) Delete(id string) *e.AppError {
	hashtagRelation, err := models.Repository.HashtagRelation.FindByID(id, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Hashtag_relation_not_found, err.Error())
		}
		return e.NewError500(e.Hashtag_relation_find_by_id_failed, err.Error())
	}

	if err := models.Repository.HashtagRelation.Delete(id, nil); err != nil {
		return e.NewError500(e.Hashtag_relation_delete_failed, err.Error())
	}

	hashtag, hErr := models.Repository.Hashtag.FindOne(&models.HashtagQuery{
		Hash: &hashtagRelation.HashtagID,
	}, nil)

	if hErr != nil {
		if errors.Is(hErr, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Hashtag_not_found, hErr.Error())
		}
		return e.NewError500(e.Hashtag_find_one_failed, hErr.Error())
	}

	if err := models.Repository.Hashtag.DecreaseHashtag(hashtag, util.HashtagCount, util.HashtagUnit, nil); err != nil {
		return e.NewError500(e.Hashtag_increase_failed, err.Error())
	}
	return nil

}

func (s *HashtagRelationService) DeleteMany(query *models.HashtagRelationQuery) *e.AppError {
	hashtagRelations, err := models.Repository.HashtagRelation.FindMany(query, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Hashtag_relation_not_found, err.Error())
		}
		return e.NewError500(e.Hashtag_relation_find_many_failed, err.Error())
	}

	hashtagIDs := lo.Map(hashtagRelations, func(r *models.HashtagRelation, idx int) string {
		return r.HashtagID
	})

	if _, err := models.Repository.HashtagRelation.DeleteMany(query, nil); err != nil {
		return e.NewError500(e.Hashtag_relation_delete_many_failed, err.Error())
	}

	hashtags, hErr := models.Repository.Hashtag.FindMany(&models.HashtagQuery{HashIn: hashtagIDs}, nil)
	if hErr != nil {
		if errors.Is(hErr, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Hashtag_not_found, hErr.Error())
		}
		return e.NewError500(e.Hashtag_find_many_failed, hErr.Error())
	}

	for _, hashtag := range hashtags {
		if err := models.Repository.Hashtag.DecreaseHashtag(hashtag, util.HashtagCount, util.HashtagUnit, nil); err != nil {
			return e.NewError500(e.Hashtag_increase_failed, err.Error())
		}
	}

	return nil
}

func (s *HashtagRelationService) FindMany(query *models.HashtagRelationQuery, options *models.FindManyOptions) ([]*models.HashtagRelation, *e.AppError) {
	if hashtags, err := models.Repository.HashtagRelation.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Hashtag_find_many_failed, err.Error())
	} else {
		return hashtags, nil
	}
}

func (s *HashtagRelationService) BuildHashtagEntities(names []string, orgID string, relatedID string, relatedType models.ModelName) ([]*dto.CreateHashtagRelationParams, *e.AppError) {
	if len(names) == 0 {
		return []*dto.CreateHashtagRelationParams{}, nil
	}
	hashtags, err := models.Repository.Hashtag.FindMany(&models.HashtagQuery{
		OrgID:  &orgID,
		NameIn: names,
	}, &models.FindManyOptions{})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Hashtag_not_found, err.Error())
		}
		return nil, e.NewError500(e.Hashtag_find_many_failed, err.Error())
	}

	hashtagIDs := []string{}
	if len(hashtags) > 0 {
		hashtagIDs = lo.Map(hashtags, func(h *models.Hashtag, _ int) string {
			return h.Hash
		})
	}

	dbHashtagNames := []string{}
	if len(hashtags) > 0 {
		dbHashtagNames = lo.Map(hashtags, func(h *models.Hashtag, _ int) string {
			return h.Name
		})
	}

	notExistHashtagNames := util.RemoveElementFromAnotherSlice(names, dbHashtagNames)
	if len(notExistHashtagNames) > 0 {
		toCreate := []*models.Hashtag{}
		for _, name := range notExistHashtagNames {
			toCreate = append(toCreate, &models.Hashtag{Name: name})

		}
		// Call hashtag create service
		if err := Hashtag.CreateMany(toCreate, orgID); err != nil {
			return nil, err
		}

		hashtags, err := models.Repository.Hashtag.FindMany(&models.HashtagQuery{
			OrgID:  &orgID,
			NameIn: names,
		}, &models.FindManyOptions{})
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, e.NewError400(e.Hashtag_not_found, err.Error())
			}
			return nil, e.NewError500(e.Hashtag_find_many_failed, err.Error())
		}

		if len(hashtags) > 0 {
			hashtagIDs = lo.Map(hashtags, func(h *models.Hashtag, _ int) string {
				return h.Hash
			})
		}
	}

	results := []*dto.CreateHashtagRelationParams{}
	for _, id := range hashtagIDs {
		results = append(results, &dto.CreateHashtagRelationParams{
			HashtagID:   id,
			RelatedID:   relatedID,
			RelatedType: relatedType,
		})
	}

	return results, nil
}

func (s *HashtagRelationService) FindManyJoinBlog(query *models.HashtagRelationQuery, options *models.FindManyOptions) ([]*models.HashtagRelation, *e.AppError) {
	if hashtags, err := models.Repository.HashtagRelation.FindManyJoinBlog(query, options); err != nil {
		return nil, e.NewError500(e.Hashtag_relation_find_many_failed, err.Error())
	} else {
		return hashtags, nil
	}
}
