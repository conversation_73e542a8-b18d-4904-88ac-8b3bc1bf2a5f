package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/payment"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"github.com/shopspring/decimal"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type OrderMessage struct {
	OrderID       string               `json:"order_id"`
	PaymentStatus models.PaymentStatus `json:"payment_status"`
	OrderStatus   models.OrderStatus   `json:"order_status"`
}

func (s *PaymentService) Create(user *models.User, data *dto.CreatePaymentRequest) (*models.Payment, *e.AppError) {
	invoiceID := fmt.Sprint(data.Payload.ID)
	exist, pErr := models.Repository.Payment.FindOne(&models.PaymentQuery{
		InvoiceId:      util.NewString(invoiceID),
		IncludeDeleted: util.NewBool(false),
	}, nil)
	if pErr != nil && !errors.Is(pErr, gorm.ErrRecordNotFound) {
		return nil, e.NewError404(e.Payment_find_one_failed, pErr.Error())
	}
	if exist != nil {
		return exist, nil
	}

	paymentData := models.Payment{
		UserID:          user.ID,
		OrderID:         data.OrderID,
		PaymentMethodID: data.PaymentMethodID,
		PaymentInfo:     data.PaymentInfo,
		Amount:          data.Amount,
		Payload:         models.JSONB{"data": data.Payload},
		InvoiceId:       invoiceID,
		Status:          data.Status,
		OrgID:           data.OrgID,
		PayFromOrgID:    data.PayFromOrgID,
		Currency:        data.Currency,
	}

	if err := models.Repository.Payment.Create(&paymentData, nil); err != nil {
		return nil, e.NewError500(e.Create_payment_failed, err.Error())
	}

	return &paymentData, nil

}

func (s *PaymentService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Payment, *e.AppError) {
	query := &models.PaymentQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}

	if payment, err := models.Repository.Payment.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Payment_not_found, err.Error())
		}
		return nil, e.NewError500(e.Payment_find_one_failed, err.Error())
	} else {
		return payment, nil
	}
}

func (s *PaymentService) FindOne(query *models.PaymentQuery, options *models.FindOneOptions) (*models.Payment, *e.AppError) {
	if payment, err := models.Repository.Payment.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Payment_not_found, err.Error())
		}
		return nil, e.NewError500(e.Payment_find_one_failed, err.Error())
	} else {
		return payment, nil
	}
}

func (s *PaymentService) FindPage(query *models.PaymentQuery, options *models.FindPageOptions) ([]*models.Payment, *models.Pagination, *e.AppError) {
	if payments, pagination, err := models.Repository.Payment.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_payment_failed, err.Error())
	} else {
		return payments, pagination, nil
	}
}

func (s *PaymentService) Update(payment *models.Payment) *e.AppError {
	if err := models.Repository.Payment.Update(payment, nil); err != nil {
		return e.NewError500(e.Update_payment_failed, err.Error())
	}
	return nil
}

func (s *PaymentService) CanUpdatePayment(p *models.Payment, user *models.User) bool {
	// org owner
	if p.UserID == user.ID {
		return true
	}

	// admin, sysadmin, mod
	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Find UserRoleOrg failed: ", err.Error())
		return false
	}
	if len(userRoles) > 0 {
		allow := false
		for _, ur := range userRoles {
			if lo.Contains[string](editRoles, ur.RoleID) {
				allow = true
				break
			}
		}
		return allow
	}
	return false

}

func (s *PaymentService) Verify(serviceName payment.PaymentServiceName, data *dto.SepayWebhookRequest) *e.AppError {
	order, aErr := Order.FindOne(&models.OrderQuery{Code: &data.Code}, &models.FindOneOptions{})
	if aErr != nil {
		return aErr
	}
	if order.Status == models.OrderStatusSuccess {
		return e.NewError200("Success")
	}

	// handle TransactionTime
	layoutTime := "2006-01-02 15:04:05"
	transactionTime, err := time.Parse(layoutTime, data.TransactionDate)
	if err != nil {
		return e.NewError500(e.Parse_time_error, err.Error())
	}
	// Create record payment
	paymentRecord := dto.CreatePaymentRequest{
		Amount: data.TransferAmount,
		Status: models.PaymentStatusPending,
		PaymentInfo: models.PaymentInfo{
			Acc:    data.AccountNumber,
			Des:    data.Content,
			Bank:   data.Gateway,
			Amount: data.TransferAmount,
		},
		Payload: dto.SepayTransaction{
			ID:                 data.ID,
			Gateway:            data.Gateway,
			TransactionDate:    transactionTime.UnixMilli(),
			AccountNumber:      data.AccountNumber,
			SubAccount:         data.SubAccount,
			AmountIn:           data.TransferAmount,
			AmountOut:          decimal.Zero,
			Accumulated:        data.Accumulated,
			Code:               data.Code,
			TransactionContent: data.Content,
			ReferenceNumber:    data.ReferenceCode,
			Body:               data.Description,
		},
		OrgID:        order.OrgID,
		PayFromOrgID: order.PayFromOrgID,
	}

	switch serviceName {
	case payment.PaymentServiceSepay:
		{
			if strings.ToLower(strings.TrimSpace(data.TransferType)) == "in" {
				//Handle incoming payment
				//Find order by unique code
				user, uErr := User.FindByID(order.UserID, &models.FindOneOptions{})
				if uErr != nil {
					return e.NewError500(e.Course_find_one_failed, "Find user failed")
				}

				paymentRecord.Status = models.PaymentStatusSuccess
				paymentRecord.OrderID = order.ID
				paymentRecord.PaymentMethodID = order.PaymentMethodID
				paymentRecord.Currency = order.Currency
				_, pErr := Payment.Create(user, &paymentRecord)
				if pErr != nil {
					log.Error("Create Payment failed: ", pErr)
					return pErr
				}
				// Handle email
				// Get course payment
				orderItemQuery := models.OrderItemQuery{
					OrderID: &order.ID,
					UserID:  &order.UserID,
				}
				// Now, platform just handle case 1 order for 1 order item then
				// we got findOne, in the further we must be use findMany to get
				// all course
				orderItem, findOIErr := OrderItem.FindOne(&orderItemQuery, &models.FindOneOptions{Sort: []string{"create_at desc"}})
				if findOIErr != nil {
					log.Error("find order item failed", findOIErr)
				}
				courseQuery := models.CourseQuery{
					Cuid: util.NewString(orderItem.EntityCuid),
				}
				course, findCourseErr := Course.FindOne(&courseQuery, &models.FindOneOptions{Sort: []string{"create_at desc"}})
				if findCourseErr != nil {
					log.Error("find course failed", findCourseErr)
				}

				//Get Org
				orgQuery := models.OrganizationQuery{
					ID: &order.OrgID,
				}
				org, findOrgErr := Organization.FindOne(&orgQuery, &models.FindOneOptions{})
				if findOrgErr != nil {
					log.Error("find org failed", findOrgErr)
				}

				emailParams := communicationdto.MapEmailParams{
					communicationdto.EmailParamCourse: course,
				}

				//Check amount
				// Check missing amount and TransferAmount
				if order.MissingAmount.LessThanOrEqual(data.TransferAmount) {
					// Update status, paid, missing amount
					order.MissingAmount = decimal.Zero
					order.Status = models.OrderStatusSuccess
					order.Paid = order.Paid.Add(data.TransferAmount)

					go func() {
						emailParams[communicationdto.EmailParamPaymentAmount] = order.Paid.Sub(order.Amount)
						if _, eErr := communication.Email.SendEmail(&communicationdto.SendEmailRequest{
							User:        user.IntoComm(),
							Org:         org.IntoComm(),
							Event:       communicationdto.EventPaymentOver,
							ExtendDatas: emailParams,
							IsQueue:     true,
						}); eErr != nil {
							log.ErrorWithAlertf("PaymenetService.Verify::Send email payment over failed: %v", eErr.Error())
						}
					}()
				} else {
					//insufficient amount
					order.MissingAmount = order.MissingAmount.Sub(data.TransferAmount)
					order.Status = models.OrderStatusInsufficient
					order.Paid = order.Paid.Add(data.TransferAmount)

					go func() {
						if _, eErr := communication.Email.SendEmail(&communicationdto.SendEmailRequest{
							User:        user.IntoComm(),
							Org:         org.IntoComm(),
							Event:       communicationdto.EventPaymentInsufficient,
							ExtendDatas: emailParams,
							IsQueue:     true,
						}); eErr != nil {
							log.ErrorWithAlertf("PaymentInsufficient.Verify::Send email payment insufficient failed: %v", eErr.Error())
						}
					}()
				}

				if oErr := Order.Update(order); oErr != nil {
					log.ErrorWithAlertf("PaymentInsufficient.Verify::Update order failed: %v", oErr)
					return oErr
				}

				// handle ws
				if wsErr := SendWsOrderStage(order); wsErr != nil {
					log.ErrorWithAlertf("PaymentInsufficient.Verify::Send webSocket order stage failed: %s", util.Struct2Json(order))
					return wsErr
				}

				return nil
			}

		}
	default:
		return e.NewError500(e.Payment_service_not_handle, "Invalid payment service name")
	}
	return nil
}

func SendWsOrderStage(order *models.Order) *e.AppError {
	// send message ws
	orderMsg := OrderMessage{
		OrderID:       order.ID,
		PaymentStatus: models.PaymentStatusSuccess,
		OrderStatus:   order.Status,
	}

	orderMsgBytes, err := json.Marshal(orderMsg)
	if err != nil {
		return e.NewError500(e.Json_encode_error, err.Error())
	}

	msg := models.NewWsMessage(models.PaymentMessage, fmt.Sprint(order.ID), orderMsgBytes)

	mapMsg, encodeErr := util.StructToMap(msg)
	if encodeErr != nil {
		return e.NewError500(e.Json_encode_error, encodeErr.Error())
	}

	bodyMsg := &communicationdto.WebsocketMessageRequest{
		Event: communicationdto.WebsocketEventPayment,
		Broadcast: communicationdto.WebsocketBroadcastParams{
			UserID: order.UserID,
		},
		Data: mapMsg,
	}

	if err := communication.Websocket.SendMsgToUserWebSocket(bodyMsg); err != nil {
		return e.NewError500(e.Write_msg_ws_error, err.Error())
	}
	return nil
}

func UpdateUsedCouponOnPayment(order *models.Order) *e.AppError {
	couponHistory, err := CouponHistory.FindOne(&models.CouponHistoryQuery{OrderID: &order.ID, Status: util.NewT(models.CouponStatusInUse)}, &models.FindOneOptions{Sort: []string{"create_at desc"}})
	if err != nil {
		if err.ErrCode != e.CouponHistoryNotFound {
			return err
		} else {
			return nil
		}
	}
	couponHistory.Status = models.CouponStatusUsed
	if uErr := CouponHistory.Update(couponHistory); uErr != nil {
		return uErr
	}
	return nil
}
