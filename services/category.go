package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"slices"
	"strings"

	"gorm.io/gorm"
)

func (s *CategoryService) UpsertMany(reqs *dto.CreateCategoriesParams) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	categories := s.convertRequestsToCategories(nil, reqs.Categories, reqs.OrgID)
	if err := models.Repository.Category.UpsertMany(categories, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_create_or_update_failed, err.Error())
	}

	var categoryBlogIDs, categoryCourseIDs, levelIDs []string
	for _, category := range categories {
		switch category.Type {
		case models.TypeLevel:
			levelIDs = append(levelIDs, category.ID)
		case models.TypeCategoryBlog:
			categoryBlogIDs = append(categoryBlogIDs, category.ID)
		case models.TypeCategoryCourse:
			categoryCourseIDs = append(categoryCourseIDs, category.ID)
		}
	}

	if len(levelIDs) > 0 {
		err := models.Repository.Category.DeactivateMany(&models.CategoryQuery{
			IDNotIn: levelIDs,
			Type:    util.NewT(models.TypeLevel),
			Active:  util.NewBool(true),
			OrgID:   &reqs.OrgID,
		}, tx)
		if err != nil {
			tx.Rollback()
			return e.NewError500(e.Category_create_or_update_failed, err.Error())
		}
	}

	if len(categoryBlogIDs) > 0 {
		err := models.Repository.Category.DeactivateMany(&models.CategoryQuery{
			IDNotIn: categoryBlogIDs,
			Type:    util.NewT(models.TypeCategoryBlog),
			Active:  util.NewBool(true),
			OrgID:   &reqs.OrgID,
		}, tx)
		if err != nil {
			tx.Rollback()
			return e.NewError500(e.Category_create_or_update_failed, err.Error())
		}
	}

	if len(categoryCourseIDs) > 0 {
		err := models.Repository.Category.DeactivateMany(&models.CategoryQuery{
			IDNotIn: categoryCourseIDs,
			Type:    util.NewT(models.TypeCategoryCourse),
			Active:  util.NewBool(true),
		}, tx)
		if err != nil {
			tx.Rollback()
			return e.NewError500(e.Category_create_or_update_failed, err.Error())
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_create_or_update_failed, "Commit transaction error: "+err.Error())
	}
	return nil
}

func (s *CategoryService) convertRequestsToCategories(parentID *string, reqs []*dto.CategoryRequest, orgID string) []*models.Category {
	var categories []*models.Category
	for _, req := range reqs {
		category := &models.Category{
			Name:     req.Name,
			Active:   req.Active,
			Order:    req.Order,
			Type:     req.Type,
			ParentID: parentID,
			OrgID:    orgID,
		}

		formatted, _ := util.RemoveSpecialCharacter(req.Name)
		category.Formatted = strings.ToLower(strings.TrimSpace(formatted))

		// if req.Type == models.TypeCategoryBlog || req.Type == models.TypeLevel {
		// 	category.OrgID = orgID
		// }

		if req.ID != "" {
			category.ID = req.ID
		} else {
			category.ID = util.GenerateId()
		}
		categories = append(categories, category)

		if len(req.Child) > 0 {
			childCategories := s.convertRequestsToCategories(&category.ID, req.Child, orgID)
			categories = append(categories, childCategories...)
		}
	}
	return categories
}

func (s *CategoryService) FindPage(query *models.CategoryQuery, options *models.FindPageOptions) ([]*models.Category, *models.Pagination, *e.AppError) {
	if categories, pagination, err := models.Repository.Category.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Category_find_page_failed, err.Error())
	} else {
		return categories, pagination, nil
	}
}

func (s *CategoryService) DeleteMany(ids []string, orgID string) *e.AppError {
	if len(ids) > 0 {
		query := models.CategoryQuery{
			Active: util.NewBool(true),
			OrgID:  &orgID,
		}

		categories, err := models.Repository.Category.FindMany(&query, nil)
		if err != nil {
			return e.NewError500(e.Category_find_many_failed, err.Error())
		}

		query = models.CategoryQuery{
			IDIn:   getToDeleteCategoryIDs(ids, categories),
			Active: util.NewBool(true),
			OrgID:  &orgID,
		}
		if err = models.Repository.Category.DeactivateMany(&query, nil); err != nil {
			return e.NewError500(e.Category_delete_failed, err.Error())
		}
	}
	return nil
}

func collectToDeleteIDs(categoryID string, childIDsByParentIDs map[string][]string) []string {
	// Start with the current category ID
	toDelete := []string{categoryID}

	// Recursively collect all child IDs
	if childIDs, found := childIDsByParentIDs[categoryID]; found {
		for _, childID := range childIDs {
			toDelete = append(toDelete, collectToDeleteIDs(childID, childIDsByParentIDs)...)
		}
	}

	return toDelete
}

func getToDeleteCategoryIDs(ids []string, categories []*models.Category) []string {
	childIDsByParentIDs := make(map[string][]string)
	for _, category := range categories {
		if category.ParentID != nil {
			if _, found := childIDsByParentIDs[*category.ParentID]; !found {
				childIDsByParentIDs[*category.ParentID] = []string{category.ID}
			} else {
				childIDsByParentIDs[*category.ParentID] = append(childIDsByParentIDs[*category.ParentID], category.ID)
			}
		}
	}
	var toDeleteIDs []string
	for _, categoryID := range ids {
		toDeleteIDs = append(toDeleteIDs, collectToDeleteIDs(categoryID, childIDsByParentIDs)...)
	}
	return toDeleteIDs
}

func (s *CategoryService) GetTree(query *models.CategoryQuery) ([]*models.TreeCategory, *e.AppError) {
	categories, err := models.Repository.Category.FindMany(query, &models.FindManyOptions{Sort: []string{models.OrderASC}})
	if err != nil {
		return nil, e.NewError500(e.Category_find_many_failed, err.Error())
	}

	categoryTrees := convertCategoriesToTrees(categories)
	return categoryTrees, nil
}

func convertCategoriesToTrees(categories []*models.Category) []*models.TreeCategory {
	categoryMap := make(map[string]*models.TreeCategory)

	// covert all categories to type treeCategory
	for _, category := range categories {
		treeCate := &models.TreeCategory{
			ID:        category.ID,
			Name:      category.Name,
			Type:      category.Type,
			Order:     category.Order,
			OrgID:     category.OrgID,
			UseCount:  category.UseCount,
			Formatted: category.Formatted,
			Child:     []*models.TreeCategory{},
		}
		categoryMap[category.ID] = treeCate
	}

	// append child to parent
	var result []*models.TreeCategory
	for _, category := range categories {
		treeCate := categoryMap[category.ID]

		// if parent append to result
		if category.ParentID == nil {
			result = append(result, treeCate)
		} else {
			// if not, check if parent already in categoryMap -> append to parent child
			parentTreeCate, existed := categoryMap[*category.ParentID]
			if existed {
				parentTreeCate.Child = append(parentTreeCate.Child, treeCate)
			}
		}
	}

	return result
}

func (s *CategoryService) FindAll() ([]*models.Category, *e.AppError) {
	if categories, err := models.Repository.Category.FindMany(nil, nil); err != nil {
		return nil, e.NewError500(e.Category_find_many_failed, "failed: "+err.Error())
	} else {
		return categories, nil
	}
}

func (s *CategoryService) FindOne(query *models.CategoryQuery, options *models.FindOneOptions) (*models.Category, *e.AppError) {
	if category, err := models.Repository.Category.FindOne(query, options); err != nil {
		return nil, e.NewError500(e.Category_find_one_failed, "failed: "+err.Error())
	} else {
		return category, nil
	}
}

// categoryMap is Map of all Categories, the reason to convert is easy to handle
func GetCategoryParent(id string, categoryMap map[string]*models.Category, result []string) []string {
	if category, ok := categoryMap[id]; ok && !slices.Contains(result, id) {
		if category.ParentID != nil {
			parentResult := GetCategoryParent(*category.ParentID, categoryMap, result)
			result = append(result, parentResult...)
		}
		result = append(result, id)
	}
	return result
}

// categoryMap is Map of all Categories, the reason to convert is easy to handle
func GetCategoryChild(categoryIDs []string, categoryMap map[string]*models.Category) []string {
	maxOrder := -1
	var highestOrderCategoryIDs []string
	for _, categoryID := range categoryIDs {
		if categoryMap[categoryID].Order > maxOrder {
			maxOrder = categoryMap[categoryID].Order
			highestOrderCategoryIDs = []string{categoryID}
		} else if categoryMap[categoryID].Order == maxOrder {
			highestOrderCategoryIDs = append(highestOrderCategoryIDs, categoryID)
		}
	}

	return highestOrderCategoryIDs
}

func ConvertCategoriesToMap() (map[string]*models.Category, *e.AppError) {
	// Get all Category in DB
	fullyCategoryID, err := models.Repository.Category.FindMany(&models.CategoryQuery{}, &models.FindManyOptions{})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Category_not_found, err.Error())
		}
		return nil, e.NewError500(e.Category_find_many_failed, err.Error())
	}

	// Convert all Categories to map[string]*models.Category
	categoryMap := make(map[string]*models.Category)

	for _, category := range fullyCategoryID {
		categoryMap[category.ID] = category
	}

	return categoryMap, nil
}

func (s *CategoryService) CheckPermissionUpsertCategories(user *models.User, org *models.Organization, req *dto.UpsertCategoriesRequest) *e.AppError {
	var categoryIDs []string
	for _, category := range req.Categories {
		if category.ID != "" {
			categoryIDs = append(categoryIDs, category.ID)
		}
	}

	categories, err := models.Repository.Category.FindMany(&models.CategoryQuery{
		IDIn: categoryIDs,
	}, nil)
	if err != nil {
		return e.NewError500(e.Category_find_many_failed, "Check permission to upsert categories error: "+err.Error())
	}

	categoriesByIDs := map[string]*models.Category{}
	for _, category := range categories {
		categoriesByIDs[category.ID] = category
	}

	for _, categoryRequest := range req.Categories {
		switch categoryRequest.Type {
		case models.TypeCategoryBlog:
			if !user.IsOrgAdmin(org.ID) && !user.IsSysAdmin() {
				return e.NewError403(e.FORBIDDEN, "Permission denied")
			}

			if categoryRequest.ID != "" {
				category, found := categoriesByIDs[categoryRequest.ID]
				if !found {
					return e.NewError400(e.INVALID_PARAMS, "Invalid Category ID: "+categoryRequest.ID)
				}

				if category.OrgID != org.ID && !user.IsSysAdmin() {
					return e.NewError403(e.FORBIDDEN, "You cannot update blog category from other organization")
				}
			}

		case models.TypeCategoryCourse:
			// Course categories are managed by sysadmin and OpenEdu admins and moderators
			if !user.IsSysAdmin() && !(org.IsRoot() && user.IsOrgAdmin(org.ID)) {
				return e.NewError403(e.FORBIDDEN, "Permission denied")
			}

		case models.TypeLevel:
			if !user.IsOrgAdmin(org.ID) && !user.IsSysAdmin() {
				return e.NewError403(e.FORBIDDEN, "Permission denied")
			}

			if categoryRequest.ID != "" {
				category, found := categoriesByIDs[categoryRequest.ID]
				if !found {
					return e.NewError400(e.INVALID_PARAMS, "Invalid Category ID: "+categoryRequest.ID)
				}

				if category.OrgID != org.ID && !user.IsSysAdmin() {
					return e.NewError403(e.FORBIDDEN, "You cannot update course levels from other organization")
				}
			}
		}
	}
	return nil
}

func (s *CategoryService) FindMany(query *models.CategoryQuery, options *models.FindManyOptions) ([]*models.Category, *e.AppError) {
	if categories, err := models.Repository.Category.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Category_find_many_failed, err.Error())
	} else {
		return categories, nil
	}
}
