package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *CategoryRelationService) Upsert(entity *dto.CreateCategoryRelationParams, needClearCAche bool) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Convert all Categories to map[string]*models.Category
	categoryMap, err := ConvertCategoriesToMap()
	if err != nil {
		tx.Rollback()
		return err
	}

	categoryIDs := entity.CategoryIDs
	var queryCategories []*models.Category
	if len(categoryIDs) != 0 {
		var qErr error
		queryCategories, qErr = models.Repository.Category.FindMany(&models.CategoryQuery{IDIn: categoryIDs}, nil)
		if qErr != nil {
			if errors.Is(qErr, gorm.ErrRecordNotFound) {
				tx.Rollback()
				return e.NewError400(e.Category_not_found, qErr.Error())
			}
			tx.Rollback()
			return e.NewError500(e.Category_find_many_failed, qErr.Error())
		}
	}

	// Use Recursion to fetch all relate category parent id in queryCategories
	// storeCategoryIds use as a checker to verify if duplicate categoryId
	// resultCategoryIds use as a result after Recursion finish
	var resultCategoryIds []string
	var storeCategoryIds []string
	for _, queryCategory := range queryCategories {
		resultCategoryIds = append(resultCategoryIds, GetCategoryParent(queryCategory.ID, categoryMap, storeCategoryIds)...)
	}

	resultCategoryIds = util.RemoveDuplicateValues(resultCategoryIds)
	// Handle clear cache for blogs
	if needClearCAche {
		switch entity.RelatedType {
		case models.BlogModelName:
			if cErr := models.Cache.Blog.DeleteBlogByManyCategory(resultCategoryIds); cErr != nil {
				log.Errorf("CategoryRelationService::Upsert Delete blog by many category cache error: %v", cErr)
			}
		default:
			break
		}
	}

	var toCreate []*models.CategoryRelation
	// Loop all resultCategoryIds and Append processed entity to toCreate and start new loop
	order := 1
	for _, resultCategoryId := range resultCategoryIds {
		elem := &models.CategoryRelation{
			RelatedID:   entity.RelatedID,
			RelatedType: entity.RelatedType,
			CategoryID:  resultCategoryId,
			Field:       entity.Field,
			Order:       0,
		}
		// if input category use input order
		if lo.Contains(entity.CategoryIDs, resultCategoryId) {
			elem.Order = order
			order++
		}
		toCreate = append(toCreate, elem)
	}

	toCreateMap := make(map[string]bool)
	for _, n := range toCreate {
		toCreateMap[n.CategoryID] = true
	}

	query := models.CategoryRelationQuery{
		RelatedID:   util.NewString(entity.RelatedID),
		RelatedType: util.NewT(entity.RelatedType),
		Field:       util.NewString(entity.Field),
	}
	var exists []*models.CategoryRelation
	if existRelations, err := models.Repository.CategoryRelation.FindMany(&query, nil); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return e.NewError400(e.Category_relation_not_found, err.Error())
		}
		tx.Rollback()
		return e.NewError500(e.Category_relation_find_many_failed, err.Error())
	} else {
		exists = existRelations
	}

	existsMap := make(map[string]bool)
	for _, e := range exists {
		existsMap[e.CategoryID] = true
	}

	// exists - toCreate = toDelete
	var toDeleteIds []string
	var decreaseCategoryIDs []string
	for _, e := range exists {
		if _, found := toCreateMap[e.CategoryID]; !found {
			toDeleteIds = append(toDeleteIds, e.ID)
			decreaseCategoryIDs = append(decreaseCategoryIDs, e.CategoryID)
		}
	}

	// find not exists for increase use_count
	var increaseCategoryIDs []string
	for _, n := range toCreate {
		if _, found := existsMap[n.CategoryID]; !found {
			increaseCategoryIDs = append(increaseCategoryIDs, n.CategoryID)
		}
	}

	// upsert many toCreate
	if len(toCreate) > 0 {
		if err := models.Repository.CategoryRelation.UpsertMany(toCreate, tx); err != nil {
			log.Error("Api::CategoryRelation.UpsertMany failed", err)
			tx.Rollback()
			return e.NewError500(e.Category_relation_create_many_failed, err.Error())
		}
	}

	// delete many toDelete
	if len(toDeleteIds) > 0 {
		if _, err := models.Repository.CategoryRelation.DeleteMany(&models.CategoryRelationQuery{IDIn: toDeleteIds}, tx); err != nil {
			log.Error("Api::CategoryRelation.DeleteMany failed", err)
			tx.Rollback()
			return e.NewError500(e.Category_relation_delete_many_failed, err.Error())
		}
	}

	// increase many increaseCategoryIDs
	if len(increaseCategoryIDs) > 0 {
		if err := models.Repository.Category.IncreaseManyUseCount(&models.CategoryQuery{
			IDIn: increaseCategoryIDs,
		}, util.CategoryCount, util.CategoryUnit, tx); err != nil {
			log.Error("Api::Category.IncreaseManyUseCount failed", err)
			tx.Rollback()
			return e.NewError500(e.Category_increase_use_count_failed, err.Error())
		}
	}

	// increase many decreaseCategoryIDs
	if len(decreaseCategoryIDs) > 0 {
		if err := models.Repository.Category.DecreaseManyUseCount(&models.CategoryQuery{
			IDIn: decreaseCategoryIDs,
		}, util.CategoryCount, util.CategoryUnit, tx); err != nil {
			log.Error("Api::Category.DecreaseManyUseCount failed", err)
			tx.Rollback()
			return e.NewError500(e.Category_decrease_use_count_failed, err.Error())
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_create_or_update_failed, "Commit transaction error: "+err.Error())
	}
	return nil
}

func (s *CategoryRelationService) FindBy(query *models.CategoryRelationQuery, options *models.FindManyOptions) ([]*models.CategoryRelation, *e.AppError) {
	if entities, err := models.Repository.CategoryRelation.FindMany(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Category_relation_not_found, err.Error())
		}
		return nil, e.NewError500(e.Category_relation_find_many_failed, err.Error())
	} else {
		// Convert all Categories to map[string]*models.Category
		categoryMap, err := ConvertCategoriesToMap()
		if err != nil {
			return nil, err
		}

		// convert entity to []string for GetCategoryChild
		var categoryIDs []string

		for _, entity := range entities {
			categoryIDs = append(categoryIDs, entity.CategoryID)
		}

		// get list highest order category id
		childCategoryIds := GetCategoryChild(categoryIDs, categoryMap)

		// loop item in childCategoryIds append childCategoryRelations for response
		var childCategoryRelations []*models.CategoryRelation
		for _, entity := range entities {
			for _, childCategoryId := range childCategoryIds {
				if entity.CategoryID == childCategoryId {
					childCategoryRelations = append(childCategoryRelations, entity)
					break
				}
			}
		}

		return childCategoryRelations, nil
	}
}

func (s *CategoryRelationService) FindById(id string, includeDeleted bool, options *models.FindOneOptions) (*models.CategoryRelation, *e.AppError) {
	query := &models.CategoryRelationQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}

	if entity, err := models.Repository.CategoryRelation.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Category_relation_not_found, err.Error())
		}
		return nil, e.NewError500(e.Category_relation_find_one_failed, err.Error())
	} else {
		return entity, nil
	}
}

func (s *CategoryRelationService) Delete(id string) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	categoryRelation, err := models.Repository.CategoryRelation.FindByID(id, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return e.NewError400(e.Category_relation_not_found, err.Error())
		}
		tx.Rollback()
		return e.NewError500(e.Category_relation_find_one_failed, err.Error())
	}

	if err = models.Repository.CategoryRelation.Delete(id, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_relation_delete_failed, err.Error())
	}

	category, cErr := models.Repository.Category.FindOne(&models.CategoryQuery{
		ID: &categoryRelation.CategoryID,
	}, nil)
	if cErr != nil {
		if errors.Is(cErr, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return e.NewError400(e.Category_not_found, cErr.Error())
		}
		tx.Rollback()
		return e.NewError500(e.Category_find_one_failed, cErr.Error())
	}

	if err = models.Repository.Category.DecreaseUseCount(category, util.CategoryCount, util.CategoryUnit, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_decrease_use_count_failed, err.Error())
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_decrease_use_count_failed, "Commit transaction error: "+err.Error())
	}

	return nil
}

func (s *CategoryRelationService) DeleteMany(query *models.CategoryRelationQuery) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	categoryRelations, err := models.Repository.CategoryRelation.FindMany(query, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return e.NewError400(e.Category_relation_not_found, err.Error())
		}
		tx.Rollback()
		return e.NewError500(e.Category_find_many_failed, err.Error())
	}

	categoryIDs := lo.Map(categoryRelations, func(r *models.CategoryRelation, idx int) string {
		return r.CategoryID
	})

	if _, err = models.Repository.CategoryRelation.DeleteMany(query, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_relation_delete_many_failed, err.Error())
	}

	categories, cErr := models.Repository.Category.FindMany(&models.CategoryQuery{
		IDIn: categoryIDs,
	}, nil)
	if cErr != nil {
		if errors.Is(cErr, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return e.NewError400(e.Category_not_found, cErr.Error())
		}
		tx.Rollback()
		return e.NewError500(e.Category_find_many_failed, cErr.Error())
	}

	for _, category := range categories {
		if err = models.Repository.Category.DecreaseUseCount(category, util.CategoryCount, util.CategoryUnit, tx); err != nil {
			tx.Rollback()
			return e.NewError500(e.Category_decrease_use_count_failed, err.Error())
		}
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Category_decrease_use_count_failed, "Commit transaction error: "+err.Error())
	}

	return nil
}

func (s *CategoryRelationService) FindMany(query *models.CategoryRelationQuery, options *models.FindManyOptions) ([]*models.CategoryRelation, *e.AppError) {
	if categories, fmErr := models.Repository.CategoryRelation.FindMany(query, options); fmErr != nil {
		return nil, e.NewError500(e.Category_relation_find_many_failed, fmErr.Error())
	} else {
		return categories, nil
	}
}

func (s *CategoryRelationService) FindPageJoinBlog(query *models.CategoryRelationQuery, options *models.FindPageOptions) ([]*models.CategoryRelation, *models.Pagination, *e.AppError) {
	if categories, pagination, err := models.Repository.CategoryRelation.FindPageJoinBlog(query, options); err != nil {
		return nil, nil, e.NewError500(e.Category_relation_find_many_failed, err.Error())
	} else {
		return categories, pagination, nil
	}
}
func (s *CategoryRelationService) FindManyJoinBlog(query *models.CategoryRelationQuery, options *models.FindManyOptions) ([]*models.CategoryRelation, *e.AppError) {
	if categories, err := models.Repository.CategoryRelation.FindManyJoinBlog(query, options); err != nil {
		return nil, e.NewError500(e.Category_relation_find_many_failed, err.Error())
	} else {
		return categories, nil
	}
}
