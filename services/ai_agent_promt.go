package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"

	"github.com/samber/lo"
	"gorm.io/gorm"

	"openedu-core/pkg/util"
)

func (s *AIAgentPromptService) FindPage(query *models.AIPromptQuery, options *models.FindPageOptions) ([]*models.AIPrompt, *models.Pagination, *e.AppError) {
	prompts, pagination, err := models.Repository.AIPrompt.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.AIPromptFindPageFailed, "Find page ai prompt error: "+err.Error())
	}
	return prompts, pagination, nil
}

func (s *AIAgentPromptService) FindOneOptions(query *models.AIPromptQuery, options *models.FindOneOptions) (*models.AIPrompt, *e.AppError) {
	cert, err := models.Repository.AIPrompt.FindOne(query, options)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.AIPromptFindFailed, err.Error())
	}

	return cert, nil
}

func (s *AIAgentPromptService) FindByID(id string) (*models.AIPrompt, *e.AppError) {
	prompt, err := models.Repository.AIPrompt.FindOne(
		&models.AIPromptQuery{
			ID: util.NewString(id),
		}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.AIPromptNotFound, "AI Prompt not found: "+err.Error())
		}
		return nil, e.NewError500(e.AIPromptFindFailed, "Find ai prompt by ID error: "+err.Error())
	}

	return prompt, nil
}

func (s *AIAgentPromptService) Create(data *dto.AIPromptRequest) (*models.AIPrompt, *e.AppError) {
	prompt := &models.AIPrompt{
		UserID:      data.UserID,
		Enable:      data.Enable,
		AIAgentType: data.AIAgentType,
		CategoryID:  data.CategoryID,
		Text:        data.Text,
		Order:       data.Order,
	}
	if err := models.Repository.AIPrompt.Create(prompt, nil); err != nil {
		return nil, e.NewError500(e.AIPromptCreateFailed, err.Error())
	}
	return prompt, nil
}

func (s *AIAgentPromptService) CreateMany(data *dto.AIPromptsRequest, userID string) *e.AppError {
	if len(data.Prompts) > 0 {
		prompts := lo.Map(data.Prompts, func(p *dto.AIPromptRequest, _ int) *models.AIPrompt {
			prompt := &models.AIPrompt{
				UserID:      userID,
				Enable:      p.Enable,
				AIAgentType: p.AIAgentType,
				CategoryID:  p.CategoryID,
				Text:        p.Text,
				Order:       p.Order,
			}
			return prompt
		})

		if err := models.Repository.AIPrompt.CreateMany(prompts, nil); err != nil {
			return e.NewError500(e.AIPromptCreateFailed, err.Error())
		}
	}

	return nil
}
