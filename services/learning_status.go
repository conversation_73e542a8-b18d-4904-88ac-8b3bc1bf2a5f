package services

import (
	"errors"
	"fmt"
	"math"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *LearningStatusService) AddLearningStatus(lp *dto.CreateLearningProgressParams) (*models.LearningProgressOverview, *models.Course, *e.AppError) {
	publishCourse, pErr := NewPublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
		CourseSlug: &lp.CourseSlug,
	}, nil)
	if pErr != nil {
		return nil, nil, pErr
	}

	course, cErr := Course.FindById(publishCourse.CourseID, false, nil)
	if cErr != nil {
		return nil, nil, cErr
	}

	courseOutline, oErr := NewCourse(s.ctx).GetOutline(course)
	if oErr != nil {
		return nil, nil, oErr
	}

	allLessonContents, appErr := NewLessonContent(s.ctx).FindByCourseID(courseOutline.ID, nil)
	if appErr != nil {
		log.Errorf("Find contents for course failed: %v", appErr)
		return nil, nil, appErr
	}

	status, lsErr := models.Repository.LearningStatus(s.ctx).FindOne(&models.LearningStatusQuery{
		UserID:     util.NewString(lp.UserID),
		CourseCuid: util.NewString(courseOutline.Cuid),
	}, nil)

	if lsErr != nil && !errors.Is(lsErr, gorm.ErrRecordNotFound) {
		appErr = e.NewError500(e.LearningStatusNotFound, "Find learning status failed: "+lsErr.Error())
		return nil, nil, appErr
	} else {
		if status == nil {
			newStatus := &models.LearningStatus{
				UserID:      lp.UserID,
				OrgID:       lp.OrgID,
				CourseCuid:  courseOutline.Cuid,
				StartAt:     time.Now().UnixMilli(),
				CompletedAt: 0,
			}
			if createErr := models.Repository.LearningStatus(s.ctx).Create(newStatus, nil); createErr != nil {
				return nil, nil, e.NewError500(e.CreateLearningStatusFailed, "Create learning status failed: "+createErr.Error())
			} else {
				status = newStatus
			}
		}
	}

	isCurrentLessonComplete := false

	status, appErr = s.appendLearningStatus(status, courseOutline, lp, allLessonContents, &isCurrentLessonComplete)
	if appErr != nil {
		return nil, nil, appErr
	}

	if updateErr := models.Repository.LearningStatus(s.ctx).Update(status, nil); updateErr != nil {
		return nil, nil, e.NewError500(e.UpdateLearningStatusFailed, "Update learning status failed: "+updateErr.Error())
	}

	log.Infof("isCurrentComplete: %#v", isCurrentLessonComplete)
	if isHalfCourse(status, courseOutline) && isCurrentLessonComplete {
		go s.TriggerHalfCourse(lp.User, lp.Org, courseOutline, status)
	}

	aigov := models.GetConfig[models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	if lp.Org.ID == aigov.OrgID {
		if status.CompletedAt != 0 {
			go func() {
				defer func() {
					if r := recover(); r != nil {
						log.ErrorWithAlertf("Recovered in triggerCompleteCourse", r)
					}
				}()
				emailReq := &communicationdto.SendEmailRequest{
					User: lp.User.IntoComm(),
					Org:  lp.Org.IntoComm(),
					Code: util.NewT(communicationdto.EmailCodeCompleteCourse),
					ExtendDatas: communicationdto.MapEmailParams{
						"user_name":          lp.User.Username,
						"user_email":         lp.User.Email,
						"course_slug":        course.Slug,
						"course_name":        course.Name,
						"current_lesson_id":  status.LatestLessonUID,
						"current_section_id": status.LatestSectionUID,
					},
					From:    lp.Org.Settings.SenderEmail,
					IsQueue: false,
				}
				if _, err := communication.Email.SendEmail(emailReq); err != nil {
					log.ErrorWithAlertf("CompleteCourse::Send email code: %v failed: %v", communicationdto.EmailCodeCompleteCourse, err)
				}

			}()
		}
	}

	return s.buildLearningProgressOverview(status, allLessonContents, courseOutline), courseOutline, nil
}

func isHalfCourse(status *models.LearningStatus, course *models.Course) bool {
	totalLessonOfCourse := 0
	totalLearnedLesson := 0

	for _, section := range course.Outline {
		for _, lesson := range section.Lessons {
			if lesson.Status == models.SectionStatusPublish {
				totalLessonOfCourse++
			}
		}
	}

	for _, section := range status.Sections {
		totalLearnedLesson += len(section.Lessons)
	}

	log.Infof("CheckHalfCourse with totalLessonOfCourse: %#v and total LearnedLesson: %#v", totalLessonOfCourse, totalLearnedLesson)

	//Odd
	if totalLessonOfCourse%2 != 0 {
		if totalLearnedLesson*2 == totalLessonOfCourse+1 {
			log.Infof("Go into odd case")
			return true
		}
	} else { //Even
		if totalLearnedLesson*2 == totalLessonOfCourse {
			log.Infof("Go into even case")
			return true
		}
	}
	return false
}

func (s *LearningStatusService) TriggerHalfCourse(user *models.User, org *models.Organization, course *models.Course, status *models.LearningStatus) {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorWithAlertf("Recovered in TriggerHalfCourse", r)
		}
	}()

	//Push Noti
	props := communicationdto.JSONB(MakeNotificationPropsForNextLesson(course, org, user, status))
	if props != nil {
		req := &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeLearnedHalfCourse,
			EntityID:   course.Cuid,
			EntityType: communicationdto.CourseEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{user.ID},
				},
			},
			Props: props,
		}
		if err := communication.Notification.PushNotification(req); err != nil {

			log.Errorf("Push notification after learning half course error: %v", err)
		}
	}

	// SendEmail
	aigov := models.GetConfig[models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	if org.ID == aigov.OrgID {
		emailReq := &communicationdto.SendEmailRequest{
			User: user.IntoComm(),
			Org:  org.IntoComm(),
			Code: util.NewT(communicationdto.EmailCodeCompleteHalfCourse),
			ExtendDatas: communicationdto.MapEmailParams{
				"user_name":          user.Username,
				"user_email":         user.Email,
				"course_name":        course.Name,
				"course_slug":        course.Slug,
				"current_lesson_id":  props["next_lesson_uid"],
				"current_section_id": props["next_section_uid"],
			},
			From:    org.Settings.SenderEmail,
			IsQueue: false,
		}
		if _, err := communication.Email.SendEmail(emailReq); err != nil {
			log.ErrorWithAlertf("HalfCourse::Send email code: %v failed: %v", communicationdto.EmailCodeCompleteHalfCourse, err)
		}
	}

}

func (s *LearningStatusService) HandleUserRefGame(orgID string, userID string, courseCuid string) *e.AppError {
	updateRefTrackingReq := &communicationdto.UpdateRefTrackingRequest{
		ActorID:      userID,
		ContextValue: courseCuid,
		OrgID:        orgID,
	}
	if err := communication.Tracking.UpdateRefTracking(updateRefTrackingReq); err != nil {
		return e.NewError500(e.External_call_error, "Update ref tracking failed: "+err.Error())
	}
	return nil
}

func (s *LearningStatusService) GetUserLearningStatusByCourse(
	courseSlug string,
	userID string,
) (*models.LearningProgressOverview, *models.Course, *e.AppError) {
	publishCourse, pErr := NewPublishCourse(s.ctx).FindBySlug(courseSlug, nil)
	if pErr != nil {
		return nil, nil, pErr
	}

	course, cErr := Course.FindById(publishCourse.CourseID, false, nil)
	if cErr != nil {
		return nil, nil, cErr
	}

	courseOutline, oErr := NewCourse(s.ctx).GetOutline(course)
	if oErr != nil {
		return nil, nil, oErr
	}

	learningStatus, sttErr := models.Repository.LearningStatus(s.ctx).FindOne(&models.LearningStatusQuery{
		CourseCuid: util.NewString(courseOutline.Cuid),
		UserID:     util.NewString(userID),
	}, nil)
	if sttErr != nil && !errors.Is(sttErr, gorm.ErrRecordNotFound) {
		return nil, nil, e.NewError500(e.LearningStatusNotFound, sttErr.Error())
	}

	contents, appErr := NewLessonContent(s.ctx).FindByCourseID(courseOutline.ID, nil)
	if appErr != nil {
		return nil, nil, appErr
	}

	return s.buildLearningProgressOverview(learningStatus, contents, courseOutline), courseOutline, nil
}

func (s *LearningStatusService) appendLearningStatus(
	status *models.LearningStatus,
	courseOutline *models.Course,
	lp *dto.CreateLearningProgressParams,
	lessonContents []*models.LessonContent,
	isCurrentLessonComplete *bool,
) (*models.LearningStatus, *e.AppError) {

	sectionOutline, soOk := lo.Find(courseOutline.Outline, func(item *models.Section) bool {
		return item.UID == lp.SectionUID
	})
	if !soOk {
		return nil, e.NewError400(e.Section_not_found, fmt.Sprintf("Section UID %s not found", lp.SectionUID))
	}

	lessonOutline, loOk := lo.Find(sectionOutline.Lessons, func(item *models.Section) bool {
		return item.UID == lp.LessonUID
	})
	if !loOk {
		return nil, e.NewError400(e.Lesson_not_found, fmt.Sprintf("Lessson UID %s not found", lp.LessonUID))
	}
	lastLesson := sectionOutline.Lessons[0]
	firstLesson := sectionOutline.Lessons[0]
	inputLessonOrder := 0
	latestLessonOrder := -1
	for _, item := range sectionOutline.Lessons[1:] {
		if item.Order > lastLesson.Order {
			lastLesson = item
		}
		if item.Order < firstLesson.Order {
			firstLesson = item
		}
		if item.UID == lp.LessonUID {
			inputLessonOrder = item.Order
		}
		if item.UID == status.LatestLessonUID {
			latestLessonOrder = item.Order
		}
	}

	lastSection := courseOutline.Outline[0]
	firstSection := courseOutline.Outline[0]
	inputSectionOrder := 0
	latestSectionOrder := -1
	for _, item := range courseOutline.Outline[1:] {
		if item.Order > lastSection.Order {
			lastSection = item
		}
		if item.Order < firstSection.Order {
			firstSection = item
		}
		if item.UID == lp.SectionUID {
			inputSectionOrder = item.Order
		}
		if item.UID == status.LatestSectionUID {
			latestSectionOrder = item.Order
		}
	}

	lessonContent, coOk := lo.Find(lessonContents, func(item *models.LessonContent) bool {
		return item.UID == lp.LessonContentUID
	})
	if !coOk {
		return nil, e.NewError400(e.Lesson_content_not_found, fmt.Sprintf("Lessson content UID %s not found", lp.LessonContentUID))
	}

	lessonContentsByLesson := lo.Filter(lessonContents, func(content *models.LessonContent, _ int) bool {
		return content.LessonID == lessonOutline.ID
	})

	lastContent := lessonContentsByLesson[0]
	for _, item := range lessonContentsByLesson[1:] {
		if item.Order > lastContent.Order {
			lastContent = item
		}
	}

	nowMilli := time.Now().UnixMilli()
	sections := status.Sections
	if sections == nil {
		sections = models.SectionRecordMap{}
	}
	section := sections[lp.SectionUID]
	if section == nil {
		section = &models.SectionRecord{
			SectionUID: lp.SectionUID,
			StartAt:    nowMilli,
		}
	}
	lessons := section.Lessons
	if lessons == nil {
		lessons = models.LessonRecordMap{}
	}
	lesson := lessons[lp.LessonUID]
	if lesson == nil {
		lesson = &models.LessonRecord{
			LessonUID: lp.LessonUID,
			StartAt:   nowMilli,
		}
	}
	contents := lesson.Contents
	if contents == nil {
		contents = models.ContentRecordMap{}
	}
	content := contents[lp.LessonContentUID]
	if content == nil {
		content = &models.ContentRecord{
			ContentUID: lp.LessonContentUID,
			StartAt:    nowMilli,
		}
	}
	content.ContentType = lessonContent.Type
	if lp.CompleteAt > content.CompleteAt {
		content.CompleteAt = lp.CompleteAt
	}
	if lp.PauseAt > content.PauseAt {
		content.PauseAt = lp.PauseAt
	}
	content.TextPercent = lp.TextPercent
	content.PdfCurrentPage = lp.PdfCurrentPage
	content.Quizzes = lp.Quizzes

	// latest learning lesson = current lesson
	status.CurrentSectionUID = lp.SectionUID
	status.CurrentLessonUID = lp.LessonUID

	if lesson.Contents == nil {
		lesson.Contents = make(models.ContentRecordMap)
	}
	lesson.Contents[content.ContentUID] = content

	if section.Lessons == nil {
		section.Lessons = make(models.LessonRecordMap)
	}
	section.Lessons[lesson.LessonUID] = lesson

	sections[section.SectionUID] = section
	status.Sections = sections

	// If user learnt all lesson contents of lesson --> lesson completed
	numLearntContents := 0
	for _, contentRecord := range lesson.Contents {
		if contentRecord.CompleteAt > 0 {
			numLearntContents++
		}
	}
	if numLearntContents >= len(lessonContentsByLesson) {
		lesson.CompleteAt = nowMilli
		*isCurrentLessonComplete = true
	}

	// neu lesson da complete va lesson la last lesson --> section Complete
	if lesson.LessonUID == lastLesson.UID && lesson.CompleteAt > 0 {
		section.CompleteAt = nowMilli
	}

	// neu section da complete va section la last section --> status Complete
	if section.SectionUID == lastSection.UID && section.CompleteAt > 0 {
		status.CompletedAt = nowMilli
	}

	// Case 1st lesson of 1st section is completed then
	// handle ref for external game
	if lesson.LessonUID == firstLesson.UID && lesson.CompleteAt > 0 {
		if aErr := NewLearningStatus(s.ctx).HandleUserRefGame(lp.OrgID, lp.UserID, courseOutline.Cuid); aErr != nil {
			log.Errorf("LearningStatusService::appendLearningStatus Handle user ref game failed: %v", aErr)
		}
	}

	if (inputSectionOrder > latestSectionOrder) ||
		(inputSectionOrder == latestSectionOrder && inputLessonOrder > latestLessonOrder) {

		status.LatestSectionUID = lp.SectionUID
		status.LatestLessonUID = lp.LessonUID
	}

	return status, nil
}

func (s *LearningStatusService) buildLearningProgressOverview(
	learningStatus *models.LearningStatus,
	allContents []*models.LessonContent,
	courseOutline *models.Course,
) *models.LearningProgressOverview {

	if learningStatus == nil {
		return &models.LearningProgressOverview{
			CourseCuid:       courseOutline.Cuid,
			TotalSection:     courseOutline.ActiveSection,
			TotalLesson:      courseOutline.ActiveLesson,
			CompleteAt:       0,
			CompletedSection: 0,
			CompletedLesson:  0,
			SectionByUID:     make(map[string]*models.SectionLearningProgress),
		}
	}

	overview := &models.LearningProgressOverview{
		CourseCuid:       courseOutline.Cuid,
		TotalSection:     courseOutline.ActiveSection,
		TotalLesson:      courseOutline.ActiveLesson,
		CompleteAt:       learningStatus.CompletedAt,
		CompletedSection: 0,
		CompletedLesson:  0,
		SectionByUID:     make(map[string]*models.SectionLearningProgress),
	}
	if len(learningStatus.Sections) <= 0 {
		return overview
	}

	sectionOutlineByUID := make(map[string]*models.Section)
	for _, section := range courseOutline.Outline {
		sectionOutlineByUID[section.UID] = section
	}

	for sectionUID, sectionStt := range learningStatus.Sections {
		sectionOutline, sFound := sectionOutlineByUID[sectionStt.SectionUID]
		if !sFound {
			log.Debugf(
				"LearningStatusService::LearningStatusService::sectionOutline: section_uid=%s not found",
				sectionStt.SectionUID,
			)
			continue
		}

		var activeLesson int
		lessonOutlineByUID := make(map[string]*models.Section)
		for _, lesson := range sectionOutline.Lessons {
			if lesson.Status == models.SectionStatusPublish {
				activeLesson++
			}
			lessonOutlineByUID[lesson.UID] = lesson
		}

		log.Debugf(
			"LearningStatusService::LearningStatusService::sectionOutline: section_uid=%s, section_id=%s, active_lesson=%d, lessons=%d",
			sectionOutline.UID,
			sectionOutline.ID,
			activeLesson,
			len(sectionOutline.Lessons),
		)

		sectionProgress := &models.SectionLearningProgress{
			SectionUID:      sectionUID,
			TotalLesson:     activeLesson,
			CompletedLesson: 0,
			CompleteAt:      sectionStt.CompleteAt,
			LessonByUID:     make(map[string]*models.LessonLearningProgress),
		}

		// lesson calculate
		for lessonUID, lessonStt := range sectionStt.Lessons {
			lessonOutline, lFound := lessonOutlineByUID[lessonUID]
			if !lFound {
				continue
			}

			contentOutlineByUID := make(map[string]*models.LessonContent)
			for _, content := range allContents {
				if content.LessonID == lessonOutline.ID {
					contentOutlineByUID[content.UID] = content
				}
			}

			lessonProgress := &models.LessonLearningProgress{
				LessonUID:              lessonUID,
				TotalLessonContent:     len(contentOutlineByUID),
				CompletedLessonContent: 0,
				CompleteAt:             lessonStt.CompleteAt,
				CompletedPercent:       0,
				LessonContentByUID:     make(map[string]*models.LessonContentLearningProgress),
			}
			if len(contentOutlineByUID) <= 0 {
				continue
			}

			// Calculate lesson content progress
			totalLessonPercent := float64(0)
			latestContentCompleteAt := int64(0)
			for _, contentStt := range lessonStt.Contents {
				contentOutline, cFound := contentOutlineByUID[contentStt.ContentUID]
				if !cFound {
					continue
				}

				lessonContent := &models.LessonContentLearningProgress{
					LessonContentUID: contentOutline.UID,
					CompleteAt:       contentStt.CompleteAt,
					PauseAt:          contentStt.PauseAt,
					StartAt:          contentStt.StartAt,
					ContentType:      contentOutline.Type,
					Duration:         int64(contentOutline.Duration),
					TextPercent:      contentStt.TextPercent,
					VideoPercent:     0,
					PdfCurrentPage:   contentStt.PdfCurrentPage,
				}
				if lessonContent.CompleteAt > latestContentCompleteAt {
					latestContentCompleteAt = lessonContent.CompleteAt
				}
				if lessonContent.ContentType == models.LessonTypeVideo && lessonContent.Duration > 0 {
					videoPercent := float64(lessonContent.PauseAt) / float64(lessonContent.Duration) * 100
					lessonContent.VideoPercent = math.Round(videoPercent*100) / 100
				}
				if lessonContent.CompleteAt > 0 {
					if lessonProgress.CompletedLessonContent < lessonProgress.TotalLessonContent {
						lessonProgress.CompletedLessonContent++
					} else {
						lessonProgress.CompletedLessonContent = lessonProgress.TotalLessonContent
					}
					totalLessonPercent += util.MaxPercent
				} else {
					totalLessonPercent += lessonContent.VideoPercent
				}
				lessonProgress.LessonContentByUID[lessonContent.LessonContentUID] = lessonContent
			}

			if lessonStt.CompleteAt > 0 {
				overview.CompletedLesson++
				sectionProgress.CompletedLesson++
				lessonProgress.CompletedPercent = util.MaxPercent
			} else {
				// LearnerPercent of lesson progress = total percent of content progress / number of contents
				lessonPercent := totalLessonPercent / float64(lessonProgress.TotalLessonContent)
				lessonProgress.CompletedPercent = math.Round(lessonPercent*100) / 100
			}

			if lessonProgress.CompleteAt == 0 && lessonProgress.CompletedPercent == util.MaxPercent {
				if _, sOk := learningStatus.Sections[sectionUID]; sOk {
					if _, lOk := learningStatus.Sections[sectionUID].Lessons[lessonUID]; lOk {
						learningStatus.Sections[sectionUID].Lessons[lessonUID].CompleteAt = latestContentCompleteAt
						if updateErr := models.Repository.LearningStatus(s.ctx).Update(learningStatus, nil); updateErr != nil {
							log.ErrorWithAlertf("LearningStatusService::buildLearningProgressOveriew Update lesson complete at failed: %v", updateErr)
						}
						lessonProgress.CompleteAt = latestContentCompleteAt
						sectionProgress.CompletedLesson++
						lessonProgress.CompletedPercent = util.MaxPercent
					}
				}
			}
			sectionProgress.LessonByUID[lessonProgress.LessonUID] = lessonProgress
		}
		if sectionStt.CompleteAt > 0 {
			overview.CompletedSection++
		}
		overview.SectionByUID[sectionUID] = sectionProgress
	}

	return overview
}

func (s *LearningStatusService) UpdateLatestLesson(req *dto.UpdateCurrentLessonRequest) (*models.LearningProgressOverview, *models.Course, *e.AppError) {
	publishCourse, pErr := NewPublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
		CourseSlug: &req.CourseSlug,
	}, nil)
	if pErr != nil {
		return nil, nil, pErr
	}

	course, cErr := Course.FindById(publishCourse.CourseID, false, nil)
	if cErr != nil {
		return nil, nil, cErr
	}

	courseOutline, oErr := NewCourse(s.ctx).GetOutline(course)
	if oErr != nil {
		return nil, nil, oErr
	}

	allLessonContents, appErr := NewLessonContent(s.ctx).FindByCourseID(courseOutline.ID, nil)
	if appErr != nil {
		log.Errorf("Find contents for course failed: %v", appErr)
		return nil, nil, appErr
	}

	status, lsErr := models.Repository.LearningStatus(s.ctx).FindOne(&models.LearningStatusQuery{
		UserID:     util.NewString(req.UserID),
		CourseCuid: util.NewString(courseOutline.Cuid),
	}, nil)

	if lsErr != nil && !errors.Is(lsErr, gorm.ErrRecordNotFound) {
		appErr = e.NewError500(e.LearningStatusNotFound, "Find learning status failed: "+lsErr.Error())
		return nil, nil, appErr
	} else {
		if status == nil {
			newStatus := &models.LearningStatus{
				UserID:      req.UserID,
				OrgID:       req.OrgID,
				CourseCuid:  courseOutline.Cuid,
				StartAt:     time.Now().UnixMilli(),
				CompletedAt: 0,
			}
			if createErr := models.Repository.LearningStatus(s.ctx).Create(newStatus, nil); createErr != nil {
				return nil, nil, e.NewError500(e.CreateLearningStatusFailed, "Create learning status failed: "+createErr.Error())
			} else {
				status = newStatus
			}
		}
	}

	status.CurrentLessonUID = req.CurrentLessonUID
	status.CurrentSectionUID = req.CurrentSectionUID
	if updateErr := models.Repository.LearningStatus(s.ctx).Update(status, nil); updateErr != nil {
		return nil, nil, e.NewError500(e.UpdateLearningStatusFailed, "Update learning status failed: "+updateErr.Error())
	}

	return s.buildLearningProgressOverview(status, allLessonContents, courseOutline), courseOutline, nil
}

func (s *LearningStatusService) FindOne(query *models.LearningStatusQuery, options *models.FindOneOptions) (*models.LearningStatus, *e.AppError) {
	if lp, err := models.Repository.LearningStatus(s.ctx).FindOne(query, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.Learning_progress_not_found, err.Error())
		}
		return nil, e.NewError500(e.Find_learning_progress_failed, err.Error())
	} else {
		return lp, nil
	}
}

func (s *LearningStatusService) FindMany(query *models.LearningStatusQuery, options *models.FindManyOptions) ([]*models.LearningStatus, *e.AppError) {
	if lp, err := models.Repository.LearningStatus(s.ctx).FindMany(query, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.Learning_progress_not_found, err.Error())
		}
		return nil, e.NewError500(e.Find_learning_progress_failed, err.Error())
	} else {
		return lp, nil
	}
}

func (s *LearningStatusService) CheckCompleteCourse(req dto.CheckUserCompleteCourse) ([]dto.CheckUserCompleteCourseResp, *e.AppError) {
	if len(req.UserIDs) == 0 {
		return []dto.CheckUserCompleteCourseResp{}, nil
	}

	lpQuery := &models.LearningStatusQuery{
		UserIDIn:   req.UserIDs,
		CourseCuid: &req.CourseCuid,
	}
	lps, findErr := s.FindMany(lpQuery, &models.FindManyOptions{})
	if findErr != nil {
		return nil, findErr
	}

	mapUserIDLP := make(map[string]*models.LearningStatus)
	for _, lp := range lps {
		mapUserIDLP[lp.UserID] = lp
	}

	var res []dto.CheckUserCompleteCourseResp

	for _, userID := range req.UserIDs {
		nResp := dto.CheckUserCompleteCourseResp{
			UserID: userID,
			Status: models.CourseProgressStatusNotStarted,
		}
		if lp, ok := mapUserIDLP[userID]; ok {
			if lp.CompletedAt != 0 {
				nResp.Status = models.CourseProgressStatusCompleted
			} else {
				nResp.Status = models.CourseProgressStatusInpProgress
			}
		}
		res = append(res, nResp)
	}

	if req.Status != nil {
		var filterResp []dto.CheckUserCompleteCourseResp
		switch *req.Status {
		case models.CourseProgressStatusCompleted:
			filterResp = lo.Filter(res, func(item dto.CheckUserCompleteCourseResp, _ int) bool {
				return item.Status == models.CourseProgressStatusCompleted
			})
		case models.CourseProgressStatusInpProgress:
			filterResp = lo.Filter(res, func(item dto.CheckUserCompleteCourseResp, _ int) bool {
				return item.Status == models.CourseProgressStatusInpProgress
			})
		case models.CourseProgressStatusNotStarted:
			filterResp = lo.Filter(res, func(item dto.CheckUserCompleteCourseResp, _ int) bool {
				return item.Status == models.CourseProgressStatusNotStarted
			})

		}
		return filterResp, nil
	}

	return res, nil
}

func (s *LearningStatusService) Count(query *models.LearningStatusQuery) (int64, *e.AppError) {
	count, err := models.Repository.LearningStatus(s.ctx).Count(query)
	if err != nil {
		return 0, e.NewError400(e.Count_learning_progress_failed, err.Error())
	}
	return count, nil
}

func (s *LearningStatusService) HandleRemindLearner() {
	learningStatus, err := models.Repository.LearningStatus(s.ctx).FindLatestLearningStatusManyUser(&models.LearningStatusQuery{UpdateAtLte: util.NewT(util.GetUnixMillisNDaysAgo(3))}, &models.FindManyOptions{})
	if err != nil {
		log.ErrorWithAlertf("Error on handle remind learner: %#v", err)
	}

	mapCourseCuidCourse := map[string]*models.Course{}

	for _, ls := range learningStatus {
		var course *models.Course
		var ok bool
		course, ok = mapCourseCuidCourse[ls.CourseCuid]
		if !ok {
			course, err = models.Repository.Course(s.ctx).FindOne(&models.CourseQuery{Cuid: &ls.CourseCuid}, &models.FindOneOptions{})
			if err != nil {
				log.ErrorWithAlertf("Error on handle remind learner: %#v", err)
			}
			mapCourseCuidCourse[ls.CourseCuid] = course
		}

		if util.IsTimestampOlderThanNDays(int64(ls.CreateAt), 7) {
			req := &communicationdto.PushNotificationRequest{
				Code: communicationdto.CodeRemindLongBreak,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{ls.UserID},
					},
				},
				Props: communicationdto.JSONB(MakeNotificationPropsForLastestLesson(ls)),
			}
			if err := communication.Notification.PushNotification(req); err != nil {

				log.Errorf("Push notification after break time error: %v", err)
			}
			continue
		}

		if util.IsTimestampOlderThanNDays(int64(ls.CreateAt), 3) {
			req := &communicationdto.PushNotificationRequest{
				Code: communicationdto.CodeRemindShortBreak,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{ls.UserID},
					},
				},
				Props: communicationdto.JSONB(MakeNotificationPropsForLastestLesson(ls)),
			}
			if err := communication.Notification.PushNotification(req); err != nil {

				log.Errorf("Push notification after break time error: %v", err)
			}

			// SendEmail
			aigov := models.GetConfig[models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
			if ls.Org.ID == aigov.OrgID {
				emailReq := &communicationdto.SendEmailRequest{
					User: ls.User.IntoComm(),
					Org:  ls.Org.IntoComm(),
					Code: util.NewT(communicationdto.EmailCodeRemind),
					ExtendDatas: communicationdto.MapEmailParams{
						"user_name":   ls.User.Username,
						"user_email":  ls.User.Email,
						"course_slug": course.Slug,
						"course_name": course.Name,
					},
					From:    ls.Org.Settings.SenderEmail,
					IsQueue: false,
				}
				if _, err := communication.Email.SendEmail(emailReq); err != nil {
					log.ErrorWithAlertf("CompleteCourse::Send email failed: %v", err)
				}
			}

		}
	}
}
