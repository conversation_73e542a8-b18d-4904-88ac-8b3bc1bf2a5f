package services

import (
	"openedu-core/models"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
)

func (s *OEAIGovCampaignService) SendEmailInviteModExitAccount(user *models.User, org *models.Organization, campaignKey string) *e.AppError {
	mailParams := commdto.MapEmailParams{
		commdto.EmailParamFullName: user.MyDisplayName(),
		commdto.EmailParamEmail:    user.Email,
		commdto.EmailParamUsername: user.Username,
	}

	go func() {
		if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       commdto.EventAiGovernmentInviteModExistAccount,
			ExtendDatas: mailParams,
			IsQueue:     true,
			From:        org.Settings.SenderEmail,
		}); eErr != nil {
			log.ErrorWithAlertf("OEReferralService.InviteReferee::Send email failed: %v", eErr)
		}
	}()
	return nil
}

func (s *OEAIGovCampaignService) SendEmailInviteModNewAccount(user *models.User, org *models.Organization, campaignKey string) *e.AppError {
	// send mail
	mailParams := commdto.MapEmailParams{
		commdto.EmailParamFullName: user.MyDisplayName(),
		commdto.EmailParamEmail:    user.Email,
		commdto.EmailParamUsername: user.Username,
	}

	go func() {
		if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       commdto.EventAiGovernmentInviteModNewAccount,
			ExtendDatas: mailParams,
			IsQueue:     true,
			From:        org.Settings.SenderEmail,
		}); eErr != nil {
			log.ErrorWithAlertf("OEReferralService.InviteReferee::Send email failed: %v", eErr)
		}
	}()
	return nil
}
