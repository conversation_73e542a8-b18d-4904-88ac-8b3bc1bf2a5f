package services

import (
	"errors"
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"
)

func (s *OEReferralLeaderBoardService) ListLeaderBoardByCampaignAndLevel(campaignKey string, ldQuery *models.OEReferralLeaderBoardQuery, options *models.FindManyOptions) ([]*dto.OEReferralLocalResponse, *e.AppError) {
	leaderBoardTbl := models.GetTblName(models.OEReferralLeaderBoardTbl)
	refCodeTbl := models.GetTblName(models.OEReferralCodeTbl)
	var results []*dto.OEReferralLocalResponse
	query := fmt.Sprintf(`
select 
ldb.*, 
r_code.code as ref_code,
(select count(child.id) from %[2]s child WHERE parent_id = ldb.user_id) as unit_count
FROM %[2]s as ldb
LEFT JOIN %[3]s as r_code on r_code.user_id = ldb.user_id 
WHERE ldb.campaign_key = '%[1]s' and ldb.delete_at = 0
`, campaignKey, leaderBoardTbl, refCodeTbl)

	if ldQuery.LocalLevel != nil {
		query += fmt.Sprintf(" and local_level = %d", *ldQuery.LocalLevel)
	}

	if ldQuery.ParentID != nil {
		query += fmt.Sprintf(" and parent_id = '%s'", *ldQuery.ParentID)
	}

	if options != nil {
		sortClause := strings.Join(options.Sort, ",")
		if len(sortClause) > 0 {
			query += fmt.Sprintf(" ORDER BY %s", sortClause)
		}
	}

	err := models.DB.Debug().Raw(query).Scan(&results).Error
	if err != nil {
		return nil, e.NewError500(e.ERROR, "OEReferralStatistic: "+err.Error())
	}
	return results, nil
}

func (s *OEReferralLeaderBoardService) UpdateLeaderBoardByCampaign(campaignID string) *e.AppError {
	_, err := models.Repository.OECampaignAccount(s.ctx).FindMany(&models.OECampaignAccountQuery{
		CampaignID: util.NewString(campaignID),
		Active:     util.NewBool(true),
	}, &models.FindManyOptions{
		Preloads: []string{models.UserField},
	})
	if err != nil {
		return e.NewError500(e.ERROR, "Find all oe by campaign"+err.Error())
	}

	return nil
}

func (s *OEReferralLeaderBoardService) OEReferralStatistic(campaignKey string, ldQuery *models.OEReferralLeaderBoardQuery) (*dto.OEReferralStatisticResponse, *e.AppError) {
	leaderBoardTbl := models.GetTblName(models.OEReferralLeaderBoardTbl)
	result := dto.OEReferralStatisticResponse{}
	query := fmt.Sprintf(`
SELECT sum(ref_count) as ref_count, SUM(cert_count) as cert_count, SUM(register_count) as register_count
FROM %[2]s 
WHERE campaign_key = '%[1]s' AND delete_at = 0 AND display_name <> '%[3]s'
`, campaignKey, leaderBoardTbl, util.ProvinceOther)

	if ldQuery.LocalLevel != nil {
		query += fmt.Sprintf(" and local_level = %d", *ldQuery.LocalLevel)
	}

	if ldQuery.ParentID != nil {
		query += fmt.Sprintf(" and parent_id = '%s'", *ldQuery.ParentID)
	}

	err := models.DB.Debug().Raw(query).Scan(&result).Error
	if err != nil {
		return nil, e.NewError500(e.ERROR, "OEReferralStatistic: "+err.Error())
	}
	return &result, nil
}

func (s *OEReferralLeaderBoardService) Create(entity *models.OEReferralLeaderBoard) (*models.OEReferralLeaderBoard, *e.AppError) {
	if err := models.Repository.OEReferralLeaderBoard(s.ctx).Create(entity, nil); err != nil {
		return nil, e.NewError500(e.OEReferralLeaderBoardCreateFailed, "create: "+err.Error())
	} else {
		return entity, nil
	}
}

func (s *OEReferralLeaderBoardService) Update(entity *models.OEReferralLeaderBoard) (*models.OEReferralLeaderBoard, *e.AppError) {
	if err := models.Repository.OEReferralLeaderBoard(s.ctx).Update(entity, nil); err != nil {
		return nil, e.NewError500(e.OEReferralLeaderBoardUpdateFailed, "create: "+err.Error())
	} else {
		return entity, nil
	}
}

func (s *OEReferralLeaderBoardService) FindOne(query *models.OEReferralLeaderBoardQuery, options *models.FindOneOptions) (*models.OEReferralLeaderBoard, *e.AppError) {
	if plan, err := models.Repository.OEReferralLeaderBoard(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OEReferralLeaderBoardNotFound, "FindOne: "+err.Error())
		}
		return nil, e.NewError500(e.OEReferralLeaderBoardFindOneFailed, "FindOne: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEReferralLeaderBoardService) FindById(id string, options *models.FindOneOptions) (*models.OEReferralLeaderBoard, *e.AppError) {
	if plan, err := models.Repository.OEReferralLeaderBoard(s.ctx).FindOne(&models.OEReferralLeaderBoardQuery{ID: util.NewString(id)}, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OEReferralLeaderBoardNotFound, "FindById: "+err.Error())
		}
		return nil, e.NewError500(e.OEReferralLeaderBoardFindByIDFailed, "FindById: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEReferralLeaderBoardService) FindPage(query *models.OEReferralLeaderBoardQuery, options *models.FindPageOptions) ([]*models.OEReferralLeaderBoard, *models.Pagination, *e.AppError) {
	if plans, pagination, err := models.Repository.OEReferralLeaderBoard(s.ctx).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.OEReferralLeaderBoardFindPageFailed, "FindPage: "+err.Error())
	} else {
		return plans, pagination, nil
	}
}

func (s *OEReferralLeaderBoardService) FindMany(query *models.OEReferralLeaderBoardQuery, options *models.FindManyOptions) ([]*models.OEReferralLeaderBoard, *e.AppError) {
	if plans, err := models.Repository.OEReferralLeaderBoard(s.ctx).FindMany(query, options); err != nil {
		return nil, e.NewError500(e.OEReferralLeaderBoardFindManyFailed, "FindMany: "+err.Error())
	} else {
		return plans, nil
	}
}

func (s *OEReferralLeaderBoardService) Delete(id string) *e.AppError {
	if err := models.Repository.OEReferralLeaderBoard(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.OEReferralLeaderBoardDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}

func (s *OEReferralLeaderBoardService) CreateForUser(request *dto.OELeaderBoardAddMod) *e.AppError {
	org := app.GetOrganization(s.ctx)
	exist, eErr := s.FindOne(&models.OEReferralLeaderBoardQuery{
		UserID:      util.NewString(request.User.ID),
		OrgID:       util.NewString(org.ID),
		CampaignKey: util.NewString(request.CampaignKey),
	}, nil)
	if eErr != nil && eErr.ErrCode != e.OEReferralLeaderBoardNotFound {
		return eErr
	}

	if exist != nil {
		return e.NewError400(e.OEReferralLeaderBoardExisted, "leader board already exists")
	}

	parentID := ""
	parentName := ""
	if request.Parent != nil {
		parentID = request.Parent.ID
		parentName = request.Parent.DisplayName
	}
	leader := models.OEReferralLeaderBoard{
		OrgID:       org.ID,
		UserID:      request.User.ID,
		ParentID:    parentID,
		ParentName:  parentName,
		LocalLevel:  request.LocalLevel,
		DisplayName: request.DisplayName,
		Email:       request.User.Email,
		CampaignKey: request.CampaignKey,
		RefCodeID:   request.RefCodeID,
		RefCode:     request.RefCode,
	}

	if _, err := s.Create(&leader); err != nil {
		return err
	}
	return nil
}

func (s *OEReferralLeaderBoardService) UpdateInfo(leader *models.OEReferralLeaderBoard, displayName string, customCode string) (*models.OEReferralLeaderBoard, *e.AppError) {
	// IF request to update ref code then check to update
	var refCode *models.OEReferralCode
	if customCode != "" {
		activeCode, acErr := OEReferralCode(s.ctx).GetUserReferralCode(leader.UserID)
		if acErr != nil {
			return nil, acErr
		}
		if customCode != activeCode.Code {
			updatedCode, ucErr := OEReferralCode(s.ctx).UpdateCustomCode(activeCode, customCode)
			if ucErr != nil {
				return nil, ucErr
			}
			activeCode = updatedCode
		}
		refCode = activeCode
	}

	relatedRecords, findErr := models.Repository.OEReferralLeaderBoard(s.ctx).FindMany(&models.OEReferralLeaderBoardQuery{
		UserID:      util.NewString(leader.UserID),
		CampaignKey: util.NewString(leader.CampaignKey),
		LocalLevel:  util.NewT(leader.LocalLevel),
	}, nil)
	if findErr != nil {
		return nil, e.NewError500(e.OEReferralLeaderBoardFindManyFailed, "OEReferralLeaderBoard(s.ctx).FindMany: "+findErr.Error())
	}

	if len(relatedRecords) > 0 {
		for _, record := range relatedRecords {
			if refCode != nil {
				record.RefCodeID = refCode.ID
				record.RefCode = refCode.Code
			}
			record.DisplayName = displayName
			if nle, err := s.Update(record); err != nil {
				return nil, err
			} else {
				if nle.ID == leader.ID {
					leader = nle
				}
			}
		}
	}

	return leader, nil
}

func (s *OEReferralLeaderBoardService) DeleteRecord(leader *models.OEReferralLeaderBoard) *e.AppError {
	relatedRecords, findErr := models.Repository.OEReferralLeaderBoard(s.ctx).FindMany(&models.OEReferralLeaderBoardQuery{
		UserID:      util.NewString(leader.UserID),
		CampaignKey: util.NewString(leader.CampaignKey),
		LocalLevel:  util.NewT(leader.LocalLevel),
	}, nil)
	if findErr != nil {
		return e.NewError500(e.OEReferralLeaderBoardFindManyFailed, "OEReferralLeaderBoard(s.ctx).FindMany: "+findErr.Error())
	}
	if len(relatedRecords) > 0 {
		for _, record := range relatedRecords {
			if delErr := s.Delete(record.ID); delErr != nil {
				return delErr
			}
		}
		uros, rErr := models.Repository.UserRoleOrg.FindByUserId(leader.UserID)
		if rErr != nil {
			return e.NewError500(e.Error_user_role_org_find_failed, "OEReferralLeaderBoardService.DeleteRecord.FindByUserId: "+rErr.Error())
		}

		orgModRoles := []string{models.OrgModeratorRoleType, models.OrgModerator2RoleType}
		for _, uro := range uros {
			if uro.OrgID == leader.OrgID && lo.Contains(orgModRoles, uro.RoleID) {
				if urErr := models.Repository.UserRoleOrg.Delete(uro.ID, nil); urErr != nil {
					return e.NewError500(e.UserRemoveRoleFailed, "OEReferralLeaderBoardService.DeleteRecord.UserRoleOrg.Delete: "+urErr.Error())
				}
			}
		}
	}

	return nil
}

func (s *OEReferralLeaderBoardService) FakeChutData(campaignKey string) *e.AppError {
	usersProvinces, _, _ := models.Repository.User.FindPage(&models.UserQuery{}, &models.FindPageOptions{Sort: []string{"create_at asc"}, PerPage: 50, Page: 1})
	usersLocals, _, _ := models.Repository.User.FindPage(&models.UserQuery{}, &models.FindPageOptions{Sort: []string{"create_at asc"}, PerPage: 300, Page: 2})
	course, _ := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{}, &models.FindOneOptions{
		Sort: []string{"create_at asc"},
	})

	for _, user := range usersProvinces {
		registerCount := util.GetRandomNumber(1, 10000)
		refCount := util.GetRandomNumber(1, registerCount)
		completedCourse := util.GetRandomNumber(1, registerCount)
		certCount := util.GetRandomNumber(1, completedCourse)
		perCertOnRef, _ := util.RoundDivision(float64(certCount), float64(refCount), 2)
		perCertOnReg, _ := util.RoundDivision(float64(certCount), float64(registerCount), 2)
		leader := models.OEReferralLeaderBoard{
			UserID:               user.ID,
			DisplayName:          user.MyDisplayName(),
			LocalLevel:           models.LocalLevelProvince,
			Email:                user.Email,
			RefCount:             int64(refCount),
			RegisterCount:        int64(registerCount),
			CertCount:            int64(certCount),
			CompletedCourseCount: int64(completedCourse),
			PercentCertOnRef:     perCertOnRef,
			PercentCertOnReg:     perCertOnReg,
			CourseCuid:           course.CourseCuid,
			CampaignKey:          campaignKey,
		}

		models.Repository.OEReferralLeaderBoard(s.ctx).Create(&leader, nil)
	}

	for _, user := range usersLocals {
		province, _ := util.GetRandomElement(usersProvinces)
		registerCount := util.GetRandomNumber(1, 10000)
		refCount := util.GetRandomNumber(1, registerCount)
		completedCourse := util.GetRandomNumber(1, registerCount)
		certCount := util.GetRandomNumber(1, completedCourse)
		perCertOnRef, _ := util.RoundDivision(float64(certCount), float64(refCount), 2)
		perCertOnReg, _ := util.RoundDivision(float64(certCount), float64(registerCount), 2)
		leader := models.OEReferralLeaderBoard{
			UserID:               user.ID,
			DisplayName:          user.MyDisplayName(),
			LocalLevel:           models.LocalLevelUnit,
			ParentID:             province.ID,
			ParentName:           province.DisplayName,
			Email:                user.Email,
			RefCount:             int64(refCount),
			RegisterCount:        int64(registerCount),
			CertCount:            int64(certCount),
			CompletedCourseCount: int64(completedCourse),
			PercentCertOnRef:     perCertOnRef,
			PercentCertOnReg:     perCertOnReg,
			CourseCuid:           course.CourseCuid,
			CampaignKey:          campaignKey,
		}

		models.Repository.OEReferralLeaderBoard(s.ctx).Create(&leader, nil)
	}

	return nil
}

func (s *OEReferralLeaderBoardService) InitProvinceByCampaign() *e.AppError {
	provinces := util.DefaultProvinceVN()
	aiCampaign := models.GetConfig[*models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	for _, province := range provinces {
		proName := province["label"].(string)
		leader, fErr := models.Repository.OEReferralLeaderBoard(s.ctx).FindOne(&models.OEReferralLeaderBoardQuery{
			OrgID:       util.NewString(aiCampaign.OrgID),
			CampaignKey: util.NewString(aiCampaign.CampaignKey),
			DisplayName: util.NewString(proName),
		}, &models.FindOneOptions{})
		if fErr != nil && !errors.Is(fErr, gorm.ErrRecordNotFound) {
			log.ErrorWithAlertf("OEReferralLeaderBoard(s.ctx).FindOne: %v", fErr)
			return nil
		}
		if leader == nil {
			newLeader := models.OEReferralLeaderBoard{
				OrgID:       aiCampaign.OrgID,
				DisplayName: proName,
				LocalLevel:  models.LocalLevelProvince,
				Email:       util.GenerateUsername(proName) + setting.DefaultEmailDomain(),
				CampaignKey: aiCampaign.CampaignKey,
			}
			_, createErr := s.Create(&newLeader)
			if createErr != nil {
				log.ErrorWithAlertf("OEReferralLeaderBoard.Create: %v", fErr)
			}
		}
	}
	return nil
}
