package sns

import (
	"openedu-core/pkg/util"
)

type SNSInfo struct {
	Provider             util.SNSProvider `json:"provider"`
	SnsId                string           `json:"sns_id"`
	Username             string           `json:"username"`
	DisplayName          string           `json:"display_name"`
	Email                string           `json:"email"`
	Avatar               string           `json:"avatar"`
	AccessToken          string           `json:"access_token"`
	AccessTokenExpireIn  int              `json:"access_token_expire_in"`
	RefreshToken         string           `json:"refresh_token"`
	RefreshTokenExpireIn int              `json:"refresh_token_expires_in"`
	Data                 any              `json:"data"`
}

type RequestTokenInfo struct {
	OauthToken             string `json:"oauth_token"`
	OauthCallbackConfirmed bool   `json:"oauth_callback_confirmed"`
}

type SNSIface interface {
	VerifyCode(code string, codeVerifier string) (SNSInfo, error)
	SetRedirectURIFromDomain(domain string)
}

func GetSNS(provider util.SNSProvider) (SNSIface, error) {
	switch provider {
	case util.Google:
		google := NewGoogle()
		return google, nil
	case util.Facebook:
		facebook := NewFaceBook()
		return facebook, nil
	default:
		return nil, nil
	}
}

func GetSNSInfo(provider util.SNSProvider, code string, codeVerifier string, domain string) (SNSInfo, error) {
	sns, err := GetSNS(provider)
	if err != nil {
		return SNSInfo{}, err
	}

	if domain != "" {
		sns.SetRedirectURIFromDomain(domain)
	}

	return sns.VerifyCode(code, codeVerifier)
}

func GetAllSupportedProvider() ([]string, error) {
	var allProvider []string
	for _, snsProvider := range util.SupportedSNSProvider {
		allProvider = append(allProvider, string(snsProvider))
	}

	return allProvider, nil
}
