package services

import (
	"fmt"
	"openedu-core/cache_clients"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/exchange"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"
)

func (s *ExchangeRateService) GetExchangeRates() (*models.ExchangeRates, *e.AppError) {
	fiatRates, err := s.getFiatExchangeRate()
	if err != nil {
		return nil, e.NewError500(e.WalletGetExchangeRatesFailed, "Get fiat exchange rate error: "+err.Error())
	}

	cryptoRates, err := s.getCryptoExchangeRate()
	if err != nil {
		return nil, e.NewError500(e.WalletGetExchangeRatesFailed, "Get crypto exchange rate error: "+err.Error())
	}

	return &models.ExchangeRates{
		VND2USD:             fiatRates.VND2USD,
		VND2USDLastUpdateAt: fiatRates.Timestamp,
		VND2USDNextUpdateAt: fiatRates.Timestamp + cache_clients.FiatExchangeRateTTl.Milliseconds(),

		USD2VND:             fiatRates.USD2VND,
		USD2VNDLastUpdateAt: fiatRates.Timestamp,
		USD2VNDNextUpdateAt: fiatRates.Timestamp + cache_clients.FiatExchangeRateTTl.Milliseconds(),

		NEAR2USD:  cryptoRates.NEAR2USD,
		USDT2USD:  cryptoRates.USDT2USD,
		USDC2USD:  cryptoRates.USDC2USD,
		AVAIL2USD: cryptoRates.AVAIL2USD,
		ETH2USD:   cryptoRates.ETH2USD,
	}, nil
}

func (s *ExchangeRateService) getFiatExchangeRate() (*exchange.FiatExchangeRates, error) {
	var fiatRatesCache interface{}
	if err := models.Cache.ExchangeRate.GetFiatExchangeRates(&fiatRatesCache); err == nil && fiatRatesCache != nil {
		var fiatRates exchange.FiatExchangeRates
		if err = models.Cache.Convert(fiatRatesCache, &fiatRates); err == nil {
			if time.UnixMilli(fiatRates.Timestamp).Add(cache_clients.FiatExchangeRateTTl).After(time.Now()) {
				return &fiatRates, nil
			}
		}
		log.Errorf("Convert fiat exchange rate cache error: %v", err)
	}

	var fiatRates *exchange.FiatExchangeRates
	var err error
	for i := 0; i < util.MaxTryTimes; i++ {
		time.Sleep(util.RetryAfterInterval)
		fiatRates, err = exchange.GetFiatExchangeRates()
		if err == nil {
			break
		}
	}

	if err != nil {
		return nil, fmt.Errorf("get fiat exchange rates error: %v", err)
	}

	if err = models.Cache.ExchangeRate.SetFiatExchangeRates(&fiatRates); err != nil {
		log.Errorf("Set fiat exchange rates cache error: %v", err)
	}

	return fiatRates, nil
}

func (s *ExchangeRateService) getCryptoExchangeRate() (*exchange.CryptoExchangeRates, error) {
	var cryptoRatesCache interface{}
	if err := models.Cache.ExchangeRate.GetCryptoExchangeRates(&cryptoRatesCache); err == nil && cryptoRatesCache != nil {
		var cryptoRates exchange.CryptoExchangeRates
		if err = models.Cache.Convert(cryptoRatesCache, &cryptoRates); err == nil {
			return &cryptoRates, nil
		}
		log.Errorf("Convert crypto exchange rate cache error: %v", err)
	}

	var cryptoRates *exchange.CryptoExchangeRates
	var err error
	for i := 0; i < util.MaxTryTimes; i++ {
		time.Sleep(util.RetryAfterInterval)
		cryptoRates, err = exchange.GetCryptoExchangeRates()
		if err == nil {
			break
		}
	}

	if err != nil {
		return nil, fmt.Errorf("get crypto exchange rates error: %v", err)
	}

	if err = models.Cache.ExchangeRate.SetCryptoExchangeRates(&cryptoRates); err != nil {
		log.Errorf("Set crypto exchange rates cache error: %v", err)
	}

	return cryptoRates, nil
}
