package services

import (
	"errors"
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
)

func generateRefCode(campaignID string) (string, *e.AppError) {
	code := util.GenerateCode(8)
	if eCode, err := models.Repository.ReferralLink.FindOne(&models.ReferralLinkQuery{
		RefCode:    util.NewString(code),
		CampaignID: util.NewString(campaignID),
	}, nil); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return "", e.New<PERSON>rror500(e.Referral_link_create_failed, "generate code failed: "+err.Error())
		}
	} else {
		if eCode != nil {
			generateRefCode(campaignID)
		}
	}
	return code, nil
}

func (s *ReferralLinkService) CreateReferralLinkByCommission(commission *models.Commission, user *models.User, org *models.Organization) (*models.ReferralLink, *e.AppError) {
	var appliedLink *models.ReferralLink
	link, linkErr := models.Repository.ReferralLink.FindOne(&models.ReferralLinkQuery{
		UserID:       util.NewString(user.ID),
		CampaignID:   util.NewString(commission.CampaignID),
		CommissionID: util.NewString(commission.ID),
		IsExtend:     util.NewBool(false),
	}, nil)
	if linkErr != nil && !errors.Is(linkErr, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Referral_link_create_failed,
			fmt.Sprintf("get ref link for %s, %s, %s, %s", user.ID, commission.CampaignID, commission.ID, linkErr.Error()))
	}

	if link != nil {
		appliedLink = link
	} else {
		_, camErr := AffiliateCampaign.GetAvailableCampaign(commission.CampaignID)
		if camErr != nil {
			return nil, camErr
		}

		refCode, genCodeErr := generateRefCode(commission.CampaignID)
		if genCodeErr != nil {
			return nil, genCodeErr
		}

		newLink := &models.ReferralLink{
			UserID:       user.ID,
			OrgID:        org.ID,
			CampaignID:   commission.CampaignID,
			CommissionID: commission.ID,
			RefCode:      refCode,
			Enable:       true,
		}
		if ulErr := models.Repository.ReferralLink.Create(newLink, nil); ulErr != nil {
			return nil, e.NewError500(e.Referral_link_create_failed,
				fmt.Sprintf("create new link %s", ulErr.Error()))
		} else {
			appliedLink = newLink
		}
	}

	return appliedLink, nil
}

func (s *ReferralLinkService) CreateReferralLink(campaign *models.AffiliateCampaign, user *models.User, org *models.Organization) (*models.ReferralLink, *e.AppError) {
	//commission, err := Commission.GetApplicableCommission(user, campaign)
	//if err != nil {
	//	return nil, err
	//}
	//
	//if commission == nil {
	//	return nil, e.NewError400(e.Campaign_not_commission_match, "User do not permission to create link")
	//}
	//
	//var appliedLink *models.ReferralLink
	//links, linkErr := models.Repository.ReferralLink.FindMany(&models.ReferralLinkQuery{
	//	UserID:     util.NewString(user.ID),
	//	CampaignID: util.NewString(campaign.ID),
	//	//CampaignID:   util.NewString(canApply.Campaign.CampaignID),
	//	//CommissionID: util.NewString(canApply.Commission.ID),
	//	IsExtend: util.NewBool(false),
	//}, nil)
	//if linkErr != nil {
	//	return nil, e.NewError500(e.Referral_link_create_failed,
	//		fmt.Sprintf("get ref link for %s, %s, %s, %s", user.ID, campaign.ID, commission.ID, linkErr.Error()))
	//}
	//createNew := true
	//if links != nil && len(links) > 0 {
	//	link, ok := lo.Find(links, func(item *models.ReferralLink) bool {
	//		return item.CommissionID == commission.ID
	//	})
	//	if ok {
	//		if !link.Enable {
	//			link.Enable = true
	//			if ulErr := models.Repository.ReferralLink.Update(link, nil); ulErr != nil {
	//				return nil, e.NewError500(e.Referral_link_create_failed,
	//					fmt.Sprintf("enable exited link %s %s", link.ID, ulErr.Error()))
	//			}
	//		}
	//		appliedLink = link
	//		createNew = false
	//	}
	//}
	//if createNew {
	//	refCode, genCodeErr := generateRefCode(campaign.ID)
	//	if genCodeErr != nil {
	//		return nil, genCodeErr
	//	}
	//
	//	newLink := &models.ReferralLink{
	//		UserID:       user.ID,
	//		OrgID:        org.ID,
	//		CampaignID:   campaign.ID,
	//		CommissionID: commission.ID,
	//		RefCode:      refCode,
	//		Enable:       true,
	//	}
	//	if ulErr := models.Repository.ReferralLink.Create(newLink, nil); ulErr != nil {
	//		return nil, e.NewError500(e.Referral_link_create_failed,
	//			fmt.Sprintf("create new link %s", ulErr.Error()))
	//	} else {
	//		appliedLink = newLink
	//	}
	//}
	//// disable others existed link
	//if len(links) > 0 {
	//	nonUseLinks := lo.Filter(links, func(item *models.ReferralLink, index int) bool {
	//		return item.CommissionID != commission.ID
	//	})
	//	if len(nonUseLinks) > 0 {
	//		for _, link := range nonUseLinks {
	//			link.Enable = false
	//			if ulErr := models.Repository.ReferralLink.Update(link, nil); ulErr != nil {
	//				return nil, e.NewError500(e.Referral_link_create_failed,
	//					fmt.Sprintf("disable other links %s %s", link.ID, ulErr.Error()))
	//			}
	//		}
	//	}
	//}
	//return appliedLink, nil
	return nil, nil
}

// User can create extend link when Campaign has BaseRate commission
// User can create extend link when user purchased course in campaign
// User buy from any link (kol, agency,..) also extend BaseRate commission
func (s *ReferralLinkService) CreateExtendReferral(user *models.User, fromLink *models.ReferralLink) (*models.ReferralLink, *e.AppError) {
	_, purErr := models.Repository.Referrer.FindOne(&models.ReferrerQuery{
		UserID: util.NewString(user.ID),
		Type:   util.NewT(models.ReferrerTypePurchasedUser),
		Enable: util.NewBool(true),
	}, nil)
	if purErr != nil {
		if errors.Is(purErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Referral_link_require_purchase_to_extend, "user didn't purchase course")
		}
		return nil, e.NewError500(e.Referral_link_create_failed, "find purchased course failed: "+purErr.Error())
	}

	_, camErr := AffiliateCampaign.GetAvailableCampaign(fromLink.CampaignID)
	if camErr != nil {
		return nil, camErr
	}

	baseRateCommission, baseErr := Commission.FindOne(&models.CommissionQuery{
		CampaignID: util.NewString(fromLink.CampaignID),
		Enable:     util.NewBool(true),
		IsBaseRate: util.NewBool(true),
	}, nil)
	if baseErr != nil {
		return nil, baseErr
	}

	exitLink, lErr := models.Repository.ReferralLink.FindOne(&models.ReferralLinkQuery{
		UserID:       util.NewString(user.ID),
		CampaignID:   util.NewString(fromLink.CampaignID),
		CommissionID: util.NewString(baseRateCommission.ID),
		IsExtend:     util.NewBool(true),
		RefLevel1:    util.NewString(fromLink.RefCode),
	}, nil)

	if lErr != nil && !errors.Is(lErr, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Referral_link_create_failed, "find exit link failed: "+lErr.Error())
	}

	if exitLink != nil {
		if !exitLink.Enable {
			return nil, e.NewError400(e.Referral_link_disabled, "link disabled")
		} else {
			return exitLink, nil
		}
	}

	refCode, genCodeErr := generateRefCode(fromLink.CampaignID)
	if genCodeErr != nil {
		return nil, genCodeErr
	}
	newLink := &models.ReferralLink{
		UserID:       user.ID,
		OrgID:        fromLink.OrgID,
		CampaignID:   fromLink.CampaignID,
		CommissionID: baseRateCommission.ID,
		RefCode:      refCode,
		RefLevel1:    fromLink.RefCode,
		RefLevel2:    fromLink.RefLevel1,
		Enable:       true,
		ShareRate:    0,
		IsExtend:     true,
	}
	if ulErr := models.Repository.ReferralLink.Create(newLink, nil); ulErr != nil {
		return nil, e.NewError500(e.Referral_link_create_failed,
			fmt.Sprintf("create new link %s, %s", fromLink.ID, ulErr.Error()))
	}
	return newLink, nil
}

func (s *ReferralLinkService) UserEditRefLink(user *models.User, link *models.ReferralLink, request *dto.UpdateReferralLink) (*models.ReferralLink, *e.AppError) {
	if user.ID != link.UserID {
		return nil, e.NewError400(e.Referral_link_owner_required, "require owner to edit link")
	}
	link.ShareRate = request.ShareRate
	if ulErr := models.Repository.ReferralLink.Update(link, nil); ulErr != nil {
		return nil, e.NewError500(e.Referral_link_update_failed,
			fmt.Sprintf("update link %s %s", link.ID, ulErr.Error()))
	}
	return link, nil
}

func (s *ReferralLinkService) GetExtendedLink(user *models.User, campaign *models.AffiliateCampaign) (*models.ReferralLink, *e.AppError) {
	if link, err := s.FindOne(&models.ReferralLinkQuery{
		UserID:     util.NewString(user.ID),
		CampaignID: util.NewString(campaign.ID),
		Enable:     util.NewBool(true),
		IsExtend:   util.NewBool(true),
	}, nil); err != nil {
		return nil, err
	} else {
		return link, nil
	}
}

func (s *ReferralLinkService) FindPage(query *models.ReferralLinkQuery, options *models.FindPageOptions) ([]*models.ReferralLink, *models.Pagination, *e.AppError) {
	if links, pagination, err := models.Repository.ReferralLink.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Referral_link_find_failed, "find page"+err.Error())
	} else {
		return links, pagination, nil
	}
}

func (s *ReferralLinkService) FindMany(query *models.ReferralLinkQuery, options *models.FindManyOptions) ([]*models.ReferralLink, *e.AppError) {
	if links, err := models.Repository.ReferralLink.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Referral_link_find_failed, "find many"+err.Error())
	} else {
		return links, nil
	}
}

func (s *ReferralLinkService) FindOne(query *models.ReferralLinkQuery, options *models.FindOneOptions) (*models.ReferralLink, *e.AppError) {
	if link, err := models.Repository.ReferralLink.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Referrer_not_found, "findOne")
		}
		return nil, e.NewError500(e.Referral_link_find_failed, "findOne: "+err.Error())
	} else {
		return link, nil
	}
}

func (s *ReferralLinkService) FindByCode(ReferralCode string) (*models.ReferralLink, *e.AppError) {
	query := &models.ReferralLinkQuery{RefCode: util.NewString(ReferralCode)}
	options := &models.FindOneOptions{Preloads: []string{models.CommissionField}}
	if link, err := models.Repository.ReferralLink.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Referral_link_not_found, "findOne")
		}
		return nil, e.NewError500(e.Referral_link_find_failed, "findOne: "+err.Error())
	} else {
		return link, nil
	}
}

func (s *ReferralLinkService) GetLinkByUserAndCampaign(user *models.User, campaign *models.AffiliateCampaign) ([]*models.ReferralLink, *e.AppError) {
	links, err := models.Repository.ReferralLink.FindMany(&models.ReferralLinkQuery{
		UserID:     util.NewString(user.ID),
		CampaignID: util.NewString(campaign.ID),
		Enable:     util.NewBool(true),
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.Referral_link_find_failed, "GetLinkByUserAndCampaign: "+err.Error())
	}

	return links, nil
}

func (s *ReferralLinkService) ValidateReferralCode(req *dto.ValidateReferralCodeRequest) (*models.ReferralLink, *e.AppError) {
	link, err := ReferralLink.FindOne(
		&models.ReferralLinkQuery{RefCode: &req.ReferralCode},
		nil,
	)
	if err != nil {
		log.Error("Find referral link by ref code failed: ", err.Msg)
		return nil, e.NewError400(e.Referral_link_find_failed, "Find the referral code "+req.ReferralCode+" failed: "+err.Msg)
	}

	if !link.Enable {
		return nil, e.NewError400(e.Referral_link_disabled, "The referral code "+req.ReferralCode+" is already disabled")
	}

	// validate campaign activated
	commission, comErr := Commission.FindOne(&models.CommissionQuery{ID: util.NewString(link.CommissionID)}, nil)
	if comErr != nil {
		log.Error("Commission.FindOne: ", comErr.Msg)
		return nil, comErr
	}

	if !commission.Enable {
		return nil, e.NewError400(e.Commission_disabled, " referral code disable")
	}

	campaign, camErr := AffiliateCampaign.GetAvailableCampaign(link.CampaignID)
	if camErr != nil {
		return nil, camErr
	}

	courseCampaigns, _, cErr := AffiliateCampaign.FindPublishCourseByCampaign(campaign, &models.PublishCourseQuery{
		Enable: util.NewBool(true),
	}, &models.FindPageOptions{
		Page:    1,
		PerPage: util.MaxPerPage,
	})
	if cErr != nil {
		return nil, cErr
	}
	_, found := lo.Find(courseCampaigns, func(item *models.CourseCampaign) bool {
		return item.CourseCuid == req.CourseCuid
	})
	if !found {
		return nil, e.NewError400(e.Campaign_not_include_course, "Campaign ID "+campaign.ID+" not included in course CUID "+req.CourseCuid)
	}

	return link, nil
}
